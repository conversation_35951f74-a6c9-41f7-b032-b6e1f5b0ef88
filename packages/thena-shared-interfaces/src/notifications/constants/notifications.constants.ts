export enum NotificationChannelType {
  PUSH = "push",
  TOAST = "toast",
  EMAIL = "email",
  SLACK = "slack",
}

export enum NotificationSubscriptionType {
  MANUAL = "manual",
  AUTO = "auto",
}

export enum NotificationEventType {
  TICKET_CREATED = "ticket_created",
  TICKET_ASSIGNED = "ticket_assigned",
  TICKET_STATUS_CHANGED = "ticket_status_changed",
  TICKET_PRIORITY_CHANGED = "ticket_priority_changed",
  TICKET_ESCALATED = "ticket_escalated",
  TICKET_ARCHIVED = "ticket_archived",
  TICKET_UNARCHIVED = "ticket_unarchived",

  TICKET_CUSTOMER_THREAD_REPLY = "ticket_customer_thread_reply",
  TICKET_CUSTOMER_THREAD_MENTION = "ticket_customer_thread_mention",
  TICKET_INTERNAL_THREAD_REPLY = "ticket_internal_thread_reply",
  TICKET_INTERNAL_THREAD_MENTION = "ticket_internal_thread_mention",
  TICKET_NOTE_REPLY = "ticket_note_reply",
  TICKET_NOTE_MENTION = "ticket_note_mention",

  TICKET_CUSTOM_FIELD_UPDATED = "ticket_custom_field_updated",
  TICKET_TAG_UPDATED = "ticket_tag_updated",

  TICKET_SLA_BREACH_WARNING = "ticket_sla_breach_warning",
  TICKET_SLA_BREACHED = "ticket_sla_breached",

  TICKET_CSAT_RECEIVED = "ticket_csat_received",
  TICKET_CSAT_UPDATED = "ticket_csat_updated",
}

export enum NotificationEventCategory {
  TICKET = "ticket",
}

export enum NotificationGroupingType {
  NO_GROUPING = "no_grouping",
  GROUP_BY_EVENT = "group_by_event",
  GROUP_BY_ENTITY = "group_by_entity",
  GROUP_BY_ENTITY_AND_EVENT = "group_by_entity_and_event",
}

export enum NotificationBatchingType {
  NO_BATCHING = "no_batching",
  BATCH_BY_ENTITY = "batch_by_entity",
}
