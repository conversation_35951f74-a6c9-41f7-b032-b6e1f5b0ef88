/**
 * Filter Type - Types of filters for CSAT rules
 */
export enum CsatFilterType {
  TICKET = "ticket",
  ACCOUNT = "account",
  CONTACT = "contact",
}

/**
 * Filter Operator - Operators for CSAT rule conditions
 */
export enum CsatFilterOperator {
  EQUALS = "=",
  NOT_EQUALS = "!=",
  GREATER_THAN = ">",
  LESS_THAN = "<",
  GREATER_THAN_EQUAL = ">=",
  LESS_THAN_EQUAL = "<=",
  CONTAINS = "contains",
  NOT_CONTAINS = "not_contains",
  IN = "in",
  NOT_IN = "not_in",
  IS_EMPTY = "is_empty",
  IS_NOT_EMPTY = "is_not_empty"
}

/**
 * Mapping Status - Tracks ticket processing lifecycle
 */
export enum CsatMappingStatus {
  CREATED = "created",       // Initial state when ticket is closed
  QUEUED = "queued",         // Queued for processing
  PROCESSING = "processing", // Currently being processed
  PROCESSED = "processed",   // Processing complete
  FAILED = "failed",         // Processing failed
}

/**
 * Sampling Status - Outcome of sampling decision
 */
export enum CsatSamplingStatus {
  PENDING = "pending",         // Awaiting sampling decision
  SELECTED = "selected",       // Selected by sampling algorithm
  NOT_SELECTED = "not_selected", // Not selected by sampling
  EXCLUDED = "excluded",       // Explicitly excluded by rules
}

/**
 * Delivery Status - Survey delivery tracking
 */
export enum CsatDeliveryStatus {
  NOT_APPLICABLE = "not_applicable", // No delivery needed (not selected)
  PENDING = "pending",               // Created but not yet sent
  SCHEDULED = "scheduled",           // Will be sent at future date
  SENDING = "sending",               // In process of being sent
  SENT = "sent",                     // Successfully delivered
  FAILED = "failed",                 // Delivery failed
  MAX_ATTEMPTS = "max_attempts",     // Failed after maximum retry attempts
  CANCELLED = "cancelled",           // Delivery cancelled
}

export enum CsatThumbsValue {
  NEGATIVE = 0,
  POSITIVE = 1
}

export enum FilterValueType {
  STRING = "string",
  NUMBER = "number",
  BOOLEAN = "boolean",
  DATE = "date",
  DATETIME = "datetime",
  TIME = "time",
  TEXT = "text",
  EMAIL = "email",
  PHONE = "phone",
  URL = "url",
  IP = "ip",
  CURRENCY = "currency",
  CHOICE = "choice",
  LOOKUP = "lookup",
  ARRAY = "array" // For multi-select fields or IN/NOT_IN operators
}
/**
 * Response Status - User interaction tracking
 */
export enum CsatResponseStatus {
  NO_RESPONSE = "no_response",     // No response received yet
  VIEWED = "viewed",               // User has viewed the survey
  PARTIAL = "partial",             // Partially completed
  COMPLETED = "completed",         // Fully completed with rating
  EXPIRED = "expired",             // Expired without response
  COMMENTED = "commented",         // Left comment with rating
}

/**
 * Delivery Channel - Channels for survey delivery
 */
export enum CsatDeliveryChannel {
  EMAIL = "email",
  TICKET_SOURCE = "source",
  CUSTOMER_PORTAL = "customer_portal",
}

/**
 * Feedback Types - Types of feedback ratings
 */
export enum CsatFeedbackType {
  STAR = "star",               // Standard 5-star rating
  THUMBS = "thumbs",           // Thumbs up/down
}

/**
 * Survey Actions - Available actions for CSAT surveys
 */
export enum CsatSurveyAction {
  SEND = "send",               // Send survey
  CANCEL = "cancel",           // Cancel survey
  FOLLOW_UP = "follow_up",     // Send follow-up
  EXPIRE = "expire",           // Mark as expired
  RESEND = "resend",           // Resend same survey
}

/**
 * Audit Actions - For tracking changes in the audit log
 */
export enum CsatAuditAction {
  CREATED = "created",
  UPDATED = "updated",
  DELETED = "deleted",
  ENABLED = "enabled",
  DISABLED = "disabled",
  SENT = "sent",
  COMPLETED = "completed",
  FAILED = "failed",
}

/**
 * Survey Status (Legacy) - For backward compatibility
 * @deprecated Use separate status enums instead
 */
export enum CsatSurveyStatus {
  NOT_APPLICABLE = "not_applicable",
  PENDING = "pending",
  CREATED = "created",
  SCHEDULED = "scheduled",
  SENDING = "sending",
  SENT = "sent",
  DELIVERY_FAILED = "delivery_failed",
  VIEWED = "viewed",
  COMPLETED = "completed",
  EXPIRED = "expired",
  CANCELLED = "cancelled",
}
