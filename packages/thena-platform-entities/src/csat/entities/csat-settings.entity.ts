import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { Organization } from "../../organizations/entities/organization.entity";
import { Team } from "../../teams/entities/team.entity";
import { TicketStatus } from "../../tickets/entities/ticket-status.entity";
import { CsatRule } from "./csat-rule.entity";

@Entity("csat_settings")
@Index("idx_csat_settings_team_org", ["team", "organization"], { unique: true })
export class CsatSettings {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @ManyToOne(() => Team, { nullable: false })
  @JoinColumn({ name: "team_id" })
  team: Team;

  @ManyToOne(() => Organization, { nullable: false })
  @JoinColumn({ name: "organization_id" })
  organization: Organization;

  @ManyToMany(() => TicketStatus)
  @JoinTable({
    name: "csat_settings_closed_statuses",
    joinColumn: {
      name: "csat_settings_id",
      referencedColumnName: "id",
    },
    inverseJoinColumn: {
      name: "status_id",
      referencedColumnName: "id",
    },
  })
  closedStatuses: TicketStatus[];

  @Column({ name: "user_cooldown_period_days", type: "int", default: 10 })
  userCooldownPeriodDays: number;

  @Column({ name: "email_config_id", nullable: true, type: "bigint" })
  emailConfigId: string;

  @Column({ default: true, name: "is_enabled" })
  isEnabled: boolean;

  @Column({ type: "int", default: 14, name: "cooldown_period_days" })
  cooldownPeriodDays: number;

  @OneToMany(() => CsatRule, (rule) => rule.csatSettings)
  rules: CsatRule[];

  @CreateDateColumn({ name: "created_at", type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", type: "timestamptz" })
  updatedAt: Date;
}
