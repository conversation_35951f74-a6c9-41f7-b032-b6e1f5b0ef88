import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { CsatDeliveryChannel } from "../constants/csat.constants";
import { CsatRule } from "./csat-rule.entity";

@Entity("csat_feedback_configs")
@Index("idx_feedback_configs_rule_id", ["ruleId"])
export class CsatFeedbackConfig {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @OneToOne(() => CsatRule, (rule) => rule.feedbackConfig, { onDelete: "CASCADE" })
  @JoinColumn({ name: "rule_id" })
  rule: CsatRule;

  @Column({ name: "rule_id" })
  ruleId: string;

  @Column({ default: true, name: "enabled" })
  enabled: boolean;

  @Column({ name: "feedback_type", default: "star" })
  feedbackType: string;

  @Column({ name: "branding_color", default: "#000000" })
  brandingColor: string;

  @Column({ type: "boolean", default: false, name: "include_comment_field" })
  includeCommentField: boolean;

  @Column({ nullable: true, name: "custom_title" })
  customTitle?: string;

  @Column({ nullable: true, name: "custom_message" })
  customMessage?: string;

  @Column({ nullable: true, name: "custom_thank_you_message" })
  customThankYouMessage?: string;
  @Column({
    type: "enum",
    enum: CsatDeliveryChannel,
    default: CsatDeliveryChannel.EMAIL,
    name: "delivery_channel",
  })
  deliveryChannel: CsatDeliveryChannel;

  @Column({ type: "jsonb", default: {}, name: "channel_config" })
  channelConfig: Record<string, any>;

  @CreateDateColumn({ name: "created_at", type: "timestamptz" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at", type: "timestamptz" })
  updatedAt: Date;
}
