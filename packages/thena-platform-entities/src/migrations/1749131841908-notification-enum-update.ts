import { MigrationInterface, QueryRunner } from "typeorm";

export class NotificationEnumUpdate1749131841908 implements MigrationInterface {
  name = "NotificationEnumUpdate1749131841908";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "ticket_sentiment" DROP CONSTRAINT "FK_d121d259023cb6ac6a3f2603c30"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_uniq_ticket_sentiment_uid"`,
    );
    await queryRunner.query(
      `ALTER TABLE "ticket_sentiment" ALTER COLUMN "organization_id" SET NOT NULL`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."unique_user_notification_preference_profile_channel_event"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."user_notification_preferences_event_type_enum" RENAME TO "user_notification_preferences_event_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."user_notification_preferences_event_type_enum" AS ENUM('ticket_created', 'ticket_assigned', 'ticket_status_changed', 'ticket_priority_changed', 'ticket_escalated', 'ticket_archived', 'ticket_unarchived', 'ticket_customer_thread_reply', 'ticket_customer_thread_mention', 'ticket_internal_thread_reply', 'ticket_internal_thread_mention', 'ticket_note_reply', 'ticket_note_mention', 'ticket_custom_field_updated', 'ticket_tag_updated', 'ticket_sla_breach_warning', 'ticket_sla_breached', 'ticket_csat_received', 'ticket_csat_updated')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_notification_preferences" ALTER COLUMN "event_type" TYPE "public"."user_notification_preferences_event_type_enum" USING "event_type"::"text"::"public"."user_notification_preferences_event_type_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."user_notification_preferences_event_type_enum_old"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."unique_notification_subscription_preference_user_event_type"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."notification_subscription_preferences_event_type_enum" RENAME TO "notification_subscription_preferences_event_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."notification_subscription_preferences_event_type_enum" AS ENUM('ticket_created', 'ticket_assigned', 'ticket_status_changed', 'ticket_priority_changed', 'ticket_escalated', 'ticket_archived', 'ticket_unarchived', 'ticket_customer_thread_reply', 'ticket_customer_thread_mention', 'ticket_internal_thread_reply', 'ticket_internal_thread_mention', 'ticket_note_reply', 'ticket_note_mention', 'ticket_custom_field_updated', 'ticket_tag_updated', 'ticket_sla_breach_warning', 'ticket_sla_breached', 'ticket_csat_received', 'ticket_csat_updated')`,
    );
    await queryRunner.query(
      `ALTER TABLE "notification_subscription_preferences" ALTER COLUMN "event_type" TYPE "public"."notification_subscription_preferences_event_type_enum" USING "event_type"::"text"::"public"."notification_subscription_preferences_event_type_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."notification_subscription_preferences_event_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_uniq_ticket_sentiment_uid" ON "ticket_sentiment" ("uid", "organization_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "unique_user_notification_preference_profile_channel_event" ON "user_notification_preferences" ("user_notification_preference_profile_id", "channel_type", "event_type") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "unique_notification_subscription_preference_user_event_type" ON "notification_subscription_preferences" ("user_id", "event_type") `,
    );
    await queryRunner.query(
      `ALTER TABLE "ticket_sentiment" ADD CONSTRAINT "FK_d121d259023cb6ac6a3f2603c30" FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "ticket_sentiment" DROP CONSTRAINT "FK_d121d259023cb6ac6a3f2603c30"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."unique_notification_subscription_preference_user_event_type"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."unique_user_notification_preference_profile_channel_event"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_uniq_ticket_sentiment_uid"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."notification_subscription_preferences_event_type_enum_old" AS ENUM('ticket_created', 'ticket_assigned', 'ticket_status_changed', 'ticket_priority_changed', 'ticket_escalated', 'ticket_archived', 'ticket_customer_thread_reply', 'ticket_customer_thread_mention', 'ticket_internal_thread_reply', 'ticket_internal_thread_mention', 'ticket_note_reply', 'ticket_note_mention', 'ticket_custom_field_updated', 'ticket_tag_updated', 'ticket_sla_breach_warning', 'ticket_sla_breached', 'ticket_csat_received', 'ticket_csat_updated')`,
    );
    await queryRunner.query(
      `ALTER TABLE "notification_subscription_preferences" ALTER COLUMN "event_type" TYPE "public"."notification_subscription_preferences_event_type_enum_old" USING "event_type"::"text"::"public"."notification_subscription_preferences_event_type_enum_old"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."notification_subscription_preferences_event_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."notification_subscription_preferences_event_type_enum_old" RENAME TO "notification_subscription_preferences_event_type_enum"`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "unique_notification_subscription_preference_user_event_type" ON "notification_subscription_preferences" ("event_type", "user_id") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."user_notification_preferences_event_type_enum_old" AS ENUM('ticket_created', 'ticket_assigned', 'ticket_status_changed', 'ticket_priority_changed', 'ticket_escalated', 'ticket_archived', 'ticket_customer_thread_reply', 'ticket_customer_thread_mention', 'ticket_internal_thread_reply', 'ticket_internal_thread_mention', 'ticket_note_reply', 'ticket_note_mention', 'ticket_custom_field_updated', 'ticket_tag_updated', 'ticket_sla_breach_warning', 'ticket_sla_breached', 'ticket_csat_received', 'ticket_csat_updated')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_notification_preferences" ALTER COLUMN "event_type" TYPE "public"."user_notification_preferences_event_type_enum_old" USING "event_type"::"text"::"public"."user_notification_preferences_event_type_enum_old"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."user_notification_preferences_event_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."user_notification_preferences_event_type_enum_old" RENAME TO "user_notification_preferences_event_type_enum"`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "unique_user_notification_preference_profile_channel_event" ON "user_notification_preferences" ("event_type", "channel_type", "user_notification_preference_profile_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "ticket_sentiment" ALTER COLUMN "organization_id" DROP NOT NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_uniq_ticket_sentiment_uid" ON "ticket_sentiment" ("uid", "organization_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "ticket_sentiment" ADD CONSTRAINT "FK_d121d259023cb6ac6a3f2603c30" FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
