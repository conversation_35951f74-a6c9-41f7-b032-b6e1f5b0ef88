import { MigrationInterface, QueryRunner } from "typeorm";

export class AddCsatEntities1747992645923 implements MigrationInterface {
  name = "AddCsatEntities1747992645923";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_0d98f7b69c16f80fed8e2e1a6f"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."csat_surveys_mapping_status_enum" AS ENUM('created', 'queued', 'processing', 'processed', 'failed')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."csat_surveys_sampling_status_enum" AS ENUM('pending', 'selected', 'not_selected', 'excluded')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."csat_surveys_delivery_status_enum" AS ENUM('not_applicable', 'pending', 'scheduled', 'sending', 'sent', 'failed', 'max_attempts', 'cancelled')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."csat_surveys_feedback_type_enum" AS ENUM('star', 'thumbs')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."csat_surveys_delivery_channel_enum" AS ENUM('email', 'slack', 'customer_portal')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."csat_surveys_response_status_enum" AS ENUM('no_response', 'viewed', 'partial', 'completed', 'expired', 'commented')`,
    );
    await queryRunner.query(
      `CREATE TABLE "csat_surveys" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "team_id" bigint NOT NULL, "organization_id" bigint NOT NULL, "ticket_id" character varying NOT NULL, "ticket_title" character varying, "ticket_closed_at" TIMESTAMP WITH TIME ZONE NOT NULL, "ticket_metadata" jsonb NOT NULL DEFAULT '{}', "mapping_status" "public"."csat_surveys_mapping_status_enum" NOT NULL DEFAULT 'created', "batch_id" character varying, "processed_at" TIMESTAMP WITH TIME ZONE, "processing_error" character varying, "sampling_status" "public"."csat_surveys_sampling_status_enum" NOT NULL DEFAULT 'pending', "sampling_method" character varying, "sampling_rate" double precision, "sampling_value" double precision, "rule_name" character varying, "rule_config" jsonb NOT NULL DEFAULT '{}', "delivery_status" "public"."csat_surveys_delivery_status_enum" NOT NULL DEFAULT 'not_applicable', "survey_token" character varying, "requestor_email" character varying, "feedback_type" "public"."csat_surveys_feedback_type_enum" DEFAULT 'star', "survey_config" jsonb NOT NULL DEFAULT '{}', "delivery_channel" "public"."csat_surveys_delivery_channel_enum", "delivery_details" jsonb NOT NULL DEFAULT '{}', "response_status" "public"."csat_surveys_response_status_enum" NOT NULL DEFAULT 'no_response', "rating_value" integer, "comment_text" character varying, "response_metadata" jsonb NOT NULL DEFAULT '{}', "has_response" boolean NOT NULL DEFAULT false, "has_comment" boolean NOT NULL DEFAULT false, "attempt_number" integer NOT NULL DEFAULT '1', "follow_up_count" integer NOT NULL DEFAULT '0', "last_follow_up_at" TIMESTAMP WITH TIME ZONE, "next_follow_up_at" TIMESTAMP WITH TIME ZONE, "previous_attempts" jsonb NOT NULL DEFAULT '[]', "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "scheduled_at" TIMESTAMP WITH TIME ZONE, "sent_at" TIMESTAMP WITH TIME ZONE, "viewed_at" TIMESTAMP WITH TIME ZONE, "completed_at" TIMESTAMP WITH TIME ZONE, "expires_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "UQ_2669a03f38c352b76caaf7b478c" UNIQUE ("ticket_id"), CONSTRAINT "UQ_8302a61586f99e4831252fe7f8d" UNIQUE ("survey_token"), CONSTRAINT "PK_10fb3e0eee98e2e12786e11107d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_csat_rating" ON "csat_surveys" ("rating_value") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_csat_closed" ON "csat_surveys" ("ticket_closed_at") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_csat_created" ON "csat_surveys" ("created_at") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_csat_email" ON "csat_surveys" ("requestor_email") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_csat_response" ON "csat_surveys" ("response_status") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_csat_delivery" ON "csat_surveys" ("delivery_status") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_csat_sampling" ON "csat_surveys" ("sampling_status") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_csat_mapping" ON "csat_surveys" ("mapping_status") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_csat_team_org" ON "csat_surveys" ("team_id", "organization_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_csat_token" ON "csat_surveys" ("survey_token") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_csat_ticket" ON "csat_surveys" ("ticket_id") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."csat_feedback_configs_delivery_channels_enum" AS ENUM('email', 'slack', 'customer_portal')`,
    );
    await queryRunner.query(
      `CREATE TABLE "csat_feedback_configs" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "rule_id" uuid NOT NULL, "enabled" boolean NOT NULL DEFAULT true, "feedback_type" character varying NOT NULL DEFAULT 'star', "branding_color" character varying NOT NULL DEFAULT '#000000', "include_comment_field" boolean NOT NULL DEFAULT false, "custom_title" character varying, "custom_message" character varying, "custom_thank_you_message" character varying, "delivery_channels" "public"."csat_feedback_configs_delivery_channels_enum" array NOT NULL DEFAULT '{email}', "channel_config" jsonb NOT NULL DEFAULT '{}', "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "REL_7354d051035b7f20a0dc30f807" UNIQUE ("rule_id"), CONSTRAINT "PK_bfbb25bc7fd647c77305aab93be" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_feedback_configs_rule_id" ON "csat_feedback_configs" ("rule_id") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."csat_rule_filters_field_operator_enum" AS ENUM('=', '!=', '>', '<', '>=', '<=', 'contains', 'not_contains', 'in', 'not_in', 'is_empty', 'is_not_empty')`,
    );
    await queryRunner.query(
      `CREATE TABLE "csat_rule_filters" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "rule_id" character varying NOT NULL, "all_rule_id" uuid, "any_rule_id" uuid, "filter_type" character varying NOT NULL, "custom_field_uid" text, "field_name" character varying NOT NULL, "field_operator" "public"."csat_rule_filters_field_operator_enum" NOT NULL DEFAULT '=', "field_value" jsonb NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_a14b9f1145e1b6e54106174d784" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_rule_filters_any_rule_id" ON "csat_rule_filters" ("any_rule_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_rule_filters_all_rule_id" ON "csat_rule_filters" ("all_rule_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_rule_filters_field_name" ON "csat_rule_filters" ("field_name") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_rule_filters_type" ON "csat_rule_filters" ("filter_type") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_rule_filters_rule_id" ON "csat_rule_filters" ("rule_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "csat_settings" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "email_config_id" bigint, "is_enabled" boolean NOT NULL DEFAULT true, "cooldown_period_days" integer NOT NULL DEFAULT '14', "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "team_id" bigint NOT NULL, "organization_id" bigint NOT NULL, CONSTRAINT "PK_7ab2f462c1efa4ca89f2e938170" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_csat_settings_team_org" ON "csat_settings" ("team_id", "organization_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "csat_trigger_configs" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "rule_id" uuid NOT NULL, "trigger_type" character varying NOT NULL DEFAULT 'always', "random_percentage" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "REL_8a295d512cec16a88e6eba3405" UNIQUE ("rule_id"), CONSTRAINT "PK_790334bfbb048b0c61382705504" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_trigger_configs_rule_id" ON "csat_trigger_configs" ("rule_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "csat_rules" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "csat_settings_id" uuid NOT NULL, "name" character varying NOT NULL, "description" character varying, "priority" integer NOT NULL DEFAULT '0', "is_active" boolean NOT NULL DEFAULT true, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "UQ_d7bee059ed2fc9c412115991a9d" UNIQUE ("csat_settings_id", "priority"), CONSTRAINT "PK_8204e2781f1efc4d34b19627505" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "csat_audit_logs" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "entity_type" character varying NOT NULL, "entity_id" character varying NOT NULL, "action" character varying NOT NULL, "previous_state" jsonb NOT NULL DEFAULT '{}', "new_state" jsonb NOT NULL DEFAULT '{}', "metadata" jsonb NOT NULL DEFAULT '{}', "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "team_id" bigint NOT NULL, "organization_id" bigint NOT NULL, CONSTRAINT "PK_d5ccc9707fa676d398d6ad4e30e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_csat_audit_logs_created" ON "csat_audit_logs" ("created_at") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_csat_audit_logs_team" ON "csat_audit_logs" ("team_id", "organization_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_csat_audit_logs_entity" ON "csat_audit_logs" ("entity_type", "entity_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0d98f7b69c16f80fed8e2e1a6f" ON "ticket" ("team_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_surveys" ADD CONSTRAINT "FK_0b8fef6a8b5740a7ea2a76d0288" FOREIGN KEY ("team_id") REFERENCES "team"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_surveys" ADD CONSTRAINT "FK_419064bd921419d4a11c4178089" FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_feedback_configs" ADD CONSTRAINT "FK_7354d051035b7f20a0dc30f8077" FOREIGN KEY ("rule_id") REFERENCES "csat_rules"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_rule_filters" ADD CONSTRAINT "FK_f8ae46ab8a965eb29deaad4bda3" FOREIGN KEY ("all_rule_id") REFERENCES "csat_rules"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_rule_filters" ADD CONSTRAINT "FK_ddd38a41de6d763811ba2014181" FOREIGN KEY ("any_rule_id") REFERENCES "csat_rules"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_rule_filters" ADD CONSTRAINT "FK_27baa9c80ab56b1a9d416bf7542" FOREIGN KEY ("custom_field_uid") REFERENCES "custom_field"("uid") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_settings" ADD CONSTRAINT "FK_357a0ad4f3bf81429ef6c6799e6" FOREIGN KEY ("team_id") REFERENCES "team"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_settings" ADD CONSTRAINT "FK_0c993f456c026b15e46e7101263" FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_trigger_configs" ADD CONSTRAINT "FK_8a295d512cec16a88e6eba34059" FOREIGN KEY ("rule_id") REFERENCES "csat_rules"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_rules" ADD CONSTRAINT "FK_0bb4b0aab560ade1f162a2b1fab" FOREIGN KEY ("csat_settings_id") REFERENCES "csat_settings"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_audit_logs" ADD CONSTRAINT "FK_a60100845cee2a203124e25f34a" FOREIGN KEY ("team_id") REFERENCES "team"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_audit_logs" ADD CONSTRAINT "FK_b2c74b762c90ceec5c0a465899b" FOREIGN KEY ("organization_id") REFERENCES "organization"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "csat_audit_logs" DROP CONSTRAINT "FK_b2c74b762c90ceec5c0a465899b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_audit_logs" DROP CONSTRAINT "FK_a60100845cee2a203124e25f34a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_rules" DROP CONSTRAINT "FK_0bb4b0aab560ade1f162a2b1fab"`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_trigger_configs" DROP CONSTRAINT "FK_8a295d512cec16a88e6eba34059"`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_settings" DROP CONSTRAINT "FK_0c993f456c026b15e46e7101263"`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_settings" DROP CONSTRAINT "FK_357a0ad4f3bf81429ef6c6799e6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_rule_filters" DROP CONSTRAINT "FK_27baa9c80ab56b1a9d416bf7542"`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_rule_filters" DROP CONSTRAINT "FK_ddd38a41de6d763811ba2014181"`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_rule_filters" DROP CONSTRAINT "FK_f8ae46ab8a965eb29deaad4bda3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_feedback_configs" DROP CONSTRAINT "FK_7354d051035b7f20a0dc30f8077"`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_surveys" DROP CONSTRAINT "FK_419064bd921419d4a11c4178089"`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_surveys" DROP CONSTRAINT "FK_0b8fef6a8b5740a7ea2a76d0288"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_0d98f7b69c16f80fed8e2e1a6f"`,
    );
    await queryRunner.query(`DROP INDEX "public"."idx_csat_audit_logs_entity"`);
    await queryRunner.query(`DROP INDEX "public"."idx_csat_audit_logs_team"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_csat_audit_logs_created"`,
    );
    await queryRunner.query(`DROP TABLE "csat_audit_logs"`);
    await queryRunner.query(`DROP TABLE "csat_rules"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_trigger_configs_rule_id"`,
    );
    await queryRunner.query(`DROP TABLE "csat_trigger_configs"`);
    await queryRunner.query(`DROP INDEX "public"."idx_csat_settings_team_org"`);
    await queryRunner.query(`DROP TABLE "csat_settings"`);
    await queryRunner.query(`DROP INDEX "public"."idx_rule_filters_rule_id"`);
    await queryRunner.query(`DROP INDEX "public"."idx_rule_filters_type"`);
    await queryRunner.query(
      `DROP INDEX "public"."idx_rule_filters_field_name"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_rule_filters_all_rule_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_rule_filters_any_rule_id"`,
    );
    await queryRunner.query(`DROP TABLE "csat_rule_filters"`);
    await queryRunner.query(
      `DROP TYPE "public"."csat_rule_filters_field_operator_enum"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."idx_feedback_configs_rule_id"`,
    );
    await queryRunner.query(`DROP TABLE "csat_feedback_configs"`);
    await queryRunner.query(
      `DROP TYPE "public"."csat_feedback_configs_delivery_channels_enum"`,
    );
    await queryRunner.query(`DROP INDEX "public"."idx_csat_ticket"`);
    await queryRunner.query(`DROP INDEX "public"."idx_csat_token"`);
    await queryRunner.query(`DROP INDEX "public"."idx_csat_team_org"`);
    await queryRunner.query(`DROP INDEX "public"."idx_csat_mapping"`);
    await queryRunner.query(`DROP INDEX "public"."idx_csat_sampling"`);
    await queryRunner.query(`DROP INDEX "public"."idx_csat_delivery"`);
    await queryRunner.query(`DROP INDEX "public"."idx_csat_response"`);
    await queryRunner.query(`DROP INDEX "public"."idx_csat_email"`);
    await queryRunner.query(`DROP INDEX "public"."idx_csat_created"`);
    await queryRunner.query(`DROP INDEX "public"."idx_csat_closed"`);
    await queryRunner.query(`DROP INDEX "public"."idx_csat_rating"`);
    await queryRunner.query(`DROP TABLE "csat_surveys"`);
    await queryRunner.query(
      `DROP TYPE "public"."csat_surveys_response_status_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."csat_surveys_delivery_channel_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."csat_surveys_feedback_type_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."csat_surveys_delivery_status_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."csat_surveys_sampling_status_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."csat_surveys_mapping_status_enum"`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0d98f7b69c16f80fed8e2e1a6f" ON "ticket" ("sub_team_id") `,
    );
  }
}
