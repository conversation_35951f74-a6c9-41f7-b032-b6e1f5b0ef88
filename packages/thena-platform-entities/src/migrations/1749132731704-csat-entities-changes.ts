import { MigrationInterface, QueryRunner } from "typeorm";

export class CsatEntitiesChanges1749132731704 implements MigrationInterface {
  transaction?: boolean;
  name = "CsatEntitiesChanges1749132731704";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."csat_feedback_configs_delivery_channels_enum" RENAME TO "csat_feedback_configs_delivery_channel_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."csat_feedback_configs_delivery_channel_enum" AS ENUM('email', 'source', 'customer_portal')`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_feedback_configs" ADD COLUMN "delivery_channel_new_temp" "public"."csat_feedback_configs_delivery_channel_enum"`,
    );
    await queryRunner.query(
      `UPDATE "csat_feedback_configs"
       SET "delivery_channel_new_temp" = CASE
           WHEN 'email' = ANY("delivery_channels") THEN 'email'::"public"."csat_feedback_configs_delivery_channel_enum"
           WHEN 'slack' = ANY("delivery_channels") THEN 'source'::"public"."csat_feedback_configs_delivery_channel_enum" -- Mapping 'slack' to 'source'
           WHEN 'customer_portal' = ANY("delivery_channels") THEN 'customer_portal'::"public"."csat_feedback_configs_delivery_channel_enum"
           ELSE 'email'::"public"."csat_feedback_configs_delivery_channel_enum" -- Fallback/default if no match or empty array
         END
       WHERE "delivery_channels" IS NOT NULL AND array_length("delivery_channels", 1) > 0`,
    );
    await queryRunner.query(
      `UPDATE "csat_feedback_configs" SET "delivery_channel_new_temp" = 'email'::"public"."csat_feedback_configs_delivery_channel_enum" WHERE "delivery_channel_new_temp" IS NULL`,
    );

    await queryRunner.query(
      `ALTER TABLE "csat_feedback_configs" ALTER COLUMN "delivery_channel_new_temp" SET NOT NULL`,
    );

    await queryRunner.query(
      `ALTER TABLE "csat_feedback_configs" DROP COLUMN "delivery_channels"`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_feedback_configs" RENAME COLUMN "delivery_channel_new_temp" TO "delivery_channel"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."csat_feedback_configs_delivery_channel_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."csat_surveys_delivery_channel_enum" RENAME TO "csat_surveys_delivery_channel_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."csat_surveys_delivery_channel_enum" AS ENUM('email', 'source', 'customer_portal')`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_surveys" ALTER COLUMN "delivery_channel" TYPE "public"."csat_surveys_delivery_channel_enum" USING
        CASE "delivery_channel"::text
          WHEN 'slack' THEN 'source'::"public"."csat_surveys_delivery_channel_enum"
          -- Add other direct mappings if necessary
          ELSE "delivery_channel"::text::"public"."csat_surveys_delivery_channel_enum"
        END`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."csat_surveys_delivery_channel_enum_old"`,
    );

    await queryRunner.query(
      `CREATE TABLE "csat_settings_closed_statuses" ("csat_settings_id" uuid NOT NULL, "status_id" bigint NOT NULL, CONSTRAINT "PK_28f8b2441ae6f3c60d89e887020" PRIMARY KEY ("csat_settings_id", "status_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b8653afffc7efcbfe6af802938" ON "csat_settings_closed_statuses" ("csat_settings_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_43c017e8cc9d3220320812d17e" ON "csat_settings_closed_statuses" ("status_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_settings" ADD "user_cooldown_period_days" integer NOT NULL DEFAULT '10'`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_settings_closed_statuses" ADD CONSTRAINT "FK_b8653afffc7efcbfe6af8029386" FOREIGN KEY ("csat_settings_id") REFERENCES "csat_settings"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_settings_closed_statuses" ADD CONSTRAINT "FK_43c017e8cc9d3220320812d17ee" FOREIGN KEY ("status_id") REFERENCES "ticket_status"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "csat_settings_closed_statuses" DROP CONSTRAINT "FK_43c017e8cc9d3220320812d17ee"`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_settings_closed_statuses" DROP CONSTRAINT "FK_b8653afffc7efcbfe6af8029386"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."csat_surveys_delivery_channel_enum_old" AS ENUM('email', 'slack', 'customer_portal')`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_surveys" ALTER COLUMN "delivery_channel" TYPE "public"."csat_surveys_delivery_channel_enum_old" USING "delivery_channel"::"text"::"public"."csat_surveys_delivery_channel_enum_old"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."csat_surveys_delivery_channel_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."csat_surveys_delivery_channel_enum_old" RENAME TO "csat_surveys_delivery_channel_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_feedback_configs" DROP COLUMN "delivery_channel"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."csat_feedback_configs_delivery_channel_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_feedback_configs" ADD "delivery_channel" "public"."csat_feedback_configs_delivery_channel_enum" array NOT NULL DEFAULT '{email}'`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_settings" DROP COLUMN "user_cooldown_period_days"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_43c017e8cc9d3220320812d17e"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b8653afffc7efcbfe6af802938"`,
    );
    await queryRunner.query(`DROP TABLE "csat_settings_closed_statuses"`);
    await queryRunner.query(
      `ALTER TYPE "public"."csat_feedback_configs_delivery_channel_enum" RENAME TO "csat_feedback_configs_delivery_channels_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "csat_feedback_configs" RENAME COLUMN "delivery_channel" TO "delivery_channels"`,
    );
  }
}
