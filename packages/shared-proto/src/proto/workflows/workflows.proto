syntax = "proto3";

package grpc.workflows.v1;

enum Source {
  PLATFORM_APP = 0;
  REGISTERED_APP = 1;
}

message CompensationActivity {
  string identifier = 1;
}

message HTTPActivityConfig {
  string method = 1;
  string url = 2;
  map<string, string> headers = 3;
}

message GRPCActivityConfig {
  string url = 1;
  string packageName = 2;
  string serviceName = 3;
  string methodName = 4;
  string protoPath = 5;
}

message ActivityConnectionDetails {
  string transport = 1;
  optional HTTPActivityConfig httpConfig = 2;
  optional GRPCActivityConfig grpcConfig = 3;
}

message Activity {
  string activityName = 1;
  string description = 2;
  string identifier = 3; // TODO - @AMit make this identifier as a composite key of activityName and appId.
  string requestSchema = 4; // stringified JSON
  string responseSchema = 5; // stringified JSON
  ActivityConnectionDetails connectionDetails = 6;
  bool isCompensable = 7;
  string metadata = 8; // stringified JSON
  optional CompensationActivity compensationActivity = 9;
  bool accessibleToTeam = 10;
  repeated string teamIds = 11;
}

message RegisterActivitiesRequest {
  Source source = 1;
  repeated Activity activities = 2;
  optional string organizationId = 3;
}

message RegisterActivitiesResponse {
  bool success = 1;
  string transactionId = 2;
}

message Event {
  string eventName = 1;
  string description = 2;
  string eventType = 3;
  string schema = 4; // stringified JSON
  string metadata = 5; // stringified JSON
  bool accessibleToTeam = 6;
  repeated string teamIds = 7;
}

message RegisterEventsRequest {
  Source source = 1;
  repeated Event events = 2;
  optional string organizationId = 3;
}

message RegisterEventsResponse {
  bool success = 1;
  string transactionId = 2;
}

message CompensateRegisterActivitiesRequest {
  string transactionId = 1;
}

message CompensateRegisterActivitiesResponse {
  bool success = 1;
}

message CompensateRegisterEventsRequest {
  string transactionId = 1;
}

message CompensateRegisterEventsResponse {
  bool success = 1;
}

message GetActivitiesRequest {
  Source source = 1;
  optional string organizationId = 2;
}

message GetActivitiesResponse {
  repeated Activity activities = 1;
}

message GetEventsRequest {
  Source source = 1;
  optional string organizationId = 2;
}

message GetEventsResponse {
  repeated Event events = 1;
}

message DeleteActivitiesRequest {
  string identifier = 1;
  string organizationId = 2; // Delete operation is only applicable for registered activities. Has to be provided.
}

message DeleteActivitiesResponse {
  bool success = 1;
  string transactionId = 2;
}

message DeleteEventsRequest {
  string eventType = 1;
  string organizationId = 2; // Delete operation is only applicable for registered events. Has to be provided.
}

message DeleteEventsResponse {
  bool success = 1;
  string transactionId = 2;
}

message CompensateDeleteActivitiesRequest {
  string transactionId = 1;
}

message CompensateDeleteActivitiesResponse {
  bool success = 1;
}

message CompensateDeleteEventsRequest {
  string transactionId = 1;
}

message CompensateDeleteEventsResponse {
  bool success = 1;
}

message UpdateAccessibleTeamsForActivitiesRequest {
  repeated string activityIdentifiers = 1;
  repeated string teamIds = 2;
}

message UpdateAccessibleTeamsForActivitiesResponse {
  bool success = 1;
}

message UpdateAccessibleTeamsForEventsRequest {
  repeated string eventTypes = 1;
  repeated string teamIds = 2;
}

message UpdateAccessibleTeamsForEventsResponse {
  bool success = 1;
}

message SeedWorkflowsRequest {
  string organizationId = 1;
  string teamId = 2;
}

message SeedWorkflowsResponse {
  bool success = 1;
}

// Execute Activity Messages
message ExecuteActivityRequest {
  string activityId = 1;
  string inputData = 2; // stringified JSON of the activity input
  string userUid = 3;
  string orgUid = 4;
  optional string workflowId = 5; // Optional, defaults to "MANUAL_ACTIVITY_EXECUTION"
  optional string workflowInstanceId = 6; // Optional, defaults to "MANUAL_ACTIVITY_EXECUTION"
  optional string workflowInstanceActivityId = 7; // Optional, will be generated if not provided
}

message ExecuteActivityResponse {
  bool success = 1;
  string message = 2;
  optional string data = 3; // stringified JSON of the activity result
  optional string error = 4; // Error message if execution failed
  optional int32 errorStatusCode = 5; // HTTP status code if applicable
}

// Workflows service for workflows app.
service WorkflowsService {
  // Register activities
  rpc RegisterActivities (RegisterActivitiesRequest) returns (RegisterActivitiesResponse);

  // Register events
  rpc RegisterEvents (RegisterEventsRequest) returns (RegisterEventsResponse);

  // Compensate register activities
  rpc CompensateRegisterActivities (CompensateRegisterActivitiesRequest) returns (CompensateRegisterActivitiesResponse);

  // Compensate register events
  rpc CompensateRegisterEvents (CompensateRegisterEventsRequest) returns (CompensateRegisterEventsResponse);

  // Get activities
  rpc GetActivities (GetActivitiesRequest) returns (GetActivitiesResponse);

  // Get events
  rpc GetEvents (GetEventsRequest) returns (GetEventsResponse);

  // Delete activities
  rpc DeleteActivities (DeleteActivitiesRequest) returns (DeleteActivitiesResponse);

  // Delete events
  rpc DeleteEvents (DeleteEventsRequest) returns (DeleteEventsResponse);

  // Compensate delete activities
  rpc CompensateDeleteActivities (CompensateDeleteActivitiesRequest) returns (CompensateDeleteActivitiesResponse);

  // Compensate delete events
  rpc CompensateDeleteEvents (CompensateDeleteEventsRequest) returns (CompensateDeleteEventsResponse);

  // Update accessible teams for activities
  rpc UpdateAccessibleTeamsForActivities (UpdateAccessibleTeamsForActivitiesRequest) returns (UpdateAccessibleTeamsForActivitiesResponse);

  // Update accessible teams for events
  rpc UpdateAccessibleTeamsForEvents (UpdateAccessibleTeamsForEventsRequest) returns (UpdateAccessibleTeamsForEventsResponse);

  // Seed workflows
  rpc SeedWorkflows (SeedWorkflowsRequest) returns (SeedWorkflowsResponse);

  // Execute activity
  rpc ExecuteActivity (ExecuteActivityRequest) returns (ExecuteActivityResponse);

}