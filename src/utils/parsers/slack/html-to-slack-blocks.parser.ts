import * as cheerio from 'cheerio';

export class HtmlToSlackBlocks {
  private html: string;
  private $: cheerio.CheerioAPI;
  private blocks: any[];
  private usersMentioned: string[];
  private userGroupsMentiond: string[];
  private preCodeContents: string[];

  constructor(html: string) {
    this.html = html;

    // Parse the HTML
    this.$ = cheerio.load(html);

    // Initialize the blocks array
    this.blocks = [];

    // Initialize the users mentioned array
    this.usersMentioned = [];
    this.userGroupsMentiond = [];

    this.preCodeContents = [];
  }

  /**
   * Converts HTML to Slack blocks, by processing each node
   */
  async convert(): Promise<{
    blocks: Array<any>;
    usersMentioned: Array<any>;
  }> {
    this.extractPreCodeContent();

    const children = this.$('body').children();

    // Use a for of loop to iterate over the children
    for (const child of children) {
      await this.processNode(child);
    }

    return {
      blocks: this.blocks,
      usersMentioned: this.usersMentioned,
    };
  }

  getUsersMentioned() {
    const regex =
      /<span class="mention" data-type="mention" data-id="(.*?)" data-email="(.*?)"/g;
    let match: any;

    // eslint-disable-next-line no-cond-assign
    // biome-ignore lint/suspicious/noAssignInExpressions: <explanation>
    while ((match = regex.exec(this.html)) !== null) {
      if (match[1].startsWith('S') && match[1].length < 15) {
        // TODO: This is a hack to avoid mentioning the whole sub team. Need to find a better way.
        this.userGroupsMentiond.push(match[1]);
      } else {
        this.usersMentioned.push(match[1]);
      }
    }

    return this.usersMentioned;
  }

  /**
   * Extracts the content of pre and code tags, and replaces them with placeholders
   */
  extractPreCodeContent() {
    this.$('pre').each((index, element) => {
      const rawContent = this.$(element).find('code').html();
      const placeholder = `PRE_CODE_PLACEHOLDER_${index}`;
      this.preCodeContents.push(rawContent);
      this.$(element).replaceWith(
        `<${element.tagName}>${placeholder}</${element.tagName}>`,
      );
    });
  }

  /**
   * Processes a node, and adds the appropriate block
   *
   * @param {Object} node Node to process
   */
  async processNode(node) {
    const nodeName = node.name;
    const nodeText = this.$(node).text().trim();

    if (nodeName === 'p') {
      if (nodeText) {
        // If this node is a paragraph, add a section block
        this.addSectionBlock(this.processInlineElements(this.$(node)));
      } else {
        this.addLineBreak();
      }
    } else if (nodeName === 'ul' || nodeName === 'ol') {
      // If this node is a list, add a list block
      await this.addListBlock(node, nodeName === 'ul');
    } else if (nodeName === 'blockquote') {
      // If this node is a blockquote, add a quote block
      this.addQuoteBlock(this.$(node));
    } else if (nodeName === 'pre') {
      // If this node is a pre, add a code block
      const placeholder = this.$(node).text();
      const index = Number.parseInt(placeholder.split('_').pop(), 10);
      const rawContent = this.preCodeContents[index];
      this.addCodeBlock(rawContent);
    } else {
      // Process the children of the node
      const children = this.$(node).children();

      // Use a for of loop to iterate over the children
      for (const child of children) {
        await this.processNode(child);
      }
    }
  }

  /**
   * Adds a line break to the blocks array
   */
  addLineBreak() {
    this.blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: '\n',
      },
    });
  }

  /**
   * Adds a section block to the blocks array
   *
   * @param {String} text Text to add as a section block
   */
  addSectionBlock(text) {
    this.blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text,
      },
    });
  }

  /**
   * Adds a list block to the blocks array
   *
   * @param {object} node Node to process
   * @param {boolean} isBullet Whether the list is a bullet list
   * @param {number} indent Indentation level of the list
   */
  async addListBlock(node, isBullet, indent = 0) {
    const blockType = isBullet ? 'bullet' : 'ordered';
    const listElements = [];

    const processListItem = (li, currentIndent) => {
      const listItem = {
        type: 'rich_text_list',
        elements: [],
        style: blockType,
        indent: currentIndent,
        border: 0,
      };

      const paragraphs = this.$(li).find('> p');

      // Use a for of loop to iterate over the paragraphs
      for (const paragraph of paragraphs) {
        const sectionElements = [];

        const constants = this.$(paragraph).contents();

        // Use a for of loop to iterate over the constants
        for (const element of constants) {
          // Process list element that is a normal text
          if (element.type === 'text') {
            const text = element.data;
            if (text) {
              sectionElements.push({
                type: 'text',
                text,
              });
            }
          } else if (element.type === 'tag' && element.name === 'code') {
            // Process list element that is a code block
            const codeText = this.$(element).text();
            if (codeText) {
              sectionElements.push({
                type: 'text',
                text: codeText,
                style: {
                  code: true,
                },
              });
            }
          } else if (element.type === 'tag' && element.name === 'strong') {
            // Process list element that is a strong text
            const boldText = this.$(element).text();
            if (boldText) {
              sectionElements.push({
                type: 'text',
                text: boldText,
                style: {
                  bold: true,
                },
              });
            }
          } else if (element.type === 'tag' && element.name === 'em') {
            // Process list element that is an emphasized text
            const italicText = this.$(element).text();
            if (italicText) {
              sectionElements.push({
                type: 'text',
                text: italicText,
                style: {
                  italic: true,
                },
              });
            }
          } else if (element.type === 'tag' && element.name === 's') {
            // Process list element that is an emphasized text
            const strikedText = this.$(element).text();
            if (strikedText) {
              sectionElements.push({
                type: 'text',
                text: strikedText,
                style: {
                  strike: true,
                },
              });
            }
          } else if (element.type === 'tag' && element.name === 'span') {
            const isEmoji = this.$(element).attr('data-type') === 'emoji';
            if (isEmoji) {
              const emojiName = this.$(element).attr('data-name');
              if (emojiName) {
                sectionElements.push({
                  type: 'emoji',
                  name: emojiName,
                });
              }
            } else {
              const userId = this.$(element).attr('data-id');
              if (userId) {
                this.usersMentioned.push(userId);
                sectionElements.push({
                  type: 'user',
                  user_id: userId,
                });
              }
            }
          } else if ('name' in element && element.name === 'a') {
            // Process list element that is a link
            const href = this.$(element).attr('href');
            const anchorText = this.$(element).text();
            sectionElements.push({
              type: 'link',
              text: ` ${anchorText} `,
              url: href,
            });
          } else {
            const processedText = this.processInlineElements(this.$(element));
            sectionElements.push({
              type: 'text',
              text: processedText,
            });
          }
        }

        if (sectionElements.length > 0) {
          listItem.elements.push({
            type: 'rich_text_section',
            elements: sectionElements,
          });
        }
      }

      listElements.push(listItem);

      // Process nested lists
      this.$(li)
        .find('> ul, > ol')
        .each((_, childList) => {
          this.$(childList)
            .find('> li')
            .each((__, childLi) => {
              processListItem(childLi, currentIndent + 1);
            });
        });
    };

    this.$(node)
      .find('> li')
      .each((_, li) => {
        processListItem(li, indent);
      });

    this.blocks.push({
      type: 'rich_text',
      elements: listElements,
    });
  }

  /**
   * Adds a quote block to the blocks array
   *
   * @param {Object} node Node to process
   */
  addQuoteBlock(node) {
    this.blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `> ${this.processInlineElements(node)}`,
      },
    });
  }

  /**
   * Adds a code block to the blocks array
   *
   * @param {String} code Code to add as a code block
   */
  addCodeBlock(code) {
    this.blocks.push({
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `\`\`\`\n${code}\n\`\`\``,
      },
    });
  }

  /**
   * Processes inline elements in a node
   *
   * @param {Object} node Node to process
   * @returns {String} Processed text
   */
  processInlineElements(node) {
    if (Array.isArray(node)) {
      let text = '';
      // convert to for of loop
      for (const childNode of node) {
        text += this.processInlineElements(childNode);
      }

      return text;
    }

    if (typeof node === 'string') {
      let text = node;

      // Handle `strong` tags
      text = text.replace(/(<strong>)(.*?)(<\/strong>)/g, '*$2*');

      // Handle `em` tags
      text = text.replace(/(<em>)(.*?)(<\/em>)/g, '_$2_');

      // Handle `s` tags
      text = text.replace(/(<s>)(.*?)(<\/s>)/g, '~$2~');

      // Handle `code` tags
      text = text.replace(/(<code>)(.*?)(<\/code>)/g, '`$2`');

      // Handle `a` tags
      text = text.replace(/(<a href=")(.*?)(">)(.*?)(<\/a>)/g, '<$2|$4>');

      // Handle `span` tags for emojis
      text = text.replace(
        /(<span data-type="emoji" data-name=")(.*?)(">)(.*?)(<\/span>)/g,
        ':$2:',
      );

      // Handle `span` tags for user mentions with class="mention"
      text = text.replace(
        /<span class="mention" data-id="([^"]+)" data-email="([^"]+)" data-label="([^"]+)">@[^<]+<\/span>/g,
        (_match, p1) => {
          this.usersMentioned.push(p1);
          return `<@${p1}>`;
        },
      );

      return text;
    }

    let text = node.text();

    // Handle `strong` tags
    node.find('strong').each((_, strong) => {
      const strongText = this.$(strong).text();
      text = text.replace(strongText, `*${strongText.trimEnd()}* `);
    });

    // Handle `em` tags
    node.find('em').each((_, em) => {
      const emText = this.$(em).text();
      text = text.replace(emText, `_${emText.trimEnd()}_ `);
    });

    // Handle `s` tags
    node.find('s').each((_, s) => {
      const sText = this.$(s).text();
      text = text.replace(sText, `~${sText.trimEnd()}~ `);
    });

    // Handle `code` tags
    node.find('code').each((_, code) => {
      const codeText = this.$(code).text();
      text = text.replace(codeText, `\`${codeText.trimEnd()}\` `);
    });

    // Handle `a` tags
    node.find('a').each((_, a) => {
      const linkText = this.$(a).text();
      const href = this.$(a).attr('href');
      text = text.replace(linkText, `<${href}|${linkText.trimEnd()}> `);
    });

    // Handle `span` tags for emojis
    node.find('span[data-type="emoji"]').each((_, span) => {
      const emojiName = this.$(span).attr('data-name');
      text = text.replace(this.$(span).text(), `:${emojiName.trimEnd()}: `);
    });

    // Handle `span` tags for user mentions with class="mention"
    node.find('span.mention').each((_, span) => {
      const userId = this.$(span).attr('data-id');
      if (userId) {
        this.usersMentioned.push(userId);
        text = text.replace(this.$(span).text(), `<@${userId.trimEnd()}> `);
      }
    });

    return text;
  }
}
