import { applyDecorators, Type } from '@nestjs/common';
import {
  ApiBody,
  ApiExtraModels,
  ApiHeader,
  ApiOperation,
  ApiParam,
  ApiProperty,
  ApiPropertyOptions,
  ApiQuery,
  ApiResponse,
  ApiSecurity,
  getSchemaPath,
} from '@nestjs/swagger';

/**
 * Custom decorator for standardizing API documentation
 * @param summary Short summary of the endpoint
 * @param description Detailed description of the endpoint
 * @param responseType Type of the response data (optional)
 * @param status HTTP status code for the response
 * @param isArray Whether the response is an array
 * @param isPaginated Whether the response is paginated
 * @param deprecated Whether the endpoint is deprecated
 */
export function ApiDocumentation<T extends Type<any>>({
  summary,
  description,
  responseType,
  status = 200,
  isArray = false,
  isPaginated = false,
  deprecated = false,
}: {
  summary: string;
  description?: string;
  responseType?: T;
  status?: number;
  isArray?: boolean;
  isPaginated?: boolean;
  deprecated?: boolean;
}) {
  const decorators = [
    ApiOperation({
      summary,
      description,
      deprecated,
    }),
  ];

  // Add standard security
  decorators.push(ApiSecurity('ApiKeyAuth'), ApiSecurity('BearerAuth'));

  // Add standard headers
  decorators.push(
    ApiHeader({
      name: 'X-API-KEY',
      description: 'API key for authentication',
      required: true,
    }),
    ApiHeader({
      name: 'Authorization',
      description: 'Bearer token for authentication',
      required: false,
    }),
  );

  // Add standard responses
  const responses = [
    {
      status: 400,
      description: 'Bad Request - Invalid input data',
    },
    {
      status: 401,
      description: 'Unauthorized - Authentication required',
    },
    {
      status: 403,
      description: 'Forbidden - Insufficient permissions',
    },
    {
      status: 404,
      description: 'Not Found - Resource does not exist',
    },
    {
      status: 500,
      description: 'Internal Server Error',
    },
  ];

  // Add all standard responses
  responses.forEach((response) => {
    decorators.push(
      ApiResponse({
        status: response.status,
        description: response.description,
      }),
    );
  });

  // Add success response with schema if responseType is provided
  if (responseType) {
    decorators.push(ApiExtraModels(responseType));

    let schema: any;

    if (isPaginated) {
      // For paginated responses
      schema = {
        properties: {
          data: {
            type: 'array',
            items: { $ref: getSchemaPath(responseType) },
          },
          meta: {
            type: 'object',
            properties: {
              total: { type: 'number', example: 100 },
              page: { type: 'number', example: 1 },
              limit: { type: 'number', example: 10 },
              totalPages: { type: 'number', example: 10 },
            },
          },
        },
      };
    } else if (isArray) {
      // For array responses
      schema = {
        type: 'array',
        items: { $ref: getSchemaPath(responseType) },
      };
    } else {
      // For single object responses
      schema = {
        $ref: getSchemaPath(responseType),
      };
    }

    decorators.push(
      ApiResponse({
        status,
        description: 'Successful operation',
        schema,
      }),
    );
  } else {
    // Generic success response if no type is provided
    decorators.push(
      ApiResponse({
        status,
        description: 'Successful operation',
      }),
    );
  }

  return applyDecorators(...decorators);
}

/**
 * Custom decorator for API request body documentation
 * @param type Type of the request body
 * @param description Description of the request body
 * @param required Whether the request body is required
 */
export function ApiBodyWithExample<T extends Type<any>>(
  type: T,
  description = 'Request payload',
  required = true,
) {
  return applyDecorators(
    ApiExtraModels(type),
    ApiBody({
      description,
      required,
      type,
    }),
  );
}

/**
 * Custom decorator for API query parameters
 * @param params Array of query parameter configurations
 */
export function ApiQueryParams(
  params: Array<{
    name: string;
    description?: string;
    required?: boolean;
    type?: 'string' | 'number' | 'boolean' | 'array';
    isArray?: boolean;
    example?: any;
    enum?: any[];
  }>,
) {
  const decorators = params.map((param) => {
    return ApiQuery({
      name: param.name,
      description: param.description,
      required: param.required ?? false,
      type: param.type ?? 'string',
      isArray: param.isArray ?? false,
      example: param.example,
      enum: param.enum,
    });
  });

  return applyDecorators(...decorators);
}

/**
 * Custom decorator for API path parameters
 * @param params Array of path parameter configurations
 */
export function ApiPathParams(
  params: Array<{
    name: string;
    description?: string;
    required?: boolean;
    type?: 'string' | 'number' | 'boolean';
    example?: any;
    enum?: any[];
  }>,
) {
  const decorators = params.map((param) => {
    return ApiParam({
      name: param.name,
      description: param.description,
      required: param.required ?? true,
      type: param.type ?? 'string',
      example: param.example,
      enum: param.enum,
    });
  });

  return applyDecorators(...decorators);
}

/**
 * Custom decorator for API pagination parameters
 */
export function ApiPaginationParams() {
  return ApiQueryParams([
    {
      name: 'page',
      description: 'Page number for pagination',
      type: 'number',
      example: 1,
    },
    {
      name: 'limit',
      description: 'Number of items per page',
      type: 'number',
      example: 10,
    },
    {
      name: 'sort',
      description: 'Sort field and direction (e.g., name:asc)',
      type: 'string',
      example: 'createdAt:desc',
    },
  ]);
}

/**
 * Custom decorator for enhanced API property documentation
 */
export function EnhancedApiProperty(
  options: ApiPropertyOptions & { example?: any },
) {
  return ApiProperty({
    ...options,
    example: options.example,
  });
}
