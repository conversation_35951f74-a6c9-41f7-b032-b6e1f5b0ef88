import { Installations, Users } from '../../database/entities';
import { Attachment } from '../../platform/type-system/events';

interface MentionCheck {
  botMentioned: boolean;
  mentionedUserGroups: string[]; // IDs of mentioned user groups
  mentionedUsers: string[]; // IDs of mentioned users
}

/**
 * Check if the bot is mentioned in the text
 * @param installation Installation
 * @param text Text
 * @returns Details about mentions in the message
 */
export function getSlackMentions(
  installation: Installations,
  text: string,
): MentionCheck {
  const result: MentionCheck = {
    botMentioned: false,
    mentionedUserGroups: [],
    mentionedUsers: [],
  };

  // Check for bot user mention
  const userRegex = /<@(\w+)>/g;
  let userMatch = userRegex.exec(text);

  while (userMatch !== null) {
    // Add user ID to the mentioned users array
    result.mentionedUsers.push(userMatch[1]);

    // Check if this is the bot
    if (userMatch[1] === installation.botSlackUserId) {
      result.botMentioned = true;
    }

    userMatch = userRegex.exec(text);
  }

  // Check for user group mentions (subteam format in Slack)
  const groupRegex = /<!subteam\^(\w+)(?:\|@([^>]+))?>/g;
  let groupMatch = groupRegex.exec(text);

  while (groupMatch !== null) {
    result.mentionedUserGroups.push(groupMatch[1]);
    groupMatch = groupRegex.exec(text);
  }

  return result;
}

/**
 * Enhance user mentions in a message with user information
 * @param text The original message text
 * @param users Array of Users entities with slackId and email
 * @returns Modified text with enhanced mentions
 */
export function enhanceUserMentions(text: string, users: Users[]): string {
  if (!users || users.length === 0) {
    return text;
  }

  // Create a map of slackId -> email for quick lookups
  const userMap = new Map<string, Users>();
  for (const user of users) {
    if (user.slackId && user.slackProfileEmail) {
      userMap.set(user.slackId, user);
    }
  }

  // If no valid users with both slackId and email, return original text
  if (userMap.size === 0) {
    return text;
  }

  // Replace all user mentions with enhanced versions
  return text.replace(/<@(\w+)>/g, (match, userId) => {
    const user = userMap.get(userId);
    if (user?.slackProfileEmail) {
      return `<@${userId}|${user.slackProfileEmail}>`;
    }
    // If user not found in our map, keep original mention
    return match;
  });
}

/**
 * Append attachments to slack blocks
 * @param blocks Slack blocks
 * @param attachments Attachments
 * @returns Slack blocks with attachments
 */
export function appendAttachmentsToSlackBlocks(
  blocks: Array<any>,
  attachments: Array<Attachment>,
) {
  const attachmentBlocks = attachments.map((attachment) => {
    return {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*<${attachment.url}|${attachment.name}>*`,
      },
    };
  });

  return [...blocks, ...attachmentBlocks];
}

/**
 * Format the timestamp for a ticket
 * @param dateString The date string to format
 * @returns The formatted timestamp
 */
export function formatTimestamp(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();

  const isToday =
    date.getDate() === now.getDate() &&
    date.getMonth() === now.getMonth() &&
    date.getFullYear() === now.getFullYear();

  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);
  const isYesterday =
    date.getDate() === yesterday.getDate() &&
    date.getMonth() === yesterday.getMonth() &&
    date.getFullYear() === yesterday.getFullYear();

  const timeOptions: Intl.DateTimeFormatOptions = {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  };

  const formattedTime = date.toLocaleTimeString('en-US', timeOptions);

  if (isToday) {
    return `today at ${formattedTime}`;
  } else if (isYesterday) {
    return `yesterday at ${formattedTime}`;
  } else {
    const dateOptions: Intl.DateTimeFormatOptions = {
      month: 'short',
      day: 'numeric',
    };
    const formattedDate = date.toLocaleDateString('en-US', dateOptions);
    return `${formattedDate.toLowerCase()} at ${formattedTime}`;
  }
}
