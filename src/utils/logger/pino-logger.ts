import { Injectable } from '@nestjs/common';
import pino, { DestinationStream, LoggerOptions } from 'pino';
import { ILogger } from './logger.interface';
import * as rTracer from 'cls-rtracer';

@Injectable()
export class PinoLoggerService implements ILogger {
  private readonly logger: pino.Logger;
  private readonly serviceTag: string;
  private readonly appTag: string;

  constructor(appTag: string, serviceTag: string) {
    this.appTag = appTag;
    this.serviceTag = serviceTag;

    const pinoConfig: LoggerOptions | DestinationStream = {
      level: 'debug',
      base: {
        app: this.appTag,
        service: this.serviceTag,
        mode: 'SLACK_APP',
      },
      timestamp: pino.stdTimeFunctions.isoTime,
      formatters: {
        level: (label) => ({ level: label.toUpperCase() }),
      },
      messageKey: 'message',
    };

    this.logger = pino(pinoConfig);
  }

  private logWithContext(
    level: pino.Level,
    message: string,
    context?: string,
    extra?: Record<string, any>,
  ) {
    const requestId = rTracer.id();
    this.logger[level](
      {
        context,
        requestId,
        ...extra,
      },
      message,
    );
  }

  log(message: string, context?: string, extra?: Record<string, any>) {
    this.logWithContext('info', message, context, extra);
  }

  error(message: string, trace?: string, context?: string) {
    this.logWithContext(
      'error',
      message,
      context,
      trace ? { error: trace } : undefined,
    );
  }

  warn(message: string, context?: string) {
    this.logWithContext('warn', message, context);
  }

  debug(message: string, context?: string) {
    this.logWithContext('debug', message, context);
  }

  verbose(message: string, context?: string) {
    this.logWithContext('trace', message, context);
  }
}
