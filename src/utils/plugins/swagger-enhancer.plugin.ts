import { INestApplication } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { OpenAPIObject } from '@nestjs/swagger/dist/interfaces';

/**
 * Swagger Enhancer Plugin
 *
 * This plugin enhances the Swagger documentation by adding additional information
 * to the OpenAPI specification that is not automatically generated by NestJS.
 */
export class SwaggerEnhancerPlugin {
  /**
   * Apply the plugin to the Swagger document
   * @param document The OpenAPI document to enhance
   * @returns The enhanced OpenAPI document
   */
  apply(document: OpenAPIObject): OpenAPIObject {
    // Add global tags with descriptions
    document.tags = [
      {
        name: 'FormBuilder',
        description: 'Form builder and submission endpoints',
      },
      {
        name: 'SlackActivities',
        description: 'Slack activities and interactions endpoints',
      },
      { name: 'Teams', description: 'Slack teams and workspaces endpoints' },
      { name: 'Settings', description: 'Application settings endpoints' },
      {
        name: 'Authorization',
        description: 'Authentication and authorization endpoints',
      },
      {
        name: 'SlackSync',
        description: 'Slack data synchronization endpoints',
      },
      { name: 'Ai', description: 'AI provider and model endpoints' },
      { name: 'Platform', description: 'Platform integration endpoints' },
      {
        name: 'SubGroups',
        description: 'Slack subgroups management endpoints',
      },
      { name: 'TriageRules', description: 'Triage rules management endpoints' },
    ];

    // Add standard response schemas if they don't exist
    if (!document.components) {
      document.components = {};
    }

    if (!document.components.schemas) {
      document.components.schemas = {};
    }

    // Add standard error response schema
    document.components.schemas.ErrorResponse = {
      type: 'object',
      properties: {
        statusCode: {
          type: 'integer',
          description: 'HTTP status code',
          example: 400,
        },
        message: {
          oneOf: [
            {
              type: 'string',
              description: 'Error message',
              example: 'Bad Request',
            },
            {
              type: 'array',
              items: {
                type: 'string',
              },
              description: 'List of error messages',
              example: ['Field is required', 'Invalid format'],
            },
          ],
        },
        error: {
          type: 'string',
          description: 'Error type',
          example: 'Validation Error',
        },
      },
    };

    // Add standard pagination response schema
    document.components.schemas.PaginationMeta = {
      type: 'object',
      properties: {
        total: {
          type: 'integer',
          description: 'Total number of items',
          example: 100,
        },
        page: {
          type: 'integer',
          description: 'Current page number',
          example: 1,
        },
        limit: {
          type: 'integer',
          description: 'Number of items per page',
          example: 10,
        },
        totalPages: {
          type: 'integer',
          description: 'Total number of pages',
          example: 10,
        },
      },
    };

    // Enhance all paths to ensure they have proper documentation
    if (document.paths) {
      for (const [path, pathItem] of Object.entries(document.paths)) {
        for (const [method, operation] of Object.entries(pathItem)) {
          if (typeof operation !== 'object' || operation === null) continue;

          // Skip non-operation properties
          if (
            ![
              'get',
              'post',
              'put',
              'delete',
              'patch',
              'options',
              'head',
            ].includes(method)
          ) {
            continue;
          }

          // Add description if missing
          if (!operation.description) {
            const action =
              {
                get: 'Retrieve',
                post: 'Create',
                put: 'Update',
                patch: 'Partially update',
                delete: 'Delete',
              }[method] || 'Perform operation on';

            const resource =
              path
                .split('/')
                .filter((p) => p && !p.includes('{'))
                .pop() || 'resource';
            operation.description = `${action} ${resource}`;
          }

          // Add summary if missing
          if (!operation.summary) {
            operation.summary = operation.description;
          }

          // Add standard responses if missing
          if (!operation.responses) {
            operation.responses = {};
          }

          // Add standard error responses
          const standardResponses = {
            '400': 'Bad Request - Invalid input',
            '401': 'Unauthorized - Authentication required',
            '403': 'Forbidden - Insufficient permissions',
            '404': 'Not Found - Resource does not exist',
            '500': 'Internal Server Error',
          };

          for (const [statusCode, description] of Object.entries(
            standardResponses,
          )) {
            if (!operation.responses[statusCode]) {
              operation.responses[statusCode] = {
                description,
                content: {
                  'application/json': {
                    schema: {
                      $ref: '#/components/schemas/ErrorResponse',
                    },
                  },
                },
              };
            }
          }

          // Add success response if missing
          if (method === 'post' && !operation.responses['201']) {
            operation.responses['201'] = {
              description: 'Created successfully',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                  },
                },
              },
            };
          } else if (method !== 'post' && !operation.responses['200']) {
            operation.responses['200'] = {
              description: 'Successful operation',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                  },
                },
              },
            };
          }

          // Add security if missing
          if (!operation.security || operation.security.length === 0) {
            operation.security = [{ ApiKeyAuth: [] }, { BearerAuth: [] }];
          }

          // Add parameter examples if missing
          if (operation.parameters) {
            for (const param of operation.parameters) {
              // Add description if missing
              if (!param.description) {
                if (param.in === 'path') {
                  param.description = `ID of the ${param.name}`;
                } else if (param.in === 'query') {
                  if (param.name === 'page') {
                    param.description = 'Page number for pagination';
                  } else if (param.name === 'limit' || param.name === 'size') {
                    param.description = 'Number of items per page';
                  } else if (param.name === 'sort') {
                    param.description =
                      'Sort field and direction (e.g., name:asc)';
                  } else if (
                    param.name === 'filter' ||
                    param.name === 'search'
                  ) {
                    param.description = 'Filter or search term';
                  } else {
                    param.description = `Filter results by ${param.name}`;
                  }
                } else if (param.in === 'header') {
                  param.description = `${param.name} header value`;
                }
              }

              // Add example if missing
              if (!param.example && param.schema && param.schema.type) {
                if (param.schema.type === 'string') {
                  if (param.name.toLowerCase().includes('id')) {
                    param.example = param.name.toLowerCase().includes('user')
                      ? 'U12345678'
                      : param.name.toLowerCase().includes('channel')
                        ? 'C12345678'
                        : param.name.toLowerCase().includes('team')
                          ? 'T12345678'
                          : 'id123';
                  } else if (param.name === 'email') {
                    param.example = '<EMAIL>';
                  } else if (param.name === 'name') {
                    param.example = 'Example Name';
                  } else {
                    param.example = `example-${param.name}`;
                  }
                } else if (
                  param.schema.type === 'integer' ||
                  param.schema.type === 'number'
                ) {
                  if (param.name === 'page') {
                    param.example = 1;
                  } else if (param.name === 'limit' || param.name === 'size') {
                    param.example = 10;
                  } else {
                    param.example = 42;
                  }
                } else if (param.schema.type === 'boolean') {
                  param.example = true;
                }
              }
            }
          }
        }
      }
    }

    return document;
  }
}

/**
 * Setup enhanced Swagger documentation
 * @param app NestJS application instance
 * @param options DocumentBuilder options
 * @param customOptions Custom Swagger UI options
 * @returns The OpenAPI document
 */
export function setupEnhancedSwagger(
  app: INestApplication,
  options: {
    title: string;
    description: string;
    version: string;
    tags?: { name: string; description?: string }[];
  },
  customOptions?: Record<string, any>,
): OpenAPIObject {
  const builder = new DocumentBuilder()
    .setTitle(options.title)
    .setDescription(options.description)
    .setVersion(options.version)
    .addApiKey(
      {
        type: 'apiKey',
        name: 'X-API-KEY',
        in: 'header',
        description: 'API key for authentication',
      },
      'ApiKeyAuth',
    )
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'JWT token for authentication',
      },
      'BearerAuth',
    );

  // Add tags if provided
  if (options.tags) {
    options.tags.forEach((tag) => {
      builder.addTag(tag.name, tag.description);
    });
  }

  const document = SwaggerModule.createDocument(app, builder.build(), {
    deepScanRoutes: true,
    ignoreGlobalPrefix: false,
  });

  // Apply the enhancer plugin
  const enhancedDocument = new SwaggerEnhancerPlugin().apply(document);

  // Set up the Swagger UI
  const defaultCustomOptions = {
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
      docExpansion: 'none',
      filter: true,
      showExtensions: true,
      tagsSorter: 'alpha',
      defaultModelsExpandDepth: 3,
      defaultModelExpandDepth: 3,
      displayOperationId: false,
      tryItOutEnabled: true,
    },
    customSiteTitle: options.title,
    customCss: `
      .swagger-ui .topbar { display: none }
      .swagger-ui .opblock .opblock-summary-description { text-align: right; }
      .swagger-ui .opblock-tag { font-size: 18px; }
      .swagger-ui .opblock .opblock-summary { padding: 8px; }
      .swagger-ui .response-col_description__inner p { margin: 0; }
      .swagger-ui .parameter__name { font-weight: bold; }
      .swagger-ui .parameter__type { font-size: 12px; }
      .swagger-ui .parameter__deprecated { font-size: 12px; }
      .swagger-ui .parameter__in { font-size: 12px; }
      .swagger-ui table tbody tr td:first-of-type { max-width: 20%; }
      .swagger-ui .response-col_status { min-width: 100px; }
    `,
  };

  SwaggerModule.setup('/api', app, enhancedDocument, {
    ...defaultCustomOptions,
    ...customOptions,
  });

  return enhancedDocument;
}
