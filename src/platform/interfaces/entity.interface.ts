export interface Ticket {
  id: string;
  teamId: string;
  teamName: string;
  teamIdentifier: string;
  subTeamId?: string;
  subTeamName?: string;
  subTeamIdentifier?: string;
  ticketId: number;
  title: string;
  description: string | null;
  status: string;
  priority: string;
  isPrivate: boolean;
  formId: string;
  assignedAgent: string;
  assignedAgentId: string;
  assignedAgentEmail: string;
  customerContactFirstName?: string;
  customerContactLastName?: string;
  customerContactEmail?: string;
  requestorEmail: string;
  submitterEmail: string | null;
  customFieldValues: string[];
  statusId: string;
  priorityId: string;
  storyPoints: number | null;
  aiGeneratedTitle: string | null;
  aiGeneratedSummary: string | null;
  createdAt: string;
  updatedAt: string;
  sentiment?: string;
  comment?: {
    id: string;
    content: string;
    contentHtml?: string;
    contentJson?: string;
  };
}
