import {
  Ticket<PERSON>ommentAddedHand<PERSON>,
  TicketCommentDeletedHandler,
  TicketCommentUpdatedHandler,
} from './comments';
import { CommentReactionAddedHandler } from './reactions/reaction-added.handler';
import { CommentReactionRemovedHandler } from './reactions/reaction-removed.handler';
import { 
  Ticket<PERSON>reated<PERSON>and<PERSON>, 
  TicketUpdatedHandler, 
  TicketArchiveHandler, 
  TicketUnarchiveHandler 
} from './tickets';

export const eventHandlers = [
  // Comment Handlers
  TicketCommentAddedHandler,
  TicketCommentUpdatedHandler,
  TicketCommentDeletedHandler,

  CommentReactionAddedHandler,
  CommentReactionRemovedHandler,

  // Ticket Handlers
  TicketUpdatedHandler,
  Ticket<PERSON>reated<PERSON>and<PERSON>,
  TicketA<PERSON>ive<PERSON>and<PERSON>,
  TicketUnarchiveHandler,
];
