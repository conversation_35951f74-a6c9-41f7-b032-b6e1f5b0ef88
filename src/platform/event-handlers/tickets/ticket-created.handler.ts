import { Inject, Injectable } from '@nestjs/common';
import { Installations } from '../../../database/entities';
import { InstallationRepository } from '../../../database/entities/installations/repositories';
import { SlackMessageCore } from '../../../slack/core/messages/slack-message.core';
import {
  EvaluateAndSendTriageMessagesOptions,
  TriageEvaluationService,
} from '../../../slack/services/triage-evaluation.service';
import { CUSTOM_LOGGER_TOKEN, ILogger, SLACK_SENTRY_TAG } from '../../../utils';
import { SentryService } from '../../../utils/filters/sentry-alerts.filter';
import { PlatformEvent } from '../../decorators';
import { PlatformWebhookEvent } from '../../type-system';
import { PlatformHandler } from '../commons/platform-handlers.interface';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';

@Injectable()
@PlatformEvent('ticket:created')
export class TicketCreatedHandler implements PlatformHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    @Inject('Sentry') private readonly sentryService: SentryService,

    // Core utilities
    private readonly slackMessageCore: SlackMessageCore,
    private readonly triageEvaluationService: TriageEvaluationService,
    private readonly platformService: ThenaPlatformApiProvider,

    // Database Repositories
    private readonly installationsRepository: InstallationRepository,
  ) {}

  async handle(event: PlatformWebhookEvent<'ticket:created'>) {
    const spanId = `[TicketCreatedHandler] [${event.message.eventId}]`;
    try {
      this.logger.log(
        `${spanId} Processing ticket created: ${event.message.payload.ticket.id}`,
      );

      const { message } = event;
      const { payload, actor, eventId, eventType, orgId, timestamp } = message;
      const { ticket } = payload;

      // [SANITY CHECK] If the event type is not ticket:updated, skip the event
      if (eventType !== 'ticket:created') {
        this.logger.debug(
          `${spanId} Skipping ticket created event processor for event ${eventId} for org ${orgId} at ${timestamp}; event id: ${eventId}, by ${actor.email} id: ${actor.id}, expected event type: ticket:created got ${eventType}`,
        );

        return;
      }

      this.logger.log(
        `${spanId} Ticket id ${ticket.id} was created for org ${orgId} at ${timestamp}; event id: ${eventId}, by ${actor.email} id: ${actor.id}`,
      );

      // Get all installations for the organization
      const installations = await this.installationsRepository.findAll({
        where: { organization: { uid: orgId } },
        relations: { organization: true },
      });

      // Evaluate and send triage messages for each installation
      for (const installation of installations) {
        const slackMessage =
          await this.slackMessageCore.getSlackMessageByPlatformTicketId(
            installation,
            ticket.id,
            { createIndependentIfNotFound: true },
          );

        // Evaluate and send triage messages
        await this.handleTicketCreated(installation, {
          ticket: {
            id: ticket.id,
            title: ticket.title,
            ticketId: ticket.ticketId,
            description: ticket.description,
            teamId: ticket.teamId,
            status: ticket.statusName,
            statusId: ticket.statusId,
            customerContactEmail: ticket.customerContactEmail,
            customerContactFirstName: ticket.customerContactFirstName,
            customerContactLastName: ticket.customerContactLastName,
            teamIdentifier: ticket.teamIdentifier,
            priority: ticket.priorityName,
            priorityId: ticket.priorityId,
            assignedAgentId: ticket.assignedAgent.id,
            assignedAgentEmail: ticket.assignedAgent.email,
            assignedAgent: ticket.assignedAgent.name,
            createdAt: ticket.createdAt,
            sentiment: ticket.sentimentName,
            requestorEmail: ticket.requestorEmail,
            submitterEmail: ticket.submitterEmail,
          },
          platformTeamId: ticket.teamId,
          slackMessage,
        });
      }
    } catch (error) {
      this.logger.error(
        `${spanId} Error processing ticket created event ${event.message.eventId}`,
        error instanceof Error ? error.stack : undefined,
      );

      // Report to Sentry
      this.sentryService.captureException(error, {
        tag: SLACK_SENTRY_TAG,
        name: '🚨 Error processing ticket created event',
        eventId: event.message.eventId,
        ticketId: event.message.payload.ticket.id,
        orgId: event.message.orgId,
      });

      // Rethrow the error to propagate it up
      throw error;
    }
  }

  private async handleTicketCreated(
    installation: Installations,
    data: EvaluateAndSendTriageMessagesOptions,
  ) {
    const spanId = `[handleTicketCreated] [${installation?.id}] [${data.ticket.id}]`;
    try {
      this.logger.debug(`${spanId} Evaluating and sending triage messages`);

      await this.triageEvaluationService.evaluateAndSendTriageMessages(
        installation,
        data,
      );

      this.logger.debug(
        `${spanId} Successfully sent triage messages for ticket ${data.ticket.id}`,
      );
    } catch (error) {
      this.logger.error(
        `${spanId} Error evaluating and sending triage messages for ticket ${data.ticket.id}`,
        error instanceof Error ? error.stack : undefined,
      );

      // Report to Sentry
      this.sentryService.captureException(error, {
        tag: SLACK_SENTRY_TAG,
        name: '🚨 Error evaluating and sending triage messages',
        ticketId: data.ticket.id,
        installationId: installation.id,
        platformTeamId: data.platformTeamId,
      });

      // Throw the error to propagate it up
      throw error;
    }
  }
}
