import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SlackMessages } from '../../../database/entities';
import { CoreTriageService } from '../../../slack/core/messages';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils/logger';
import { PlatformEvent } from '../../decorators';
import { PlatformWebhookEvent } from '../../type-system';
import { PlatformHandler } from '../commons/platform-handlers.interface';
import { SentryService } from '../../../utils/filters/sentry-alerts.filter';
import { SLACK_SENTRY_TAG } from '../../../utils';


@Injectable()
@PlatformEvent('ticket:archived')
export class TicketArchiveHandler implements PlatformHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    @Inject('Sentry') private readonly sentryService: SentryService,

    // Core utilities
    private readonly coreTriageService: CoreTriageService,

    // Database Repositories
    @InjectRepository(SlackMessages)
    private readonly slackMessagesRepository: Repository<SlackMessages>,
  ) {}

  async handle(event: PlatformWebhookEvent<'ticket:archived'>) {
    const spanId = `[TicketArchiveHandler] [${event.message.eventId}]`;
    this.logger.log(
      `${spanId} Processing ticket archive: ${event.message.payload.ticket.id}`,
    );

    const { message } = event;
    const { payload, actor, eventId, eventType, orgId, timestamp } = message;
    const { ticket } = payload;

    try {
      // [SANITY CHECK] If the event type is not ticket:archived, skip the event
      if (eventType !== 'ticket:archived') {
        this.logger.debug(
          `${spanId} Skipping ticket archive event processor for event ${eventId} for org ${orgId} at ${timestamp}; event id: ${eventId}, by ${actor.email} id: ${actor.id}, expected event type: ticket:archived got ${eventType}`,
        );

        return;
      }

      this.logger.log(
        `${spanId} Ticket id ${ticket.id} was archived for org ${orgId} at ${timestamp}; event id: ${eventId}, by ${actor.email} id: ${actor.id}`,
      );

      // Find the slack message for the ticket
      const slackMessage = await this.slackMessagesRepository.findOne({
        where: { platformTicketId: ticket.id, organization: { uid: orgId } },
        relations: ['installation', 'organization'],
      });

      // Return if the slack message is not found
      if (!slackMessage) {
        this.logger.debug(
          `${spanId} No slack message found for ticket ${ticket.id} in org ${orgId}`,
        );

        return;
      }

      const installation = {
        ...slackMessage.installation,
        organization: slackMessage.organization,
      };

      // Update the triage messages for the slack message
      try {
        await this.coreTriageService.updateTriageMessagesForSlackMessage(
          installation,
          slackMessage,
        );
      } catch (updateError) {
        this.logger.error(
          `${spanId} Error updating triage messages for ticket ${ticket.id} in org ${orgId}`,
          updateError instanceof Error ? updateError.stack : undefined,
        );

        // Report to Sentry
        this.sentryService.captureException(updateError, {
          tag: SLACK_SENTRY_TAG,
          name: '🚨 Error updating triage messages',
          ticketId: ticket.id,
          orgId: orgId,
          eventId: eventId,
        });
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `${spanId} Error processing ticket archive event ${event.message.eventId} for org ${orgId} at ${timestamp}; event id: ${eventId}, by ${actor.email} id: ${actor.id}`,
          error.stack,
        );

        // Report to Sentry
        this.sentryService.captureException(error, {
          tag: SLACK_SENTRY_TAG,
          name: '🚨 Error processing ticket archive event',
          ticketId: ticket.id,
          orgId: orgId,
          eventId: eventId,
          actorEmail: actor.email,
        });
      } else {
        this.logger.error(
          `${spanId} Error processing ticket archive event ${event.message.eventId} for org ${orgId} at ${timestamp}; event id: ${eventId}, by ${actor.email} id: ${actor.id}`,
          JSON.stringify(error),
        );

        // Report to Sentry for non-Error objects
        this.sentryService.captureException(
          new Error(
            `Non-Error thrown in ticket archive handler: ${JSON.stringify(error)}`,
          ),
          {
            tag: SLACK_SENTRY_TAG,
            name: '🚨 Non-Error thrown in ticket archive handler',
            ticketId: ticket.id,
            orgId: orgId,
            eventId: eventId,
            actorEmail: actor.email,
          },
        );
      }
    }
  }
}
