import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Users } from '../../../database/entities';
import { SlackMessagesRepository } from '../../../database/entities/slack-messages/repositories/slack-messages.repository';
import { CommentConversationMapsRepository } from '../../../database/entities/mappings/repositories/comment-conversation-maps.repository';
import { SlackWebAPIService } from '../../../slack/providers/slack-apis/slack-apis.service';
import { SLACK_SENTRY_TAG } from '../../../utils';
import { SentryService } from '../../../utils/filters/sentry-alerts.filter';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils/logger';
import { PlatformEvent } from '../../decorators';
import { PlatformWebhookEvent } from '../../type-system';
import { PlatformHandler } from '../commons/platform-handlers.interface';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';

interface CommentReactionRemovedPayload {
  commentId: string;
  reactionName: string;
  userId: string;
}

interface PlatformReactionEvent {
  commentId: string;
  reactionName: string;
  userId: string;
}

@Injectable()
@PlatformEvent('ticket:comment:reaction:removed')
export class CommentReactionRemovedHandler implements PlatformHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    @Inject('Sentry') private readonly sentryService: SentryService,

    // External API Providers
    private readonly slackApiProvider: SlackWebAPIService,
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,

    // Database Repositories
    private readonly slackMessagesRepository: SlackMessagesRepository,
    private readonly commentConversationMapsRepository: CommentConversationMapsRepository,

    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,
  ) {}

  async handle(event: PlatformWebhookEvent) {
    try {
      this.logger.log(
        `Processing comment reaction removed on: ${event.message?.payload?.comment?.id}`,
      );

      const { payload, orgId } = event.message;
      // Type casting the payload to ensure TypeScript recognizes the properties
      const typedPayload = payload as PlatformReactionEvent;
      const commentId = payload.comment.id;
      const reactionName = payload.reaction.name;
      const userEmail =
        payload.reaction?.metadata?.impersonatedUserEmail ??
        payload.reaction.author.email;

      // Find the slack message associated with this comment
      const slackMessage = await this.slackMessagesRepository.findByCondition({
        where: {
          platformCommentId: commentId,
          organization: { uid: orgId },
        },
        relations: { installation: true, channel: true, organization: true },
      });

      // If no slack message is found, try to find it in the comment conversation maps
      if (!slackMessage) {
        const commentConversationMap =
          await this.commentConversationMapsRepository.findByCondition({
            where: {
              platformCommentId: commentId,
              organization: { uid: orgId },
            },
            relations: {
              installation: true,
              channel: true,
              organization: true,
            },
          });

        if (!commentConversationMap) {
          this.logger.error(`No slack message found for comment ${commentId}`);
          return;
        }

        // Remove the reaction from the conversation
        await this.removeReactionFromMessage(
          commentConversationMap.installation,
          orgId,
          commentConversationMap.channel.channelId,
          commentConversationMap.slackTs,
          reactionName,
          userEmail,
        );
      } else {
        // Remove the reaction from the message
        await this.removeReactionFromMessage(
          slackMessage.installation,
          orgId,
          slackMessage.channel.channelId,
          slackMessage.slackMessageTs,
          reactionName,
          userEmail,
        );
      }

      this.logger.log(
        `Successfully processed reaction ${reactionName} removed from comment ${commentId}`,
      );
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error processing comment reaction removed: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(`Error processing comment reaction removed:`, error);
      }

      // Capture the error
      this.sentryService.captureException(error, {
        name: '🚨 Error processing comment reaction removed!',
        tag: SLACK_SENTRY_TAG,
      });
    }
  }

  /**
   * Removes a reaction from a message
   * @param token The bot token
   * @param channel The channel ID
   * @param messageTs The message timestamp
   * @param reactionName The reaction name
   */
  private async removeReactionFromMessage(
    installation: any,
    orgId: string,
    channel: string,
    messageTs: string,
    reactionName: string,
    userEmail: string,
  ) {
    try {
      const user = await this.usersRepository.findOneBy({
        slackProfileEmail: userEmail,
        installation: { id: installation.id },
        organization: { uid: orgId },
      });

      if (user && user.slackAccessToken) {
        this.logger.log(
          `Using user token for reaction ${reactionName} from user ${user.id}`,
        );

        try {
          // Remove reaction using the user's token
          await this.slackApiProvider.removeReactionFromMessage(
            user.slackAccessToken,
            {
              channel,
              timestamp: messageTs,
              name: reactionName,
            },
          );

          // If we successfully removed the reaction with the user token, return early
          // This prevents the reaction from being removed twice
          return;
        } catch (userTokenError) {
          if (userTokenError instanceof Error) {
            const slackError = userTokenError as any;
            const reactionNotFoundErr =
              slackError?.data?.error === 'no_reaction';

            if (reactionNotFoundErr) {
              this.logger.log(
                `Reaction ${reactionName} not found on message ${messageTs} in channel ${channel} by user ${userEmail}`,
              );
              return;
            }
          }
          // If there's an error with the user token, log it and continue to use bot token
          this.logger.error(
            `Error using user token, falling back to bot token: ${userTokenError instanceof Error ? userTokenError.message : 'Unknown error'}`,
          );
        }
      }

      this.logger.log(
        `Using bot token for reaction removal ${reactionName} - user not authorized or token failed`,
      );

      await this.slackApiProvider.removeReactionFromMessage(
        installation.botToken,
        {
          channel,
          timestamp: messageTs,
          name: reactionName,
        },
      );
    } catch (slackError) {
      if (slackError instanceof Error) {
        this.logger.error(
          `Error removing reaction from slack message: ${slackError.message}`,
        );
      } else {
        console.error(slackError);
      }

      // Capture the error
      this.sentryService.captureException(slackError, {
        name: '🚨 Error removing reaction from slack message!',
        tag: SLACK_SENTRY_TAG,
      });
    }
  }
}
