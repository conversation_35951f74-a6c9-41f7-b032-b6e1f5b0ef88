export interface CreateNewTicket {
  title: string;
  text: string;
  description?: string;
  teamId: string;
  urgency?: string;
  sentiment?: string;
  requestorEmail: string;
  subTeamId?: string;
  performRouting?: boolean;
  metadata: {
    slack: { channel: string; ts: string; user: string };
  };
  // New comment fields
  commentContentHtml?: string;
  commentContent?: string;
  commentContentJson?: string;
  commentAttachmentIds?: string[];
  commentMetadata?: Record<string, any>;
  commentImpersonatedUserName?: string;
  commentImpersonatedUserEmail?: string;
  commentImpersonatedUserAvatar?: string;
}

export interface UpdateTicketData {
  statusId: string;
  priorityId: string;
  assignedAgentId: string;
}
