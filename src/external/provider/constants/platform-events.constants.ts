import { MessageEvent } from '@slack/types/dist/events/message';

export enum EmittableSlackEvents {
  MEMBER_JOINED_CHANNEL = 'slack:member:joined',
  MEMBER_LEFT_CHANNEL = 'slack:member:left',

  CHANNEL_CREATED = 'slack:channel:created',
  CHANNEL_DELETED = 'slack:channel:deleted',
  CHANNEL_ARCHIVED = 'slack:channel:archived',

  MESSAGE = 'slack:message',

  REACTION_ADDED = 'slack:reaction:added',
  REACTION_REMOVED = 'slack:reaction:removed',
}

export interface MemberJoinedEvent {
  type: EmittableSlackEvents.MEMBER_JOINED_CHANNEL;
  user: string;
  channel: string;
  channel_name: string;
  channel_type: string;
  team: string;
  inviter?: string;
  userInfo: {
    id: string;
    email: string;
    name: string;
    avatar: string;
  };
  userType: 'internal_member' | 'customer';
}

export interface MemberLeftEvent {
  type: EmittableSlackEvents.MEMBER_LEFT_CHANNEL;
  user: string;
  channel: string;
  channel_name: string;
  userInfo: {
    id: string;
    email: string;
    name: string;
    avatar: string;
  };
  userType: 'internal_member' | 'customer';
}

export interface ChannelCreatedEvent {
  type: EmittableSlackEvents.CHANNEL_CREATED;
  channel: {
    id: string;
    name: string;
    created: number;
    creator: string;
  };
}

export interface ChannelDeletedEvent {
  type: EmittableSlackEvents.CHANNEL_DELETED;
  channel: string;
}

export interface ChannelArchivedEvent {
  type: EmittableSlackEvents.CHANNEL_ARCHIVED;
  channel: string;
  user: string;
}

export interface SlackMessageEvent extends Omit<MessageEvent, 'type'> {
  type: EmittableSlackEvents.MESSAGE;
}

export interface ReactionAddedEvent {
  type: EmittableSlackEvents.REACTION_ADDED;
  user: string;
  reaction: string;
  item_user: string;
  item: {
    type: string;
    channel: string;
    ts: string;
  };
  event_ts: string;
}

export interface ReactionRemovedEvent {
  type: EmittableSlackEvents.REACTION_REMOVED;
  user: string;
  reaction: string;
  item_user: string;
  item: {
    type: string;
    channel: string;
    ts: string;
  };
  event_ts: string;
}

export type EmittableSlackEventData =
  | MemberJoinedEvent
  | MemberLeftEvent
  | ChannelCreatedEvent
  | ChannelDeletedEvent
  | ChannelArchivedEvent
  | SlackMessageEvent
  | ReactionAddedEvent
  | ReactionRemovedEvent;
