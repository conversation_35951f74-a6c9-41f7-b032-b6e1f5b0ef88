import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CommonModule } from '../common/common.module';
import { ConfigModule } from '../config/config.module';
import { Users } from '../database/entities';
import { UserPlatformLookupModule } from '../shared/user-platform-lookup';
import { ExternalService } from './external.service';
import { ThenaPlatformApiProvider } from './provider/thena-platform-api.provider';

@Module({
  imports: [
    ConfigModule,
    CommonModule,
    TypeOrmModule.forFeature([Users]),
    UserPlatformLookupModule,
  ],
  providers: [ExternalService, ThenaPlatformApiProvider],
  exports: [ThenaPlatformApiProvider, UserPlatformLookupModule],
})
export class ExternalModule {}
