import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  Inject,
  InternalServerErrorException,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { GetBotCtx } from '../../auth/decorators';
import { NoSlackTeam } from '../../auth/decorators/no-slack-team.decorator';
import { AuthGuard } from '../../auth/guards';
import { BotCtx } from '../../auth/interfaces';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import {
  AddTeamDTO,
  DisconnectTeamToChannelsDTO,
  MapTeamToChannelsDTO,
  MapTeamToSecondaryChannelsDTO,
  RemoveTeamDTO,
} from '../dtos';
import { CommonQueryParamsToFetchEntityData } from '../query-params';
import { SlackTeamsService } from '../services/teams.service';

@Controller('v1/slack/teams')
@UseGuards(AuthGuard)
export class TeamsController {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Services
    private readonly teamsService: SlackTeamsService,
  ) {}

  @Get()
  @NoSlackTeam()
  async getAllTeams(
    @Query() query: CommonQueryParamsToFetchEntityData,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      const results = await this.teamsService.getAllTeams(botCtx, query);
      return results;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(`Error adding team: ${error.message}`, error.stack);
      } else {
        console.error('Error getting all teams!', error);
      }

      throw new InternalServerErrorException('Something went wrong!');
    }
  }

  @Post()
  async addTeam(@Body() body: AddTeamDTO, @GetBotCtx() botCtx: BotCtx) {
    try {
      return this.teamsService.addTeam(body, botCtx);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(`Error adding team: ${error.message}`, error.stack);
      } else {
        console.error('Error adding team!', error);
      }

      throw new InternalServerErrorException('Something went wrong!');
    }
  }

  @Delete()
  async removeTeam(@Body() body: RemoveTeamDTO, @GetBotCtx() botCtx: BotCtx) {
    try {
      await this.teamsService.removeTeam(body, botCtx);
      return { ok: true, message: 'Team removed successfully' };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(`Error adding team: ${error.message}`, error.stack);
      } else {
        console.error('Error removing team!', error);
      }

      throw new InternalServerErrorException('Something went wrong!');
    }
  }

  @Post('/map-channels')
  async mapTeamToChannels(
    @Body() body: MapTeamToChannelsDTO,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      await this.teamsService.mapTeamToChannels(body, botCtx);
      return { ok: true, message: 'Team mapped to channels successfully' };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(`Error adding team: ${error.message}`, error.stack);
      } else {
        console.error('Error mapping channels to team!', error);
      }

      throw new InternalServerErrorException('Something went wrong!');
    }
  }

  @Post('/map-secondary-channels')
  async mapTeamToSecondaryChannels(
    @Body() body: MapTeamToSecondaryChannelsDTO,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      await this.teamsService.mapTeamToSecondaryChannels(body, botCtx);
      return { ok: true, message: 'Team mapped to channels successfully' };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(`Error adding team: ${error.message}`, error.stack);
      } else {
        console.error('Error mapping channels to team!', error);
      }

      throw new InternalServerErrorException('Something went wrong!');
    }
  }

  @Delete('/disconnect-channels')
  async disconnectTeamToChannels(
    @Body() body: DisconnectTeamToChannelsDTO,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      await this.teamsService.disconnectTeamToChannels(body, botCtx);
      return {
        ok: true,
        message: 'Team disconnected to channels successfully',
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(`Error adding team: ${error.message}`, error.stack);
      } else {
        console.error('Error mapping channels to team!', error);
      }

      throw new InternalServerErrorException('Something went wrong!');
    }
  }

  @Get('/:teamId')
  async getPlatformTeam(
    @Param('teamId') teamId: string,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      const platformTeam = await this.teamsService.getPlatformTeam(
        teamId,
        botCtx,
      );

      return platformTeam;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error fetching platform team: ${error.message}`,
          error.stack,
        );
      } else {
        console.error('Failed to fetch platform team', error);
      }

      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException('Something went wrong!');
    }
  }

  @Delete('/disconnect/:teamId')
  async disconnectTeam(
    @Param('teamId') teamId: string,
    @GetBotCtx() botCtx: BotCtx,
  ) {
    try {
      if (!teamId) {
        throw new BadRequestException('Team ID is required');
      }

      await this.teamsService.disconnectTeam(teamId, botCtx);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error
      if (error instanceof Error) {
        this.logger.error(
          `Error disconnecting team: ${error.message}`,
          error.stack,
        );
      } else {
        console.error('Error disconnecting team!', error);
      }

      throw new InternalServerErrorException('Something went wrong!');
    }
  }
}
