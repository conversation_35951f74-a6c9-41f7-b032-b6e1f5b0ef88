import { Injectable } from '@nestjs/common';
import { SlackOptionsMiddlewareArgs } from '@slack/bolt';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';
import { SelectAssigneeComposite } from '../../blocks/components/composite/triage';
import { TicketDetailsMetadata } from '../../constants/interfaces';
import { SlackOptions } from '../../decorators';
import { SlackActionHandler } from '../../interfaces';
import { EnrichedSlackArgsContext } from '../../services/slack-action-discovery.service';

interface TicketAssigneeSlackView {
  channelId: string;
  metadata: TicketDetailsMetadata;
}

@Injectable()
@SlackOptions(SelectAssigneeComposite.ACTION_IDS.ASSIGNEE_SELECT)
export class TicketAssigneeOptionsHandler implements SlackActionHandler {
  constructor(
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
  ) {}

  async handle(args: SlackOptionsMiddlewareArgs) {
    // Get the context
    let context: EnrichedSlackArgsContext;
    if ('context' in args) {
      context = args.context as EnrichedSlackArgsContext;
    }

    const { installation } = context;

    // Get the payload
    const { payload } = args;
    const { view, value } = payload;

    // Get the private metadata
    const privateMetadata = view.private_metadata;

    // Parse the private metadata
    const { metadata }: TicketAssigneeSlackView = JSON.parse(privateMetadata);

    if (metadata?.event_type !== 'ticket_details') {
      return { options: [] };
    }

    const { installation_id: installationId, ticket_team_id: ticketTeamId } =
      metadata.event_payload;
    // [SANITY CHECK] If the installation ID does not match, throw an error
    if (installation.id !== installationId) {
      throw new Error("You're not allowed to interact with this entity!");
    }

    // Get the team members
    const teamMembers = await this.thenaPlatformApiProvider.getTeamMembers(
      installation,
      ticketTeamId,
      value,
    );

    // Build the options
    const options = teamMembers.map((member) => ({
      text: {
        type: 'plain_text',
        text: member.name,
      },
      value: member.id,
    }));

    return { options };
  }
}
