import { Inject, Injectable } from '@nestjs/common';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { safeJsonStringify } from '../../../../utils/external/safe-json-stringify.utils';
import { CHANNEL_SETUP_ACTION_ID } from '../../../constants/channel-setup.constants';
import { SlackAction } from '../../../decorators';
import { DecoratedSlackActionMiddlewareArgs } from '../../../event-handlers';
import { SlackActionHandler } from '../../../interfaces';
import { ChannelSetupService } from '../../../services/channel-setup.service';
import { SlackResponse } from '../../../types';
import {
  ChannelData,
  ChannelSetupActionBody,
  Team,
} from '../../../types/channel-setup.types';

@Injectable()
@SlackAction([CHANNEL_SETUP_ACTION_ID])
export class Configure<PERSON>hannelActionHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly channelSetupService: ChannelSetupService,
  ) {}

  async handle(
    args: DecoratedSlackActionMiddlewareArgs,
  ): Promise<SlackResponse> {
    const { body } = args;

    if (!('context' in args)) {
      this.logger.error('No context found in arguments');
      return;
    }

    const context = args.context;
    const { client, installation } = context;
    let responseUrl: string;
    try {
      const actionBody = body as ChannelSetupActionBody;
      responseUrl = actionBody.response_url;
      if (
        !actionBody.actions ||
        !Array.isArray(actionBody.actions) ||
        actionBody.actions.length === 0
      ) {
        this.logger.error('No actions found in body');
        return;
      }

      const action = actionBody.actions[0];
      let channelData: ChannelData | undefined;

      if (action.value) {
        try {
          channelData = JSON.parse(action.value) as ChannelData;
        } catch (error) {
          this.logger.error(
            `Error parsing channel data: ${safeJsonStringify(error, { fallback: 'Unknown error parsing channel data' })}`,
          );
        }
      }

      if (!channelData?.channelId) {
        this.logger.error('No channel ID found in action value');
        return;
      }

      // Fetch teams from the platform
      let teams: Team[];
      try {
        teams = await this.channelSetupService.fetchTeams(installation);

        if (teams.length === 0) {
          await client.chat.postEphemeral({
            token: installation.botToken,
            channel: channelData.channelId,
            user: actionBody.user.id,
            text: 'No teams were found in the system. Please contact your administrator to set up teams first.',
          });
          return;
        }
      } catch (error) {
        this.logger.error(
          `Error fetching teams: ${safeJsonStringify(error, { fallback: 'Unknown error fetching teams' })}`,
        );
        await client.chat.postEphemeral({
          token: installation.botToken,
          channel: channelData.channelId,
          user: actionBody.user.id,
          text: 'Failed to connect to the team management system. Please try again later or contact support.',
        });
        return;
      }

      // Show the configuration modal
      await this.channelSetupService.openConfigurationModal(
        client,
        installation,
        actionBody.trigger_id,
        channelData,
        teams,
        responseUrl,
      );

      await this.channelSetupService.checkChannelState(
        channelData.channelId,
        installation,
      );
    } catch (error) {
      this.logger.error(
        `Error handling configure channel action: ${safeJsonStringify(error, { fallback: 'Unknown error in channel configuration' })}`,
      );
    }
  }
}
