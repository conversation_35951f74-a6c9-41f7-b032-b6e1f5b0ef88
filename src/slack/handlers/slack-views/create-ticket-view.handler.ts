import { Inject, Injectable } from '@nestjs/common';
import { SlackViewMiddlewareArgs } from '@slack/bolt';
import { TransactionService } from '../../../database/common/transactions.service';
import {
  CustomerContacts,
  Installations,
  PlatformTeams,
  PlatformTeamsToChannelMappings,
} from '../../../database/entities';
import { TeamChannelMapsRepository } from '../../../database/entities/mappings/repositories/team-channel-maps.repository';
import { SlackMessagesRepository } from '../../../database/entities/slack-messages/repositories/slack-messages.repository';
import { TeamsRepository } from '../../../database/entities/teams';
import {
  CreateNewComment,
  CreateNewTicket,
} from '../../../external/provider/interfaces';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils';
import { CreateTicketsBlocksComposite } from '../../blocks/components';
import { CreateTicketCommand } from '../../commands/create-ticket.command';
import { SlackAppManagementService } from '../../core';
import { SlackView } from '../../decorators';
import { DecoratedSlackViewMiddlewareArgs } from '../../event-handlers';
import { SlackActionHandler } from '../../interfaces';
import { SlackWebAPIService } from '../../providers/slack-apis/slack-apis.service';
import { EnrichedSlackArgsContext } from '../../services/slack-action-discovery.service';

const LOG_SPAN = 'CreateTicketViewHandler';

@Injectable()
@SlackView(CreateTicketCommand.CREATE_TICKET_VIEW_CALLBACK_ID)
export class CreateTicketViewHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Database Repositories
    private readonly slackMessagesRepository: SlackMessagesRepository,
    private readonly teamChannelMapsRepo: TeamChannelMapsRepository,
    private readonly platformTeamsRepo: TeamsRepository,

    // Core utilities
    private readonly transactionService: TransactionService,
    private readonly slackAppManagementService: SlackAppManagementService,

    // External API Providers
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
    private readonly slackApiProvider: SlackWebAPIService,
  ) {}

  async handle(args: DecoratedSlackViewMiddlewareArgs) {
    let context: EnrichedSlackArgsContext;
    if ('context' in args) {
      context = args.context as EnrichedSlackArgsContext;
    }

    this.logger.log(`${LOG_SPAN} Handling create ticket view submission`);

    const { body } = args;
    const { installation, organization, client } = context;

    try {
      if ('view' in args) {
        const view = args.view;

        const privateMetadata: {
          channelId: string;
          responseUrl: string;
          threadTs?: string;
          shouldLinkSlackMessage?: boolean;
        } = this.getPrivateMetadata(view);

        // Get the channel from the database
        const teamChannelMap = await this.teamChannelMapsRepo.findByCondition({
          where: {
            channel: { channelId: privateMetadata.channelId },
            installation: { id: installation.id },
            organization: { id: organization.id },
          },
          relations: { channel: true, platformTeam: true },
        });

        let platformTeam: PlatformTeams | null = null;

        // If the team channel map is not found, throw an error
        if (!teamChannelMap) {
          this.logger.debug(
            `${LOG_SPAN} Team channel map not found for channel ${privateMetadata.channelId}`,
          );

          const selectedTeamId =
            view.state.values?.team_select_block?.team_select?.selected_option
              ?.value;
          if (selectedTeamId) {
            platformTeam = await this.platformTeamsRepo.findByCondition({
              where: {
                uid: selectedTeamId,
                installation: { id: installation.id },
              },
            });

            if (!platformTeam) {
              throw new Error(
                'This channel was not found mapped to a team on platform.',
              );
            }
          } else {
            throw new Error(
              'This channel was not found mapped to a team on platform.',
            );
          }
        } else {
          platformTeam = teamChannelMap.platformTeam;
        }

        const ticketData = this.getFormValues(view);

        const ticketPayload: CreateNewTicket = {
          title: ticketData.title,
          requestorEmail: ticketData.requestorEmail,
          teamId: platformTeam.uid,
          text: ticketData.description,
          description: ticketData.description,
          metadata: {
            slack: {
              channel: privateMetadata.channelId,
              ts: 'SLASH_TICKET',
              user: body.user.id,
            },
          },
        };

        if (privateMetadata.shouldLinkSlackMessage) {
          // If the should link slack message flag is present, link the slack message
          await this.linkSlackMessage(
            installation,
            ticketPayload,
            privateMetadata.threadTs,
            teamChannelMap,
          );
        } else {
          // Create a ticket
          const ticket = await this.thenaPlatformApiProvider.createNewTicket(
            installation,
            ticketPayload,
          );

          // Send a message to the user to configure the channel
          await client.chat.postMessage({
            token: installation.botToken,
            channel: privateMetadata.channelId,
            text: `Ticket created successfully. Ticket ID: ${ticket.ticketId}`,
          });
        }

        if (privateMetadata.responseUrl) {
          // If the response URL is present, send a message to the user
          await fetch(privateMetadata.responseUrl, {
            method: 'POST' as const,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              response_type: 'ephemeral',
              delete_original: true,
            }),
          });
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `${LOG_SPAN} Error handling create ticket view submission`,
          error.stack,
        );
      } else {
        console.error(
          `${LOG_SPAN} Error handling create ticket view submission`,
          error,
        );
      }

      // Send a message to the user to configure the channel
      await client.chat.postMessage({
        token: installation.botToken,
        channel: body.user.id,
        text: 'An error occurred while creating a ticket. Please try again later or if the issue persists, contact support.',
      });
    }
  }

  private async linkSlackMessage(
    installation: Installations,
    ticketPayload: CreateNewTicket,
    threadTs: string,
    teamChannelMap: PlatformTeamsToChannelMappings,
  ) {
    const { channel } = teamChannelMap;

    // Get the message history
    const slackMessageHistory =
      await this.slackApiProvider.getConversationHistory(
        installation.botToken,
        {
          oldest: threadTs,
          inclusive: true,
          channel: channel.channelId,
          limit: 1,
        },
      );

    // If the slack message history is not ok, log an error and return
    if (!slackMessageHistory.ok) {
      this.logger.error(
        `${LOG_SPAN} Error getting slack message history`,
        slackMessageHistory.error,
      );

      // Create a ticket without a parent comment
      await this.thenaPlatformApiProvider.createNewTicket(
        installation,
        ticketPayload,
      );

      return;
    }

    // Get the slack message
    const slackMessage = slackMessageHistory.messages[0];

    // Get the user
    const user =
      await this.slackAppManagementService.upsertPersonWithIdentification(
        slackMessage.user,
        installation,
        channel,
      );

    // Get the permalink
    const permalinkResponse = await this.slackApiProvider.getPermalink(
      installation.botToken,
      {
        channel: channel.channelId,
        message_ts: threadTs,
      },
    );

    // If the permalink response is not ok, log an error and return
    if (!permalinkResponse.ok) {
      this.logger.error(
        `${LOG_SPAN} Error getting slack message permalink`,
        permalinkResponse.error,
      );
    }

    // Get the permalink
    const permalink = permalinkResponse?.permalink;

    // Create a comment payload
    const commentPayload: Omit<CreateNewComment, 'ticketId'> = {
      channelId: channel.channelId,
      content: slackMessage.text,
      files: [],
      impersonatedUserAvatar: user.getUserAvatar(),
      impersonatedUserEmail: user.slackProfileEmail,
      impersonatedUserName: user.displayName || user.realName,
      commentVisibility: 'public',
      metadata: {
        ignoreSelf: true,
        ts: slackMessage.ts,
        threadTs: threadTs,
      },
    };

    if (user instanceof CustomerContacts) {
      // If the user is a customer contact, add the customer email to the comment payload
      commentPayload.customerEmail = user.slackProfileEmail;
    }

    // Construct the comment body
    const commentBody =
      await this.thenaPlatformApiProvider.constructCommentBody(
        installation,
        commentPayload,
      );

    // Create the ticket with parent comment
    const ticket = await this.thenaPlatformApiProvider.createNewTicket(
      installation,
      {
        ...ticketPayload,
        commentContentHtml: commentBody.contentHtml,
        commentContent: commentBody.content,
        commentContentJson: commentBody.contentJson,
        commentAttachmentIds: commentBody.attachmentIds,
        commentMetadata: commentBody.metadata,
        commentImpersonatedUserName: commentBody.impersonatedUserName,
        commentImpersonatedUserEmail: commentBody.impersonatedUserEmail,
        commentImpersonatedUserAvatar: commentBody.impersonatedUserAvatar,
      },
    );

    // Save the slack message
    await this.slackMessagesRepository.save({
      channel: { id: channel.id },
      platformTicketId: ticket.id,
      slackPermalink: permalink,
      slackMessageTs: slackMessage.ts,
      slackMessageThreadTs: slackMessage.thread_ts,
      slackUserId: slackMessage.user,
      installation: { id: installation.id },
      organization: { id: installation.organization.id },
      metadata: {
        ticket_details: {
          status: ticket.status,
          statusId: ticket.statusId,
          priority: ticket.priority,
          priorityId: ticket.priorityId,
        },
      },
      platformCommentId: ticket.comment.id,
    });

    try {
      // Send a message in the thread informing about the ticket creation
      await this.slackApiProvider.sendMessage(installation.botToken, {
        channel: channel.channelId,
        text: `Ticket created successfully. Ticket ID: ${ticket.ticketId}`,
        thread_ts: threadTs,
        unfurl_links: true,
        unfurl_media: true,
      });

      const commentPayload: CreateNewComment = {
        channelId: channel.channelId,
        files: [],
        content: `Ticket created successfully. Ticket ID: ${ticket.ticketId}`,
        ticketId: ticket.id,
        commentVisibility: 'public',
        parentCommentId: ticket.comment.id,
        impersonatedUserAvatar: user.getUserAvatar(),
        impersonatedUserEmail: user.slackProfileEmail,
        impersonatedUserName: user.displayName || user.realName,
        metadata: {
          ignoreSelf: true,
          ts: slackMessage.ts,
          threadTs: threadTs,
        },
      };

      // If the user is a customer contact, add the customer email to the comment payload
      if (user instanceof CustomerContacts) {
        commentPayload.customerEmail = user.slackProfileEmail;
      }

      // Create a comment on the ticket
      await this.thenaPlatformApiProvider.createNewComment(
        installation,
        commentPayload,
      );
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `${LOG_SPAN} Error sending message in the thread informing about the ticket creation`,
          error.stack,
        );
      } else {
        console.error(
          `${LOG_SPAN} Error sending message in the thread informing about the ticket creation`,
          error,
        );
      }
    }
  }

  /**
   * Get the form values from the view
   * @param view The view
   * @returns The form values
   */
  private getFormValues(view: SlackViewMiddlewareArgs['view']) {
    const values = view.state.values;

    // Get the form values
    const ticketData = {
      title: values.title_block.title_input.value,
      priority:
        values?.priority_block?.[
          CreateTicketsBlocksComposite.ACTION_IDS.PRIORITY
        ]?.selected_option?.value,
      requestorEmail: values.requestor_email_block.requestor_email_input.value,
      description: values.description_block.description_input.value,
    };

    return ticketData;
  }

  /**
   * Get the private metadata from the view
   * @param view The view
   * @returns The private metadata
   */
  private getPrivateMetadata(view: SlackViewMiddlewareArgs['view']) {
    try {
      return JSON.parse(view.private_metadata);
    } catch (parsingError) {
      if (parsingError instanceof Error) {
        this.logger.error(
          `${LOG_SPAN} Error parsing private metadata`,
          parsingError.stack,
        );
      } else {
        console.error(
          `${LOG_SPAN} Error parsing private metadata`,
          parsingError,
        );
      }

      throw parsingError;
    }
  }
}
