/**
 * !! This event is triggered when a user leaves a channel, this is mainly used to
 * !! determine if the bot has left a channel, because if the bot leaves a channel we
 * !! never get a ping on `member_left_channel`. For doing operations when a user/customer
 * !! leaves a channel please prefer using `member_left_channel` instead; until and unless
 * !! there is a very good reason to use this event.
 *
 * @see https://api.slack.com/events/channel_left
 */

import { Inject, Injectable } from '@nestjs/common';
import { TransactionService } from '../../../../database/common';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import { Installations } from '../../../../database/entities/installations/installations.entity';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { SlackEvent } from '../../../decorators';
import { SlackWebAPIService } from '../../../providers/slack-apis/slack-apis.service';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>H<PERSON><PERSON>, SlackEventMap } from '../../interface';
import { ChannelType } from '../../../../database/entities/channels/channels.entity';
import { TeamChannelMapsRepository } from '../../../../database/entities/mappings/repositories/team-channel-maps.repository';
import { TeamRelationshipType } from '../../../../database/entities/mappings';

@Injectable()
@SlackEvent('channel_left')
export class SlackChannelLeftHandler extends BaseSlackEventHandler<'channel_left'> {
  eventType = 'channel_left' as const;

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    private readonly slackWebAPIService: SlackWebAPIService,
    private readonly transactionService: TransactionService,
    private readonly channelsRepository: ChannelsRepository,
    private readonly teamChannelMapsRepository: TeamChannelMapsRepository,
  ) {
    super();
  }

  canHandle(event: SlackEventMap['channel_left']): boolean {
    return event.event.type === 'channel_left';
  }

  async handle(e: SlackEventMap['channel_left']): Promise<void> {
    try {
      // Check if the channel property is present
      if (!('channel' in e.event) || !this.canHandle(e)) {
        throw new Error('Invalid event received in the `channel_left` handler');
      }

      const { context, event } = e;
      const { channel, event_ts } = event;
      const { installation } = context;

      this.logger.log(
        `Received 'channel_left' event for slack team ${installation.teamId} for channel ${channel} at ${event_ts}`,
      );

      // Check if the bot is still a member of the channel
      await this.checkBotsExistenceInChannel(installation, channel);
      const foundChannelTeam =
        await this.teamChannelMapsRepository.findByCondition({
          where: {
            installation: { id: installation.id },
            channel: { channelId: channel },
            relationshipType: TeamRelationshipType.PRIMARY,
          },
        });
      // Update details in transaction
      await this.transactionService.runInTransaction(async (txnContext) => {
        // Update the channel and mark the bot out
        await this.channelsRepository.updateWithTxn(
          txnContext,
          { channelId: channel, installation: { id: installation.id } },
          {
            isBotActive: false,
            isBotJoined: false,
            lastBotLeftAt: event_ts,
            channelType: ChannelType.NOT_CONFIGURED,
          },
        );

        if (foundChannelTeam) {
          await this.teamChannelMapsRepository.removeWithTxn(
            txnContext,
            foundChannelTeam,
          );
        }
      });

      this.logger.log(
        `Bot left channel ${channel} at ${event_ts} for slack team ${installation.teamId}`,
      );
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `[ChannelLeftHandler] Failed to handle channel left event, ${error.message}`,
          error.stack,
        );
      } else {
        console.error(
          `[ChannelLeftHandler] Failed to handle channel left event, ${error}`,
        );
      }
    }
  }

  /**
   * @description Check if the bot is still a member of the channel
   * @param installation The installation entity
   * @param channel The channel id
   */
  private async checkBotsExistenceInChannel(
    installation: Installations,
    channel: string,
  ) {
    try {
      // Get the history of the channel
      const historyResponse =
        await this.slackWebAPIService.getConversationHistory(
          installation.botToken,
          { channel, limit: 1 },
        );

      // If the bot is still a member of the channel, we don't need to proceed further
      if (historyResponse.ok) {
        this.logger.warn(
          `Bot is still a member of this channel ${channel}, not proceeding further with the event process.`,
        );

        // TODO: We should notify this to someone honestly 🙃
        return;
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.debug(
          `Encountered an error from Slack, ${error.message}, checking if this is because the bot is not present in the channel`,
        );

        // Check if the error has the correct error codes from Slack
        const slackError = error as any;
        const NotInChannelErr = slackError?.data?.error === 'not_in_channel';
        const ChannelNotFoundErr =
          slackError?.data?.error === 'channel_not_found';

        // If the error is because the bot is not in the channel or the channel doesn't exist, we can proceed with marking the channel as left from Bot
        if (NotInChannelErr || ChannelNotFoundErr) {
          this.logger.debug(
            'Expected errors encountered from Slack, proceeding with marking the channel as left from Bot!',
          );
        } else {
          this.logger.error(
            `Unexpected error encountered from Slack, ${slackError?.data?.error}, rethrowing the error!`,
          );

          throw error;
        }
      } else {
        throw error;
      }
    }
  }
}
