import { Inject, Injectable } from '@nestjs/common';
import { TransactionService } from '../../../../database/common';
import { AuditLogOp, AuditLogVisibility } from '../../../../database/constants';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import { SlackAuditLogRepository } from '../../../../database/entities/slack-audit-logs/repositories/slack-audit-log.repository';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { SlackEvent } from '../../../decorators';
import { SlackEventMap } from '../../interface';
import { BaseSlackEventHandler } from '../../interface';

@Injectable()
@SlackEvent('channel_unarchive')
export class SlackChannelUnarchivedHandler extends BaseSlackEventHandler<'channel_unarchive'> {
  eventType = 'channel_unarchive' as const;

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    private readonly transactionService: TransactionService,
    private readonly channelsRepository: ChannelsRepository,
    private readonly slackAuditLogRepository: SlackAuditLogRepository,
  ) {
    super();
  }

  canHandle(event: SlackEventMap['channel_unarchive']): boolean {
    return event.event.type === 'channel_unarchive';
  }

  async handle(e: SlackEventMap['channel_unarchive']): Promise<void> {
    try {
      // Check if the channel property is present
      if (!('channel' in e.event) || !this.canHandle(e)) {
        throw new Error(
          'Invalid event received in the `channel_unarchive` handler',
        );
      }

      const { context, event } = e;
      const { channel } = event;
      const { installation, organization } = context;

      // Record this activity
      await this.slackAuditLogRepository.recordAuditLog({
        eventTs: event.event_ts,
        activityPerformedBy: event.user,
        activity: `Channel unarchived by ${event.user} channel id: ${channel}`,
        description: `Channel unarchived by ${event.user} channel id: ${channel}`,
        externalId: channel,

        // Base
        op: AuditLogOp.INFO,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation,
        organization,
      });

      // Check if the channel exists in our database
      const channelExists = await this.channelsRepository.exists({
        where: { channelId: channel, installation: { id: installation.id } },
      });

      // If the channel does not exist, we won't do anything
      if (!channelExists) {
        this.logger.debug(
          `[ChannelUnarchivedHandler] The channel ${channel} was not found for installation ${installation.id}`,
        );

        return;
      }

      // Perform the update in a transaction
      await this.transactionService.runInTransaction(async (txnContext) => {
        // Update the channel name
        await this.channelsRepository.updateWithTxn(
          txnContext,
          { channelId: channel, installation: { id: installation.id } },
          { isArchived: false },
        );

        // Record this activity after successful unarchive
        await this.slackAuditLogRepository.recordAuditLog({
          eventTs: event.event_ts,
          activityPerformedBy: event.user,
          activity: `Channel unarchived successfully by ${event.user} channel id: ${channel}`,
          description: `Channel unarchived successfully by ${event.user} channel id: ${channel} for Installation ${installation.name} (${installation.teamId})`,
          externalId: channel,

          // Base
          op: AuditLogOp.UPDATED,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation,
          organization,
        });
      });

      // Note: EmittableSlackEvents does not contain CHANNEL_UNARCHIVED event type
      // so we're not posting this event to the platform
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          '[ChannelUnarchivedHandler] Failed to handle channel unarchived event',
          error.stack,
        );

        // Log the error to audit log
        if (e?.context?.installation && e?.context?.organization) {
          await this.slackAuditLogRepository.recordAuditLog({
            eventTs: e?.event?.event_ts,
            activityPerformedBy: e?.event?.user,
            activity: `Failed to handle channel unarchived event for channel id: ${e?.event?.channel}`,
            description: `Failed to unarchive channel id: ${e?.event?.channel} for Installation ${e?.context?.installation?.name} (${e?.context?.installation?.teamId}). Error: ${error.message}`,
            externalId: e?.event?.channel,

            // Base
            op: AuditLogOp.ERROR,
            visibility: AuditLogVisibility.ORGANIZATION,
            installation: e.context.installation,
            organization: e.context.organization,
          });
        }
      } else {
        console.error(
          '[ChannelUnarchivedHandler] Failed to handle channel unarchived event',
          error,
        );
      }
    }
  }
}
