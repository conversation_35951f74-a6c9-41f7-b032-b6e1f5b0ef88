import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { cloneDeep, get } from 'lodash';
import { Repository } from 'typeorm';
import {
  Channels,
  CustomerContacts,
  Installations,
  Users,
} from '../../../../database/entities';
import { ChannelsRepository } from '../../../../database/entities/channels/repositories';
import { EmittableSlackEvents } from '../../../../external/provider/constants/platform-events.constants';
import { ThenaAppsPlatformApiProvider } from '../../../../external/provider/thena-apps-platform-api.provider';
import { ThenaPlatformApiProvider } from '../../../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';
import { SlackEvent } from '../../../decorators';
import { BaseSlackEventH<PERSON><PERSON>, SlackEventMap } from '../../interface';

@Injectable()
@SlackEvent('member_left_channel')
export class SlackMemberLeftChannelHandler extends BaseSlackEventHandler<'member_left_channel'> {
  eventType = 'member_left_channel' as const;

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    @InjectRepository(CustomerContacts)
    private readonly customerContactRepository: Repository<CustomerContacts>,
    @InjectRepository(Users) private readonly userRepository: Repository<Users>,
    private readonly channelsRepository: ChannelsRepository,

    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
    private readonly thenaAppsPlatformApiProvider: ThenaAppsPlatformApiProvider,
  ) {
    super();
  }

  canHandle(event: SlackEventMap['member_left_channel']): boolean {
    return event.event.type === 'member_left_channel';
  }

  async handle(e: SlackEventMap['member_left_channel']): Promise<void> {
    try {
      const { event, context } = e;
      const { user, channel, team } = event;
      const { installation } = context;

      // [SANITY CHECK] If the team id doesn't match, then we don't know what to do
      if (context.installation.teamId !== team) {
        this.logger.warn(
          `Received 'member_left_channel' event for team ${team} but the bot is attached to team ${context.installation.teamId}`,
        );

        return;
      }

      // Find the channel in the database
      const foundChannel = await this.channelsRepository.findByCondition({
        where: { channelId: channel, installation: { id: installation.id } },
      });

      // Find the person who left the channel
      const personLeft = await this.findPerson(user, context.installation);
      if (!personLeft) {
        this.logger.warn(
          `Received 'member_left_channel' event for team ${team} but the user ${user} is not a customer contact or a user`,
        );

        return;
      }

      if (personLeft.type === 'customer') {
        try {
          await this.syncPlatform(
            installation,
            foundChannel,
            personLeft.person as CustomerContacts,
          );
        } catch (error) {
          this.logger.error(
            `Failed to sync platform for customer contact ${personLeft.person.id}. Error: ${error}`,
          );
        }
      }

      // Post the event to the platform
      await this.thenaAppsPlatformApiProvider.postEventsToPlatform(
        installation.organization,
        {
          ...event,
          channel_name: foundChannel.name,
          type: EmittableSlackEvents.MEMBER_LEFT_CHANNEL,
          userInfo: {
            id: personLeft.person.id,
            email: personLeft.person.slackProfileEmail,
            name:
              personLeft.person.displayName ||
              personLeft.person.realName ||
              personLeft.person.slackProfileRealName ||
              personLeft.person.slackProfileDisplayName,
            avatar: personLeft.person.images?.image_48,
          },
          userType:
            personLeft.type === 'customer' ? 'customer' : 'internal_member',
        },
      );

      return;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `[SlackMemberLeftChannelHandler] Failed to handle 'member_left_channel' event for team ${e.context.installation.teamId} for user ${e.event.user} in channel ${e.event.channel} at ${e.event.event_ts}`,
          error.stack,
        );
      } else {
        console.error(
          `[SlackMemberLeftChannelHandler] Failed to handle 'member_left_channel' event for team ${e.context.installation.teamId} for user ${e.event.user} in channel ${e.event.channel} at ${e.event.event_ts}`,
          error,
        );
      }
    }
  }

  /**
   * Find the person who joined the channel do we already know about them?
   * @param userId The user ID from Slack
   * @param installation The installation from the context
   * @returns The person who joined the channel
   */
  private async findPerson(userId: string, installation: Installations) {
    // Check if the user is a customer contact
    const customerContact = await this.customerContactRepository.findOne({
      where: { slackId: userId, installation: { id: installation.id } },
    });

    // If the user is a customer contact, return that
    if (customerContact) {
      return { type: 'customer', person: customerContact };
    }

    // Check if the user is a user
    const user = await this.userRepository.findOne({
      where: { slackId: userId, installation: { id: installation.id } },
    });

    // If the user is a user, return that
    if (user) {
      return { type: 'user', person: user };
    }

    // If the user is not a customer contact or a user, then we don't know what to do
    return null;
  }

  private async syncPlatform(
    installation: Installations,
    channel: Channels,
    contact: CustomerContacts,
  ) {
    // Get existing platform dump data
    const platformContactId = get(contact, 'platformDump.customerContactId');
    const existingCustomObjectRecordIds = get(
      contact,
      'platformDump.customObjectRecordIds',
      [],
    );

    // Get the custom object IDs
    const contactCustomObjectId = get(
      installation.platformDump,
      'customObjects.contactCustomObjectId',
    );

    // Get the custom field Ids
    const slackChannelIdCF = get(
      installation.platformDump,
      'customFields.slackChannelId',
    );

    if (!platformContactId) {
      this.logger.error(
        `Customer contact ${contact.id} has no platform contact ID`,
      );
      return;
    }

    // Get the platform contact
    const platformContacts =
      await this.thenaPlatformApiProvider.getCustomerContactsByIds(
        installation,
        [platformContactId],
      );
    if (platformContacts.length === 0) {
      this.logger.error(
        `Customer contact ${contact.id} has no platform contact ID`,
      );
      return;
    }

    const platformContact = platformContacts[0];

    // Update the metadata
    const newMetadata = cloneDeep(platformContact.metadata);
    if (get(newMetadata, 'sinks.slack.channels', [])) {
      newMetadata.sinks.slack.channels =
        newMetadata.sinks.slack.channels.filter(
          (c) => c.channelId !== channel.channelId,
        );
    }

    // Update the platform contact
    await this.thenaPlatformApiProvider.updateCustomerContact(
      installation,
      platformContact.id,
      { metadata: newMetadata },
    );

    if (existingCustomObjectRecordIds.length > 0) {
      // Get the custom object records
      const customObjectRecords =
        await this.thenaPlatformApiProvider.getCustomObjectRecordsByIds(
          installation,
          contactCustomObjectId,
          existingCustomObjectRecordIds,
        );

      // Prepare for custom object records deletion
      const customObjectRecordsToDelete: string[] = [];

      for (const customObjectRecord of customObjectRecords) {
        const slackChannelCFValue = customObjectRecord.customFieldValues.find(
          (c) => c.customFieldId === slackChannelIdCF,
        );

        if (slackChannelCFValue?.data[0].value === channel.channelId) {
          customObjectRecordsToDelete.push(customObjectRecord.id);
        }
      }

      // Delete the custom object records
      await Promise.all(
        customObjectRecordsToDelete.map((id) =>
          this.thenaPlatformApiProvider.deleteCustomObjectRecord(
            installation,
            contactCustomObjectId,
            id,
          ),
        ),
      );

      // Update the customer contact
      await this.customerContactRepository.update(contact.id, {
        platformDump: {
          customerContactId: platformContact.id,
          customObjectRecordIds: existingCustomObjectRecordIds.filter(
            (id) => !customObjectRecordsToDelete.includes(id),
          ),
        },
      });
    }
  }
}
