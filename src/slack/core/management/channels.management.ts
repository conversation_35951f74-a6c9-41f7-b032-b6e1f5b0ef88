import { Inject, Injectable } from '@nestjs/common';
import { Channels, Installations } from '../../../database/entities';
import { ChannelType } from '../../../database/entities/channels/channels.entity';
import { ILogger } from '../../../utils';
import { CUSTOM_LOGGER_TOKEN } from '../../../utils';
import { SlackWebAPIService } from '../../providers/slack-apis/slack-apis.service';

interface JoinChannelsOpts {
  channelType?: ChannelType;
}

@Injectable()
export class ChannelsManagementService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Slack Web API Service
    private readonly slackWebAPIService: SlackWebAPIService,
  ) {}

  public async joinChannels(
    installation: Installations,
    channels: Channels[],
    _opts: JoinChannelsOpts = {},
  ) {
    try {
      // If there are no channels to join, return
      if (!channels.length) {
        this.logger.warn('No channels to join');
        return;
      }

      this.logger.log(`Joining ${channels.length} channels`);

      // Note: Prefer using a for...of loop here; the reason being that each call to
      // Slack API using our service is scheduled, this prevents from concurrently
      // calling the Slack API and getting rate limited
      for (const channel of channels) {
        // Join the channel
        await this.slackWebAPIService.joinConversation(installation.botToken, {
          channel: channel.channelId,
        });
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error joining channels: ${error.message}`,
          error.stack,
        );
      } else {
        console.error('Error joining channels:', error);
      }
    }
  }

  public async disconnectChannels(
    installation: Installations,
    channels: Channels[],
  ) {
    try {
      // If there are no channels to disconnect, return
      if (!channels.length) {
        this.logger.warn('No channels to disconnect');
        return;
      }

      this.logger.log(`Disconnecting ${channels.length} channels`);

      // Note: Prefer using a for...of loop here; the reason being that each call to
      // Slack API using our service is scheduled, this prevents from concurrently
      // calling the Slack API and getting rate limited
      for (const channel of channels) {
        // Join the channel
        await this.slackWebAPIService.leaveConversation(installation.botToken, {
          channel: channel.channelId,
        });
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error disconnecting channels: ${error.message}`,
          error.stack,
        );
      } else {
        console.error('Error disconnecting channels:', error);
      }
    }
  }
}
