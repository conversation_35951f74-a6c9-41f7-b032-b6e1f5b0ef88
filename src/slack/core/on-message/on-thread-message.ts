import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { KnownEventFromType } from '@slack/bolt';
import { FileShareMessageEvent } from '@slack/types';
import { MessageElement } from '@slack/web-api/dist/types/response/ConversationsHistoryResponse';
import { DeepPartial, In, Repository } from 'typeorm';
import { TransactionService } from '../../../database/common';
import {
  Channels,
  CommentThreadMappings,
  CustomerContacts,
  Installations,
  SlackMessages,
  Users,
} from '../../../database/entities';
import { ChannelsRepository } from '../../../database/entities/channels/repositories/channels.repository';
import { CommentThreadMapsRepository } from '../../../database/entities/mappings/repositories/comment-thread-maps.repository';
import {
  GroupedSlackMessages,
  SlackTriageMessages,
} from '../../../database/entities/slack-messages';
import { GroupedSlackMessagesRepository } from '../../../database/entities/slack-messages/repositories/grouped-slack-messages.repository';
import { SlackMessagesRepository } from '../../../database/entities/slack-messages/repositories/slack-messages.repository';
import { SlackTriageMessagesRepository } from '../../../database/entities/slack-messages/repositories/slack-triage-messages.repository';
import { TeamsRepository } from '../../../database/entities/teams';
import { Person } from '../../../database/interfaces/person.interface';
import { PlatformApiUnavailableError } from '../../../external/provider/errors/platform-api.errors';
import { CreateNewComment } from '../../../external/provider/interfaces';
import { ThenaPlatformApiProvider } from '../../../external/provider/thena-platform-api.provider';
import { Ticket } from '../../../platform/interfaces';
import { CUSTOM_LOGGER_TOKEN, type ILogger } from '../../../utils';
import { enhanceUserMentions, getSlackMentions } from '../../../utils/common';
import { BaseSlackBlocksToHtml } from '../../../utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';
import { SlackEventMap } from '../../event-handlers';
import { SlackWebAPIService } from '../../providers/slack-apis/slack-apis.service';
import { ForMessageFactory } from '../factories/for-message.factory';
import { SettingsCore } from '../management';
import { CoreSlackMessage } from './core-slack-message';

const LOG_SPAN = 'OnThreadMessageHandler';

@Injectable()
export class OnThreadMessageHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,

    // Database Repositories
    private readonly channelsRepository: ChannelsRepository,
    private readonly slackTriageMessagesRepository: SlackTriageMessagesRepository,
    private readonly groupedSlackMessagesRepository: GroupedSlackMessagesRepository,
    private readonly slackMessagesRepository: SlackMessagesRepository,
    private readonly commentThreadMapsRepository: CommentThreadMapsRepository,
    private readonly platformTeamsRepository: TeamsRepository,

    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,

    @InjectRepository(CustomerContacts)
    private readonly customerContactsRepository: Repository<CustomerContacts>,

    // External API Providers
    private readonly platformApiProvider: ThenaPlatformApiProvider,
    private readonly slackWebAPIService: SlackWebAPIService,

    // Services
    private readonly transactionService: TransactionService,
    private readonly coreSlackMessage: CoreSlackMessage,
    private readonly settingsCore: SettingsCore,

    // Parsers
    private readonly baseSlackBlocksToHtml: BaseSlackBlocksToHtml,

    // Factories
    private readonly forMessageFactory: ForMessageFactory,
  ) {}

  async handle(e: SlackEventMap['message']) {
    // TypeGuard: Validate that the event has a context
    if (!('context' in e)) {
      throw new Error(`${LOG_SPAN} [handle] Context not found`);
    }

    // TypeGuard: Validate that the event has a context
    const { event, context } = e;
    const { installation } = context;

    // TypeGuard: Validate that the message has thread_ts [Thread Message]
    const threadMessage = 'text' in event && 'thread_ts' in event;

    // TypeGuard: Validate that the message has thread_ts [Thread Message]
    if (!threadMessage) {
      throw new Error(
        `${LOG_SPAN} [handle] Invalid handler called for message event!`,
      );
    }

    // Get the channel from the database
    const channel = await this.getChannel(installation, event.channel);
    if (!channel) {
      this.logger.debug(
        `${LOG_SPAN} [handle] Channel not found for thread_ts ${event.thread_ts} in channel ${event.channel}`,
      );

      return;
    }

    // Get the correct type of message from db
    let message: SlackMessages | SlackTriageMessages | GroupedSlackMessages;
    message = await this.getMessage(
      installation,
      channel,
      event.thread_ts,
      false,
    );

    // If the message is not found, check if it is grouped
    if (!message) {
      this.logger.debug(
        `${LOG_SPAN} [handle] Message not found for thread_ts ${event.thread_ts} in channel ${event.channel}`,
      );

      // Check if the message is grouped
      const groupedMessage =
        await this.groupedSlackMessagesRepository.findByCondition({
          where: {
            slackMessageTs: event.thread_ts,
            channel: { id: channel.id },
            installation: { id: installation.id },
            organization: { id: installation.organization.id },
          },
        });

      // If the message is grouped, set the message to the grouped message
      if (groupedMessage) {
        this.logger.debug(
          `${LOG_SPAN} [handle] Message is grouped with ${groupedMessage.platformTicketId}`,
        );

        message = groupedMessage;
      } else {
        this.logger.debug(
          `${LOG_SPAN} [handle] Message is not grouped with any ticket in channel ${channel.channelId}`,
        );
      }
    }

    // Get the bot mention status
    const { botMentioned } = getSlackMentions(installation, event.text);

    // Get the ticket id
    let ticketId = '';
    if (message instanceof GroupedSlackMessages) {
      ticketId = message.platformTicketId;
    } else if (message instanceof SlackTriageMessages) {
      ticketId = message.slackRequestMessage.platformTicketId;
    } else if (message instanceof SlackMessages) {
      ticketId = message.platformTicketId;
    }

    // Get the ticket details
    let ticketDetails: Ticket | undefined;
    let platformTeam = null;

    // Only attempt to get ticket details if we have a ticketId
    if (ticketId) {
      try {
        ticketDetails = await this.platformApiProvider.getTicket(
          installation,
          ticketId,
        );

        // Get the platform team
        platformTeam = await this.platformTeamsRepository.findByCondition({
          where: {
            uid: ticketDetails.teamId,
            installation: { id: installation.id },
            organization: { id: installation.organization.id },
          },
        });
      } catch (error) {
        this.logger.error(
          `Failed to get ticket details for ticket ${ticketId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );

        // If the platform API is unavailable, log and return early
        if (error instanceof PlatformApiUnavailableError) {
          this.logger.warn(
            `Platform API service unavailable, skipping thread message processing for ticket ${ticketId}`,
          );
          return;
        }

        // Re-throw other errors
        throw error;
      }

      // Bail out if the ticket could not be retrieved
      if (!ticketDetails) {
        this.logger.warn(
          `Ticket ${ticketId} not found – skipping thread-message handling.`,
        );
        return;
      }
    }

    // Get the user from the database
    const user = await this.getUser(installation, event.user);
    if (!user) {
      this.logger.debug(
        `${LOG_SPAN} [handle] User not found for thread_ts ${event.thread_ts} in channel ${event.channel}`,
      );

      return;
    }

    // If the bot is mentioned and the platform team is set, check if the setting is enabled
    if (botMentioned && platformTeam && !message) {
      const createTicketOnBotMention = await this.settingsCore.getValue(
        'thena_bot_tagging_enabled',
        {
          recursivelyLookup: false,
          workspace: installation,
          platformTeam,
        },
      );

      this.logger.log(`createTicketOnBotMention: ${createTicketOnBotMention}`);

      // If the setting is not enabled, return
      if (createTicketOnBotMention) {
        this.logger.log(
          `${LOG_SPAN} [handle] createTicketOnBotMention is enabled for platform team ${platformTeam.uid}`,
        );

        // Handle the bot mention
        const { ticket } = await this.handleBotMention(
          installation,
          channel,
          user,
          event,
        );

        // If the ticket is created, return
        if (ticket) {
          this.logger.log(
            `${LOG_SPAN} [handle] Ticket created for bot mention in thread_ts ${event.thread_ts} in channel ${event.channel}`,
          );

          return;
        }
      }
    }

    // This is a possible new ticket as a thread message
    if (!message) {
      this.logger.debug(
        `${LOG_SPAN} [handle] Message is not found for thread_ts ${event.thread_ts} in channel ${event.channel}`,
      );

      // Sync the new ticket
      await this.syncPossibleNewTicket(installation, event);
      return;
    }

    // Send the comment to the platform
    await this.sendCommentToPlatform(installation, event, message, user);
  }

  private async handleBotMention(
    installation: Installations,
    channel: Channels,
    user: Person,
    event: KnownEventFromType<'message'>,
  ) {
    // Get the platform team from the channel
    const platformTeam =
      channel.platformTeamsToChannelMappings?.[0]?.platformTeam;

    // If the platform team is not found, return
    if (!platformTeam) {
      this.logger.debug(
        `${LOG_SPAN} [handle] Platform team not found for channel ${channel.channelId}`,
      );

      return { ticket: null };
    }

    // Create a new ticket
    const { ticket } = await this.createNewTicket(
      installation,
      channel,
      user,
      event,
    );

    return { ticket };
  }

  private async syncPossibleNewTicket(
    installation: Installations,
    event: KnownEventFromType<'message'>,
  ) {
    if (!('text' in event)) {
      throw new Error('Invalid event data');
    }

    // Get the channel, user, and isGrouped from the event
    const { channel, user, isGrouped, isCustomer } =
      await this.coreSlackMessage.checkAndGetSlackMessageDetails(
        installation,
        event,
      );

    // If the message is grouped with an existing conversation, we don't need to create a ticket
    if (isGrouped) {
      this.logger.debug(
        `${LOG_SPAN} [syncPossibleNewTicket] Message is grouped with an existing conversation!`,
      );
      return;
    }

    // If the user is not a customer, we don't need to create a ticket
    if (!isCustomer) {
      this.logger.debug(
        `${LOG_SPAN} [syncPossibleNewTicket] Message is not a customer!`,
      );
      return;
    }

    // Check if the bot is mentioned
    const platformTeam =
      channel.platformTeamsToChannelMappings?.[0]?.platformTeam;

    this.logger.log(
      `${LOG_SPAN} [syncPossibleNewTicket] Checking if the message is a valid ticket for platform team ${platformTeam?.uid}`,
    );

    // Check if the message is a valid ticket
    const isValidTicket = await this.coreSlackMessage.aiCheckIsValidTicket(
      event.text,
      platformTeam,
      installation,
    );

    // If the message is not a valid ticket, we don't need to create a ticket
    if (!isValidTicket) {
      this.logger.debug(
        `${LOG_SPAN} [syncPossibleNewTicket] Message is not a valid ticket!`,
      );
      return;
    }

    // Create a new ticket
    await this.createNewTicket(installation, channel, user, event);
  }

  private async createNewTicket(
    installation: Installations,
    channel: Channels,
    customer: Person,
    event: KnownEventFromType<'message'>,
  ) {
    // TypeGuard: Validate that the message has text to fetch the union type [[Possibly] A Top Level Message]
    const topLevelMessage = 'text' in event;
    if (!topLevelMessage) {
      throw new Error(
        `${LOG_SPAN} [createNewTicket] Message is not a top level message`,
      );
    }

    // TypeGuard: Validate that the message has a team
    if (!('team' in event)) {
      throw new Error(
        `${LOG_SPAN} [createNewTicket] Slack workspace not attached to the event message!`,
      );
    }

    // TypeGuard: Validate that the event has a thread_ts
    if (!('thread_ts' in event)) {
      throw new Error(
        `${LOG_SPAN} [syncSlackThread] Thread timestamp not found`,
      );
    }

    // Get the files from the event
    let files: FileShareMessageEvent['files'] = [];
    if ('files' in event) {
      files = event.files;
    }

    // Get the slack thread response
    const slackThreadResponse =
      await this.slackWebAPIService.getConversationHistory(
        installation.botToken,
        {
          channel: event.channel,
          oldest: event.thread_ts,
          inclusive: true,
          limit: 1,
        },
      );

    // If the slack thread response is not ok, throw an error
    if (!slackThreadResponse.ok) {
      this.logger.error(
        `${LOG_SPAN} [syncSlackThread] Failed to get slack thread response for thread_ts ${event.thread_ts} in channel ${event.channel}, ${slackThreadResponse.error}`,
      );
      throw new Error(slackThreadResponse.error);
    }

    // Get the first message from the thread
    const firstMessage = slackThreadResponse.messages[0];

    // Get the replies to the first message
    const slackThreadRepliesResponse =
      await this.slackWebAPIService.getConversationReplies(
        installation.botToken,
        {
          channel: event.channel,
          ts: firstMessage.ts,
        },
      );

    // If the slack thread replies response is not ok, throw an error
    if (!slackThreadRepliesResponse.ok) {
      this.logger.error(
        `${LOG_SPAN} [syncSlackThread] Failed to get slack thread replies response for thread_ts ${event.thread_ts} in channel ${event.channel}, ${slackThreadRepliesResponse.error}`,
      );
      throw new Error(slackThreadRepliesResponse.error);
    }

    const slackThreadReplies = slackThreadRepliesResponse.messages;

    // Get the mentioned users
    const { mentionedUsers } = getSlackMentions(
      installation,
      firstMessage.text,
    );

    const mentionedSlackUsers = await this.usersRepository.find({
      where: {
        slackId: In(mentionedUsers),
        installation: { id: installation.id },
      },
    });

    // Enhance the user mentions
    const enhancedText = enhanceUserMentions(
      firstMessage.text,
      mentionedSlackUsers,
    );

    // Create a new comment payload
    const commentPayload: Omit<CreateNewComment, 'ticketId'> = {
      content: enhancedText,
      channelId: event.channel,
      files,
      metadata: {
        ignoreSelf: true,
        ts: firstMessage.ts,
        threadTs: firstMessage.ts,
        slackUserId: firstMessage.user, // Add Slack user ID for X-User-ID header
      },
      impersonatedUserAvatar: customer.getUserAvatar(),
      impersonatedUserEmail: customer.slackProfileEmail,
      impersonatedUserName: customer.displayName || customer.realName,
    };

    // If the customer is a customer contact, add the customer email to the comment payload
    if (customer instanceof CustomerContacts) {
      commentPayload.customerEmail = customer.slackProfileEmail;
    }

    // Perform updates in a transaction
    return await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Create a new ticket with the comment
        const {
          ticket,
          event_ts: eventTs,
          threadTs,
          messagePermalink,
        } = await this.coreSlackMessage.createTicketForTeam(
          installation,
          channel,
          customer,
          event,
          commentPayload,
        );

        // Keep the common data required for the factory
        const __common = { ticket, channel, customer, installation };

        // If the parent comment is not created, throw an error
        if (!ticket.comment?.id) {
          this.logger.error(
            `${LOG_SPAN} [syncSlackThread] Failed to create a new comment on the platform for thread_ts ${event.thread_ts} in channel ${event.channel}`,
          );

          throw new Error('Failed to create a new comment on the platform');
        }

        // Construct the slack message
        const sm = this.forMessageFactory.constructSlackMessage({
          ...__common,
          slackDetails: {
            eventTs,
            messagePermalink: messagePermalink.permalink,
            threadTs,
            ts: event.ts,
            user: event.user,
          },
        });

        // Save the slack message
        await this.slackMessagesRepository.saveWithTxn(txnContext, {
          ...sm,
          platformCommentId: ticket.comment.id,
          platformTicketId: ticket.id,
        });

        // Get all the users from the slack thread
        const allUsersMap = await this.getAllUsersFromSlackThread(
          installation,
          slackThreadReplies,
        );

        // Create a new child comment for each reply
        const childComments = [];
        for (const reply of slackThreadReplies) {
          if (reply.ts === firstMessage.ts) {
            continue;
          }

          // Enhance the user mentions
          const enhancedChildText = enhanceUserMentions(
            reply.text,
            mentionedSlackUsers,
          );

          // Create a new child comment payload
          const childCommentPayload: CreateNewComment = {
            files: [],
            content: enhancedChildText,
            ticketId: ticket.id,
            channelId: event.channel,
            metadata: {
              ignoreSelf: true,
              ts: reply.ts,
              threadTs: reply.thread_ts ?? reply.ts,
              slackUserId: reply.user, // Add Slack user ID for X-User-ID header
            },
            impersonatedUserAvatar: allUsersMap
              .get(reply.user)
              ?.getUserAvatar(),
            impersonatedUserEmail: allUsersMap.get(reply.user)
              ?.slackProfileEmail,
            impersonatedUserName:
              allUsersMap.get(reply.user)?.displayName ||
              allUsersMap.get(reply.user)?.realName,
            parentCommentId: ticket.comment.id,
          };

          // If the customer is a customer contact, add the customer email to the comment payload
          if (allUsersMap.get(reply.user) instanceof CustomerContacts) {
            childCommentPayload.customerEmail = allUsersMap.get(
              reply.user,
            )?.slackProfileEmail;
          }

          // Create a new child comment on the platform
          const commentCreated =
            await this.platformApiProvider.createNewComment(
              installation,
              childCommentPayload,
            );

          childComments.push(commentCreated);
        }

        // Create all the child comments
        const commentMappings: Array<DeepPartial<CommentThreadMappings>> =
          childComments.map((comment) => ({
            slackMessageId: comment.data.id,
            platformCommentTicketId: ticket.id,
            platformCommentThreadId: ticket.comment.id,
            slackChannelId: event.channel,
            slackThreadId: event.thread_ts,
            installation: { id: installation.id },
            organization: { id: installation.organization.id },
          }));

        // Save the comment mappings
        await this.commentThreadMapsRepository.saveManyWithTxn(
          txnContext,
          commentMappings,
        );

        // Return the ticket
        return { ticket };
      },
    );
  }

  private async getAllUsersFromSlackThread(
    installation: Installations,
    threads: MessageElement[],
  ) {
    const allUsersMap = new Map<string, Person>();
    const usersToFetch = new Set<string>();

    // Get all the users from the slack thread
    for (const thread of threads) {
      if (thread.user) {
        usersToFetch.add(thread.user);
      }
    }

    // Get the users from the database
    const [customerContacts, users] = await Promise.all([
      // Get the customer contacts from the database
      this.customerContactsRepository.find({
        where: {
          slackId: In(Array.from(usersToFetch)),
          installation: { id: installation.id },
        },
      }),

      // Get the users from the database
      this.usersRepository.find({
        where: {
          slackId: In(Array.from(usersToFetch)),
          installation: { id: installation.id },
          organization: { id: installation.organization.id },
        },
      }),
    ]);

    // Add the users to the map
    for (const user of users) {
      allUsersMap.set(user.slackId, user);
    }

    // Add the customer contacts to the map
    for (const customerContact of customerContacts) {
      allUsersMap.set(customerContact.slackId, customerContact);
    }

    return allUsersMap;
  }

  /**
   * Get the channel from the database
   * @param installation Installation Entity
   * @param channel Channel ID from Slack
   * @returns Channel Entity
   */
  private async getChannel(installation: Installations, channel: string) {
    // Get the channel from the database
    const foundChannel = await this.channelsRepository.findByCondition({
      where: {
        installation: { id: installation.id },
        channelId: channel,
      },
    });

    return foundChannel;
  }

  /**
   * Get the message from the database
   * @param installation Installation Entity
   * @param channel Channel Entity
   * @param threadTs Thread Timestamp
   * @returns Message Entity
   */
  private async getMessage(
    installation: Installations,
    channel: Channels,
    threadTs: string,
    throwOnError = true,
  ) {
    const __commonLookup = {
      channel: { id: channel.id },
      installation: { id: installation.id },
      organization: { id: installation.organization.id },
    };

    // Get the message from the database
    let message: SlackMessages | SlackTriageMessages;

    // Check if the message is on a customer channel
    // First, search by slackMessageTs only
    message = await this.slackMessagesRepository.findByCondition({
      where: {
        slackMessageTs: threadTs,
        ...__commonLookup,
      },
      order: {
        id: 'ASC',
      },
      relations: { channel: true },
    });

    // If no message found, then search by slackMessageThreadTs
    if (!message) {
      message = await this.slackMessagesRepository.findByCondition({
        where: {
          slackMessageThreadTs: threadTs,
          ...__commonLookup,
        },
        order: {
          id: 'ASC',
        },
        relations: { channel: true },
      });
    }

    // If the message is not found, check if it is on a triage channel
    if (!message) {
      this.logger.debug(
        `${LOG_SPAN} [getMessage] Message not found for thread_ts ${threadTs} in channel ${channel.channelId}`,
      );

      message = await this.slackTriageMessagesRepository.findByCondition({
        where: {
          slackMessageTs: threadTs,
          ...__commonLookup,
        },
        relations: { slackRequestMessage: true, channel: true },
      });
    }

    // If the message is still not found, throw an error
    if (!message && throwOnError) {
      throw new Error(
        `${LOG_SPAN} [getMessage] Message not found for thread_ts ${threadTs} in channel ${channel.channelId}`,
      );
    }

    return message;
  }

  /**
   * Get the user from the database
   * @param installation Installation Entity
   * @param userId User ID from Slack
   * @returns User Entity
   */
  private async getUser(installation: Installations, userId: string) {
    // Get the user and customer contact from the database
    const [userSettled, customerContactSettled] = await Promise.allSettled([
      // Get the user from the database
      this.usersRepository.findOne({
        where: {
          slackId: userId,
          installation: { id: installation.id },
          organization: { id: installation.organization.id },
        },
      }),

      // Get the customer contact from the database
      this.customerContactsRepository.findOne({
        where: { slackId: userId, installation: { id: installation.id } },
      }),
    ]);

    const user = userSettled.status === 'fulfilled' ? userSettled.value : null;
    const customerContact =
      customerContactSettled.status === 'fulfilled'
        ? customerContactSettled.value
        : null;

    // If the user and customer contact are both found, throw an error
    if (user?.id && customerContact?.id) {
      this.logger.error(
        `${LOG_SPAN} [getUser] User and customer contact both found for user ${userId} in installation ${installation.id}`,
      );

      throw new Error('Something went wrong!');
    }

    return user ?? customerContact;
  }

  /**
   * Send the comment to the platform
   * @param installation Installation Entity
   * @param data Event Data
   * @param message Message Entity
   */
  private async sendCommentToPlatform(
    installation: Installations,
    data: SlackEventMap['message']['event'],
    message: SlackMessages | SlackTriageMessages | GroupedSlackMessages,
    user: Person,
  ) {
    // TypeGuard: Validate that the event has a text
    if (!('text' in data)) {
      throw new Error(`${LOG_SPAN} [sendCommentToPlatform] Invalid event data`);
    }

    // Get the files from the event
    let files: FileShareMessageEvent['files'] = [];
    if ('files' in data) {
      files = data.files;
    }

    let threadTs: string | undefined;
    if ('thread_ts' in data) {
      threadTs = data.thread_ts;
    } else if ('ts' in data) {
      threadTs = data.ts;
    }

    let htmlContent: string | undefined;
    if ('blocks' in data) {
      // Initialize the converter with the blocks and installation
      this.baseSlackBlocksToHtml.initialize(data.blocks, installation);

      // Convert blocks to HTML content
      htmlContent = await this.baseSlackBlocksToHtml.convert();
    }

    // Get the mentioned users
    const { mentionedUsers } = getSlackMentions(installation, data.text);

    // Get the mentioned slack users
    const mentionedSlackUsers = await this.usersRepository.find({
      where: {
        slackId: In(mentionedUsers),
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      },
    });

    // Enhance the user mentions
    const _enhancedText = enhanceUserMentions(data.text, mentionedSlackUsers);

    // Get the user ID from the event
    const slackUserId = 'user' in data ? data.user : undefined;

    let commentPayload: CreateNewComment;
    if (message instanceof SlackMessages) {
      commentPayload = {
        content: _enhancedText,
        files,
        htmlContent,
        commentVisibility: 'public',
        channelId: data.channel,
        metadata: {
          ignoreSelf: true,
          ts: data.ts,
          threadTs,
          slackUserId, // Add Slack user ID for X-User-ID header
        },
        ticketId: message.platformTicketId,
        parentCommentId: message.platformCommentId,
        impersonatedUserAvatar: user.getUserAvatar(),
        impersonatedUserEmail: user.slackProfileEmail,
        impersonatedUserName: user.displayName || user.realName,
      };
    } else if (message instanceof SlackTriageMessages) {
      commentPayload = {
        content: _enhancedText,
        files,
        htmlContent,
        commentVisibility: 'private',
        channelId: data.channel,
        metadata: {
          ignoreSelf: true,
          ts: data.ts,
          threadTs,
          slackUserId, // Add Slack user ID for X-User-ID header
        },
        parentCommentId: message.platformThreadId,
        ticketId: message.slackRequestMessage.platformTicketId,
        impersonatedUserAvatar: user.getUserAvatar(),
        impersonatedUserEmail: user.slackProfileEmail,
        impersonatedUserName: user.displayName || user.realName,
      };
    } else if (message instanceof GroupedSlackMessages) {
      commentPayload = {
        content: _enhancedText,
        files,
        htmlContent,
        commentVisibility: 'public',
        channelId: data.channel,
        metadata: {
          ignoreSelf: true,
          ts: data.ts,
          threadTs,
          slackUserId, // Add Slack user ID for X-User-ID header
        },
        ticketId: message.platformTicketId,
        parentCommentId: message.parentCommentId,
        impersonatedUserAvatar: user.getUserAvatar(),
        impersonatedUserEmail: user.slackProfileEmail,
        impersonatedUserName: user.displayName || user.realName,
      };
    }

    // If the user is a customer contact, add the customer email to the comment payload
    if (user instanceof CustomerContacts) {
      commentPayload.customerEmail = user.slackProfileEmail;
    }

    // Send the comment to the platform
    const platformComment = await this.platformApiProvider.createNewComment(
      installation,
      commentPayload,
    );

    // Save the comment ID and ticket ID to the database
    if (platformComment?.data?.id) {
      let ticketId: string;
      if (message instanceof SlackMessages) {
        ticketId = message.platformTicketId;
      } else if (message instanceof SlackTriageMessages) {
        ticketId = message.slackRequestMessage.platformTicketId;
      } else if (message instanceof GroupedSlackMessages) {
        ticketId = message.platformTicketId;
      }
      await this.slackMessagesRepository.save({
        slackMessageTs: data.ts,
        slackMessageThreadTs: threadTs,
        platformCommentId: platformComment.data.id,
        platformTicketId: ticketId,
        channel: { id: message.channel.id },
        installation: { id: installation.id },
        organization: { id: installation.organization.id },
      });
    }
  }
}
