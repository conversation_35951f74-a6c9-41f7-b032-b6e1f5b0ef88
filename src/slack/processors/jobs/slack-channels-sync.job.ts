import { Inject, Injectable } from '@nestjs/common';
import { Channel } from '@slack/web-api/dist/types/response/ConversationsListResponse';
import { DeepPartial } from 'typeorm';
import { Channels, Installations } from '../../../database/entities';
import { ChannelType } from '../../../database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../database/entities/channels/repositories/channels.repository';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../utils/logger';
import { SlackWebAPIService } from '../../providers/slack-apis/slack-apis.service';

/**
 * The maximum number of channels to sync in a single batch, this is the limit for the Slack's WebAPI
 * @see https://api.slack.com/methods/conversations.list#arg_limit
 */
const MAX_CHANNELS_TO_SYNC = 100;

@Injectable()
export class SlackChannelsSyncJob {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN)
    private readonly logger: ILogger,
    private readonly channelsRepository: ChannelsRepository,

    // Slack Web API Service
    private readonly slackWebAPIService: SlackWebAPIService,
  ) {}

  /**
   * @description
   * Executes the Slack channels sync job
   * @param installation The installation to sync the channels for
   */
  async execute(installation: Installations) {
    try {
      let hasMore = true;
      let cursor: string | undefined = '';
      let channelCount = 0;

      // Checking for existing channels in db
      const existingChannels = await this.channelsRepository.findAll({
        where: {
          installation: { id: installation.id },
          organization: { id: installation.organization.id },
          slackDeletedAt: null,
        },
      });

      const existingChannelIds =
        existingChannels.length > 0
          ? existingChannels.map((existingChannel) => existingChannel.channelId)
          : [];
      // While there are more channels to sync, we will continue to sync them
      while (hasMore) {
        // Get the channels from the Slack's WebAPI
        const conversationsResponse =
          await this.slackWebAPIService.listConversations(
            installation.botToken,
            {
              limit: MAX_CHANNELS_TO_SYNC,
              exclude_archived: true,
              cursor,
            },
          );

        // If the response is not ok, we will break the loop
        if (!conversationsResponse.ok) {
          this.logger.error(
            `Slack channels sync failed, error: ${conversationsResponse.error}`,
          );

          // TODO: We should retry the request
          continue;
        }

        // Get the channels and the response metadata
        let { channels, response_metadata } = conversationsResponse;

        // Get the external shared channels
        const externalSharedChannels = channels.filter((c) => c.is_ext_shared);

        await Promise.all(
          externalSharedChannels.map(async (c) => {
            const conversationInfo =
              await this.slackWebAPIService.getConversationInfo(
                installation.botToken,
                {
                  channel: c.id,
                },
              );

            if (conversationInfo.ok) {
              const {
                channel: { id, shared_team_ids },
              } = conversationInfo;

              const foundChannel = channels.findIndex((c) => c.id === id);

              if (foundChannel !== -1) {
                channels[foundChannel].shared_team_ids = shared_team_ids;
              }
            }
          }),
        );

        if (existingChannelIds.length > 0) {
          channels = channels.filter(
            (channel) => !existingChannelIds.includes(channel.id),
          );
        }

        // Bulk write[upsert]/commit the channels to the database
        await this.writeChannels(channels, installation);

        // Increment the channel count
        channelCount += channels.length;

        // If there is no next cursor, we will break the loop
        if (!response_metadata.next_cursor) {
          hasMore = false;
        } else {
          // Otherwise, we will set the cursor to the next cursor
          cursor = response_metadata.next_cursor;
          hasMore = true;
        }
      }

      if (!hasMore) {
        this.logger.log(
          `Slack channels sync completed for installation ${installation.id}, ${channelCount} channels synced`,
        );
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Slack channels sync failed, error: ${error.message}`,
          error.stack,
        );
      }
    }
  }

  /**
   * @description
   * Writes the channels to the database
   * @param channels The channels to write
   * @param installation The installation to write the channels for
   */
  private async writeChannels(
    channels: Channel[],
    installation: Installations,
  ) {
    // Construct the channels to write, note this should be a deep partial of the Channels entity
    const channelsToWrite: Array<DeepPartial<Channels>> = channels.map((c) =>
      this.constructChannel(c, installation),
    );

    // Upsert the channels into the database
    await this.channelsRepository.upsert(channelsToWrite, {
      conflictPaths: ['channelId', 'installation'],
      skipUpdateIfNoValuesChanged: true,
    });
  }

  /**
   * @description
   * Constructs a channel object to be written to the database
   * @param channel The channel to construct
   * @param installation The installation to construct the channel for
   * @returns The constructed channel
   */
  private constructChannel(
    channel: Channel,
    installation: Installations,
  ): DeepPartial<Channels> {
    // Ensure shared_team_ids is properly extracted, using an empty array as fallback
    const sharedTeamIds = channel.shared_team_ids || [];

    // Add additional logging to track this data
    if (channel.is_ext_shared) {
      this.logger.log(
        `Channel ${channel.id} (${channel.name}) is externally shared. ` +
          `Shared team IDs: ${sharedTeamIds.length ? sharedTeamIds.join(', ') : 'none'}`,
      );
    }

    return {
      name: channel.name || channel.name_normalized,
      channelDump: channel as Record<string, any>,
      channelId: channel.id,
      slackCreatedAt: channel.created.toString(),
      isBotActive: false,
      isBotJoined: false,
      channelType: ChannelType.NOT_SETUP,
      isArchived: channel.is_archived,
      isPrivate: channel.is_private,
      isShared: channel.is_ext_shared,
      sharedTeamIds: sharedTeamIds,
      installation: { id: installation.id },
      organization: { id: installation.organization.id },
    };
  }
}
