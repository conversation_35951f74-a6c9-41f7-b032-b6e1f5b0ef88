import {
  BadRequestException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { In, Repository } from 'typeorm';
import { BotCtx } from '../../auth/interfaces/context.interface';
import { ApiResponse } from '../../common/interfaces/api-response.interface';
import {
  ChannelType,
  Channels,
} from '../../database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../database/entities/channels/repositories/channels.repository';
import { TeamTriageRuleMappingRepository } from '../../database/entities/mappings/repositories/teams-triage-mappings.repository';
import { TeamTriageRuleMapping } from '../../database/entities/mappings/teams-triage-mappings.entity';
import { TeamsRepository } from '../../database/entities/teams';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import {
  CreateTriageRuleDto,
  TriageConditionDto,
  TriageRulesDto,
  UpdateTriageRuleDto,
} from '../dtos/triage-rule.dto';
import { SlackWebAPIService } from '../providers/slack-apis/slack-apis.service';
import { TriageRuleEvaluatorService } from './triage-rule-evaluator.service';
import { InjectRepository } from '@nestjs/typeorm';
import { PlatformTeamsToChannelMappings } from '../../database/entities';
import { TeamRelationshipType } from '../../database/entities/mappings';

@Injectable()
export class TriageRulesService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly triageRuleRepo: TeamTriageRuleMappingRepository,
    private readonly platformTeamsRepo: TeamsRepository,
    private readonly channelsRepo: ChannelsRepository,
    private readonly slackApiService: SlackWebAPIService,
    private readonly ruleEvaluator: TriageRuleEvaluatorService,
    @InjectRepository(PlatformTeamsToChannelMappings)
    private readonly platformTeamsToChannelMappingsRepository: Repository<PlatformTeamsToChannelMappings>,
  ) {}

  private async getPlatformTeam(teamExternalId: string, botCtx: BotCtx) {
    const platformTeam = await this.platformTeamsRepo.findByCondition({
      where: {
        uid: teamExternalId,
        organization: { id: botCtx.organization.id },
      },
    });

    if (!platformTeam) {
      throw new NotFoundException(
        `Platform team not found for ID ${teamExternalId}`,
      );
    }

    return platformTeam;
  }

  private async validateRules(rules: TriageRulesDto, botCtx: BotCtx) {
    const validateConditions = async (conditions: TriageConditionDto[]) => {
      for (const condition of conditions) {
        // Check for common mistakes like using simple field names for object fields
        const commonObjectFields = [
          'priority',
          'status',
          'type',
          'assignedAgent',
          'team',
        ];
        if (
          commonObjectFields.includes(condition.field) &&
          !condition.field.includes('.')
        ) {
          throw new BadRequestException(
            `Field "${condition.field}" is an object and requires a property accessor, e.g. "${condition.field}.name"`,
          );
        }

        const isValid = await this.ruleEvaluator.validateField(
          condition.category,
          condition.field,
          botCtx.installation,
        );

        if (!isValid) {
          throw new BadRequestException(
            `Invalid field "${condition.field}" for category "${condition.category}"`,
          );
        }
      }
    };

    if (rules.AND) {
      await validateConditions(rules.AND);
    }

    if (rules.OR) {
      await validateConditions(rules.OR);
    }
  }

  private async validateChannels(
    channelIds: string[],
    botCtx: BotCtx,
  ): Promise<Channels[]> {
    const channels = await this.channelsRepo.findAll({
      where: {
        channelId: In(channelIds),
        installation: { id: botCtx.installation.id },
      },
    });

    const foundChannelIds = channels.map((channel) => channel.channelId);
    const invalidChannelIds = channelIds.filter(
      (id) => !foundChannelIds.includes(id),
    );

    if (invalidChannelIds.length > 0) {
      throw new BadRequestException(
        `Invalid channel IDs: ${invalidChannelIds.join(', ')}`,
      );
    }

    return channels;
  }

  private async setupTriageChannels(
    channelIds: string[],
    botCtx: BotCtx,
  ): Promise<void> {
    try {
      // Update channel types to TRIAGE in database
      await this.channelsRepo.update(
        {
          channelId: In(channelIds),
          installation: { id: botCtx.installation.id },
        },
        { channelType: ChannelType.TRIAGE_CHANNEL },
      );

      const channelDetails = await this.channelsRepo.findAll({
        where: {
          channelId: In(channelIds),
          installation: { id: botCtx.installation.id },
        },
      });

      // Make bot join the channels if not already a member
      for (const channel of channelDetails) {
        try {
          if (channel.isBotActive) {
            continue;
          }
          await this.slackApiService.joinConversation(
            botCtx.installation.botToken,
            { channel: channel.channelId },
          );
        } catch (error) {
          const slackError = error as { data?: { error?: string } };
          if (slackError.data?.error !== 'already_in_channel') {
            throw error;
          }
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Failed to setup triage channels: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }

      throw new BadRequestException('Failed to setup triage channels');
    }
  }

  private async cleanupTriageChannel(
    channelId: string,
    botCtx: BotCtx,
  ): Promise<void> {
    try {
      // Check if channel is used in any other triage rules
      const usedInOtherRules = await this.triageRuleRepo.findOne({
        where: {
          installation: { id: botCtx.installation.id },
          triageChannels: { channelId },
        },
      });

      // Only update type if channel is not used in other rules
      if (!usedInOtherRules) {
        await this.channelsRepo.update(
          {
            channelId,
            installation: { id: botCtx.installation.id },
          },
          {
            channelType: ChannelType.NOT_CONFIGURED,
          },
        );
      }
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Failed to cleanup triage channel: ${error.message}`,
          error.stack,
        );
      } else {
        console.error(error);
      }

      throw new BadRequestException('Failed to cleanup triage channel');
    }
  }

  async createRule(
    teamExternalId: string,
    createDto: CreateTriageRuleDto,
    botCtx: BotCtx,
  ): Promise<ApiResponse<TeamTriageRuleMapping>> {
    try {
      const channels = await this.validateChannels(
        createDto.channelIds,
        botCtx,
      );

      const { isDefault, triageRules, channelIds } = createDto;

      // Checking if default rule already exists
      // If default rule exists and no triage rules are provided, delete the default rule
      if (!triageRules && isDefault) {
        const defaultChannel = await this.triageRuleRepo.findOne({
          where: {
            platformTeam: { uid: teamExternalId },
            installation: { id: botCtx.installation.id },
            organization: { id: botCtx.organization.id },
            isDefault: true,
          },
        });

        if (defaultChannel) {
          await this.deleteRule(defaultChannel.id, botCtx);
        }
      }

      // Check if default rule already exists
      const defaultRule = await this.triageRuleRepo.findOne({
        where: {
          platformTeam: { uid: teamExternalId },
          triageChannels: { channelId: In(createDto.channelIds) },
          installation: { id: botCtx.installation.id },
          organization: { id: botCtx.organization.id },
          isDefault: true,
        },
      });

      if (defaultRule) {
        throw new BadRequestException('Default triage rule already exists');
      }

      // Validate default rule constraints
      if (createDto.isDefault) {
        if (createDto.triageRules) {
          throw new BadRequestException(
            'Default triage rules should not have conditions',
          );
        }
        if (createDto.channelIds.length !== 1) {
          throw new BadRequestException(
            'Default triage rules must have exactly one channel',
          );
        }
      } else {
        // Validate rules for non-default triage mappings
        if (!createDto.triageRules) {
          throw new BadRequestException(
            'Triage rules are required for non-default mappings',
          );
        }

        await this.validateRules(createDto.triageRules, botCtx);
      }

      const platformTeam = await this.getPlatformTeam(teamExternalId, botCtx);

      // Setup channels before creating rule
      await this.setupTriageChannels(createDto.channelIds, botCtx);

      const rule = this.triageRuleRepo.create({
        platformTeam: { id: platformTeam.id },
        installation: { id: botCtx.installation.id },
        organization: { id: botCtx.organization.id },
        isEnabled: true,
        isDefault: createDto.isDefault ?? false,
        triageRules: createDto.isDefault ? null : createDto.triageRules,
        triageChannels: channels.map((channel) => ({ id: channel.id })),
      });

      const savedRule = rule.isDefault
        ? await this.triageRuleRepo.setDefaultMapping(
            platformTeam.id,
            botCtx,
            channels[0],
          )
        : await this.triageRuleRepo.save(rule);

      //Getting all existing channels for the given team , installation and organization
      const existingChannels =
        await this.platformTeamsToChannelMappingsRepository.find({
          where: {
            platformTeam: { id: platformTeam.id },
            installation: { id: botCtx.installation.id },
            organization: { id: botCtx.organization.id },
          },
          relations: {
            channel: true,
          },
        });

      //Checking existingChannels length
      //Logic for not duplicating entries
      if (existingChannels.length > 0) {
        //Extracting channel Ids of existing Channels
        const existingChannelIds = existingChannels.map(
          (existingChannel) => existingChannel.channel.id,
        );

        //Filtering channel Ids that doesn't exist in platformTeamsToChannelMappingsRepository ( existing channels)
        const nonExistingChannels = channels
          .map((channel) => channel.id)
          .filter((channel) => !existingChannelIds.includes(channel));

        //Creating promises of  entry in platformTeamsToChannelMappings
        const mappingPromises = nonExistingChannels.map((channel) => {
          const mapping = this.platformTeamsToChannelMappingsRepository.create({
            relationshipType: TeamRelationshipType.PRIMARY,
            platformTeam: { id: platformTeam.id },
            channel: { id: channel },
            installation: { id: botCtx.installation.id },
            organization: { id: botCtx.organization.id },
          });
          return this.platformTeamsToChannelMappingsRepository.save(mapping);
        });

        //Executing in parallel
        await Promise.all(mappingPromises);
      }
      return {
        ok: true,
        data: savedRule,
      };
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error creating triage rule: ${error.message}`,
          error.stack,
        );
      }
      throw error;
    }
  }

  async getTeamRules(
    teamExternalId: string,
    botCtx: BotCtx,
  ): Promise<ApiResponse<TeamTriageRuleMapping[]>> {
    const platformTeam = await this.getPlatformTeam(teamExternalId, botCtx);
    const rules = await this.triageRuleRepo.findTeamMappings(
      platformTeam.id,
      botCtx,
    );

    return {
      ok: true,
      data: rules,
    };
  }

  async updateRule(
    ruleId: string,
    updateDto: UpdateTriageRuleDto,
    botCtx: BotCtx,
  ): Promise<ApiResponse<TeamTriageRuleMapping>> {
    const rule = await this.triageRuleRepo.findOne({
      where: {
        id: ruleId,
        installation: { id: botCtx.installation.id },
        organization: { id: botCtx.organization.id },
      },
      relations: ['triageChannels'],
    });

    if (!rule) {
      throw new NotFoundException('Triage rule not found');
    }

    if (updateDto.channelIds) {
      const channels = await this.validateChannels(
        updateDto.channelIds,
        botCtx,
      );

      // If it's a default rule, ensure only one channel
      if (rule.isDefault && updateDto.channelIds.length !== 1) {
        throw new BadRequestException(
          'Default triage rules must have exactly one channel',
        );
      }

      // Get channels being removed
      const oldChannelIds = rule.triageChannels.map(
        (channel) => channel.channelId,
      );
      const removedChannelIds = oldChannelIds.filter(
        (id) => !updateDto.channelIds.includes(id),
      );

      // Setup new channels
      await this.setupTriageChannels(updateDto.channelIds, botCtx);

      // Cleanup removed channels
      for (const channelId of removedChannelIds) {
        await this.cleanupTriageChannel(channelId, botCtx);
      }

      await this.triageRuleRepo.updateRuleChannels(ruleId, channels, botCtx);
    }

    // Validate rules if provided for non-default rules
    if (updateDto.triageRules) {
      if (rule.isDefault) {
        throw new BadRequestException(
          'Cannot add conditions to default triage rule',
        );
      }

      await this.validateRules(updateDto.triageRules, botCtx);
    }

    Object.assign(rule, {
      isEnabled: updateDto.isEnabled ?? rule.isEnabled,
      triageRules: updateDto.triageRules ?? rule.triageRules,
    });

    const updatedRule = await this.triageRuleRepo.save(rule);
    return {
      ok: true,
      data: updatedRule,
    };
  }

  async deleteRule(
    ruleId: string,
    botCtx: BotCtx,
  ): Promise<ApiResponse<{ deleted: boolean }>> {
    const rule = await this.triageRuleRepo.findOne({
      where: {
        id: ruleId,
        installation: { id: botCtx.installation.id },
      },
      relations: ['triageChannels'],
    });

    if (!rule) {
      throw new NotFoundException('Triage rule not found');
    }

    // Cleanup channels before deleting rule
    for (const channel of rule.triageChannels) {
      await this.cleanupTriageChannel(channel.channelId, botCtx);
    }

    await this.triageRuleRepo.delete({
      id: ruleId,
      installation: { id: botCtx.installation.id },
    });

    return {
      ok: true,
      data: { deleted: true },
    };
  }
}
