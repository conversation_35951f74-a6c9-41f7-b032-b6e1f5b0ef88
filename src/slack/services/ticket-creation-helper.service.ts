import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { EventDeduplicationService } from '../../common/redis/event-deduplication.service';
import { ConfigKeys, ConfigService } from '../../config/config.service';
import { TransactionService } from '../../database/common/transactions.service';
import { CustomerContacts, Users } from '../../database/entities';
import { ChannelsRepository } from '../../database/entities/channels/repositories';
import { CommentThreadMapsRepository } from '../../database/entities/mappings/repositories/comment-thread-maps.repository';
import { SlackMessagesRepository } from '../../database/entities/slack-messages/repositories/slack-messages.repository';
import { CreateNewComment } from '../../external/provider/interfaces';
import { ThenaPlatformApiProvider } from '../../external/provider/thena-platform-api.provider';
import { Ticket } from '../../platform/interfaces';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import { parseWithMentions } from '../../utils/parsers/slack/mentions.parser';
import { BaseSlackBlocksToHtml } from '../../utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';
import { processSlackMessageText } from '../../utils/parsers/slack/text-processing.utils';
import { SlackAppManagementService } from '../core';
import { SlackWebAPIService } from '../providers/slack-apis/slack-apis.service';
import { CommentThreadCacheService } from './comment-thread-cache.service';

export interface TicketCreationPayload {
  title: string;
  requestorEmail: string;
  teamId: string;
  text: string;
  description?: string;
  priorityId?: string;
  urgency?: string;
  subTeamId?: string;
  performRouting?: boolean;
  formId?: string;
  customFieldValues?: any[];
  metadata: {
    slack: {
      channel: string;
      ts: string;
      user: string;
    };
    slackTeamId?: string;
  };
  [key: string]: any; // For additional standard fields
}

export interface ThreadProcessingOptions {
  channelId: string;
  messageTs: string;
  userId: string;
  shouldProcessReactions?: boolean;
  shouldSendConfirmation?: boolean;
}

interface MessageProcessingContext {
  slackMessageRecordId: string;
  slackMessage: any;
  permalink: string;
  threadReplies?: any[];
  preloadedUsers?: Map<string, Users | null>;
}

interface CommentCreationResult {
  commentId: string;
  slackMessageRecordId: string;
  slackMessageTs: string;
}

@Injectable()
export class TicketCreationHelper {
  private readonly LOG_SPAN = '[TicketCreationHelper]';

  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly configService: ConfigService,
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
    private readonly slackWebAPIService: SlackWebAPIService,
    private readonly slackAppManagementService: SlackAppManagementService,
    private readonly transactionService: TransactionService,
    private readonly eventDeduplicationService: EventDeduplicationService,
    private readonly channelsRepository: ChannelsRepository,
    private readonly slackMessagesRepository: SlackMessagesRepository,
    private readonly commentThreadMapsRepository: CommentThreadMapsRepository,
    private readonly baseSlackBlocksToHtml: BaseSlackBlocksToHtml,
    private readonly commentThreadCacheService: CommentThreadCacheService,
    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,
  ) {}

  /**
   * Creates a ticket with common metadata handling
   */
  async createTicketWithMetadata(
    installation: any,
    payload: TicketCreationPayload,
  ): Promise<Ticket> {
    this.logger.debug(
      `${this.LOG_SPAN} Creating ticket with payload:`,
      JSON.stringify(payload, null, 2),
    );

    // Define the fields that should be explicitly controlled and not overridden
    const controlledFields = [
      'title',
      'requestorEmail',
      'teamId',
      'text',
      'description',
      'priorityId',
      'urgency',
      'subTeamId',
      'performRouting',
      'formId',
      'customFieldValues',
      'metadata',
    ] as const;

    // Extract additional fields that are not in the controlled list
    // These will be spread first and can be overridden by explicit values
    const additionalFields = Object.fromEntries(
      Object.entries(payload).filter(
        ([key]) => !controlledFields.includes(key as any),
      ),
    );

    // Build the ticket payload with explicit field precedence
    const ticketPayload = {
      ...additionalFields,
      // Explicitly set controlled fields to ensure they take precedence over any duplicates
      requestorEmail: payload.requestorEmail,
      title: payload.title,
      text: payload.text,
      description: payload.description,
      teamId: payload.teamId,
      subTeamId: payload.subTeamId,
      performRouting: payload.performRouting,
      priorityId: payload.priorityId || payload.urgency,
      formId: payload.formId,
      customFieldValues: payload.customFieldValues,
      metadata: {
        slackTeamId: payload.metadata.slackTeamId || installation.teamId,
        slack: {
          channel: payload.metadata.slack.channel,
          ts: payload.metadata.slack.ts,
          user: payload.metadata.slack.user,
        },
      },
    };

    const ticket = await this.thenaPlatformApiProvider.createNewTicket(
      installation,
      ticketPayload,
    );

    this.logger.debug(`${this.LOG_SPAN} Ticket created: ${ticket.id}`);
    return ticket;
  }

  /**
   * Posts a conversation thread to the platform with proper comment hierarchy
   */
  async postConversationThreadToPlatform(
    installation: any,
    ticket: Ticket,
    options: ThreadProcessingOptions,
  ): Promise<void> {
    const {
      channelId,
      messageTs,
      userId,
      shouldProcessReactions = true,
      shouldSendConfirmation = true,
    } = options;

    try {
      this.logger.debug(
        `${this.LOG_SPAN} Posting conversation thread to platform for ticket ${ticket.id} from channel ${channelId}, message ${messageTs}`,
      );

      // Get the channel from the database
      const slackChannel = await this.channelsRepository.findByCondition({
        where: {
          channelId: channelId,
          installation: { id: installation.id },
        },
      });

      if (!slackChannel) {
        this.logger.error(
          `${this.LOG_SPAN} Channel not found for team: ${installation.teamId}`,
        );
        return;
      }

      // Get the message history for the parent message
      const messageHistory =
        await this.slackWebAPIService.getConversationHistory(
          installation.botToken,
          { channel: channelId, latest: messageTs, limit: 1, inclusive: true },
        );

      if (!messageHistory.ok) {
        this.logger.error(
          `${this.LOG_SPAN} Error getting slack message history`,
          messageHistory.error,
        );
        return;
      }

      const slackMessage = messageHistory.messages[0];
      if (!slackMessage) {
        this.logger.error(
          `${this.LOG_SPAN} No message found with timestamp ${messageTs}`,
        );
        return;
      }

      // Get message permalink
      const permalinkResponse = await this.slackWebAPIService.getPermalink(
        installation.botToken,
        { channel: channelId, message_ts: messageTs },
      );

      if (!permalinkResponse.ok) {
        this.logger.error(
          `${this.LOG_SPAN} Error getting slack message permalink`,
          permalinkResponse.error,
        );
        return;
      }
      const permalink = permalinkResponse?.permalink;

      // Process the parent message and thread replies
      await this.processMessageAndReplies(
        installation,
        ticket,
        slackChannel,
        slackMessage,
        permalink,
        channelId,
        messageTs,
        userId,
      );

      // Process reactions if requested
      if (shouldProcessReactions) {
        await this.processAllReactionsForComment(
          installation,
          channelId,
          messageTs,
        );
      }

      // Send confirmation if requested
      if (shouldSendConfirmation) {
        await this.sendTicketCreationConfirmation(
          installation,
          ticket,
          channelId,
          messageTs,
        );
      }

      this.logger.log(
        `${this.LOG_SPAN} Successfully posted conversation thread to platform for ticket ${ticket.id}`,
      );
    } catch (error) {
      this.logger.error(
        `${this.LOG_SPAN} Error posting conversation thread to platform: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    } finally {
      // Clear the request-scoped cache
      this.commentThreadCacheService.clearCache();

      // Log cache statistics for monitoring
      const stats = this.commentThreadCacheService.getCacheStats();
      this.logger.debug(
        `${this.LOG_SPAN} Cache cleared. Final stats: ${JSON.stringify(stats)}`,
      );
    }
  }

  /**
   * Processes a Slack message and converts it to a platform comment
   */
  async processMessageToComment(
    installation: any,
    ticket: Ticket,
    slackMessage: any,
    slackChannel: any,
    parentCommentId?: string,
    preloadedUsers?: Map<string, Users | null>,
  ): Promise<any> {
    // Get the user who sent the message
    const messageUser =
      await this.slackAppManagementService.upsertPersonWithIdentification(
        slackMessage.user,
        installation,
        slackChannel,
      );

    // Process the message text to preserve email addresses and format links
    const rawMessageContent = processSlackMessageText(
      slackMessage.text,
      'Ticket created from Slack',
      this.logger,
    );

    // Use parseWithMentions to properly handle user mentions in the message
    const messageContent = await parseWithMentions(
      rawMessageContent,
      this.usersRepository,
      installation,
      preloadedUsers,
    );

    // Convert blocks to HTML content if blocks exist
    let htmlContent = messageContent;
    if (slackMessage.blocks) {
      // Initialize the converter with the blocks and installation
      this.baseSlackBlocksToHtml.initialize(slackMessage.blocks, installation);

      // Convert blocks to HTML content
      htmlContent = await this.baseSlackBlocksToHtml.convert();
    }

    // Get files from the message if they exist
    const files = slackMessage.files ? (slackMessage.files as any) : [];

    // Create a comment payload
    const commentPayload: CreateNewComment = {
      channelId: slackChannel.channelId,
      content: htmlContent || messageContent,
      htmlContent,
      files,
      impersonatedUserAvatar: messageUser.getUserAvatar(),
      impersonatedUserEmail: messageUser.slackProfileEmail,
      impersonatedUserName: messageUser.displayName || messageUser.realName,
      ticketId: ticket.id,
      commentVisibility: 'public',
      parentCommentId,
      metadata: {
        ignoreSelf: true,
        ts: slackMessage.ts,
        threadTs: slackMessage.thread_ts || slackMessage.ts,
      },
    };

    // If the user is a customer contact, add the customer email to the comment payload
    if (messageUser instanceof CustomerContacts) {
      commentPayload.customerEmail = messageUser.slackProfileEmail;
    }

    return { commentPayload, messageUser };
  }

  /**
   * Sends a ticket creation confirmation message
   */
  async sendTicketCreationConfirmation(
    installation: any,
    ticket: Ticket,
    channelId: string,
    messageTs: string,
  ): Promise<void> {
    try {
      // Generate the ticket URL
      const platformUrl = this.configService.get(ConfigKeys.THENA_WEB_URL);
      const teamSegment =
        ticket.subTeamIdentifier ?? ticket.teamId ?? ticket.id;
      const ticketUrl = `${platformUrl}/dashboard/${ticket.teamId}?ticketId=${ticket.id}`;

      // Send a message in the thread informing about the ticket creation with a clickable link
      await this.slackWebAPIService.sendMessage(installation.botToken, {
        channel: channelId,
        text: `<${ticketUrl}|${teamSegment}#${ticket.ticketId}>: Ticket created successfully`,
        thread_ts: messageTs,
        unfurl_links: true,
        unfurl_media: true,
      });
    } catch (error) {
      this.logger.error(
        `${this.LOG_SPAN} Error sending ticket creation confirmation: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Processes the parent message and all thread replies using two-phase approach
   * Phase 1: Persist minimal identifiers in short transactions
   * Phase 2: Perform external API calls and update records in separate transactions
   */
  private async processMessageAndReplies(
    installation: any,
    ticket: Ticket,
    slackChannel: any,
    slackMessage: any,
    permalink: string,
    channelId: string,
    messageTs: string,
    userId: string,
  ): Promise<void> {
    const eventId = `${installation.teamId}:${channelId}:${messageTs}`;

    // Use idempotency to prevent duplicate processing
    await this.eventDeduplicationService.processIdempotently(
      eventId,
      'process_message_and_replies',
      async () => {
        // Phase 1: Persist minimal data in short transaction
        const context = await this.persistMessageContext(
          installation,
          ticket,
          slackChannel,
          slackMessage,
          permalink,
          channelId,
          messageTs,
          userId,
        );

        // Phase 2: External API calls and updates (outside transaction)
        await this.processCommentsWithExternalCalls(
          installation,
          ticket,
          slackChannel,
          channelId,
          messageTs,
          context,
        );

        return true;
      },
      installation.teamId,
    );
  }

  /**
   * Phase 1: Persist minimal message context in a short transaction
   */
  private async persistMessageContext(
    installation: any,
    ticket: Ticket,
    slackChannel: any,
    slackMessage: any,
    permalink: string,
    _channelId: string,
    _messageTs: string,
    userId: string,
  ): Promise<MessageProcessingContext> {
    return await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Save the parent message with minimal data
        const slackMessageRecord =
          await this.slackMessagesRepository.saveWithTxn(txnContext, {
            channel: { id: slackChannel.id },
            platformTicketId: ticket.id,
            slackPermalink: permalink,
            slackMessageTs: slackMessage.ts,
            slackMessageThreadTs: slackMessage.thread_ts,
            slackUserId: slackMessage.user || userId,
            installation: { id: installation.id },
            organization: { id: installation.organization.id },
            metadata: {
              ticket_details: {
                status: ticket.status,
                statusId: ticket.statusId,
                priority: ticket.priority,
                priorityId: ticket.priorityId,
              },
            },
          });

        return {
          slackMessageRecordId: slackMessageRecord.id,
          slackMessage,
          permalink,
        };
      },
    );
  }

  /**
   * Phase 2: Process comments with external API calls (outside transaction)
   */
  private async processCommentsWithExternalCalls(
    installation: any,
    ticket: Ticket,
    slackChannel: any,
    channelId: string,
    messageTs: string,
    context: MessageProcessingContext,
  ): Promise<void> {
    try {
      // External API call: Get thread replies (outside transaction)
      const threadRepliesResponse =
        await this.slackWebAPIService.getConversationReplies(
          installation.botToken,
          {
            channel: channelId,
            ts: messageTs,
          },
        );

      // Preload users from all messages in thread if available
      let preloadedUsers: Map<string, Users | null> | undefined;
      if (threadRepliesResponse.ok && threadRepliesResponse.messages) {
        preloadedUsers = await this.preloadUsersFromMessages(
          installation,
          threadRepliesResponse.messages,
        );
        this.logger.debug(
          `${this.LOG_SPAN} Preloaded ${preloadedUsers.size} users for thread processing`,
        );
      }

      // Process the parent message to create a comment payload
      const { commentPayload } = await this.processMessageToComment(
        installation,
        ticket,
        context.slackMessage,
        slackChannel,
        undefined, // No parent for the main message
        preloadedUsers,
      );

      // External API call: Create comment on platform (outside transaction)
      const comment = await this.thenaPlatformApiProvider.createNewComment(
        installation,
        commentPayload,
      );

      // Update database with comment ID in separate short transaction
      await this.updateMessageWithCommentId(
        context.slackMessageRecordId,
        comment.data.id,
        installation,
        ticket,
        messageTs,
        channelId,
      );

      // Process thread replies if available
      if (
        threadRepliesResponse.ok &&
        threadRepliesResponse.messages &&
        threadRepliesResponse.messages.length > 1
      ) {
        await this.processThreadRepliesInPhases(
          installation,
          ticket,
          slackChannel,
          channelId,
          messageTs,
          comment.data.id,
          threadRepliesResponse.messages.slice(1), // Skip the parent message
          preloadedUsers,
        );
      }
    } catch (error) {
      this.logger.error(
        `${this.LOG_SPAN} Error in phase 2 processing: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      // Implement compensation logic if needed
      await this.compensateFailedProcessing(context.slackMessageRecordId);
      throw error;
    }
  }

  /**
   * Update message with comment ID in separate short transaction
   */
  private async updateMessageWithCommentId(
    slackMessageRecordId: string,
    commentId: string,
    installation: any,
    ticket: Ticket,
    messageTs: string,
    channelId: string,
  ): Promise<void> {
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Update the parent slack message with the comment id
      await this.slackMessagesRepository.updateWithTxn(
        txnContext,
        slackMessageRecordId,
        { platformCommentId: commentId },
      );

      // Create comment thread mapping for the parent message
      await this.commentThreadMapsRepository.saveWithTxn(txnContext, {
        organization: installation.organization,
        platformCommentThreadId: commentId,
        platformCommentTicketId: ticket.id,
        slackThreadId: messageTs,
        slackChannelId: channelId,
      });
    });
  }

  /**
   * Compensation logic for failed processing
   */
  private async compensateFailedProcessing(
    slackMessageRecordId: string,
  ): Promise<void> {
    try {
      // Mark the message as failed or remove it to allow retry
      await this.transactionService.runInTransaction(async (txnContext) => {
        // Get the current message to preserve existing metadata
        const currentMessage =
          await this.slackMessagesRepository.findByCondition({
            where: { id: slackMessageRecordId },
          });

        const updatedMetadata = {
          ...currentMessage?.metadata,
          processing_failed: true,
          failed_at: new Date().toISOString(),
        } as any; // Type assertion to handle the metadata structure

        await this.slackMessagesRepository.updateWithTxn(
          txnContext,
          slackMessageRecordId,
          { metadata: updatedMetadata },
        );
      });
    } catch (compensationError) {
      this.logger.error(
        `${this.LOG_SPAN} Error in compensation logic: ${compensationError instanceof Error ? compensationError.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Process thread replies using two-phase approach
   */
  private async processThreadRepliesInPhases(
    installation: any,
    ticket: Ticket,
    slackChannel: any,
    channelId: string,
    messageTs: string,
    parentCommentId: string,
    messages: any[],
    preloadedUsers?: Map<string, Users | null>,
  ): Promise<void> {
    if (!messages || messages.length === 0) {
      return;
    }

    this.logger.debug(
      `${this.LOG_SPAN} Processing ${messages.length} replies with two-phase approach`,
    );

    // Process each reply with two-phase approach
    for (const reply of messages) {
      const replyEventId = `${installation.teamId}:${channelId}:${reply.ts}`;

      await this.eventDeduplicationService.processIdempotently(
        replyEventId,
        'process_thread_reply',
        async () => {
          // Phase 1: Persist reply message in short transaction
          const replyContext = await this.persistReplyContext(
            installation,
            ticket,
            slackChannel,
            reply,
            messageTs,
            channelId,
          );

          // Phase 2: External API calls and updates
          await this.processReplyWithExternalCalls(
            installation,
            ticket,
            slackChannel,
            reply,
            parentCommentId,
            replyContext,
            preloadedUsers,
          );

          return true;
        },
        installation.teamId,
      );
    }
  }

  /**
   * Phase 1: Persist reply context in short transaction
   */
  private async persistReplyContext(
    installation: any,
    ticket: Ticket,
    slackChannel: any,
    reply: any,
    messageTs: string,
    channelId: string,
  ): Promise<CommentCreationResult> {
    return await this.transactionService.runInTransaction(
      async (txnContext) => {
        // Get the permalink for the reply (this is a quick operation)
        const replyPermalinkResponse =
          await this.slackWebAPIService.getPermalink(installation.botToken, {
            channel: channelId,
            message_ts: reply.ts,
          });

        const replyPermalink = replyPermalinkResponse.ok
          ? replyPermalinkResponse.permalink
          : '';

        // Save the reply message with minimal data
        const replyRecord = await this.slackMessagesRepository.saveWithTxn(
          txnContext,
          {
            channel: { id: slackChannel.id },
            platformTicketId: ticket.id,
            slackPermalink: replyPermalink,
            slackMessageTs: reply.ts,
            slackMessageThreadTs: messageTs, // The parent message ts
            slackUserId: reply.user,
            installation: { id: installation.id },
            organization: { id: installation.organization.id },
          },
        );

        return {
          commentId: '', // Will be filled in phase 2
          slackMessageRecordId: replyRecord.id,
          slackMessageTs: reply.ts,
        };
      },
    );
  }

  /**
   * Phase 2: Process reply with external API calls
   */
  private async processReplyWithExternalCalls(
    installation: any,
    ticket: Ticket,
    slackChannel: any,
    reply: any,
    parentCommentId: string,
    replyContext: CommentCreationResult,
    preloadedUsers?: Map<string, Users | null>,
  ): Promise<void> {
    try {
      // Process the reply to create a comment payload
      const { commentPayload } = await this.processMessageToComment(
        installation,
        ticket,
        reply,
        slackChannel,
        parentCommentId, // Set parent comment ID for hierarchy
        preloadedUsers,
      );

      // External API call: Create comment on platform
      const replyComment = await this.thenaPlatformApiProvider.createNewComment(
        installation,
        commentPayload,
      );

      // Update database with comment ID in separate short transaction
      await this.transactionService.runInTransaction(async (txnContext) => {
        await this.slackMessagesRepository.updateWithTxn(
          txnContext,
          replyContext.slackMessageRecordId,
          { platformCommentId: replyComment.data.id },
        );
      });

      // Phase 3: Process reactions for this reply (after comment is created)
      this.logger.debug(
        `${this.LOG_SPAN} Processing reactions for reply ${reply.ts}`,
      );

      // Add debugging to verify we're processing the correct message
      this.logger.debug(
        `${this.LOG_SPAN} About to process reactions for Slack message TS: ${reply.ts} in channel: ${slackChannel.channelId}`,
      );

      // Add a small delay to ensure the database transaction is fully committed
      // This prevents race conditions where we try to process reactions before
      // the reply comment is fully available in the database
      await new Promise((resolve) => setTimeout(resolve, 100));

      try {
        await this.processAllReactionsForComment(
          installation,
          slackChannel.channelId,
          reply.ts, // Process reactions for this specific reply
        );
      } catch (reactionError) {
        this.logger.error(
          `${this.LOG_SPAN} Failed to process reactions for reply ${reply.ts}: ${reactionError instanceof Error ? reactionError.message : 'Unknown error'}`,
        );
        // Don't rethrow - reaction processing is secondary to comment creation
      }
    } catch (error) {
      this.logger.error(
        `${this.LOG_SPAN} Error processing reply ${reply.ts}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      // Implement compensation for this specific reply
      await this.compensateFailedProcessing(replyContext.slackMessageRecordId);
      throw error;
    }
  }

  /**
   * Preloads all users mentioned in a batch of messages
   * @param installation The Slack installation
   * @param messages The messages to extract user mentions from
   * @returns A map of email to user objects
   */
  private async preloadUsersFromMessages(
    installation: any,
    messages: any[],
  ): Promise<Map<string, Users | null>> {
    // capture user-id, ignore optional display part
    const mentionRegex = /<@([A-Z0-9]+)(?:\|[^>]+)?>/g;
    const userIdsSet = new Set<string>();

    // Extract all user IDs from message mentions
    for (const message of messages) {
      if (!message.text) {
        continue;
      }

      // Find all user mentions in the message
      let match: RegExpExecArray | null;
      // Reset the lastIndex to reuse the compiled RegExp
      mentionRegex.lastIndex = 0;

      while (true) {
        match = mentionRegex.exec(message.text);
        if (match === null) {
          break;
        }

        const userId = match[1]; // Use user ID instead of display name
        if (userId) {
          userIdsSet.add(userId);
        }
      }
    }

    // Convert set to array for processing
    const userIds = Array.from(userIdsSet);
    if (userIds.length === 0) {
      return new Map();
    }

    // Get user info from Slack API to get emails
    const userInfoMap = await this.batchGetUserInfo(installation, userIds);
    const userEmailsSet = new Set<string>();

    // Extract emails from user info
    for (const [_userId, userInfo] of userInfoMap.entries()) {
      if (userInfo?.user?.profile?.email) {
        userEmailsSet.add(userInfo.user.profile.email);
      }
    }

    const userEmails = Array.from(userEmailsSet);
    if (userEmails.length === 0) {
      return new Map();
    }

    // Query database for all users at once
    const users = await this.usersRepository
      .find({
        where: {
          installation: { id: installation.id },
          slackProfileEmail: In(userEmails), // Use TypeORM's In operator
        },
      })
      .catch((error) => {
        this.logger.error(
          `${this.LOG_SPAN} Error looking up batch of users: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
        return [];
      });

    // Create a map of email to user
    const userMap = new Map<string, Users | null>();
    for (const email of userEmails) {
      // Find the user with this email
      const user = users.find((u) => u.slackProfileEmail === email) || null;
      userMap.set(email, user);
    }

    return userMap;
  }

  /**
   * Process all existing reactions from a Slack message to platform comment
   */
  async processAllReactionsForComment(
    installation: any,
    channelId: string,
    messageTs: string,
  ): Promise<void> {
    try {
      // Get the slack channel
      const slackChannel = await this.channelsRepository.findByCondition({
        where: {
          channelId: channelId,
          installation: { id: installation.id },
        },
      });

      if (!slackChannel) {
        throw new Error(`Channel not found for ${installation.teamId}`);
      }

      // Get the specific message to check for existing reactions
      this.logger.debug(
        `${this.LOG_SPAN} Fetching specific message for TS: ${messageTs} in channel: ${channelId}`,
      );

      let slackMessage: any = null;

      // Strategy: Always try to get the message using conversations.replies first
      // This works for both main messages and thread replies

      // First, find the thread timestamp (parent message timestamp)
      const slackMessageRecord =
        await this.slackMessagesRepository.findByCondition({
          where: [
            {
              channel: { id: slackChannel.id },
              installation: { id: installation.id },
              slackMessageTs: messageTs,
            },
            {
              channel: { id: slackChannel.id },
              installation: { id: installation.id },
              slackMessageThreadTs: messageTs,
            },
          ],
        });

      if (slackMessageRecord) {
        // Determine the thread timestamp (for main messages, it's the message itself)
        const threadTs =
          slackMessageRecord.slackMessageThreadTs ||
          slackMessageRecord.slackMessageTs;

        this.logger.debug(
          `${this.LOG_SPAN} Using thread TS: ${threadTs} to fetch message: ${messageTs}`,
        );

        // Use conversations.replies to get all messages in the thread
        const threadReplies =
          await this.slackWebAPIService.getConversationReplies(
            installation.botToken,
            {
              channel: channelId,
              ts: threadTs,
            },
          );

        if (threadReplies.ok && threadReplies.messages) {
          // Find the specific message by timestamp
          slackMessage = threadReplies.messages.find(
            (msg) => msg.ts === messageTs,
          );

          if (!slackMessage) {
            this.logger.error(
              `${this.LOG_SPAN} Could not find message with TS: ${messageTs} in thread: ${threadTs}`,
            );
            return;
          }

          this.logger.debug(
            `${this.LOG_SPAN} Successfully found message ${messageTs} in thread ${threadTs}`,
          );
        } else {
          this.logger.error(
            `${this.LOG_SPAN} Could not get thread replies for TS: ${threadTs}`,
          );
          return;
        }
      } else {
        this.logger.error(
          `${this.LOG_SPAN} Could not find Slack message record for TS: ${messageTs}`,
        );
        return;
      }

      // Verify we got the correct message
      if (slackMessage.ts !== messageTs) {
        this.logger.error(
          `${this.LOG_SPAN} ERROR: Retrieved message TS (${slackMessage.ts}) does not match requested TS (${messageTs})`,
        );
        return;
      }

      this.logger.debug(
        `${this.LOG_SPAN} ✅ Successfully retrieved correct message with TS: ${slackMessage.ts}`,
      );

      // Check if the message has any reactions
      if (!slackMessage.reactions || slackMessage.reactions.length === 0) {
        this.logger.debug(
          `${this.LOG_SPAN} No reactions found on message ${messageTs}`,
        );
        return;
      }

      // Debug: Log the reactions we found
      const reactionNames = slackMessage.reactions.map((r: any) => r.name);
      this.logger.debug(
        `${this.LOG_SPAN} Found ${slackMessage.reactions.length} reactions on message ${messageTs}: [${reactionNames.join(', ')}]`,
      );

      // Get the related platform comment ID
      this.logger.debug(
        `${this.LOG_SPAN} Getting platform comment ID for Slack message TS: ${messageTs}`,
      );

      const commentId = await this.getPlatformCommentId(
        installation,
        slackChannel.id,
        messageTs,
      );

      this.logger.debug(
        `${this.LOG_SPAN} Platform comment ID for Slack TS ${messageTs}: ${commentId}`,
      );

      await this.processReactionsOptimized(
        installation,
        slackMessage.reactions,
        commentId,
        messageTs,
      );
    } catch (error) {
      this.logger.error(
        `${this.LOG_SPAN} Error processing reactions for comment: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      // Don't rethrow, just log the error - this is a secondary operation
    }
  }

  private async processReactionsOptimized(
    installation: any,
    reactions: any[],
    commentId: string,
    messageTs: string,
  ): Promise<void> {
    const allUserIds = new Set<string>();
    for (const reaction of reactions) {
      for (const userId of reaction.users ?? []) {
        allUserIds.add(userId);
      }
    }

    const uniqueUserIds = Array.from(allUserIds);
    this.logger.debug(
      `${this.LOG_SPAN} Batch fetching user info for ${uniqueUserIds.length} unique users`,
    );

    const userInfoMap = await this.batchGetUserInfo(
      installation,
      uniqueUserIds,
    );

    const reactionPromises = reactions.flatMap((reaction) =>
      (reaction.users || []).map(async (userId: string) => {
        try {
          const userInfo = userInfoMap.get(userId);
          if (!userInfo) {
            this.logger.warn(
              `${this.LOG_SPAN} No user info found for user ${userId}, skipping reaction ${reaction.name}`,
            );
            return;
          }

          const displayName =
            userInfo.user.profile?.display_name ??
            userInfo.user.profile?.real_name ??
            userInfo.user.profile?.real_name_normalized;

          this.logger.debug(
            `${this.LOG_SPAN} Adding reaction '${reaction.name}' to platform comment ${commentId} for user ${displayName} (Slack TS: ${messageTs})`,
          );

          await this.thenaPlatformApiProvider.addReaction(
            installation,
            commentId,
            reaction.name,
            displayName,
            userInfo.user.profile?.email,
            userInfo.user.profile?.image_512,
          );

          this.logger.debug(
            `${this.LOG_SPAN} ✅ Successfully added reaction ${reaction.name} to comment ${commentId} for user ${userId} (Slack TS: ${messageTs})`,
          );
        } catch (error) {
          this.logger.error(
            `${this.LOG_SPAN} Error adding reaction ${reaction.name} for user ${userId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
        }
      }),
    );

    await Promise.allSettled(reactionPromises);
  }

  private async batchGetUserInfo(
    installation: any,
    userIds: string[],
  ): Promise<Map<string, any>> {
    const userInfoMap = new Map<string, any>();

    if (userIds.length === 0) {
      return userInfoMap;
    }

    const results = await Promise.allSettled(
      userIds.map(async (userId) => {
        try {
          const userInfo = await this.slackWebAPIService.getUserInfo(
            installation.botToken,
            { user: userId },
          );
          return { userId, userInfo };
        } catch (error) {
          this.logger.error(
            `${this.LOG_SPAN} Error fetching user info for ${userId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
          return null;
        }
      }),
    );

    for (const result of results) {
      if (result.status === 'fulfilled' && result.value?.userInfo) {
        userInfoMap.set(result.value.userId, result.value.userInfo);
      }
    }

    this.logger.debug(
      `${this.LOG_SPAN} Successfully fetched user info for ${userInfoMap.size}/${userIds.length} users`,
    );

    return userInfoMap;
  }

  /**
   * Get the platform comment ID for a given Slack message (with caching)
   */
  private async getPlatformCommentId(
    installation: any,
    channelId: string,
    ts: string,
  ): Promise<string> {
    this.logger.debug(
      `${this.LOG_SPAN} [getPlatformCommentId] Looking for Slack message with TS: ${ts} in channel: ${channelId}`,
    );

    const commonWhereClause = {
      channel: { id: channelId },
      installation: { id: installation.id },
    };

    // Check if it's a direct message or threaded message
    const slackMessage = await this.slackMessagesRepository.findByCondition({
      where: [
        { ...commonWhereClause, slackMessageTs: ts },
        { ...commonWhereClause, slackMessageThreadTs: ts },
      ],
    });

    // If a message is found
    if (slackMessage) {
      this.logger.debug(
        `${this.LOG_SPAN} [getPlatformCommentId] Found Slack message: TS=${slackMessage.slackMessageTs}, ThreadTS=${slackMessage.slackMessageThreadTs}, PlatformCommentId=${slackMessage.platformCommentId}`,
      );

      // If it's the main message
      if (ts === slackMessage.slackMessageTs) {
        if (!slackMessage.platformCommentId) {
          throw new Error(
            `No platform comment ID found for the message: ${ts}`,
          );
        }
        this.logger.debug(
          `${this.LOG_SPAN} [getPlatformCommentId] Returning main message platform comment ID: ${slackMessage.platformCommentId}`,
        );
        return slackMessage.platformCommentId;
      }

      // It's a reply in a thread
      if (!slackMessage.platformCommentId) {
        throw new Error(
          `No parent platform comment ID found for the thread: ${ts}`,
        );
      }

      this.logger.debug(
        `${this.LOG_SPAN} [getPlatformCommentId] Looking up thread comment for TS ${ts} in parent comment ${slackMessage.platformCommentId}`,
      );

      // 🚀 Use cached service instead of direct API call
      const commentId =
        await this.commentThreadCacheService.getCommentIdBySlackTs(
          installation,
          slackMessage.platformCommentId,
          ts,
        );

      if (!commentId) {
        this.logger.error(
          `${this.LOG_SPAN} [getPlatformCommentId] No thread comment found for TS: ${ts} in parent comment: ${slackMessage.platformCommentId}`,
        );
        throw new Error(`No thread comment found for ts: ${ts}`);
      }

      this.logger.debug(
        `${this.LOG_SPAN} [getPlatformCommentId] Found thread comment ID: ${commentId} for TS: ${ts}`,
      );
      return commentId;
    }

    this.logger.error(
      `${this.LOG_SPAN} [getPlatformCommentId] No Slack message found for TS: ${ts} in channel: ${channelId}`,
    );
    throw new Error(`No related slack message found for ts: ${ts}`);
  }
}
