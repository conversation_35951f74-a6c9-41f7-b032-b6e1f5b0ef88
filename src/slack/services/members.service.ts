import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { FindOptionsWhere, In, Not } from 'typeorm';
import { BotCtx } from '../../auth/interfaces';
import { Users } from '../../database/entities';
import { UsersRepository } from '../../database/entities/users/repositories/users.repository';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import { CommonQueryParamsToFetchEntityData } from '../query-params';

@Injectable()
export class SlackMembersService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,

    // Database Repositories
    private readonly usersRepository: UsersRepository,
  ) {}

  /**
   * Get all members
   * @param botCtx - The bot context
   * @param query - The query parameters
   * @returns The members
   */
  public async getAllMembers(
    botCtx: BotCtx,
    query: CommonQueryParamsToFetchEntityData,
  ) {
    const { installations, organization } = botCtx;
    const { page = 1, limit = 10 } = query;

    // Validate pagination parameters
    if (limit > 100) {
      throw new BadRequestException('Limit cannot exceed 100');
    }

    if (page < 1) {
      throw new BadRequestException('Page must be greater than 0');
    }

    const whereClause: FindOptionsWhere<Users> = {
      isBot: false,
      slackId: Not('USLACKBOT'),
      slackDeleted: false,
      organization: { id: organization.id },
      installation: { id: In(installations.map((i) => i.id)) },
    };

    const users = await this.usersRepository.findAll({
      where: whereClause,
    });

    return {
      data: users,
      meta: {
        page,
        limit,
        total: users.length,
        totalPages: Math.ceil(users.length / limit),
      },
    };
  }
}
