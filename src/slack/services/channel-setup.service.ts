import { Inject, Injectable } from '@nestjs/common';
import { Installations } from '../../database/entities';
import { ChannelsRepository } from '../../database/entities/channels/repositories';
import { ThenaPlatformApiProvider } from '../../external/provider/thena-platform-api.provider';
import { CUSTOM_LOGGER_TOKEN, ILogger, SLACK_SENTRY_TAG } from '../../utils';
import { SentryService } from '../../utils/filters/sentry-alerts.filter';
import { safeJsonStringify } from '../../utils/external/safe-json-stringify.utils';
import { ChannelSetupBlocks } from '../blocks/components';
import { CHANNEL_SETUP_CONFIG } from '../constants/channel-setup.constants';
import { BotChannelJoinedHandler } from '../core/slack-channel/bot-channel-joined.handler';
import {
  ChannelData,
  ChannelState,
  IChannelSetupService,
} from '../types/channel-setup.types';

@Injectable()
export class ChannelSetupService implements IChannelSetupService {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    @Inject('Sentry') private readonly sentryService: SentryService,
    private readonly channelsRepository: ChannelsRepository,
    private readonly thenaPlatformApiProvider: ThenaPlatformApiProvider,
    private readonly channelSetupBlocks: ChannelSetupBlocks,
  ) {}

  async checkChannelState(
    channelId: string,
    installation: Installations,
  ): Promise<void> {
    this.logger.log(`Checking current database state of channel ${channelId}`);

    try {
      const channel = await this.channelsRepository.findByCondition({
        where: { channelId, installation: { id: installation.id } },
        relations: {
          installation: true,
          organization: true,
          platformTeamsToChannelMappings: {
            platformTeam: true,
          },
        },
      });

      if (!channel) {
        this.logger.log(`Channel ${channelId} not found in database!`);
        return;
      }

      const channelState: ChannelState = {
        channelId,
        channelType: channel.channelType,
        teamMappings:
          channel.platformTeamsToChannelMappings?.map((mapping) => ({
            relationshipType: mapping.relationshipType,
            platformTeamId: mapping.platformTeam?.id,
            platformTeamUid: mapping.platformTeam?.uid,
          })) || [],
        sharedTeamIds: channel.sharedTeamIds || [],
      };

      this.logChannelState(channelState);
    } catch (error) {
      this.logger.error(
        `Error checking channel state: ${safeJsonStringify(error, { fallback: 'Unknown error checking channel state' })}`,
      );

      // Report to Sentry
      this.sentryService.captureException(error, {
        tag: SLACK_SENTRY_TAG,
        name: '🚨 Error checking channel state',
        channelId,
        installationId: installation.id,
      });
    }
  }

  async fetchTeams(installation: Installations): Promise<any[]> {
    let teams: any[] = [];
    let retryCount = 0;

    while (retryCount <= CHANNEL_SETUP_CONFIG.maxRetries) {
      try {
        teams = await this.thenaPlatformApiProvider.getTeams(installation);
        this.logger.log(
          `Fetched ${teams.length} teams for organization ${installation.organization.id}`,
        );

        if (teams.length === 0) {
          this.logger.warn(
            `API returned zero teams for organization ${installation.organization.id}`,
          );
          if (retryCount < CHANNEL_SETUP_CONFIG.maxRetries) {
            await new Promise((resolve) =>
              setTimeout(resolve, CHANNEL_SETUP_CONFIG.retryDelayMs),
            );
            retryCount++;
            continue;
          }
        }
        break;
      } catch (error) {
        this.logger.error(
          `Error fetching teams from platform: ${safeJsonStringify(error, { fallback: 'Unknown error fetching teams' })}`,
        );

        // Report to Sentry on final retry
        if (retryCount >= CHANNEL_SETUP_CONFIG.maxRetries) {
          this.sentryService.captureException(error, {
            tag: SLACK_SENTRY_TAG,
            name: '🚨 Error fetching teams from platform',
            installationId: installation.id,
            organizationId: installation.organization.id,
            retryCount,
          });
          throw error;
        }

        await new Promise((resolve) =>
          setTimeout(resolve, CHANNEL_SETUP_CONFIG.retryDelayMs),
        );
        retryCount++;
        continue;
      }
    }

    return teams;
  }

  async openConfigurationModal(
    client: any,
    installation: Installations,
    triggerId: string,
    channelData: ChannelData,
    teams: any[],
    responseUrl?: string,
  ): Promise<void> {
    try {
      await client.views.open({
        token: installation.botToken,
        trigger_id: triggerId,
        view: {
          type: 'modal',
          private_metadata: JSON.stringify({ ...channelData, responseUrl }),
          callback_id: BotChannelJoinedHandler.CHANNEL_SETUP_MODAL_CALLBACK_ID,
          title: {
            type: 'plain_text',
            text: CHANNEL_SETUP_CONFIG.modalConfig.title,
            emoji: true,
          },
          blocks: this.channelSetupBlocks.build(teams).blocks,
          submit: {
            type: 'plain_text',
            text: CHANNEL_SETUP_CONFIG.modalConfig.submitText,
          },
          close: {
            type: 'plain_text',
            text: CHANNEL_SETUP_CONFIG.modalConfig.closeText,
          },
        },
      });
      this.logger.log(
        `Opened channel configuration modal for channel ${channelData.channelId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error opening configuration modal: ${safeJsonStringify(error, { fallback: 'Unknown error opening modal' })}`,
      );
      throw error;
    }
  }

  private logChannelState(state: ChannelState): void {
    this.logger.log(`Channel ${state.channelId} - Current state:`);
    this.logger.log(`  Type: ${state.channelType}`);
    this.logger.log(`  Team mappings count: ${state.teamMappings.length}`);

    state.teamMappings.forEach((mapping, index) => {
      this.logger.log(`  Team mapping #${index + 1}:`);
      this.logger.log(`    Relationship: ${mapping.relationshipType}`);
      this.logger.log(`    Platform team ID: ${mapping.platformTeamId}`);
      this.logger.log(`    Platform team UID: ${mapping.platformTeamUid}`);
    });

    this.logger.log(
      `  Shared team IDs: ${state.sharedTeamIds.join(', ') || 'none'}`,
    );
  }
}
