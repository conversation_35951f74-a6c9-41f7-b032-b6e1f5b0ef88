import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Channel } from '@slack/web-api/dist/types/response/ConversationsJoinResponse';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { ChannelType } from '../../database/entities/channels/channels.entity';

export class PostMessageDTO {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The Slack channel ID to post the message to',
    example: 'C12345678',
    required: true,
  })
  channel: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The text content of the message',
    example: 'Hello world!',
    required: true,
  })
  text: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'The timestamp of the thread to reply to',
    example: '1234567890.123456',
    required: false,
  })
  threadTs?: string;

  @IsArray()
  @IsOptional()
  @ApiProperty({
    description: 'Slack Block Kit blocks for rich message formatting',
    example: [
      { type: 'section', text: { type: 'mrkdwn', text: '*Hello* world!' } },
    ],
    required: false,
  })
  blocks?: any;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({
    description: 'Whether to unfurl links in the message',
    example: true,
    required: false,
    default: true,
  })
  unfurlLinks?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({
    description: 'Whether to unfurl media in the message',
    example: true,
    required: false,
    default: true,
  })
  unfurlMedia?: boolean;
}

export class PostMessageResponseDTO {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  ok: boolean;

  @ApiProperty({
    description: 'The channel ID where the message was posted',
    example: 'C12345678',
  })
  channel?: string;

  @ApiProperty({
    description: 'The timestamp of the message',
    example: '1234567890.123456',
  })
  ts?: string;

  @ApiProperty({
    description: 'The message object',
    example: { text: 'Hello world!' },
  })
  message?: {
    text?: string;
  };
}

export class UpdateMessageDTO {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The Slack channel ID where the message is located',
    example: 'C12345678',
    required: true,
  })
  channel: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The new text content for the message',
    example: 'Updated message text',
    required: true,
  })
  text: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The timestamp of the message to update',
    example: '1234567890.123456',
    required: true,
  })
  ts: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'Slack Block Kit blocks for rich message formatting',
    example: JSON.stringify([
      { type: 'section', text: { type: 'mrkdwn', text: '*Updated* message!' } },
    ]),
    required: false,
  })
  blocks?: string;
}

export class UpdateMessageResponseDTO {
  ok: boolean;
  channel?: string;
  ts?: string;
  text?: string;
}
export class DeleteMessageDTO {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The Slack channel ID where the message is located',
    example: 'C12345678',
    required: true,
  })
  channel: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The timestamp of the message to delete',
    example: '1234567890.123456',
    required: true,
  })
  ts: string;
}

export class DeleteMessageResponseDTO {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  ok: boolean;
}

export class AddReactionDTO {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The Slack channel ID where the message is located',
    example: 'C12345678',
    required: true,
  })
  channel: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The timestamp of the message to add a reaction to',
    example: '1234567890.123456',
    required: true,
  })
  ts: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The name of the emoji reaction to add',
    example: 'thumbsup',
    required: true,
  })
  name: string;
}

export class AddReactionResponseDTO {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  ok: boolean;
}

export class RemoveReactionDTO {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The Slack channel ID where the message is located',
    example: 'C12345678',
    required: true,
  })
  channel: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The timestamp of the message to remove a reaction from',
    example: '1234567890.123456',
    required: true,
  })
  ts: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The name of the emoji reaction to remove',
    example: 'thumbsup',
    required: true,
  })
  name: string;
}

export class RemoveReactionResponseDTO {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  ok: boolean;
}

export class InviteUserToConversationDTO {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The Slack channel ID to invite the user to',
    example: 'C12345678',
    required: true,
  })
  channel: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The Slack user ID to invite',
    example: 'U12345678',
    required: true,
  })
  user: string;
}

export class InviteUserToConversationResponseDTO {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  ok: boolean;
}

export class KickUserFromConversationDTO {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The Slack channel ID to remove the user from',
    example: 'C12345678',
    required: true,
  })
  channel: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The Slack user ID to remove from the channel',
    example: 'U12345678',
    required: true,
  })
  user: string;
}

export class KickUserFromConversationResponseDTO {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  ok: boolean;
}

export class LeaveConversationDTO {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The Slack channel ID to leave',
    example: 'C12345678',
    required: true,
  })
  channel: string;
}

export class LeaveConversationResponseDTO {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  ok: boolean;
}

export class JoinConversationDTO {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The Slack channel ID to join',
    example: 'C12345678',
    required: true,
  })
  channel: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The platform team ID to join',
    example: 'T12345678',
    required: true,
  })
  platformTeam: string;

  @IsEnum(ChannelType)
  @IsOptional()
  @ApiPropertyOptional({
    description: 'The type of channel to join',
    example: ChannelType.NOT_CONFIGURED,
    required: false,
  })
  channelType?: ChannelType;
}

export class JoinConversationResponseDTO {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  ok: boolean;

  @ApiPropertyOptional({
    description: 'The channel object',
    example: { id: 'C12345678', name: 'general' },
  })
  channel?: Channel;
}
