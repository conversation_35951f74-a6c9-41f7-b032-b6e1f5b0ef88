import { IsArray, IsNotEmpty, IsString } from 'class-validator';

export class AddTeamDTO {
  @IsString()
  @IsNotEmpty()
  teamId: string;

  @IsString()
  @IsNotEmpty()
  installedBy: string;

  @IsArray()
  @IsString({ each: true })
  workspaces: string[];
}

export class RemoveTeamDTO {
  @IsString()
  @IsNotEmpty()
  teamId: string;
}

export class MapTeamToChannelsDTO {
  @IsString()
  @IsNotEmpty()
  teamId: string;

  @IsArray()
  @IsString({ each: true })
  channelIds: string[];

  @IsString()
  @IsNotEmpty()
  channelType: 'customer_channel' | 'internal_helpdesk' | 'triage_channel';
}

export class MapTeamToSecondaryChannelsDTO {
  @IsString()
  @IsNotEmpty()
  teamId: string;

  @IsString()
  @IsNotEmpty()
  newTeamId: string;

  @IsArray()
  @IsString({ each: true })
  channelIds: string[];

  @IsString()
  @IsNotEmpty()
  channelType: 'customer_channel' | 'internal_helpdesk' | 'triage_channel';
}

export class DisconnectTeamToChannelsDTO {
  @IsString()
  @IsNotEmpty()
  teamId: string;

  @IsArray()
  @IsString({ each: true })
  channelIds: string[];
}
