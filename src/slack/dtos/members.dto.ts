import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { Users } from '../../database/entities';

/**
 * Query parameters for fetching members data
 */
export class MembersQueryParamsDTO {
  @ApiProperty({
    description: 'Page number for pagination',
    example: 1,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  page?: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  limit?: number;

  @ApiProperty({
    description: 'Search term to filter members',
    example: 'john',
    required: false,
  })
  @IsString()
  @IsOptional()
  search?: string;
}

/**
 * Slack member profile data
 */
export class SlackMemberProfileDTO {
  @ApiProperty({
    description: 'The display name of the user',
    example: '<PERSON>',
  })
  display_name: string;

  @ApiProperty({
    description: 'The real name of the user',
    example: '<PERSON>',
  })
  real_name: string;

  @ApiProperty({
    description: 'The email address of the user',
    example: '<EMAIL>',
  })
  email?: string;

  @ApiProperty({
    description: 'The profile image URL',
    example: 'https://secure.gravatar.com/avatar/123456.jpg',
  })
  image_72?: string;
}

/**
 * Slack member data
 */
export class SlackMemberDTO {
  @ApiProperty({
    description: 'The Slack user ID',
    example: 'U12345678',
  })
  id: string;

  @ApiProperty({
    description: 'The username of the user',
    example: 'johndoe',
  })
  name: string;

  @ApiProperty({
    description: 'Whether the user is deleted',
    example: false,
  })
  deleted: boolean;

  @ApiProperty({
    description: 'Whether the user is a bot',
    example: false,
  })
  is_bot: boolean;

  @ApiProperty({
    description: 'The profile information of the user',
    type: SlackMemberProfileDTO,
  })
  profile: SlackMemberProfileDTO;
}

export class MetaData {
  @ApiProperty({
    description: 'Total count of members',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit: number;
}

/**
 * Response for the get all members endpoint
 */
export class GetAllMembersResponseDTO {
  @ApiProperty({
    description: 'List of Slack users',
    type: [Users],
  })
  data: Users[];

  @ApiProperty({
    description: 'Meta data',
    type: MetaData,
  })
  meta: MetaData;
}
