import { Type } from 'class-transformer';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  IsBoolean,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON>al,
  IsString,
  Max,
  <PERSON>,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateSettingsDTO {
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  @Min(0)
  @Max(1440)
  @ApiProperty({
    description: 'Time window in minutes for conversation context',
    minimum: 0,
    maximum: 1440,
    required: false,
    example: 60,
  })
  conversationWindow: number;

  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  @ApiProperty({
    description: 'Whether to automatically create tickets',
    required: false,
    example: true,
  })
  automaticTickets: boolean;

  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  @ApiProperty({
    description: 'Whether to enable slash commands',
    required: false,
    example: true,
  })
  slashCommands: boolean;

  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  @ApiProperty({
    description: 'Whether to require form submission',
    required: false,
    example: false,
  })
  requireForm: boolean;

  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  @ApiProperty({
    description: 'Whether to enable Thena bot tagging',
    required: false,
    example: true,
  })
  thenaBotTaggingEnabled: boolean;

  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  @ApiProperty({
    description: 'Whether to enable ticket command',
    required: false,
    example: true,
  })
  ticketCommand: boolean;

  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  @ApiProperty({
    description: 'Whether to enable ticket creation via reaction',
    required: false,
    example: true,
  })
  enableTicketCreationViaReaction: boolean;

  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  @ApiProperty({
    description: 'Whether to enable extended AI thinking',
    required: false,
    example: true,
  })
  aiEnableExtendedThinking: boolean;

  @IsString()
  @IsOptional()
  @Type(() => String)
  @ApiProperty({
    description: 'AI model to use',
    required: false,
    example: 'gpt-4',
  })
  aiModel: string;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  @ApiProperty({
    description: 'AI temperature setting',
    required: false,
    example: 0.7,
  })
  aiTemperature: number;

  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  @ApiProperty({
    description: 'Maximum tokens for AI responses',
    required: false,
    example: 2048,
  })
  aiMaxTokens: number;

  @IsArray()
  @IsOptional()
  @Type(() => String)
  @ApiProperty({
    description: 'Selected forms for the team',
    required: false,
    type: 'array',
    items: {
      type: 'string',
    },
    example: ['form1', 'form2'],
  })
  selectedForms: string[];
}
