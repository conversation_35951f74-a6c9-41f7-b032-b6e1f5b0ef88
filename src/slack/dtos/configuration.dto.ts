import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>NotEmpty,
  <PERSON>Optional,
  IsString,
  Length,
} from 'class-validator';

export class ConfigureTriageChannelDTO {
  @IsString()
  @Length(11)
  @IsNotEmpty({ message: 'Please provide a valid Slack channel id' })
  channelToTriage: string;

  @IsString()
  @Length(11)
  @IsNotEmpty({ message: 'Please provide a valid Slack channel id' })
  triageChannel: string;
}

export class CreateTriageThreadDTO {
  @IsString()
  @IsNotEmpty({ message: 'Please provide a valid message' })
  message: string;

  @IsString()
  @IsOptional()
  commentAsEmail?: string;

  @IsString()
  @IsOptional()
  commentAsName?: string;

  @IsString()
  @IsOptional()
  commentAsAvatar?: string;

  @IsString()
  @IsNotEmpty({ message: 'Please provide a valid platform comment id' })
  platformCommentId: string;

  @IsString()
  @IsNotEmpty({ message: 'Please provide a valid platform ticket id' })
  platformTicketId: string;

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  attachmentUrls?: string[];
}
