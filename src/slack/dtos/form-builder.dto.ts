import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for form selection payload
 */
export class FormSelectionDTO {
  @ApiProperty({
    description: 'The Slack payload for form selection',
    example: {
      type: 'block_actions',
      trigger_id: '123.456.abc',
      team: { id: 'T12345', domain: 'example' },
      channel: { id: 'C12345', name: 'general' },
      actions: [
        {
          action_id: 'form_continue',
          block_id: 'form_selector_block',
          value: 'form_123',
        },
      ],
      user: { id: 'U12345', name: 'user' },
    },
  })
  @IsObject()
  @IsNotEmpty()
  payload: Record<string, any>;
}

/**
 * DTO for form submission payload
 */
export class FormSubmissionDTO {
  @ApiProperty({
    description: 'The Slack payload for form submission',
    example: {
      type: 'view_submission',
      view: {
        id: 'V12345',
        callback_id: 'form_submission_modal',
        private_metadata:
          '{"formId":"form_123","teamId":"T12345","channelId":"C12345"}',
        state: {
          values: {
            field_block_1: {
              field_1: { value: 'Sample response' },
            },
          },
        },
      },
      team: { id: 'T12345', domain: 'example' },
      user: { id: 'U12345', name: 'user' },
    },
  })
  @IsObject()
  @IsNotEmpty()
  payload: Record<string, any>;
}

/**
 * DTO for form interaction payload
 */
export class FormInteractionDTO {
  @ApiProperty({
    description: 'The Slack payload for form interaction',
    example: {
      type: 'block_actions',
      view: {
        id: 'V12345',
        callback_id: 'form_submission_modal',
        private_metadata:
          '{"formId":"form_123","teamId":"T12345","channelId":"C12345"}',
        hash: 'hash123',
        state: {
          values: {},
        },
      },
      actions: [
        {
          action_id: 'field_1',
          block_id: 'field_block_1',
          value: 'Selected value',
        },
      ],
      team: { id: 'T12345', domain: 'example' },
      user: { id: 'U12345', name: 'user' },
    },
  })
  @IsObject()
  @IsNotEmpty()
  payload: Record<string, any>;
}
