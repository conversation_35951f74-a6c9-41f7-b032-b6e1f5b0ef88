import {
  DeepPartial,
  Find<PERSON>anyOptions,
  FindOneOptions,
  FindOptionsWhere,
  InsertResult,
  UpdateResult,
} from 'typeorm';
import type { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity.js';
import { TransactionContext } from './transaction-interface';

export type Criteria<T> =
  | string
  | string[]
  | number
  | number[]
  | Date
  | Date[]
  | FindOptionsWhere<T>;

export interface SoftDeleteOptions {
  id: string;
  uid?: string;
}

/**
 * Base interface for a database repository
 * @template T The type of the entity
 */
export interface BaseInterfaceRepository<T> {
  /**
   * Creates a new entity and returns it
   * @param data The data to create the entity with
   * @returns The created entity
   */
  create(data: DeepPartial<T>): T;

  /**
   * Counts the number of entities
   * @param options The options to count the entities by
   * @returns The number of entities
   */
  count(options?: FindManyOptions<T>): Promise<number>;

  /**
   * Inserts an entity
   * @param data The data to insert the entity with
   * @returns The inserted entity
   */
  insert(
    data: QueryDeepPartialEntity<T> | QueryDeepPartialEntity<T>[],
  ): Promise<InsertResult>;

  /**
   * Inserts an entity with a transaction
   * @param transactionContext The transaction context
   * @param data The data to insert the entity with
   * @returns The inserted entity
   */
  insertWithTxn?(
    transactionContext: TransactionContext,
    data: QueryDeepPartialEntity<T> | QueryDeepPartialEntity<T>[],
  ): Promise<InsertResult>;

  /**
   * Updates an entity
   * @param criteria The criteria to update the entity by
   * @param partialEntity The partial entity to update
   * @returns The updated entity
   */
  update(
    criteria: Criteria<T>,
    partialEntity: QueryDeepPartialEntity<T>,
  ): Promise<UpdateResult>;

  /**
   * Updates an entity with a transaction
   * @param transactionContext The transaction context
   * @param criteria The criteria to update the entity by
   * @param partialEntity The partial entity to update
   * @returns The updated entity
   */
  updateWithTxn?(
    transactionContext: TransactionContext,
    criteria: Criteria<T>,
    partialEntity: QueryDeepPartialEntity<T>,
  ): Promise<UpdateResult>;

  /**
   * Creates multiple entities and returns them
   * @param data The data to create the entities with
   * @returns The created entities
   */
  createMany(data: DeepPartial<T>[]): T[];

  /**
   * Saves an entity and returns it
   * @param data The data to save the entity with
   * @returns The saved entity
   */
  save(data: DeepPartial<T>): Promise<T>;

  /**
   * Saves an entity with a transaction
   * @param transactionContext The transaction context
   * @param data The data to save the entity with
   * @returns The saved entity
   */
  saveWithTxn?(
    transactionContext: TransactionContext,
    data: DeepPartial<T>,
  ): Promise<T>;

  /**
   * Checks if an entity exists
   * @param options The options to check if the entity exists by
   * @returns Whether the entity exists
   */
  exists(options: FindOneOptions<T>): Promise<boolean>;

  /**
   * Saves multiple entities and returns them
   * @param data The data to save the entities with
   * @returns The saved entities
   */
  saveMany(data: DeepPartial<T>[]): Promise<T[]>;

  /**
   * Saves multiple entities with a transaction
   * @param transactionContext The transaction context
   * @param data The data to save the entities with
   * @returns The saved entities
   */
  saveManyWithTxn?(
    transactionContext: TransactionContext,
    data: DeepPartial<T>[],
  ): Promise<T[]>;

  /**
   * Finds an entity by its ID
   * @param id The ID of the entity to find
   * @returns The found entity
   */
  findOneById(id: string): Promise<T | null>;

  /**
   * Finds an entity by a condition
   * @param filterCondition The condition to find the entity by
   * @returns The found entity
   */
  findByCondition(filterCondition: FindOneOptions<T>): Promise<T | null>;

  /**
   * Finds all entities
   * @param options The options to find the entities by
   * @returns The found entities
   */
  findAll(options?: FindManyOptions<T>): Promise<T[]>;

  /**
   * Removes an entity
   * @param data The entity to remove
   * @returns The removed entity
   */
  remove(data: T): Promise<T>;

  /**
   * Removes an entity with a transaction
   * @param transactionContext The transaction context
   * @param data The entity to remove
   * @returns The removed entity
   */
  removeWithTxn?(transactionContext: TransactionContext, data: T): Promise<T>;

  /**
   * Soft deletes an entity
   * @param options The options to soft delete the entity with
   * @returns The soft deleted entity
   */
  softDelete(options: SoftDeleteOptions): Promise<UpdateResult>;

  /**
   * Soft deletes an entity with a transaction
   * @param transactionContext The transaction context
   * @param options The options to soft delete the entity with
   * @returns The soft deleted entity
   */
  softDeleteWithTxn?(
    transactionContext: TransactionContext,
    options: SoftDeleteOptions,
  ): Promise<UpdateResult>;

  /**
   * Finds entities with relations
   * @param relations The relations to find the entities with
   * @returns The found entities
   */
  findWithRelations(relations: FindManyOptions<T>): Promise<T[]>;

  /**
   * Preloads an entity
   * @param entityLike The entity to preload
   * @returns The preloaded entity
   */
  preload(entityLike: DeepPartial<T>): Promise<T | null>;

  /**
   * Executes a raw query and returns the results
   * @param query The raw query to execute
   * @returns The results of the query
   */
  queryRaw<TResult = any>(query: string, parameters?: any[]): Promise<TResult>;
}
