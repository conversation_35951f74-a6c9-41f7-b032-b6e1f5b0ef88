import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterTeamChannelsMappingAddUniqueConstraint1749041996464
  implements MigrationInterface
{
  name = 'AlterTeamChannelsMappingAddUniqueConstraint1749041996464';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."idx_platform_slack_channel_organization"`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_platform_slack_channel_organization" ON "platform_teams_to_channel_mappings" ("platform_team_id", "channel_id", "organization_id") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."idx_platform_slack_channel_organization"`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_platform_slack_channel_organization" ON "platform_teams_to_channel_mappings" ("platform_team_id", "channel_id", "organization_id") `,
    );
  }
}
