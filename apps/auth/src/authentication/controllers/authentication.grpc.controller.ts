import { <PERSON>ada<PERSON> } from '@grpc/grpc-js';
import {
  BadRequestException,
  Controller,
  Inject,
  NotFoundException,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { GrpcMethod, RpcException } from '@nestjs/microservices';
import { handleRpcError } from '@repo/nestjs-commons/errors';
import { ILogger } from '@repo/nestjs-commons/logger';
import {
  extractSupabaseUserMetadata,
  extractUserMetadata,
} from '@repo/nestjs-commons/utils';
import { authentication } from '@repo/shared-proto';
import { ApiKeyType, UserType } from '@repo/thena-platform-entities';
import { RegisterBotDto, RegisterCustomerDto } from '../dto';
import { GrpcAuthGuard } from '../grpc-auth.guard';
import { ApiKeyService } from '../services/api-key.service';
import { AuthenticationService } from '../services/authentication.service';
import {
  createBotUserValidator,
  createOrganizationAdminValidator,
  createUserValidator,
} from '../validators';

@Controller('v1/authentication')
export class AuthenticationGrpcController {
  constructor(
    @Inject('CustomLogger')
    private readonly logger: ILogger,
    private readonly authenticationService: AuthenticationService,
    private readonly apiKeyService: ApiKeyService,
  ) {}

  @GrpcMethod(authentication.AUTHENTICATION_SERVICE_NAME, 'SignIn')
  async signIn(data: authentication.SignInRequest) {
    try {
      // Validate the request
      if (!data.email || !data.password) {
        throw new BadRequestException('Invalid email or password!');
      }

      // Login the user
      const { accessToken, refreshToken } =
        await this.authenticationService.login(data);

      // Construct the response
      const response: authentication.SignInResponse = {
        token: accessToken,
        refreshToken: refreshToken,
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error as Error);
    }
  }

  @GrpcMethod(
    authentication.AUTHENTICATION_SERVICE_NAME,
    'ValidateAndGetUserByInternalIds',
  )
  async validateAndGetUserByInternalIds(
    _data: authentication.ValidateAndGetUserByInternalIdsRequest,
    metadata: Metadata,
  ): Promise<authentication.ValidateAndGetUserByInternalIdsResponse> {
    try {
      const userId = metadata.get('user_id')?.[0]?.toString();
      const orgId = metadata.get('org_id')?.[0]?.toString();

      const dbUser = await this.authenticationService.getUserForOrg(
        userId,
        orgId,
      );

      if (!dbUser) {
        throw new RpcException('User not found');
      }

      return {
        sub: dbUser.sub,
        authId: dbUser.authId,
        uid: dbUser.uid,
        email: dbUser.email,
        orgId: dbUser.orgId,
        orgUid: dbUser.orgUid,
        orgTier:
          authentication.OrganizationTier[
            dbUser.orgTier as keyof typeof authentication.OrganizationTier
          ],
        userType: dbUser.userType,
        timezone: dbUser.timezone,
        scopes: [],
      };
    } catch (error) {
      handleRpcError(error as Error);
    }
  }

  @UseGuards(GrpcAuthGuard)
  @GrpcMethod(authentication.AUTHENTICATION_SERVICE_NAME, 'GetSupabaseAuthUser')
  getSupabaseAuthUser(
    _data: authentication.GetSupabaseAuthUserRequest,
    metadata: Metadata,
  ) {
    try {
      // Get the supabase user
      const user = extractSupabaseUserMetadata(metadata);

      // Construct the response
      const payload: authentication.GetSupabaseAuthUserResponse = {
        id: user.id,
        email: user.email,
        phone: user.phone,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };

      // Return the response
      return payload;
    } catch (error) {
      handleRpcError(error as Error);
    }
  }

  @UseGuards(GrpcAuthGuard)
  @GrpcMethod(authentication.AUTHENTICATION_SERVICE_NAME, 'ValidateAndGetUser')
  async validateAndGetUser(
    _data: authentication.ValidateAndGetUserRequest,
    metadata: Metadata,
  ) {
    try {
      // Get access token from request
      const user = extractUserMetadata(metadata);

      // Validate and get user
      const { dbUser } = await this.authenticationService.validateAndGetUser(
        user,
      );

      const orgTierMap: Record<string, authentication.OrganizationTier> = {
        FREE: authentication.OrganizationTier.FREE,
        STANDARD: authentication.OrganizationTier.STANDARD,
        ENTERPRISE: authentication.OrganizationTier.ENTERPRISE,
      };

      // Construct the response
      const payload: authentication.ValidateAndGetUserResponse = {
        sub: dbUser.id,
        uid: dbUser.uid,
        email: dbUser.email,
        authId: dbUser.authId,
        timezone: dbUser.timezone,
        userType: dbUser.userType,
        orgId: dbUser.organization.id,
        orgUid: dbUser.organization.uid,
        orgTier:
          orgTierMap[dbUser.organization.tier] ??
          authentication.OrganizationTier.FREE,
        metadata: dbUser.metadata,
        scopes: [],
      };

      // Return the response
      return payload;
    } catch (error) {
      handleRpcError(error as Error);
    }
  }

  @GrpcMethod(authentication.AUTHENTICATION_SERVICE_NAME, 'CreateBotUser')
  async createBotUser(
    createBotUserRequest: authentication.CreateBotUserRequest,
  ) {
    try {
      // Validate the request
      const validationResult =
        createBotUserValidator.safeParse(createBotUserRequest);
      if (!validationResult.success) {
        let stringifiedErrors = '';
        try {
          stringifiedErrors = JSON.stringify(
            validationResult?.error?.flatten(),
          );
        } catch (error) {
          if (error instanceof Error) {
            this.logger.error(
              `Error encountered while stringifying the error: ${error.message}`,
            );
          }
        }

        this.logger.error(
          `Error encountered while creating bot user: ${validationResult.error.message}, flatten: ${stringifiedErrors}`,
        );

        throw new BadRequestException('Invalid user data provided!');
      }

      // Get validated user
      const validatedUser = validationResult.data;

      // Create user
      const registeredUserData = await this.authenticationService.register({
        name: validatedUser.name,
        email: validatedUser.email,
        password: validatedUser.password,
        organizationId: validatedUser.organizationUid,
        appId: validatedUser.appId,
        isBot: true,
        purpose: validatedUser.purpose,
        avatarUrl: validatedUser.avatarUrl,
      } as RegisterBotDto);

      // Get the user
      const { dbUser } = registeredUserData;

      // Construct the response
      const payload: authentication.CreateUserResponse = {
        id: dbUser.uid,
        name: dbUser.name,
        email: dbUser.email,
        userType: dbUser.userType,
      };

      // Return the response
      return payload;
    } catch (error) {
      handleRpcError(error as Error);
    }
  }

  @GrpcMethod(
    authentication.AUTHENTICATION_SERVICE_NAME,
    'CreateOrganizationAdmin',
  )
  async createOrgAdminUser(
    createUserRequest: authentication.CreateOrganizationAdminRequest,
  ) {
    try {
      // Validate the request
      const validationResult =
        createOrganizationAdminValidator.safeParse(createUserRequest);
      if (!validationResult.success) {
        throw new BadRequestException('Invalid user data provided!');
      }

      // Get validated user
      const validatedUser = validationResult.data;

      // Get the user name
      const userName = validatedUser.email.split('@')[0];

      // Create user
      const registeredUserData = await this.authenticationService.register({
        name: userName,
        email: validatedUser.email,
        password: validatedUser.password,
        organizationId: validatedUser.organizationUid,
        isOrgAdmin: true,
      } as RegisterCustomerDto);

      // Get the user
      const { dbUser } = registeredUserData;

      // Construct the response
      const payload: authentication.CreateUserResponse = {
        id: dbUser.uid,
        name: dbUser.name,
        email: dbUser.email,
        userType: dbUser.userType,
      };

      // Return the response
      return payload;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error encountered while creating organization admin: ${error.message}`,
          error?.stack,
        );
      }

      handleRpcError(error as Error);
    }
  }

  @UseGuards(GrpcAuthGuard)
  @GrpcMethod(
    authentication.AUTHENTICATION_SERVICE_NAME,
    'CreateUserWithoutAuth',
  )
  async createUserWithoutAuth(
    createUserRequest: authentication.CreateUserWithoutAuthRequest,
  ) {
    try {
      // Validate the request
      const validationResult = createUserValidator.safeParse(createUserRequest);
      if (!validationResult.success) {
        throw new BadRequestException('Invalid user data provided!');
      }

      // Get validated user
      const validatedUser = validationResult.data;

      // Create user
      const registeredUserData = await this.authenticationService.register({
        name: validatedUser.name,
        email: validatedUser.email,
        password: validatedUser.password,
        organizationId: validatedUser.organizationUid,
        isCustomer: validatedUser.isCustomer,
      } as RegisterCustomerDto);

      // Get the user
      const { dbUser } = registeredUserData;

      // Construct the response
      const payload: authentication.CreateUserResponse = {
        id: dbUser.uid,
        name: dbUser.name,
        email: dbUser.email,
        userType: dbUser.userType,
      };

      // Return the response
      return payload;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error encountered while creating user without auth: ${error.message}`,
          error?.stack,
        );
      }

      handleRpcError(error as Error);
    }
  }

  @UseGuards(GrpcAuthGuard)
  @GrpcMethod(authentication.AUTHENTICATION_SERVICE_NAME, 'CreateUser')
  async createUser(
    createUserRequest: authentication.CreateUserRequest,
    metadata: Metadata,
  ) {
    try {
      const user = extractUserMetadata(metadata);

      // Validate the request
      const validationResult = createUserValidator.safeParse(createUserRequest);
      if (!validationResult.success) {
        throw new BadRequestException('Invalid user data provided!');
      }

      // Get validated user
      const validatedUser = validationResult.data;

      // Check if the user belongs to this organization
      if (
        validatedUser.organizationUid !== user.orgUid &&
        user.userType !== UserType.ORG_ADMIN
      ) {
        throw new UnauthorizedException('Invalid access token!');
      }

      // Create user
      const registeredUserData = await this.authenticationService.register({
        name: validatedUser.name,
        email: validatedUser.email,
        password: validatedUser.password,
        organizationId: validatedUser.organizationUid,
        isCustomer: validatedUser.isCustomer,
        externalId: validatedUser.externalId,
      } as RegisterCustomerDto);

      // Get the user
      const { dbUser } = registeredUserData;

      // Construct the response
      const payload: authentication.CreateUserResponse = {
        id: dbUser.uid,
        name: dbUser.name,
        email: dbUser.email,
        userType: dbUser.userType,
      };

      // Return the response
      return payload;
    } catch (error) {
      if (error instanceof Error) {
        this.logger.error(
          `Error encountered while creating user: ${error.message}`,
          error?.stack,
        );
      }

      handleRpcError(error as Error);
    }
  }

  @GrpcMethod(
    authentication.AUTHENTICATION_SERVICE_NAME,
    'ValidateAndGetUserByApiKey',
  )
  async validateAndGetUserByApiKey(
    data: authentication.ValidateAndGetUserByApiKeyRequest,
  ) {
    try {
      const { apiKey } = data;
      const user = await this.apiKeyService.validateApiKey(apiKey, {
        ipAddress: '0.0.0.0',
        userAgent: 'unknown',
      });

      const orgTierMap: Record<string, authentication.OrganizationTier> = {
        FREE: authentication.OrganizationTier.FREE,
        STANDARD: authentication.OrganizationTier.STANDARD,
        ENTERPRISE: authentication.OrganizationTier.ENTERPRISE,
      };

      const returnUser = {
        sub: user.sub,
        authId: user.authId,
        uid: user.uid,
        email: user.email,
        orgId: user.orgId,
        orgUid: user.orgUid,
        orgTier: orgTierMap[user.orgTier],
        userType: user.userType,
        timezone: user.timezone,
        scopes: user.scopes,
      };

      if (user.isValid) {
        return returnUser;
      } else {
        throw new UnauthorizedException('Invalid API key');
      }
    } catch (error) {
      handleRpcError(error as Error);
    }
  }

  @UseGuards(GrpcAuthGuard)
  @GrpcMethod(authentication.AUTHENTICATION_SERVICE_NAME, 'CreateNewAPIKey')
  async createNewAPIKey(
    data: authentication.CreateNewAPIKeyRequest,
    metadata: Metadata,
  ) {
    try {
      const user = extractUserMetadata(metadata);

      // Create the API key for the user
      const { apiKey, secretKey } = await this.apiKeyService.createApiKey({
        name: data.name,
        userId: user.authId,
        organizationId: user.orgUid,
        type: ApiKeyType.LIVE,
        requestMetadata: {
          ipAddress: '0.0.0.0',
          userAgent: 'unknown',
        },
      });

      const response: authentication.CreateNewAPIKeyResponse = {
        apiKey: apiKey.keyId,
        secretKey,
      };

      // Return the response
      return response;
    } catch (error) {
      handleRpcError(error as Error);
    }
  }

  @GrpcMethod(
    authentication.AUTHENTICATION_SERVICE_NAME,
    'FetchApiKeyForBotUser',
  )
  async fetchApiKeyForBotUser(
    data: authentication.FetchApiKeyForBotUserRequest,
  ): Promise<authentication.FetchApiKeyForBotUserResponse> {
    try {
      if (!data.botUserId) {
        throw new BadRequestException('Bot user ID is required');
      }

      const apiKey = await this.apiKeyService.fetchBotToken(data.botUserId);

      if (!apiKey) {
        throw new NotFoundException('API key not found for the bot user');
      }

      return {
        botToken: apiKey,
      };
    } catch (error) {
      handleRpcError(error as Error);
    }
  }
}
