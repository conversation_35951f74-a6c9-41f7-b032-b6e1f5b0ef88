import { CSATRule, CSATSettings } from "@/types/csat";

export async function fetchCSATSettings(teamId: string): Promise<CSATSettings> {
  try {
    const response = await fetch(`/api/teams/${teamId}/csat-settings`);

    if (!response.ok) {
      throw new Error("Failed to fetch CSAT settings.");
    }
    const responseData = await response.json();
    return responseData;
  } catch {
    return { cooldownPeriodDays: 0, rules: [] };
  }
}

export async function fetchCSATRule(teamId: string, ruleId: string): Promise<CSATRule | null> {
  try {
    const response = await fetch(`/api/teams/${teamId}/csat-settings/rules/${ruleId}`);

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error("Failed to fetch CSAT rule.");
    }

    const rule = await response.json();
    return rule;
  } catch (error) {
    throw error;
  }
}

export async function createCSATRule(teamId: string, rule: Omit<CSATRule, "id" | "createdAt" | "updatedAt" | "priority">): Promise<CSATRule> {
  try {
    const response = await fetch(`/api/teams/${teamId}/csat-settings/rules`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(rule),
    });

    if (!response.ok) {
      throw new Error("Failed to create CSAT rule.");
    }

    const createdRule = await response.json();
    return createdRule;
  } catch (error) {
    throw error;
  }
}

export async function updateCSATRule(teamId: string, ruleId: string, rule: Partial<CSATRule>): Promise<CSATRule> {
  try {
    const { priority: _priority, ...rest } = rule;
    const response = await fetch(`/api/teams/${teamId}/csat-settings/rules/${ruleId}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(rest),
    });

    if (!response.ok) {
      throw new Error("Failed to update CSAT rule.");
    }

    const updatedRule = await response.json();
    return updatedRule;
  } catch (error) {
    throw error;
  }
}

export async function deleteCSATRule(teamId: string, ruleId: string): Promise<void> {
  try {
    const response = await fetch(`/api/teams/${teamId}/csat-settings/rules/${ruleId}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      throw new Error("Failed to delete CSAT rule.");
    }
  } catch (error) {
    throw error;
  }
}

export async function reorderCSATRules(teamId: string, reorderRules: Array<{ ruleId: string; priority: number }>): Promise<void> {
  try {
    const response = await fetch(`/api/teams/${teamId}/csat-settings/`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(reorderRules),
    });

    if (!response.ok) {
      throw new Error("Failed to reorder CSAT rules.");
    }
  } catch (error) {
    throw error;
  }
}

export async function updateCSATSettings(
  teamId: string,
  cooldownPeriodDays: number,
  emailConfigId: string,
  closedStatusIds: string[],
  userCooldownPeriodDays: number
): Promise<void> {
  const response = await fetch(`/api/teams/${teamId}/csat-settings/update`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ cooldownPeriodDays, emailConfigId, closedStatusIds, userCooldownPeriodDays }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update CSAT cooldown period');
  }

  return response.json();
}