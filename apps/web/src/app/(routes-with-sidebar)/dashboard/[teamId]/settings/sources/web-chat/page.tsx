"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ChevronDown, Co<PERSON>, Loader2, Plus, X } from "lucide-react";
import { useParams, useSearchParams } from "next/navigation";
import { useEffect, useMemo, useRef, useState } from "react";
import { toast } from "sonner";
import { WebChatAgentBanner } from "../../../../../../../components/agents/web-chat-agent-banner";
import CommonSelectWrapper from "../../../../../../../components/common-select-wrapper";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../../../../../../../components/ui/accordion";
import { Button } from "../../../../../../../components/ui/button";
import { Checkbox } from "../../../../../../../components/ui/checkbox";
import { Input } from "../../../../../../../components/ui/input";
import { Label } from "../../../../../../../components/ui/label";
import { AGENT_NAME } from "../../../../../../../constants/web-chat";
import { getAgents } from "../../../../../../../lib/api/agents";
import { Agent } from "../../../../../../../types/agent";
import { ColorPicker } from "../../../../../helpcenter/[helpcenterId]/settings/_components/color-picker";
import WebChatEmptyConfig from "./empty-config";
import WebSettingsEmptyState from "./empty-state";
// Get Base URL and WS Endpoint from environment variables
const baseUrl = process.env.NEXT_PUBLIC_AGENT_STUDIO_URL;
const wsEndpoint = process.env.NEXT_PUBLIC_AGENT_STUDIO_WS_URL;
const widgetUrl = process.env.NEXT_PUBLIC_WIDGET_CDN_URL;

interface WidgetSettings {
  targetElementId?: string;
  useCustomLauncher?: boolean;
  initialPosition?: {
    top?: string;
    left?: string;
    bottom?: string;
    right?: string;
  };
  themeColorStart?: string;
  themeColorEnd?: string;
  gradientDirection?: string;
  brandLogoUrl?: string;
  userContextExample?: {
    email?: string;
    name?: string;
  };
  darkMode?: boolean;
  autoclose?: boolean;
}

interface WidgetConfig {
  baseUrl: string;
  apiKey: string;
  agentId: string;
  wsEndpoint: string;
  useCustomLauncher: boolean;
  user?: {
    email?: string;
    name?: string;
    hash?: string;
  };
  initialPosition?: {
    top?: string;
    left?: string;
    bottom?: string;
    right?: string;
  };
  targetElementId?: string;
  themeColorStart?: string;
  themeColorEnd?: string;
  gradientDirection?: string;
  brandLogoUrl?: string;
  darkMode?: boolean;
  autoclose?: boolean;
  [key: string]: unknown;
}

export default function WebChatSettings() {
  const searchParams = useSearchParams();
  const params = useParams();
  const teamId = params.teamId as string;
  const agentId = searchParams.get("agentId");
  const [isLoadingDeployments, setIsLoadingDeployments] = useState(false);
  const [allowedDomains, setAllowedDomains] = useState<string[]>([]);
  const [selectedTeam, setSelectedTeam] = useState<string | null>(null);
  const [deploymentData, setDeploymentData] = useState(null);
  const [agentConfigurationPrompt, setAgentConfigurationPrompt] =
    useState<string>("");
  const [isSavingChatWidgetInstructions, setIsSavingChatWidgetInstructions] =
    useState(false);
  const queryClient = useQueryClient();

  // Add initial state tracking
  const [initialState, setInitialState] = useState<{
    targetElementId: string;
    useCustomLauncher: boolean;
    initialPositionTop?: string;
    initialPositionLeft?: string;
    initialPositionBottom?: string;
    initialPositionRight?: string;
    selectedPositionType: string;
    themeColorStart: string;
    themeColorEnd: string;
    brandLogoUrl: string;
    gradientDirection: string;
    deploymentMode: "floating" | "embedded";
    userEmail: string;
    userName: string;
    allowedDomains: string[];
    agentConfigurationPrompt: string;
    darkMode: boolean;
    autoclose: boolean;
  } | null>(null);

  // Widget specific settings states
  const [targetElementId, setTargetElementId] = useState<string>("");
  const [useCustomLauncher, setUseCustomLauncher] = useState<boolean>(false);
  const [initialPositionTop, setInitialPositionTop] = useState<
    string | undefined
  >(undefined);
  const [initialPositionLeft, setInitialPositionLeft] = useState<
    string | undefined
  >(undefined);
  const [initialPositionBottom, setInitialPositionBottom] = useState<
    string | undefined
  >(undefined);
  const [initialPositionRight, setInitialPositionRight] = useState<
    string | undefined
  >(undefined);
  const [selectedPositionType, setSelectedPositionType] =
    useState<string>("bottom-right");
  const [themeColorStart, setThemeColorStart] = useState<string>("#3B82F6");
  const [themeColorEnd, setThemeColorEnd] = useState<string>("#1D4ED8");
  const [gradientDirection, setGradientDirection] = useState<string>("135deg");
  const [brandLogoUrl, setBrandLogoUrl] = useState<string>("");
  const [deploymentMode, setDeploymentMode] = useState<"floating" | "embedded">(
    "floating",
  );
  const [userEmail, setUserEmail] = useState<string>("<EMAIL>");
  const [userName, setUserName] = useState<string>("Test User (HMAC)");
  const [darkMode, setDarkMode] = useState<boolean>(false);
  const [autoclose, setAutoclose] = useState<boolean>(false);
  const [isApiInfoOpen, setIsApiInfoOpen] = useState<boolean>(false);
  const { data: agents } = useQuery({
    queryKey: ["agents"],
    queryFn: () => getAgents(),
    refetchInterval: 10000, // Add polling every 10 seconds
    staleTime: 5000, // Consider data stale after 5 seconds
  });

  const agentDetails = useMemo(() => {
    return agents?.find(
      (agent) => agent.name === AGENT_NAME && agent.team_id.includes(teamId),
    );
  }, [agents]);

  const { data: agent, isLoading } = useQuery<Agent>({
    queryKey: ["agent", agentId],
    queryFn: async () => {
      const response = agentId
        ? await fetch(`/api/agents/${agentId}`)
        : await fetch(`/api/agents/${agentDetails?.id}`);
      if (!response.ok) throw new Error("Failed to fetch agent");
      return response.json();
    },
    enabled: !!agentId || !!agentDetails?.id,
  });

  // Add a ref to track if we've set initial state
  const hasSetInitialState = useRef(false);

  const [originalPositionConfig, setOriginalPositionConfig] = useState<{
    type: string;
    top?: string;
    left?: string;
    bottom?: string;
    right?: string;
  } | null>(null);

  const isBackToOriginalPosition = () => {
    if (!originalPositionConfig) return false;

    // If position type is different, it's definitely changed
    if (selectedPositionType !== originalPositionConfig.type) {
      return false;
    }

    // If we're in custom mode, check coordinates
    if (selectedPositionType === "custom") {
      return (
        initialPositionTop === originalPositionConfig.top &&
        initialPositionLeft === originalPositionConfig.left &&
        initialPositionBottom === originalPositionConfig.bottom &&
        initialPositionRight === originalPositionConfig.right
      );
    }

    // For preset positions, if the type matches, consider it as back to original
    // unless the original was also a preset with different coordinates
    if (originalPositionConfig.type !== "custom") {
      // Both are preset positions and types match
      return true;
    }

    return false;
  };

  // Updated customization changes function
  const hasCustomizationChanges = () => {
    if (!initialState || !originalPositionConfig) return false;

    // Simple field comparisons
    const simpleFieldChanges =
      themeColorStart !== initialState.themeColorStart ||
      themeColorEnd !== initialState.themeColorEnd ||
      gradientDirection !== initialState.gradientDirection ||
      brandLogoUrl !== initialState.brandLogoUrl ||
      deploymentMode !== initialState.deploymentMode ||
      targetElementId !== initialState.targetElementId ||
      useCustomLauncher !== initialState.useCustomLauncher ||
      darkMode !== initialState.darkMode ||
      autoclose !== initialState.autoclose;

    // Position change check
    const positionChanged = !isBackToOriginalPosition();

    return simpleFieldChanges || positionChanged;
  };

  // Updated deployment changes function (this one should be fine as is)
  const hasDeploymentChanges = () => {
    if (!initialState) return false;

    return (
      JSON.stringify(allowedDomains) !==
        JSON.stringify(initialState.allowedDomains) ||
      userEmail !== initialState.userEmail ||
      userName !== initialState.userName
    );
  };

  // Updated agent config changes function
  const hasAgentConfigChanges = () => {
    if (!initialState) return false;

    return agentConfigurationPrompt !== initialState.agentConfigurationPrompt;
  };

  useEffect(() => {
    async function fetchDeployments() {
      setIsLoadingDeployments(true);
      try {
        const response = agentId
          ? await fetch(`/api/agents/${agentId}/deployments`)
          : await fetch(`/api/agents/${agentDetails?.id}/deployments`);

        if (!response.ok) {
          const errorData = await response.json();
          console.error("Failed to fetch deployments", errorData);
          setIsLoadingDeployments(false);
          return;
        }

        const deployments = await response.json();
        setDeploymentData(deployments[0]);
        // If we have deployments for this agent, use the first one
        if (deployments && deployments.length > 0) {
          const latestDeployment = deployments[0];
          // If the deployment has allowed origins, update state
          if (
            latestDeployment.allowed_origins &&
            latestDeployment.allowed_origins.length > 0
          ) {
            setAllowedDomains(latestDeployment.allowed_origins);
          }

          // Set selected team based on team_id if available
          if (latestDeployment.team_id) {
            setSelectedTeam(latestDeployment.team_id);
          }

          // Populate widget settings states from latestDeployment.widget_settings
          if (latestDeployment.widget_settings) {
            const settings = latestDeployment.widget_settings;
            setTargetElementId(settings.targetElementId || "my-thena-widget");
            setUseCustomLauncher(
              settings.useCustomLauncher === undefined
                ? false
                : settings.useCustomLauncher,
            );
            if (settings.initialPosition) {
              setInitialPositionTop(settings.initialPosition.top);
              setInitialPositionLeft(settings.initialPosition.left);
              setInitialPositionBottom(settings.initialPosition.bottom);
              setInitialPositionRight(settings.initialPosition.right);
            }
            if (settings.themeColorStart && settings.themeColorEnd) {
              setThemeColorStart(settings.themeColorStart);
              setThemeColorEnd(settings.themeColorEnd);
            }
            setBrandLogoUrl(settings.brandLogoUrl || "");
            if (settings.userContextExample) {
              setUserEmail(
                settings.userContextExample.email || "<EMAIL>",
              );
              setUserName(
                settings.userContextExample.name || "Test User (HMAC)",
              );
            }
            // Determine deploymentMode based on targetElementId presence in fetched settings
            if (settings.targetElementId) {
              setDeploymentMode("embedded");
            } else {
              setDeploymentMode("floating");
            }
            if (settings.gradientDirection) {
              setGradientDirection(settings.gradientDirection);
            }
            if (settings.darkMode !== undefined) {
              setDarkMode(settings.darkMode);
            }
            if (settings.autoclose !== undefined) {
              setAutoclose(settings.autoclose);
            }
          }
        }
      } catch (err) {
        console.error("Error fetching deployments:", err);
      } finally {
        setIsLoadingDeployments(false);
      }
    }

    if (agentId || agentDetails?.id) {
      fetchDeployments();
    }
  }, [agentId, agentDetails]);

  useEffect(() => {
    if (
      !isLoadingDeployments &&
      deploymentData &&
      agent &&
      !hasSetInitialState.current
    ) {
      // Capture the original position configuration as it was loaded
      setOriginalPositionConfig({
        type: selectedPositionType,
        top: initialPositionTop,
        left: initialPositionLeft,
        bottom: initialPositionBottom,
        right: initialPositionRight,
      });

      setInitialState({
        targetElementId,
        useCustomLauncher,
        initialPositionTop,
        initialPositionLeft,
        initialPositionBottom,
        initialPositionRight,
        selectedPositionType,
        themeColorEnd,
        themeColorStart,
        gradientDirection,
        brandLogoUrl,
        deploymentMode,
        userEmail,
        userName,
        allowedDomains,
        agentConfigurationPrompt:
          agent?.configuration?.chat_widget_instructions || "",
        darkMode,
        autoclose,
      });
      hasSetInitialState.current = true;
    }
  }, [
    isLoadingDeployments,
    deploymentData,
    agent,
    targetElementId,
    useCustomLauncher,
    initialPositionTop,
    initialPositionLeft,
    initialPositionBottom,
    initialPositionRight,
    selectedPositionType,
    themeColorStart,
    themeColorEnd,
    gradientDirection,
    brandLogoUrl,
    deploymentMode,
    userEmail,
    userName,
    allowedDomains,
    agentConfigurationPrompt,
    darkMode,
    autoclose,
  ]);

  // Reset initial state when agent changes
  useEffect(() => {
    hasSetInitialState.current = false;
    setInitialState(null);
  }, [agentId, agentDetails?.id]);

  useEffect(() => {
    if (agent) {
      setAgentConfigurationPrompt(
        agent?.configuration?.chat_widget_instructions || "",
      );

      // Reset initial state flag so it gets recalculated with new agent data
      if (hasSetInitialState.current) {
        hasSetInitialState.current = false;
        setInitialState(null);
      }
    }
  }, [agent]);

  const generateWidgetCode = (apiKey: string, agentId: string) => {
    const thenaWidgetConfig: WidgetConfig = {
      baseUrl: baseUrl || "http://localhost:8008",
      apiKey: apiKey,
      agentId: agentId,
      wsEndpoint: wsEndpoint || "ws://localhost:8008",
      useCustomLauncher: useCustomLauncher,
    };

    const user =
      userEmail || userName
        ? {
            ...(userEmail ? { email: userEmail } : {}),
            ...(userName ? { name: userName } : {}),
            hash: "YOUR_SERVER_SIDE_GENERATED_HMAC_HASH",
          }
        : undefined;

    if (user) {
      thenaWidgetConfig.user = user;
    }

    if (deploymentMode === "floating") {
      const cleanedInitialPosition = Object.fromEntries(
        Object.entries({
          top: initialPositionTop,
          left: initialPositionLeft,
          bottom: initialPositionBottom,
          right: initialPositionRight,
        }).filter(([_, v]) => v != null),
      );

      if (Object.keys(cleanedInitialPosition).length > 0) {
        thenaWidgetConfig.initialPosition = cleanedInitialPosition;
      }
    } else if (deploymentMode === "embedded") {
      if (targetElementId) {
        thenaWidgetConfig.targetElementId = targetElementId;
      }
    }

    if (themeColorStart && themeColorEnd) {
      thenaWidgetConfig.themeColorStart = themeColorStart;
      thenaWidgetConfig.themeColorEnd = themeColorEnd;
    }
    if (gradientDirection) {
      thenaWidgetConfig.gradientDirection = gradientDirection;
    }
    if (brandLogoUrl) {
      thenaWidgetConfig.brandLogoUrl = brandLogoUrl;
    }
    if (darkMode !== undefined) {
      thenaWidgetConfig.darkMode = darkMode;
    }
    if (autoclose !== undefined) {
      thenaWidgetConfig.autoclose = autoclose;
    }

    // Function to convert the object to a nicely formatted string for the script tag
    const configToString = (obj: Record<string, unknown>, indent = "  ") => {
      let result = "{\n";
      const keys = Object.keys(obj);
      keys.forEach((key, index) => {
        const value = obj[key];
        result += `${indent}  ${key}: `;
        if (typeof value === "string") {
          result += `\'${value.replace(/'/g, "\\\\'")}\'`;
        } else if (typeof value === "boolean" || typeof value === "number") {
          result += value;
        } else if (typeof value === "object" && value !== null) {
          result += configToString(
            value as Record<string, unknown>,
            `${indent}  `,
          ); // Recursive call for nested objects
        } else if (value === undefined) {
          // Skip undefined values
          result = result.substring(
            0,
            result.lastIndexOf(`${indent}  ${key}: `),
          ); // Remove the key line
          return; // and skip adding comma
        } else {
          result += "null";
        }
        if (
          index < keys.length - 1 &&
          !(
            value === undefined &&
            keys.slice(index + 1).every((k) => obj[k] === undefined)
          )
        ) {
          // Add comma if not the last real key
          const nextRealKeyIndex = keys.findIndex(
            (k, i) => i > index && obj[k] !== undefined,
          );
          if (nextRealKeyIndex !== -1) {
            result += ",\n";
          }
        } else if (value !== undefined) {
          result += "\n";
        }
      });
      result += `${indent.substring(0, indent.length - 2)}}`;
      return result;
    };

    const configString = configToString(thenaWidgetConfig);

    return `<script>\n  window.thenaWidget = ${configString};\n</script>\n<script src="${(
      widgetUrl || "https://widget.thena.tools/shim.js"
    ).replace(/'/g, "\\\\'")}"></script>`;
  };

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        console.log(`${type} copied to clipboard.`);
      },
      (err) => {
        console.error(`Could not copy ${type}: ${err}`);
      },
    );
  };

  const handleDeploy = async () => {
    try {
      const widgetSettingsForApi: WidgetSettings = {
        useCustomLauncher: useCustomLauncher,
        themeColorStart: themeColorStart || undefined,
        themeColorEnd: themeColorEnd || undefined,
        gradientDirection: gradientDirection || undefined,
        brandLogoUrl: brandLogoUrl || undefined,
        userContextExample: {
          email: userEmail || undefined,
          name: userName || undefined,
        },
        darkMode: darkMode,
        autoclose: autoclose,
      };

      if (deploymentMode === "floating") {
        const cleanedInitialPosition = Object.fromEntries(
          Object.entries({
            top: initialPositionTop,
            left: initialPositionLeft,
            bottom: initialPositionBottom,
            right: initialPositionRight,
          }).filter(([_, v]) => v != null),
        );
        if (Object.keys(cleanedInitialPosition).length > 0) {
          widgetSettingsForApi.initialPosition = cleanedInitialPosition;
        }
      } else if (deploymentMode === "embedded") {
        widgetSettingsForApi.targetElementId = targetElementId;
      }
      // Remove userContextExample if both fields are empty
      if (
        !widgetSettingsForApi.userContextExample.email &&
        !widgetSettingsForApi.userContextExample.name
      ) {
        delete widgetSettingsForApi.userContextExample;
      }

      const response = await fetch(`/api/agents/${agent.id}/deployments`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          team_id: selectedTeam,
          allowed_origins: allowedDomains,
          deployment_type: "widget",
          widget_settings: widgetSettingsForApi,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Deployment failed");
      }

      toast.success(
        "Deployment successful! Your widget configuration is ready.",
      );
      setDeploymentData(result);
      // Update initial state to current values
      const newInitialState = {
        targetElementId,
        useCustomLauncher,
        initialPositionTop,
        initialPositionLeft,
        initialPositionBottom,
        initialPositionRight,
        selectedPositionType,
        themeColorEnd,
        themeColorStart,
        gradientDirection,
        brandLogoUrl,
        deploymentMode,
        userEmail,
        userName,
        allowedDomains: [...allowedDomains], // Create new array to avoid reference issues
        agentConfigurationPrompt:
          agent?.configuration?.chat_widget_instructions || "",
        darkMode,
        autoclose,
      };

      setInitialState(newInitialState);

      // Update original position config to current position
      const newOriginalPositionConfig = {
        type: selectedPositionType,
        top: initialPositionTop,
        left: initialPositionLeft,
        bottom: initialPositionBottom,
        right: initialPositionRight,
      };

      setOriginalPositionConfig(newOriginalPositionConfig);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An unexpected error occurred.";
      toast.error(errorMessage);
    }
  };
  const handleDomainChange = (index: number, value: string) => {
    const newDomains = [...allowedDomains];
    newDomains[index] = value;
    setAllowedDomains(newDomains);
  };

  const addDomain = () => {
    setAllowedDomains([...allowedDomains, ""]);
  };

  const removeDomain = (index: number) => {
    setAllowedDomains(allowedDomains.filter((_, i) => i !== index));
  };

  const updateAgentConfigMutation = useMutation({
    mutationFn: async (newInstructions: string) => {
      if (!agent?.id) throw new Error("Agent data not available for update.");
      setIsSavingChatWidgetInstructions(true);
      const updatedConfiguration = {
        ...agent.configuration,
        chat_widget_instructions: newInstructions,
      };

      const response = await fetch(`/api/agents/${agent.id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: agent.name,
          status: agent.status,
          configuration: updatedConfiguration,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || "Failed to update agent configuration",
        );
      }
      return response.json();
    },
    onSuccess: () => {
      toast.success("Chat widget instructions saved successfully.");
      queryClient.invalidateQueries({ queryKey: ["agent", agent.id] });
      setIsSavingChatWidgetInstructions(false);
      if (initialState) {
        setInitialState({
          ...initialState,
          agentConfigurationPrompt: agentConfigurationPrompt, // Set to current value
        });
      }
    },
    onError: (error: Error) => {
      toast.error(`Error saving instructions: ${error.message}`);
      // Reset to initial state value, not current agent data
      if (initialState) {
        setAgentConfigurationPrompt(initialState.agentConfigurationPrompt);
      }
      setIsSavingChatWidgetInstructions(false);
    },
  });

  if (isLoadingDeployments) {
    return;
  }

  if (!deploymentData) {
    return <WebChatEmptyConfig />;
  }

  if (teamId !== deploymentData?.team_id) {
    return <WebSettingsEmptyState />;
  }

  return (
    <div className="min-h-[calc(100vh-14rem)] flex flex-col">
      <div className="px-6 pt-14 pb-20 h-full overflow-y-auto overflow-x-hidden">
        <div className="grid gap-6 mx-auto max-w-[640px] pb-16 w-full">
          <div>
            <h2 className="text-2xl font-medium">Web chat</h2>
            <p className="text-sm text-[var(--color-text-muted)] mt-1">
              Embed our AI-native widget in your product for instant support.
            </p>
          </div>

          <Accordion
            type="single"
            collapsible
            defaultValue="customization"
            className="w-full space-y-4"
          >
            <AccordionItem
              value="customization"
              className="border border-border rounded-sm overflow-hidden"
            >
              <AccordionTrigger className="px-4 py-3 hover:bg-muted/50 transition-colors rounded-t-sm">
                <span className="text-base font-medium">Set customization</span>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4">
                <div className="space-y-6">
                  <div>
                    <label className="text-sm font-medium text-color-text block mb-1">
                      Logo
                    </label>
                    <p className="text-sm text-[var(--color-text-muted)] mb-3">
                      Add your company logo using a public URL (SVG, PNG, JPG,
                      JPEG, WebP supported).
                    </p>
                    <Input
                      id="brand-logo-url"
                      type="url"
                      value={brandLogoUrl}
                      className="w-full h-10"
                      onChange={(e) => setBrandLogoUrl(e.target.value)}
                      placeholder="https://example.com/logo.svg"
                      disabled={isLoading || isLoadingDeployments}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium text-color-text block mb-1">
                      Theme color
                    </label>
                    <p className="text-sm text-[var(--color-text-muted)] mb-3">
                      Pick a color to match your brand. This will be used across
                      the chat widget and interface.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <label className="text-sm text-color-text mb-2 block">
                          Theme Color Start (hex)
                        </label>
                        <div className="flex gap-3 items-center border border-border rounded-md px-3 py-2 h-10 bg-background hover:border-border-hover transition-colors">
                          <ColorPicker
                            color={themeColorStart}
                            setColor={(color) => setThemeColorStart(color)}
                          />
                          <span className="text-sm text-color-text font-mono">
                            {themeColorStart || "#3B82F6"}
                          </span>
                        </div>
                      </div>
                      <div>
                        <label className="text-sm text-color-text mb-2 block">
                          Theme Color End (hex)
                        </label>
                        <div className="flex gap-3 items-center border border-border rounded-md px-3 py-2 h-10 bg-background hover:border-border-hover transition-colors">
                          <ColorPicker
                            color={themeColorEnd}
                            setColor={(color) => setThemeColorEnd(color)}
                          />
                          <span className="text-sm text-color-text font-mono">
                            {themeColorEnd || "#1D4ED8"}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label
                      className="text-sm font-medium text-color-text block mb-2"
                      htmlFor="gradient-direction"
                    >
                      Gradient direction
                    </label>
                    <Input
                      id="gradient-direction"
                      value={gradientDirection}
                      onChange={(e) => setGradientDirection(e.target.value)}
                      placeholder="135deg"
                      disabled={isLoading || isLoadingDeployments}
                      className="w-full h-10 mb-2"
                    />
                    <p className="text-sm text-[var(--color-text-muted)]">
                      Examples: To right 45deg. Uses solid color if end color is
                      empty.
                    </p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-color-text block mb-1">
                      Position
                    </label>
                    <p className="text-sm text-[var(--color-text-muted)] mb-3">
                      Choose where the chat widget should appear on your site.
                    </p>

                    <div className="mb-4">
                      <CommonSelectWrapper
                        placeholder="Select position"
                        hideClearIndicator
                        isClearable={false}
                        options={[
                          { value: "bottom-right", label: "Bottom right" },
                          { value: "bottom-left", label: "Bottom left" },
                          { value: "top-right", label: "Top right" },
                          { value: "top-left", label: "Top left" },
                          { value: "custom", label: "Custom position" },
                        ]}
                        name="position"
                        value={{
                          value: selectedPositionType,
                          label:
                            selectedPositionType === "bottom-right"
                              ? "Bottom right"
                              : selectedPositionType === "bottom-left"
                              ? "Bottom left"
                              : selectedPositionType === "top-right"
                              ? "Top right"
                              : selectedPositionType === "top-left"
                              ? "Top left"
                              : "Custom position",
                        }}
                        onChange={(value) => {
                          const position = value as {
                            value: string;
                            label: string;
                          };
                          setSelectedPositionType(position.value);
                          switch (position.value) {
                            case "bottom-right":
                              setInitialPositionBottom("30px");
                              setInitialPositionRight("35px");
                              setInitialPositionLeft(undefined);
                              setInitialPositionTop(undefined);
                              break;
                            case "bottom-left":
                              setInitialPositionBottom("30px");
                              setInitialPositionLeft("35px");
                              setInitialPositionRight(undefined);
                              setInitialPositionTop(undefined);
                              break;
                            case "top-right":
                              setInitialPositionTop("30px");
                              setInitialPositionRight("35px");
                              setInitialPositionLeft(undefined);
                              setInitialPositionBottom(undefined);
                              break;
                            case "top-left":
                              setInitialPositionTop("30px");
                              setInitialPositionLeft("35px");
                              setInitialPositionRight(undefined);
                              setInitialPositionBottom(undefined);
                              break;
                            case "custom":
                              // Do not change values, allow user to edit
                              break;
                          }
                        }}
                        isVirtualized={false}
                        isOptionsMemoized
                        labelClass="w-full h-10 !min-h-10 !py-0 flex items-center"
                        wrapperClassname="w-full"
                      />
                    </div>

                    {selectedPositionType === "custom" && (
                      <div className="grid grid-cols-4 gap-4 mb-4">
                        <div>
                          <label className="text-sm text-color-text mb-2 block">
                            Left
                          </label>
                          <Input
                            type="text"
                            value={initialPositionLeft}
                            onChange={(e) =>
                              setInitialPositionLeft(e.target.value)
                            }
                            placeholder="px"
                            className="w-full h-10"
                          />
                        </div>
                        <div>
                          <label className="text-sm text-color-text mb-2 block">
                            Right
                          </label>
                          <Input
                            type="text"
                            value={initialPositionRight}
                            onChange={(e) =>
                              setInitialPositionRight(e.target.value)
                            }
                            placeholder="px"
                            className="w-full h-10"
                          />
                        </div>
                        <div>
                          <label className="text-sm text-color-text mb-2 block">
                            Top
                          </label>
                          <Input
                            type="text"
                            value={initialPositionTop}
                            onChange={(e) =>
                              setInitialPositionTop(e.target.value)
                            }
                            placeholder="px"
                            className="w-full h-10"
                          />
                        </div>
                        <div>
                          <label className="text-sm text-color-text mb-2 block">
                            Bottom
                          </label>
                          <Input
                            type="text"
                            value={initialPositionBottom}
                            onChange={(e) =>
                              setInitialPositionBottom(e.target.value)
                            }
                            placeholder="px"
                            className="w-full h-10"
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="text-sm font-medium text-color-text block mb-1">
                      Widget type
                    </label>
                    <p className="text-sm text-[var(--color-text-muted)] mb-3">
                      Select the default chat experience. Use Thena&apos;s
                      standard widget or link it to a custom element on your
                      site.
                    </p>
                    <div className="mb-4">
                      <CommonSelectWrapper
                        placeholder="Select widget type"
                        hideClearIndicator
                        isClearable={false}
                        options={[
                          {
                            value: "floating",
                            label: "Thena's default widget",
                          },
                          { value: "embedded", label: "Custom embed" },
                        ]}
                        name="widget-type"
                        value={{
                          value: deploymentMode,
                          label:
                            deploymentMode === "floating"
                              ? "Thena's default widget"
                              : "Custom embed",
                        }}
                        onChange={(value) => {
                          const option = value as {
                            value: string;
                            label: string;
                          };
                          setDeploymentMode(
                            option.value as "floating" | "embedded",
                          );
                        }}
                        isVirtualized={false}
                        isOptionsMemoized
                        labelClass="w-full h-10 !min-h-10 !py-0 flex items-center"
                        wrapperClassname="w-full"
                      />
                    </div>
                    {deploymentMode === "embedded" && (
                      <div className="mb-4">
                        <label className="text-sm font-medium text-color-text flex items-center gap-1 mb-1">
                          Target element ID
                          <span
                            className="ml-1 text-sm text-[var(--color-text-muted)] cursor-pointer"
                            tabIndex={0}
                            aria-label="Specify the ID element where the chat widget should be embedded."
                          >
                            &#9432;
                          </span>
                        </label>
                        <p className="text-sm text-[var(--color-text-muted)] mb-3">
                          Specify the ID element where the chat widget should be
                          embedded.
                        </p>
                        <Input
                          type="text"
                          value={targetElementId}
                          onChange={(e) => setTargetElementId(e.target.value)}
                          placeholder="e.g., my-thena-widget"
                          className="w-full h-10"
                          aria-label="Target element ID"
                        />
                      </div>
                    )}
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <Checkbox
                        id="use-custom-launcher"
                        checked={useCustomLauncher}
                        onCheckedChange={(checkedState) =>
                          setUseCustomLauncher(
                            checkedState === true ||
                              checkedState === "indeterminate"
                              ? true
                              : false,
                          )
                        }
                        disabled={isLoading || isLoadingDeployments}
                        className="mt-1"
                      />
                      <div className="space-y-1">
                        <Label
                          htmlFor="use-custom-launcher"
                          className="text-sm font-medium cursor-pointer text-color-text"
                        >
                          Use custom launcher button
                        </Label>
                        <p className="text-xs text-[var(--color-text-muted)]">
                          Hide the default widget launcher and use your own
                          custom button to open the chat widget.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <Checkbox
                        id="dark-mode"
                        checked={darkMode}
                        onCheckedChange={(checkedState) =>
                          setDarkMode(checkedState === true)
                        }
                        disabled={isLoading || isLoadingDeployments}
                        className="mt-1"
                      />
                      <div className="space-y-1">
                        <Label
                          htmlFor="dark-mode"
                          className="text-sm font-medium cursor-pointer text-color-text"
                        >
                          Initial dark mode
                        </Label>
                        <p className="text-xs text-[var(--color-text-muted)]">
                          Widget will open in dark mode by default.
                        </p>
                      </div>
                    </div>

                    {deploymentMode === "floating" && (
                      <div className="flex items-start space-x-3">
                        <Checkbox
                          id="autoclose-widget"
                          checked={autoclose}
                          onCheckedChange={(checkedState) =>
                            setAutoclose(checkedState === true)
                          }
                          disabled={isLoading || isLoadingDeployments}
                          className="mt-1"
                        />
                        <div className="space-y-1">
                          <Label
                            htmlFor="autoclose-widget"
                            className="text-sm font-medium cursor-pointer text-color-text"
                          >
                            Auto-close on outside click
                          </Label>
                          <p className="text-xs text-[var(--color-text-muted)]">
                            Automatically close the widget when users click
                            outside of it.
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* API information */}
                  <div className="mt-6 p-4 border rounded-sm bg-muted/50 space-y-3">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-color-text">
                        Using the widget JavaScript API
                      </p>
                      <button
                        onClick={() => setIsApiInfoOpen(!isApiInfoOpen)}
                        className="flex items-center gap-1 text-xs text-[var(--color-text-muted)] hover:text-color-text transition-colors"
                      >
                        {isApiInfoOpen ? "Read less" : "Read more"}
                        <ChevronDown
                          className={`h-3 w-3 transition-transform ${
                            isApiInfoOpen ? "rotate-180" : ""
                          }`}
                        />
                      </button>
                    </div>

                    {isApiInfoOpen && (
                      <div className="space-y-3">
                        <p className="text-sm text-[var(--color-text-muted)]">
                          You can control the widget programmatically using the
                          following JavaScript functions on the{" "}
                          <code>window.thena</code> object:
                        </p>
                        <ul className="list-disc pl-5 space-y-1 text-sm text-[var(--color-text-muted)]">
                          <li>
                            <code>window.thena.open()</code> - Opens the widget.
                          </li>
                          <li>
                            <code>window.thena.close()</code> - Closes the
                            widget.
                          </li>
                          <li>
                            <code>window.thena.toggle()</code> - Toggles the
                            widget visibility.
                          </li>
                          <li>
                            <code>window.thena.toggleDarkMode()</code> - Toggles
                            between light and dark themes.
                          </li>
                          <li>
                            <code>window.thena.toggleVisibility()</code> -
                            Shows/hides the widget and its launcher (if default
                            launcher is used).
                          </li>
                        </ul>
                        <p className="text-sm text-[var(--color-text-muted)]">
                          Example (for a custom button with ID
                          &apos;myChatButton&apos; to open the chat):
                        </p>
                        <pre className="mt-2 p-3 rounded-sm bg-background font-mono text-xs overflow-x-auto max-w-full whitespace-pre-wrap break-all">
                          {`document.getElementById('myChatButton').addEventListener('click', function() {\n  if (window.thena && window.thena.open) {\n    window.thena.open();\n  }\n});`}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>

                <div className="w-full flex justify-end mt-6">
                  <Button
                    onClick={handleDeploy}
                    disabled={
                      isLoading ||
                      isLoadingDeployments ||
                      !hasCustomizationChanges()
                    }
                    className="h-10 px-6 text-sm"
                  >
                    {isLoading && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Save
                  </Button>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem
              value="deployment"
              className="border border-border rounded-sm overflow-hidden"
            >
              <AccordionTrigger className="px-4 py-3 hover:bg-muted/50 transition-colors rounded-t-sm">
                <span className="text-base font-medium">Deploy web chat</span>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4">
                <div className="space-y-6">
                  <div>
                    <label className="text-sm font-medium text-color-text block mb-1">
                      Allowed domains
                    </label>
                    <p className="text-sm text-[var(--color-text-muted)] mb-3">
                      Enter the full domains (including http/https) where your
                      widget will be hosted. This helps secure your deployment.
                    </p>

                    <div className="space-y-3">
                      {allowedDomains.map((domain, index) => (
                        <div key={index} className="flex items-center gap-3">
                          <Input
                            type="text"
                            value={domain}
                            onChange={(e) =>
                              handleDomainChange(index, e.target.value)
                            }
                            placeholder="https://www.example.com"
                            className="flex-grow h-10"
                            disabled={isLoading || isLoadingDeployments}
                          />
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => removeDomain(index)}
                            aria-label="Remove domain"
                            disabled={isLoading || isLoadingDeployments}
                            className="h-10 w-10"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                    <div className="flex gap-2 mt-4">
                      <Button
                        variant="outline"
                        onClick={addDomain}
                        disabled={isLoading || isLoadingDeployments}
                        className="h-10 px-4 text-sm"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add domain
                      </Button>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-color-text block mb-1">
                      Code snippet
                    </label>
                    <p className="text-sm text-[var(--color-text-muted)] mb-3">
                      Add this code to your website&apos;s HTML, just before the
                      closing {`</body>`} tag. Customize the appearance and
                      behavior using the configuration options.
                    </p>
                    <div className="space-y-4">
                      <div className="relative">
                        <pre className="p-4 rounded-sm bg-muted font-mono text-sm overflow-x-auto whitespace-pre-wrap break-all min-h-[120px]">
                          {deploymentData &&
                            deploymentData.agent_key &&
                            deploymentData.agent_id &&
                            generateWidgetCode(
                              deploymentData.agent_key,
                              deploymentData.agent_id,
                            )}
                        </pre>
                        {deploymentData &&
                          deploymentData.agent_key &&
                          deploymentData.agent_id && (
                            <Button
                              size="sm"
                              variant="ghost"
                              className="absolute top-2 right-2 h-8 w-8 p-0"
                              onClick={() => {
                                copyToClipboard(
                                  generateWidgetCode(
                                    deploymentData.agent_key,
                                    deploymentData.agent_id,
                                  ),
                                  "Widget Code",
                                );
                                toast.success(
                                  "Widget code copied to clipboard!",
                                );
                              }}
                            >
                              <Copy className="h-4 w-4" />
                              <span className="sr-only">Copy code</span>
                            </Button>
                          )}
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-color-text block mb-2">
                      HMAC secret key
                    </label>
                    <p className="text-sm text-[var(--color-text-muted)] mb-2">
                      This key is shown ONLY ONCE. Store it securely on your
                      server and never expose it client-side.
                    </p>

                    <div className="relative flex items-center">
                      <Input
                        id="hmac-key-display"
                        readOnly
                        value={deploymentData?.hmac_secret_key || ""}
                        className="font-mono text-sm pr-12 bg-background h-10"
                      />
                      {deploymentData?.hmac_secret_key !==
                        "Secret key only shown during creation" && (
                        <Button
                          size="sm"
                          variant="ghost"
                          className="absolute right-1 h-8 w-8 p-0"
                          onClick={() => {
                            copyToClipboard(
                              deploymentData?.hmac_secret_key,
                              "HMAC Secret Key",
                            );
                            toast.success(
                              "HMAC secret key copied to clipboard!",
                            );
                          }}
                        >
                          <Copy className="h-4 w-4" />
                          <span className="sr-only">Copy HMAC Secret Key</span>
                        </Button>
                      )}
                    </div>
                  </div>
                </div>

                <div className="w-full flex justify-end mt-6">
                  <Button
                    onClick={handleDeploy}
                    disabled={
                      isLoading ||
                      isLoadingDeployments ||
                      !hasDeploymentChanges()
                    }
                    className="h-10 px-6 text-sm"
                  >
                    {isLoading && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Save
                  </Button>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem
              value="configuration"
              className="border border-border rounded-sm overflow-hidden"
            >
              <AccordionTrigger className="px-4 py-3 hover:bg-muted/50 transition-colors rounded-t-sm">
                <span className="text-base font-medium">
                  Agent configuration prompt
                </span>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4">
                <div className="space-y-4">
                  <textarea
                    id="agent-configuration-prompt"
                    className="w-full p-3 text-sm border border-border rounded-sm resize-y min-h-[120px] bg-background focus:ring-1 focus:ring-primary/30 focus:border-primary/30 outline-none transition-colors"
                    value={agentConfigurationPrompt}
                    placeholder={`Set ${agent?.name}'s support style and handoff rules. Define tone, response format, and when to escalate to your team.`}
                    onChange={(e) => {
                      setAgentConfigurationPrompt(e.target.value);
                    }}
                  />
                  <div className="flex justify-end">
                    <Button
                      onClick={() =>
                        updateAgentConfigMutation.mutate(
                          agentConfigurationPrompt,
                        )
                      }
                      disabled={
                        isSavingChatWidgetInstructions ||
                        !hasAgentConfigChanges()
                      }
                      className="h-10 px-6 text-sm"
                    >
                      {isSavingChatWidgetInstructions && (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      )}
                      Save
                    </Button>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          {agent && (
            <WebChatAgentBanner AGENT_NAME={AGENT_NAME} agentDetails={agent} />
          )}
        </div>
      </div>
    </div>
  );
}
