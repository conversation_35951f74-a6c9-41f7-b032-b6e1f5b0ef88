"use client";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/thena-loader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { fetchCSATSettings, reorderCSATRules, updateCSATSettings } from "@/services/csat-service";
import { useKanbanStore } from "@/store/kanbanStore";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { CSATRule, CSATSettings as CSATSettingsType, DefaultEmailType, DomainType, EmailDataType, EmailResponseItemType } from "@/types/csat";
import { getLaneIcon } from "@/utils/kanban";
import { DragDropContext, Draggable, Droppable, DropResult } from "@hello-pangea/dnd";
import { useFlags } from "launchdarkly-react-client-sdk";
import { AlertCircle, ArrowUpDown, Plus, Search, Star, X } from "lucide-react";
import { useRouter } from "next/navigation";
import * as React from "react";
import { toast } from "sonner";
import { CSATRuleCard } from "../../../../../../components/csat/csat-rule-card";

function ComingSoon() {
  return (
    <div className="space-y-6 p-6 max-w-[640px] mx-auto">
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="max-w-md w-full p-8">
          <div className="space-y-6 text-center">
            <div className="relative">
              <div className="absolute inset-0 bg-primary/5 animate-pulse rounded-[var(--radius-sm)] blur-xl" />
              <div className="relative mx-auto w-20 h-20 flex items-center justify-center bg-primary/10 rounded-[var(--radius-sm)]">
                <Star className="h-10 w-10 text-primary/80 transition-transform duration-200 hover:scale-110" />
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="text-xl font-semibold tracking-tight">
                CSAT coming soon
              </h3>
              <p className="text-muted-foreground text-sm">
                We&apos;re working on customer satisfaction surveys to help you gather feedback and improve your service quality.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function CSATSettings({
  params,
}: {
  params: Promise<{ teamId: string }>;
}) {
  const resolvedParams = React.use(params);
  const teamId = resolvedParams.teamId;
  const router = useRouter();
  const [isLoading, setIsLoading] = React.useState(true);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [dataFetched, setDataFetched] = React.useState(false);

  const [settings, setSettings] = React.useState<CSATSettingsType>({
    rules: [],
    cooldownPeriodDays: 14,
    userCooldownPeriodDays: 10,
    emailConfigId: undefined,
    closedStatusIds: []
  });

  const [isReordering, setIsReordering] = React.useState(false);
  const [orderedRules, setOrderedRules] = React.useState<CSATRule[]>([]);

  const [defaultEmail, setDefaultEmail] = React.useState<DefaultEmailType | null>(null);

  const [isSettingsModalOpen, setIsSettingsModalOpen] = React.useState(false);
  const [cooldownPeriodDays, setCooldownPeriodDays] = React.useState<number | undefined>();
  const [userCooldownPeriodDays, setUserCooldownPeriodDays] = React.useState<number | undefined>();

  const [domains, setDomains] = React.useState<string[]>([]);
  const [verifiedEmails, setVerifiedEmails] = React.useState<EmailDataType[]>([]);
  const [selectedDomain, setSelectedDomain] = React.useState("");
  const [selectedEmailId, setSelectedEmailId] = React.useState("");
  const [selectedEmailAddress, setSelectedEmailAddress] = React.useState("");
  const [allEmails, setAllEmails] = React.useState<EmailDataType[]>([]);
  const [useDefaultEmail, setUseDefaultEmail] = React.useState(true);

  // Status selection states
  const allTeamStatuses = useTicketMetaStore((state) => state.statuses);
  const statusOrder = useKanbanStore((state) => state.statusOrder);
  const [selectedClosedStatusIds, setSelectedClosedStatusIds] = React.useState<string[]>([]);
  const [statusValidationError, setStatusValidationError] = React.useState<string>("");
  const [statusDropdownOpen, setStatusDropdownOpen] = React.useState(false);

  // Search state
  const [searchQuery, setSearchQuery] = React.useState("");

  // Get closed sub-statuses
  const closedSubStatuses = React.useMemo(() => {
    const closedParentStatus = allTeamStatuses.find(
      (status) => status.name.toLowerCase() === 'closed' && !status.parent_status
    );

    if (!closedParentStatus) {
      return [];
    }

    const closedStatusOrder = statusOrder[closedParentStatus.uid];
    if (!closedStatusOrder?.sub_statuses) {
      return [];
    }

    return closedStatusOrder.sub_statuses
      .map(subStatusId => allTeamStatuses.find(status => status.uid === subStatusId))
      .filter(Boolean);
  }, [allTeamStatuses, statusOrder]);

  const hasClosedSubStatuses = closedSubStatuses.length > 0;

  const selectedStatuses = React.useMemo(() => {
    if (!selectedClosedStatusIds || selectedClosedStatusIds.length === 0) return [];
    return selectedClosedStatusIds
      .map(id => closedSubStatuses.find(s => s.id.toString() === id))
      .filter(Boolean);
  }, [selectedClosedStatusIds, closedSubStatuses]);

  const availableStatuses = React.useMemo(() => {
    return closedSubStatuses.filter(status =>
      !selectedClosedStatusIds.includes(status.id.toString())
    );
  }, [closedSubStatuses, selectedClosedStatusIds]);

  const addStatus = (statusId: string) => {
    if (!selectedClosedStatusIds.includes(statusId)) {
      setSelectedClosedStatusIds([...selectedClosedStatusIds, statusId]);
      // Clear validation error when user adds status
      if (statusValidationError) {
        setStatusValidationError("");
      }
    }
    setStatusDropdownOpen(false);
  };

  const removeStatus = (statusId: string) => {
    setSelectedClosedStatusIds(selectedClosedStatusIds.filter(id => id !== statusId));
  };

  // Initialize with default status selected
  React.useEffect(() => {
    if (hasClosedSubStatuses && selectedClosedStatusIds.length === 0 && settings.closedStatusIds?.length === 0) {
      // Find the default status and pre-select it
      const defaultStatus = closedSubStatuses.find(status => status.is_default);
      if (defaultStatus) {
        setSelectedClosedStatusIds([defaultStatus.id.toString()]);
      }
    }
  }, [hasClosedSubStatuses, closedSubStatuses, selectedClosedStatusIds.length, settings.closedStatusIds]);

  // Filter rules based on search query
  const filteredRules = React.useMemo(() => {
    if (!searchQuery.trim()) {
      return settings.rules;
    }

    const query = searchQuery.toLowerCase();
    return settings.rules.filter(rule =>
      rule.name.toLowerCase().includes(query) ||
      rule.description?.toLowerCase().includes(query)
    );
  }, [settings.rules, searchQuery]);

  const ldFlags = useFlags();
  const isCSATEnabled = ldFlags.csat || false;

  const fetchCSATSettingsData = React.useCallback(async (): Promise<CSATSettingsType | null> => {
    try {
      const fetchedSettings = await fetchCSATSettings(teamId);

      // Default cooldown period to 10 if not set
      const settingsWithDefaults: CSATSettingsType = {
        ...fetchedSettings,
        cooldownPeriodDays: fetchedSettings.cooldownPeriodDays ?? 10
      };

      setSettings(settingsWithDefaults);
      setCooldownPeriodDays(settingsWithDefaults.cooldownPeriodDays);
      setUserCooldownPeriodDays(settingsWithDefaults.userCooldownPeriodDays);
      setSelectedClosedStatusIds(settingsWithDefaults.closedStatusIds || []);

      return settingsWithDefaults;
    } catch {
      toast.error("Failed to load CSAT settings");
      return null;
    }
  }, [teamId]);

  const fetchDefaultEmail = React.useCallback(async (): Promise<DefaultEmailType | null> => {
    try {
      const response = await fetch(
        `/api/organization/teams/email-config/default-email?teamId=${teamId}`,
        {
          method: "POST",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch default email");
      }

      const data = await response.json() as DefaultEmailType;
      setDefaultEmail(data);
      return data;
    } catch (error) {
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to fetch default email"
      );
      return null;
    }
  }, [teamId]);

  const fetchVerifiedDomains = React.useCallback(async (): Promise<string[]> => {
    try {
      const response = await fetch("/api/organization/domains/fetch");

      if (!response.ok) throw new Error("Failed to fetch domains");
      const data = await response.json() as DomainType[];

      const verifiedDomains = data
        .filter((domain) => domain.isDnsVerified)
        .map((domain) => domain.domain);

      setDomains(verifiedDomains);
      return verifiedDomains;
    } catch {
      toast.error("Failed to load verified domains");
      return [];
    }
  }, []);

  const fetchAllVerifiedEmails = React.useCallback(async (
    defaultEmailData: DefaultEmailType | null = null,
    fetchedSettings: CSATSettingsType | null = null
  ): Promise<EmailDataType[]> => {
    try {
      const response = await fetch(
        `/api/organization/teams/email-config/custom-email-config?teamId=${teamId}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch custom email configurations");
      }

      const responseData = await response.json();
      const emailsArray: EmailResponseItemType[] = Array.isArray(responseData)
        ? responseData
        : responseData.data || [];

      const allVerifiedEmails: EmailDataType[] = emailsArray
        .filter((item) => item.isEmailForwardingVerified)
        .map((item) => {
          const domain = item.domain ||
            (item.customEmail ? item.customEmail.split('@')[1] : "");

          return {
            id: item.id || item.uid || "",
            email: item.customEmail || "",
            domain
          };
        });

      setAllEmails(allVerifiedEmails);

      const settingsToUse = fetchedSettings || settings;
      if (settingsToUse.emailConfigId) {
        const foundCustomEmail = allVerifiedEmails.find(email => email.id === settingsToUse.emailConfigId);

        if (foundCustomEmail) {
          setUseDefaultEmail(false);
          setSelectedEmailId(foundCustomEmail.id);
          setSelectedEmailAddress(foundCustomEmail.email);
          setSelectedDomain(foundCustomEmail.domain || "");
        } else if (defaultEmailData && settingsToUse.emailConfigId === defaultEmailData.id) {
          setUseDefaultEmail(true);
          setSelectedEmailId(defaultEmailData.id);
          setSelectedEmailAddress(defaultEmailData.email);
          setSelectedDomain("");
        }
      } else if (defaultEmailData) {
        setUseDefaultEmail(true);
        setSelectedEmailId(defaultEmailData.id);
        setSelectedEmailAddress(defaultEmailData.email);
        setSelectedDomain("");
      }

      return allVerifiedEmails;
    } catch {
      toast.error("Failed to load verified emails");
      return [];
    }
  }, [teamId, settings]);

  const handleStartEditingSettings = () => {
    setCooldownPeriodDays(settings.cooldownPeriodDays);
    setUserCooldownPeriodDays(settings.userCooldownPeriodDays);
    setSelectedClosedStatusIds(settings.closedStatusIds || []);

    if (settings.emailConfigId) {
      const foundCustomEmail = allEmails.find(email => email.id === settings.emailConfigId);

      if (foundCustomEmail) {
        setUseDefaultEmail(false);
        setSelectedEmailId(foundCustomEmail.id);
        setSelectedEmailAddress(foundCustomEmail.email);
        setSelectedDomain(foundCustomEmail.domain || "");
      } else if (defaultEmail && settings.emailConfigId === defaultEmail.id) {
        setUseDefaultEmail(true);
        setSelectedEmailId(defaultEmail.id);
        setSelectedEmailAddress(defaultEmail.email);
        setSelectedDomain("");
      }
    } else if (defaultEmail) {
      setUseDefaultEmail(true);
      setSelectedEmailId(defaultEmail.id);
      setSelectedEmailAddress(defaultEmail.email);
      setSelectedDomain("");
    }

    setIsSettingsModalOpen(true);
  };

  const handleCancelEditingSettings = () => {
    setCooldownPeriodDays(settings.cooldownPeriodDays);
    setUserCooldownPeriodDays(settings.userCooldownPeriodDays);
    setSelectedClosedStatusIds(settings.closedStatusIds || []);

    if (settings.emailConfigId) {
      const foundCustomEmail = allEmails.find(email => email.id === settings.emailConfigId);

      if (foundCustomEmail) {
        setUseDefaultEmail(false);
        setSelectedEmailId(foundCustomEmail.id);
        setSelectedEmailAddress(foundCustomEmail.email);
        setSelectedDomain(foundCustomEmail.domain || "");
      } else if (defaultEmail && settings.emailConfigId === defaultEmail.id) {
        setUseDefaultEmail(true);
        setSelectedEmailId(defaultEmail.id);
        setSelectedEmailAddress(defaultEmail.email);
        setSelectedDomain("");
      }
    } else if (defaultEmail) {
      setUseDefaultEmail(true);
      setSelectedEmailId(defaultEmail.id);
      setSelectedEmailAddress(defaultEmail.email);
      setSelectedDomain("");
    }

    setIsSettingsModalOpen(false);
  };

  React.useEffect(() => {
    if (dataFetched) return;

    const initializeData = async () => {
      setIsLoading(true);
      try {
        const [fetchedSettings, defaultEmailData, _verifiedDomains] = await Promise.all([
          fetchCSATSettingsData(),
          fetchDefaultEmail(),
          fetchVerifiedDomains()
        ]);

        await fetchAllVerifiedEmails(defaultEmailData, fetchedSettings);

        setDataFetched(true);
      } catch {
        toast.error("Error initializing data");
      } finally {
        setIsLoading(false);
      }
    };

    initializeData();
  }, [dataFetched, fetchCSATSettingsData, fetchDefaultEmail, fetchVerifiedDomains, fetchAllVerifiedEmails]);

  React.useEffect(() => {
    if (selectedDomain && allEmails.length > 0) {
      const filteredEmails = allEmails.filter(email => email.domain === selectedDomain);
      setVerifiedEmails(filteredEmails);

      if (isSettingsModalOpen && selectedEmailId) {
        const emailFromThisDomain = filteredEmails.some(email => email.id === selectedEmailId);
        if (!emailFromThisDomain) {
          if (filteredEmails.length > 0) {
            setSelectedEmailId(filteredEmails[0].id);
            setSelectedEmailAddress(filteredEmails[0].email);
          } else {
            setSelectedEmailId("");
            setSelectedEmailAddress("");
          }
        }
      }
    } else {
      setVerifiedEmails([]);
    }
  }, [selectedDomain, allEmails, isSettingsModalOpen, selectedEmailId]);

  React.useEffect(() => {
    if (!isReordering && settings.rules.length > 0) {
      setOrderedRules([...settings.rules]);
    }
  }, [settings.rules, isReordering]);

  const handleEditRule = (rule: CSATRule) => {
    const url = `/dashboard/${teamId}/settings/csat/rules/${rule.id}`;
    //@ts-expect-error - Next js router typig issue in this project
    router.push(url, { state: { rule } });
  };

  const handleCreateRule = () => {
    const url = `/dashboard/${teamId}/settings/csat/rules/new`;
    //@ts-expect-error - Next js router typig issue in this project
    router.push(url);
  };

  const handleStartReordering = React.useCallback(() => {
    setOrderedRules([...settings.rules]);
    setIsReordering(true);
  }, [settings.rules]);

  const handleDragEnd = React.useCallback(
    (result: DropResult) => {
      if (!result.destination) return;

      const items = Array.from(orderedRules);
      const [reorderedItem] = items.splice(result.source.index, 1);
      items.splice(result.destination.index, 0, reorderedItem);

      const updatedItems = items.map((item, index) => ({
        ...item,
        priority: items.length - index,
      }));

      setOrderedRules(updatedItems);
    },
    [orderedRules],
  );

  const handleCancelReordering = React.useCallback(() => {
    setOrderedRules([...settings.rules]);
    setIsReordering(false);
  }, [settings.rules]);

  const handleSaveOrder = React.useCallback(async () => {
    if (!orderedRules.length) return;

    setIsSubmitting(true);
    try {
      const priorityUpdates = orderedRules.map(rule => ({
        ruleId: rule.id,
        priority: rule.priority,
      }));

      await reorderCSATRules(teamId, priorityUpdates);

      setSettings(prev => ({
        ...prev,
        rules: orderedRules,
      }));

      toast.success("CSAT rules reordered successfully");
      setIsReordering(false);
    } catch {
      toast.error("Failed to reorder CSAT rules");
      setOrderedRules([...settings.rules]);
    } finally {
      setIsSubmitting(false);
    }
  }, [orderedRules, teamId, settings.rules]);

  const handleRuleUpdate = React.useCallback((updatedRule: CSATRule) => {
    setSettings(prev => ({
      ...prev,
      rules: prev.rules.map(rule =>
        rule.id === updatedRule.id ? updatedRule : rule
      ),
    }));
    toast.success("CSAT rule updated successfully");
  }, []);

  const handleRuleDelete = React.useCallback((deletedRuleId: string) => {
    setSettings(prev => ({
      ...prev,
      rules: prev.rules.filter(rule => rule.id !== deletedRuleId),
    }));
    toast.success("CSAT rule deleted successfully");
  }, []);

  const handleToggleDefaultEmail = (useDefault: boolean) => {
    setUseDefaultEmail(useDefault);

    if (useDefault && defaultEmail) {
      setSelectedDomain("");
      setSelectedEmailId(defaultEmail.id);
      setSelectedEmailAddress(defaultEmail.email);
    } else if (domains.length > 0) {
      const firstDomain = domains[0];
      setSelectedDomain(firstDomain);

      const domainEmails = allEmails.filter(email => email.domain === firstDomain);
      if (domainEmails.length > 0) {
        setSelectedEmailId(domainEmails[0].id);
        setSelectedEmailAddress(domainEmails[0].email);
      } else {
        setSelectedEmailId("");
        setSelectedEmailAddress("");
      }
    }
  };

  const handleSaveSettings = async () => {
    // Validate that at least one status is selected when sub-statuses exist
    if (hasClosedSubStatuses && selectedClosedStatusIds.length === 0) {
      setStatusValidationError("At least one status must be selected to trigger CSAT surveys");
      return;
    }

    setIsSubmitting(true);
    try {
      let emailIdToSave: string | undefined;

      if (useDefaultEmail && defaultEmail) {
        emailIdToSave = defaultEmail.id;
      } else if (!useDefaultEmail && selectedEmailId) {
        emailIdToSave = selectedEmailId;
      } else {
        emailIdToSave = undefined;
      }

      await updateCSATSettings(
        teamId,
        cooldownPeriodDays,
        emailIdToSave,
        selectedClosedStatusIds,
        userCooldownPeriodDays
      );

      setSettings(prev => ({
        ...prev,
        cooldownPeriodDays,
        userCooldownPeriodDays,
        emailConfigId: emailIdToSave,
        closedStatusIds: selectedClosedStatusIds
      }));

      toast.success("CSAT settings updated successfully");
      setIsSettingsModalOpen(false);
    } catch {
      toast.error("Failed to update CSAT settings");
      setCooldownPeriodDays(settings.cooldownPeriodDays);
      setUserCooldownPeriodDays(settings.userCooldownPeriodDays);
      setSelectedClosedStatusIds(settings.closedStatusIds || []);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDomainSelect = (domain: string) => {
    setSelectedDomain(domain);
    setUseDefaultEmail(false);

    const domainEmails = allEmails.filter(email => email.domain === domain);

    if (domainEmails.length > 0) {
      setSelectedEmailId(domainEmails[0].id);
      setSelectedEmailAddress(domainEmails[0].email);
    } else {
      setSelectedEmailId("");
      setSelectedEmailAddress("");
    }
  };

  const handleEmailSelect = (id: string, email: string) => {
    setSelectedEmailId(id);
    setSelectedEmailAddress(email);
    setUseDefaultEmail(false);
  };

  const noRulesContent = (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="max-w-md w-full p-8">
        <div className="space-y-6 text-center">
          <div className="relative">
            <div className="absolute inset-0 bg-primary/5 animate-pulse rounded-[var(--radius-sm)] blur-xl" />
            <div className="relative mx-auto w-20 h-20 flex items-center justify-center bg-primary/10 rounded-[var(--radius-sm)]">
              <Star className="h-10 w-10 text-primary/80 transition-transform duration-200 hover:scale-110" />
            </div>
          </div>
          <div className="space-y-2">
            <h3 className="text-xl font-medium tracking-tight">
              No CSAT rules yet
            </h3>
            <p className="text-muted-foreground text-sm">
              Create your first rule to start collecting customer satisfaction feedback and improve your service quality.
            </p>
          </div>
          <Button
            onClick={handleCreateRule}
            className="mx-auto flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Create your first rule
          </Button>
        </div>
      </div>
    </div>
  );

  if (!isCSATEnabled) {
    return <ComingSoon />
  }

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center min-h-[640px]">
          <ThenaLoader />
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Only show header when we have rules */}
      {settings.rules.length > 0 && (
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 max-w-[664px] mx-auto mb-6">
          <div>
            <h2 className="text-2xl font-medium tracking-tight">CSAT</h2>
            <p className="text-muted-foreground text-sm">
              Configure customer satisfaction surveys for your team.
            </p>
          </div>
        </div>
      )}

      {/* Show empty state if no rules */}
      {settings.rules.length === 0 && noRulesContent}

      {/* Rules List */}
      {settings.rules.length > 0 && (
        <div className="max-w-[664px] mx-auto">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-8 mb-6">
            <div className="flex-1">
              <div className="relative max-w-xs">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search rules..."
                  className="h-8 pl-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <div className="flex gap-2">
              {settings.rules.length > 1 && !isReordering && (
                <Button variant="outline" size="icon" className="h-8 w-8" onClick={handleStartReordering}>
                  <ArrowUpDown className="h-4 w-4" />
                </Button>
              )}
              <Dialog open={isSettingsModalOpen} onOpenChange={setIsSettingsModalOpen}>
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    className="h-8"
                    onClick={handleStartEditingSettings}
                    disabled={isReordering}
                  >
                    Settings
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[600px]">
                  <DialogHeader>
                    <DialogTitle className="font-medium">Global CSAT settings</DialogTitle>
                    <DialogDescription>
                      Configure timing and trigger statuses. Applied to all rules.
                    </DialogDescription>
                  </DialogHeader>

                  <div className="space-y-6 py-4">
                    {/* Customer Cooldown Period */}
                    <div className="space-y-1">
                      <Label htmlFor="cooldown-period" className="font-medium">Delivery delay</Label>
                      <p className="text-sm text-muted-foreground">
                        Days to wait after ticket closure before sending CSAT survey.
                      </p>
                      <div className="flex items-center space-x-2 pt-2">
                        <Input
                          id="cooldown-period"
                          type="number"
                          min="1"
                          max="365"
                          value={cooldownPeriodDays === undefined ? '0' : cooldownPeriodDays}
                          onChange={(e) => setCooldownPeriodDays(e.target.value === '' ? undefined : parseInt(e.target.value))}
                          className="w-24 h-8"
                        />
                        <span className="text-sm text-muted-foreground">days</span>
                      </div>
                    </div>

                    {/* User/Agent Cooldown Period */}
                    <div className="space-y-1">
                      <Label htmlFor="user-cooldown-period" className="font-medium">Customer cooldown</Label>
                      <p className="text-sm text-muted-foreground">
                        Minimum days between CSAT surveys sent to the same customer contact.
                      </p>
                      <div className="flex items-center space-x-2 pt-2">
                        <Input
                          id="user-cooldown-period"
                          type="number"
                          min="1"
                          max="365"
                          value={userCooldownPeriodDays === undefined ? '0' : userCooldownPeriodDays}
                          onChange={(e) => setUserCooldownPeriodDays(e.target.value === '' ? undefined : parseInt(e.target.value))}
                          className="w-24 h-8"
                        />
                        <span className="text-sm text-muted-foreground">days</span>
                      </div>
                    </div>

                    {/* Status Selection */}
                    <div className="space-y-1">
                      <Label className="font-medium">Trigger statuses</Label>
                      <p className="text-sm text-muted-foreground">
                        Select which statuses will trigger CSAT surveys.
                      </p>
                      {hasClosedSubStatuses ? (
                        <div className="pt-2">
                          <div className="flex flex-wrap items-center" style={{ gap: "12px" }}>
                            {/* Selected status badges */}
                            {selectedStatuses.map((status) => (
                              <div
                                key={status.id}
                                className="px-2 py-1 rounded border flex items-center w-min whitespace-nowrap"
                                style={{ gap: "10px" }}
                              >
                                {getLaneIcon("CLOSED", "16")}
                                <span className="text-[14px]">
                                  {status.name}
                                  {status.is_default && (
                                    <span className="text-xs text-muted-foreground ml-1">(Default)</span>
                                  )}
                                </span>
                                <button
                                  type="button"
                                  onClick={() => removeStatus(status.id.toString())}
                                  className="hover:text-destructive"
                                >
                                  <X className="h-3 w-3" />
                                </button>
                              </div>
                            ))}

                            {/* Add status button */}
                            {availableStatuses.length > 0 && (
                              <Popover open={statusDropdownOpen} onOpenChange={setStatusDropdownOpen}>
                                <PopoverTrigger asChild>
                                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                                    <Plus className="h-4 w-4" />
                                    <span>Add status</span>
                                  </Button>
                                </PopoverTrigger>
                                <PopoverContent
                                  className="w-64 p-0 animate-in fade-in-50 zoom-in-95 duration-100 mr-8"
                                  align="start"
                                  sideOffset={10}
                                >
                                  <div className="max-h-[200px] overflow-y-auto py-0 scrollbar-thin">
                                    {availableStatuses.length > 0 ? (
                                      availableStatuses.map((status) => (
                                        <div
                                          key={status.uid}
                                          className="flex items-center gap-2 px-3 py-2 cursor-pointer hover:bg-muted"
                                          onClick={() => addStatus(status.id.toString())}
                                        >
                                          <div className="flex items-center gap-2 flex-1">
                                            {getLaneIcon("CLOSED", "16")}
                                            <span className="flex-1 text-[14px]">
                                              {status.name}
                                            </span>
                                            {status.is_default && (
                                              <span className="text-xs text-muted-foreground">Default</span>
                                            )}
                                          </div>
                                        </div>
                                      ))
                                    ) : (
                                      <div className="px-3 py-2 text-sm text-muted-foreground">
                                        All statuses selected
                                      </div>
                                    )}
                                  </div>
                                </PopoverContent>
                              </Popover>
                            )}
                          </div>

                          {statusValidationError && (
                            <p className="text-sm text-destructive mt-2">{statusValidationError}</p>
                          )}
                        </div>
                      ) : (
                        <div className="pt-2">
                          <Select disabled value="closed">
                            <SelectTrigger className="w-full">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="closed">
                                <div className="flex items-center gap-2">
                                  {getLaneIcon("CLOSED", "16")}
                                  <span>Closed</span>
                                </div>
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <p className="text-xs text-muted-foreground mt-2">
                            CSAT will trigger when any ticket is closed. Create custom sub-statuses for more control.
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Notification Email */}
                    <div className="space-y-1">
                      <Label className="font-medium">Sender email</Label>
                      <p className="text-sm text-muted-foreground">
                        Email address used to send CSAT surveys.
                      </p>
                      <div className="space-y-4 pt-2">
                        {defaultEmail && (
                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id="use-default-email"
                              checked={useDefaultEmail}
                              onChange={(e) => handleToggleDefaultEmail(e.target.checked)}
                              className="h-4 w-4"
                            />
                            <label htmlFor="use-default-email" className="text-sm">
                              Use default email ({defaultEmail.email})
                            </label>
                          </div>
                        )}

                        {!useDefaultEmail && (
                          <div className="space-y-3">
                            <div>
                              <Label className="text-xs text-muted-foreground font-medium">Domain</Label>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="outline" className="w-full justify-start">
                                    {selectedDomain || "Select Domain"}
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent className="w-full">
                                  {domains.length > 0 ? (
                                    domains.map((domain) => (
                                      <DropdownMenuItem
                                        key={domain}
                                        onClick={() => handleDomainSelect(domain)}
                                      >
                                        {domain}
                                      </DropdownMenuItem>
                                    ))
                                  ) : (
                                    <DropdownMenuItem disabled>
                                      No verified domains available
                                    </DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>

                            <div>
                              <Label className="text-xs text-muted-foreground font-medium">Email address</Label>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="outline"
                                    disabled={!selectedDomain}
                                    className="w-full justify-start"
                                  >
                                    {selectedEmailAddress || "Select Email"}
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent className="w-full">
                                  {verifiedEmails.length > 0 ? (
                                    verifiedEmails.map((item) => (
                                      <DropdownMenuItem
                                        key={item.id}
                                        onClick={() => handleEmailSelect(item.id, item.email)}
                                      >
                                        {item.email}
                                      </DropdownMenuItem>
                                    ))
                                  ) : (
                                    <DropdownMenuItem disabled>
                                      {selectedDomain ? "No verified emails for this domain" : "Select a domain first"}
                                    </DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>

                            {selectedDomain && verifiedEmails.length === 0 && (
                              <div className="flex items-start gap-2 text-amber-600">
                                <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                                <p className="text-xs">
                                  No verified emails found for {selectedDomain}. Please verify an email for this domain.
                                </p>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={handleCancelEditingSettings}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSaveSettings}
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Saving..." : "Save changes"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
              <Button className="h-8" onClick={handleCreateRule} disabled={isReordering}>
                Create new rule
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            {isReordering ? (
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="csat-rules">
                  {(provided) => (
                    <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
                      {orderedRules.map((rule, index) => (
                        <Draggable key={rule.id} draggableId={rule.id} index={index}>
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className={snapshot.isDragging ? 'opacity-75' : ''}
                            >
                              <CSATRuleCard
                                rule={rule}
                                teamId={teamId}
                                onEdit={handleEditRule}
                                onDelete={handleRuleDelete}
                                onUpdate={handleRuleUpdate}
                                isReordering={true}
                                dragHandleProps={provided.dragHandleProps}
                              />
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            ) : (
              <>
                {filteredRules.length > 0 ? (
                  filteredRules.map((rule) => (
                    <CSATRuleCard
                      key={rule.id}
                      rule={rule}
                      teamId={teamId}
                      onEdit={handleEditRule}
                      onDelete={handleRuleDelete}
                      onUpdate={handleRuleUpdate}
                      isReordering={false}
                    />
                  ))
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No rules found matching your search.</p>
                  </div>
                )}
              </>
            )}

            {isReordering && (
              <div className="flex gap-2 justify-end">
                <Button variant="outline" onClick={handleCancelReordering}>
                  Cancel
                </Button>
                <Button onClick={handleSaveOrder} disabled={isSubmitting}>
                  {isSubmitting ? "Saving..." : "Save order"}
                </Button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}