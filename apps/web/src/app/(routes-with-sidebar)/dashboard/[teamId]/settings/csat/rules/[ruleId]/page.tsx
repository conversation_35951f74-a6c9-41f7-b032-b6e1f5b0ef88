"use client";

import <PERSON><PERSON><PERSON>oa<PERSON> from "@/components/thena-loader";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { createCSATRule, updateCSATRule } from "@/services/csat-service";
import { createEmptyRuleFilters, CSATRule } from "@/types/csat";
import { ChevronLeft, Clock, FileText, Filter, MessageSquare } from "lucide-react";
import { useRouter } from "next/navigation";
import * as React from "react";
import { toast } from "sonner";
import { CsatFilterSection } from "../../../../../../../../components/csat/csat-filter-section";
import { TriggerConfigSection } from "../../../../../../../../components/csat/csat-trigger-config";
import { FeedbackConfigSection } from "../../../../../../../../components/csat/feedback-config-section";


const useRuleData = (teamId, ruleId, isEditMode) => {
  const [isLoading, setIsLoading] = React.useState(true);
  const [notFound, setNotFound] = React.useState(false);
  const [ruleData, setRuleData] = React.useState(null);

  React.useEffect(() => {
    if (!isEditMode) {
      setIsLoading(false);
      return;
    }

    let isMounted = true;
    const fetchRuleData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/teams/${teamId}/csat-settings/rules/${ruleId}`);

        if (!isMounted) return;

        if (response.status === 404) {
          setNotFound(true);
          return;
        }

        const rule = await response.json();
        setRuleData(rule);
      } catch {
        if (isMounted) {
          toast("Failed to load rule data.", {
            description: "Could not retrieve the rule information. Please try again.",
          });
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchRuleData();

    return () => {
      isMounted = false;
    };
  }, [isEditMode, teamId, ruleId]);

  return { isLoading, notFound, ruleData };
};

const createDefaultFormState = (): Omit<CSATRule, "id" | "createdAt" | "updatedAt"> => ({
  name: "",
  description: "",
  isActive: true,
  allFilters: createEmptyRuleFilters(),
  anyFilters: createEmptyRuleFilters(),
  priority: 0,
  triggerConfig: {
    triggerType: "always",
    randomPercentage: 10,
    delayMinutes: 30,
  },
  feedbackConfig: {
    enabled: true,
    feedbackType: "star",
    customTitle: "",
    customMessage: "",
    customThankYouMessage: "",
    includeCommentField: true,
    commentFieldLabel: "",
    commentFieldPlaceholder: "",
    brandingColor: "#6366f1",
    deliveryChannel: "email",
  },
});

export default function CSATRulePage({
  params,
}: {
  params: Promise<{ teamId: string; ruleId: string }>;
}) {
  const resolvedParams = React.use(params);
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const [hasFilters, setHasFilters] = React.useState(false);
  const [filtersExplicitlyRemoved, setFiltersExplicitlyRemoved] = React.useState(false);
  const originalFiltersRef = React.useRef(null);

  const isEditMode = React.useMemo(() => {
    return resolvedParams.ruleId !== 'new';
  }, [resolvedParams.ruleId]);

  const { isLoading, notFound, ruleData } = useRuleData(
    resolvedParams.teamId,
    resolvedParams.ruleId,
    isEditMode
  );

  const [formState, setFormState] = React.useState(() => createDefaultFormState());

  function checkForFilters(filters) {
    if (!filters) return false;

    const hasValidItems = (arr) => {
      if (!arr) return false;

      if (Array.isArray(arr)) {
        return arr.some(item =>
          item !== undefined &&
          item !== null &&
          item.field &&
          item.operator
        );
      }

      if (typeof arr === 'object') {
        return Object.entries(arr).some(([key, item]) => {
          const typedItem = item as { field: string; operator: string };
          return !isNaN(parseInt(key)) &&
            item !== undefined &&
            item !== null &&
            typedItem.field &&
            typedItem.operator;
        }
        );
      }

      return false;
    };

    const hasValidFields = (entity) => {
      if (!filters[entity]) return false;

      if (hasValidItems(filters[entity]?.standardFields)) {
        return true;
      }

      if (hasValidItems(filters[entity]?.customFields)) {
        return true;
      }

      return false;
    };

    return (
      hasValidFields('ticket') ||
      hasValidFields('account') ||
      hasValidFields('contact')
    );
  }

  React.useEffect(() => {
    if (!ruleData) return;

    try {
      const clonedAllFilters = JSON.parse(JSON.stringify(ruleData.allFilters || createEmptyRuleFilters()));
      const clonedAnyFilters = JSON.parse(JSON.stringify(ruleData.anyFilters || createEmptyRuleFilters()));

      originalFiltersRef.current = {
        allFilters: clonedAllFilters,
        anyFilters: clonedAnyFilters
      };

      const hasValidFilters = checkForFilters(clonedAllFilters) || checkForFilters(clonedAnyFilters);
      setHasFilters(hasValidFilters);

      if (hasValidFilters) {
        setFiltersExplicitlyRemoved(false);
      }

      setFormState({
        name: ruleData.name || "",
        description: ruleData.description || "",
        isActive: ruleData.isActive ?? true,
        allFilters: clonedAllFilters,
        anyFilters: clonedAnyFilters,
        priority: ruleData.priority || 0,
        triggerConfig: {
          triggerType: ruleData.triggerConfig?.triggerType || "always",
          randomPercentage: ruleData.triggerConfig?.randomPercentage || 10,
          delayMinutes: ruleData.triggerConfig?.delayMinutes || 30,
        },
        feedbackConfig: {
          enabled: ruleData.feedbackConfig?.enabled ?? true,
          feedbackType: ruleData.feedbackConfig?.feedbackType || "star",
          customTitle: ruleData.feedbackConfig?.customTitle || "",
          customMessage: ruleData.feedbackConfig?.customMessage || "",
          customThankYouMessage: ruleData.feedbackConfig?.customThankYouMessage || "",
          includeCommentField: ruleData.feedbackConfig?.includeCommentField ?? true,
          commentFieldLabel: ruleData.feedbackConfig?.commentFieldLabel || "",
          commentFieldPlaceholder: ruleData.feedbackConfig?.commentFieldPlaceholder || "",
          brandingColor: ruleData.feedbackConfig?.brandingColor || "#6366f1",
          deliveryChannel: ruleData.feedbackConfig?.deliveryChannel || "email",
        },
      });
    } catch {
      toast("There was an error loading the rule data.", {
        description: "Some data may not be displayed correctly. You can try refreshing the page.",
      });
    }
  }, [ruleData]);

  const updateFormField = (field, value) => {
    setFormState(prev => ({ ...prev, [field]: value }));
  };

  const handleTriggerConfigChange = React.useCallback((newConfig: CSATRule["triggerConfig"]) => {
    updateFormField('triggerConfig', newConfig);
  }, []);

  const _updateNestedField = (parent, field, value) => {
    setFormState(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value
      }
    }));
  };

  const handleAllFiltersChange = React.useCallback((newFilters) => {
    try {
      const clonedFilters = JSON.parse(JSON.stringify(newFilters));
      updateFormField('allFilters', clonedFilters);
      const hasValidFilters = checkForFilters(clonedFilters) || checkForFilters(formState.anyFilters);
      setHasFilters(hasValidFilters);
      if (hasValidFilters) {
        setFiltersExplicitlyRemoved(false);
      }
    } catch (error) {
      console.error("Error handling all filters change:", error);
    }
  }, [formState.anyFilters]);

  const handleAnyFiltersChange = React.useCallback((newFilters) => {
    try {
      const clonedFilters = JSON.parse(JSON.stringify(newFilters));
      updateFormField('anyFilters', clonedFilters);
      const hasValidFilters = checkForFilters(formState.allFilters) || checkForFilters(clonedFilters);
      setHasFilters(hasValidFilters);
      if (hasValidFilters) {
        setFiltersExplicitlyRemoved(false);
      }
    } catch (error) {
      console.error("Error handling any filters change:", error);
    }
  }, [formState.allFilters]);

  const handleFeedbackConfigChange = React.useCallback((newConfig) => {
    updateFormField('feedbackConfig', newConfig);
  }, []);

  const handleFilterAction = React.useCallback(() => {
    setHasFilters(true);
    setFiltersExplicitlyRemoved(false);
  }, []);

  const handleAllFiltersRemoved = React.useCallback(() => {
    setHasFilters(false);
    setFiltersExplicitlyRemoved(true);
  }, []);

  const _hasAnyFiltersDefined = React.useCallback(() => {
    if (filtersExplicitlyRemoved) {
      return false;
    }

    const formStateHasFilters = checkForFilters(formState.allFilters) ||
      checkForFilters(formState.anyFilters);

    if (formStateHasFilters) {
      return true;
    }

    if (isEditMode && hasFilters && !formStateHasFilters && originalFiltersRef.current) {
      return checkForFilters(originalFiltersRef.current.allFilters) ||
        checkForFilters(originalFiltersRef.current.anyFilters);
    }

    return false;
  }, [formState.allFilters, formState.anyFilters, hasFilters, filtersExplicitlyRemoved, isEditMode]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formState.name.trim()) {
      toast("Rule name is required.", {
        description: "Please provide a name for this CSAT rule.",
      });
      return;
    }

    // if (!hasAnyFiltersDefined()) {
    //   toast("Filters are required.", {
    //     description: "Please add at least one filter in either the 'Match ALL filters' or 'Match ANY filter' section.",
    //   });
    //   return;
    // }

    setIsSubmitting(true);

    try {
      let submissionFilters = {
        allFilters: formState.allFilters,
        anyFilters: formState.anyFilters
      };

      if (isEditMode &&
        hasFilters &&
        !filtersExplicitlyRemoved &&
        !checkForFilters(formState.allFilters) &&
        !checkForFilters(formState.anyFilters) &&
        originalFiltersRef.current) {
        submissionFilters = {
          allFilters: originalFiltersRef.current.allFilters,
          anyFilters: originalFiltersRef.current.anyFilters
        };
      }

      const ruleData = {
        name: formState.name,
        description: formState.description,
        isActive: formState.isActive,
        allFilters: submissionFilters.allFilters,
        anyFilters: submissionFilters.anyFilters,
        priority: formState.priority,
        triggerConfig: formState.triggerConfig,
        feedbackConfig: formState.feedbackConfig,
      };

      if (isEditMode) {
        await updateCSATRule(resolvedParams.teamId, resolvedParams.ruleId, ruleData);
        toast("CSAT rule updated.", {
          description: "Your CSAT rule has been updated successfully.",
        });
      } else {
        await createCSATRule(resolvedParams.teamId, ruleData);
        toast("CSAT rule created.", {
          description: "Your new CSAT rule has been created successfully.",
        });
      }

      router.push(`/dashboard/${resolvedParams.teamId}/settings/csat`);
      router.refresh();
    } catch {
      toast(`Failed to ${isEditMode ? 'update' : 'create'} CSAT rule.`, {
        description: `An error occurred while ${isEditMode ? 'updating' : 'creating'} the CSAT rule. Please try again.`,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (notFound) {
    return (
      <div className="flex flex-col items-center justify-center p-6 h-[calc(100vh-200px)]">
        <h2 className="text-2xl font-medium mb-4">Rule Not Found</h2>
        <p className="text-muted-foreground mb-6">
          The CSAT rule you&apos;re looking for doesn&apos;t exist or has been deleted.
        </p>
        <Button onClick={() => router.push(`/dashboard/${resolvedParams.teamId}/settings/csat`)}>
          Back to CSAT Settings
        </Button>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="h-full w-full flex items-center justify-center min-h-[640px]">
        <div className="flex flex-col items-center gap-4">
          <ThenaLoader />
        </div>
      </div>
    );
  }

  const {
    name,
    description,
    isActive,
    allFilters,
    anyFilters,
    triggerConfig,
    feedbackConfig
  } = formState;


  return (
    <main className="flex-1 overflow-y-auto bg-background">
      <div className="container mx-auto py-8">
        <div className="max-w-[640px] mx-auto">
          <div className="flex flex-col space-y-6">
            <div className="space-y-2">
              <h1 className="text-2xl font-medium tracking-tight">
                {isEditMode ? "Edit CSAT rule" : "Create new CSAT rule"}
              </h1>
              <p className="text-sm text-muted-foreground">
                Configure when and how CSAT surveys are sent to your customers.
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <Accordion
                type="single"
                collapsible
                className="w-full space-y-4"
              >
                <AccordionItem value="basic-info" className="rounded-sm border overflow-hidden">
                  <AccordionTrigger className="px-4 py-3 bg-background hover:bg-muted/30 text-base font-medium">
                    <div className="flex items-center gap-3">
                      <FileText className="h-5 w-5 text-blue-500" />
                      Basic information
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="border-t">
                    <div className="p-4 space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Rule name</Label>
                        <Input
                          id="name"
                          value={name}
                          onChange={(e) => updateFormField('name', e.target.value)}
                          placeholder="e.g., High priority tickets"
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="description">Description (optional)</Label>
                        <Textarea
                          id="description"
                          value={description}
                          onChange={(e) => updateFormField('description', e.target.value)}
                          placeholder="Describe the purpose of this rule..."
                          rows={3}
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="active">Active</Label>
                          <p className="text-sm text-muted-foreground">
                            Inactive rules won&apos;t send CSAT surveys.
                          </p>
                        </div>
                        <Switch
                          id="active"
                          checked={isActive}
                          onCheckedChange={(value) => updateFormField('isActive', value)}
                        />
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="filters" className="rounded-sm border overflow-hidden">
                  <AccordionTrigger className="px-4 py-3 bg-background hover:bg-muted/30 text-base font-medium">
                    <div className="flex items-center gap-3">
                      <Filter className="h-5 w-5 text-yellow-500" />
                      <span>Filters</span>
                      {hasFilters && !filtersExplicitlyRemoved && (
                        <Badge variant="secondary">
                          Configured
                        </Badge>
                      )}
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="border-t">
                    <div className="p-4">
                      <CsatFilterSection
                        allFilters={allFilters}
                        anyFilters={anyFilters}
                        onAllFiltersChange={handleAllFiltersChange}
                        onAnyFiltersChange={handleAnyFiltersChange}
                        teamId={resolvedParams.teamId}
                        onFilterAction={handleFilterAction}
                        onAllFiltersRemoved={handleAllFiltersRemoved}
                      />
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="trigger-config"
                  className="rounded-sm border overflow-hidden"
                >
                  <AccordionTrigger className="px-4 py-3 bg-background hover:bg-muted/30 text-base font-medium">
                    <div className="flex items-center gap-3">
                      <Clock className="h-5 w-5 text-green-500" />
                      <span>Survey delivery</span>
                      {triggerConfig.triggerType === "random" && (
                        <Badge variant="secondary">
                          Random ({triggerConfig.randomPercentage}%)
                        </Badge>
                      )}
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="border-t">
                    <div className="p-4">
                      <TriggerConfigSection
                        triggerConfig={triggerConfig}
                        onTriggerConfigChange={handleTriggerConfigChange}
                        teamId={resolvedParams.teamId}
                      />
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="feedback-config"
                  className="rounded-sm border overflow-hidden"
                >
                  <AccordionTrigger className="px-4 py-3 bg-background hover:bg-muted/30 text-base font-medium">
                    <div className="flex items-center gap-3">
                      <MessageSquare className="h-5 w-5 text-pink-500" />
                      <span>Feedback</span>
                      <Badge variant="secondary">
                        {feedbackConfig.feedbackType === "star" ? "5-star rating" : "Thumbs up/down"}
                      </Badge>
                      {feedbackConfig.includeCommentField && (
                        <Badge variant="outline">
                          Comments
                        </Badge>
                      )}
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="border-t">
                    <div className="p-4">
                      <FeedbackConfigSection
                        feedbackConfig={feedbackConfig}
                        onFeedbackConfigChange={handleFeedbackConfigChange}
                      />
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>

              <div className="flex items-center justify-between">
                <Button
                  variant="outline"
                  onClick={(e) => {
                    e.preventDefault();
                    router.push(`/dashboard/${resolvedParams.teamId}/settings/csat`);
                  }}
                  className="flex items-center gap-2"
                  disabled={isSubmitting}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Back
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary-foreground border-t-transparent" />
                      {isEditMode ? "Updating..." : "Creating..."}
                    </>
                  ) : (
                    isEditMode ? "Save changes" : "Create rule"
                  )}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </main>
  );
}