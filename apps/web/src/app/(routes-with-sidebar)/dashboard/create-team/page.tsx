"use client";

import { ICON_COLORS } from "@/components/hero-icon";
import { IconName, IconPicker } from "@/components/icon-picker";
import <PERSON><PERSON><PERSON>oa<PERSON> from "@/components/thena-loader";
import TooltipWrapper from "@/components/tooltip-wrapper";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { GET_ALL_TEAMS } from "@/services/kanban";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { IconColor } from "@/types/icon-picker";
import { Team } from "@/types/kanban";
import { generateIdentifier } from "@/utils/teamsUtils";
import * as HeroIcons from "@heroicons/react/24/solid";
import { kebabCase } from "lodash";
import { ArrowRight, Globe2, Loader2, Lock } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { getOrgDetails } from "../../../../utils/browserUtils";

export default function TeamSetup() {
  const [formData, setFormData] = useState({
    name: "",
    identifier: "",
    visibility: "public",
    icon: "",
    iconColor: ICON_COLORS.purple,
  });
  const [showCreateTeamForm, setShowCreateTeamForm] = useState(false);
  const [errors, setErrors] = useState({
    name: "",
    identifier: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const [iconSearch, setIconSearch] = useState("");
  const filteredIcons = Object.keys(HeroIcons).filter((iconName) =>
    iconName.toLowerCase().includes(iconSearch.toLowerCase()),
  );
  const teamsList = useTicketMetaStore((state) => state.teams);
  const [teamsToJoin, setTeamsToJoin] = useState<Team[]>([]);
  const { orgId } = getOrgDetails();
  const orgsList = useGlobalConfigPersistStore((state) => state.orgs);
  const currentOrgDetails = orgsList.find((org) => org.id === orgId);
  const currentUser = useGlobalConfigPersistStore((state) => state.currentUser);
  const [isLoadingTeams, setIsLoadingTeams] = useState(true);
  const [showAllTeams, setShowAllTeams] = useState(false);

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      name: "",
      identifier: "",
    };

    if (!formData.name.trim()) {
      newErrors.name = "Team name is required";
      isValid = false;
    } else if (
      !/^[\p{L}\p{N}\p{Emoji}][\p{L}\p{N}\p{Emoji}\s\-_]*[\p{L}\p{N}\p{Emoji}]$/u.test(
        formData.name.trim(),
      )
    ) {
      newErrors.name =
        "Team name must start and end with a letter, and contain only letters, numbers, emojis, single spaces, hyphens, or underscores.";
      isValid = false;
    }

    if (!formData.identifier.trim()) {
      newErrors.identifier = "Team identifier is required";
      isValid = false;
    } else if (formData.identifier.length < 1) {
      newErrors.identifier = "Identifier must be at least 1 character";
      isValid = false;
    } else if (formData.identifier.length > 7) {
      newErrors.identifier = "Identifier must not exceed 7 characters";
      isValid = false;
    } else if (!/^[A-Z]+$/.test(formData.identifier)) {
      newErrors.identifier = "Identifier must contain only capital letters";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    const body = {
      name: formData.name.trim(),
      identifier: formData.identifier.trim(),
      isPrivate: formData.visibility === "private",
      icon: formData.icon,
      color: formData.iconColor,
    };
    try {
      const response = await fetch("/api/teams", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });
      const data = await response.json();
      if (!response.ok) {
        // Handle validation errors from the server
        if (Array.isArray(data)) {
          const newErrors = { ...errors };
          data.forEach((error) => {
            if (error.property in newErrors) {
              newErrors[error.property as keyof typeof newErrors] =
                error.constraints.matches;
            }
          });
          setErrors(newErrors);
          return;
        }
        toast.error(data?.error || "Failed to create team");
        return;
      }
      const user = useGlobalConfigPersistStore.getState().currentUser;
      if (user) {
        const teamsList = await fetch(GET_ALL_TEAMS(user.uid), {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });
        const teamsData = await teamsList.json();
        useTicketMetaStore.getState().setAllTeams([...teamsData]);
      }
      router.push(`/dashboard/${data.data?.teamId}`);
    } catch (error) {
      toast.error("Error creating team");
      console.error("Error creating team:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const fetchTeams = async () => {
      try {
        const response = await fetch("/api/teams/public");
        if (!response.ok) {
          throw new Error("Failed to fetch available teams");
        }
        const allTeams = await response.json();
        const userAddedTeamsList = teamsList.map((item) => item.uid);

        // First, get the team IDs where the user is a member
        // Filter out teams that the user is already a member of
        // AND filter out sub-teams (teams with parentTeamId)
        const teamsToJoinFiltered = allTeams?.data?.filter(
          (team: { uid: string; parentTeamId?: string }) =>
            !userAddedTeamsList.includes(team.uid) && !team.parentTeamId,
        );

        setTeamsToJoin(teamsToJoinFiltered || []);
      } catch (error) {
        console.error("Failed to fetch teams:", error);
      } finally {
        setIsLoadingTeams(false);
      }
    };

    // Extract the team IDs into an array
    if (orgId) {
      fetchTeams();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orgId]);

  const addToTeamHandler = async (teamId: string) => {
    try {
      const response = await fetch(`/api/teams/${teamId}/members`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email: currentUser?.email }),
      });

      if (!response.ok) {
        throw new Error("Failed to add member");
      }

      useTicketMetaStore.getState().updateUserIsPartOf(teamId, true);
      const teamsList = await fetch(GET_ALL_TEAMS(currentUser?.uid), {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });
      const teamsData = await teamsList.json();
      useTicketMetaStore.getState().setAllTeams([...teamsData]);
      router.push(`/dashboard/${teamId}`);
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to add member",
      );
    }
  };

  const TeamsListView = () => {
    const displayedTeams = showAllTeams ? teamsToJoin : teamsToJoin.slice(0, 5);

    const hasMoreTeams = teamsToJoin.length > 5;

    return (
      <div className="w-[656px] flex rounded-sm overflow-hidden border">
        <div className="flex-1 bg-background p-8">
          <div className="w-full">
            <h2 className="text-xl font-medium">
              Welcome to {currentOrgDetails.name}!
            </h2>
            <p className="text-muted-text text-sm pb-8">
              We found public teams in your organization. Join one or create a
              new team to get started.
            </p>

            {/* Container for teams list - fixed height when showing all teams */}
            <div
              className={`w-full pb-4 flex flex-col gap-3 ${
                showAllTeams
                  ? "min-h-[400px] max-h-[500px] overflow-y-auto pr-2"
                  : "min-h-[52px]"
              }`}
            >
              {displayedTeams.map((team) => (
                <div
                  key={team.id}
                  className="w-full flex items-center justify-between px-4 py-2 h-[52px] border rounded-sm hover:bg-gray-50 dark:hover:bg-gray-900 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <IconPicker
                      iconColor={team.color}
                      name={team.icon as IconName}
                    />
                    <div>
                      <h3 className="font-medium text-sm">{team.name}</h3>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    onClick={() => addToTeamHandler(team.id.toString())}
                    className="h-8 px-3 group hover:bg-transparent"
                  >
                    <span className="group-hover:[color:var(--color-hover-accent)]">
                      Join
                    </span>
                    <ArrowRight className="w-4 h-4 ml-1 group-hover:[color:var(--color-hover-accent)] group-hover:translate-x-1 transition-transform duration-200" />
                  </Button>
                </div>
              ))}
            </div>

            {hasMoreTeams && !showAllTeams && (
              <div className="flex justify-start mb-4">
                <Button
                  variant="ghost"
                  onClick={() => setShowAllTeams(true)}
                  className="px-3 h-8 text-sm font-medium"
                >
                  Show all public teams
                </Button>
              </div>
            )}

            <div className="relative mb-4">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-border"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-background text-muted-foreground">
                  or
                </span>
              </div>
            </div>
            <div className="flex justify-center w-full">
              <Button
                variant="outline"
                onClick={() => setShowCreateTeamForm(true)}
                className="w-full group"
              >
                <span>Create your own team</span>
                <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-200" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (isLoadingTeams) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center p-8">
        <ThenaLoader />
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen flex items-center justify-center p-8 bg-color-bg-subtle">
      {teamsToJoin.length > 0 && !showCreateTeamForm && <TeamsListView />}
      {(teamsToJoin.length === 0 || showCreateTeamForm) && (
        <div className="w-[656px] rounded-sm overflow-hidden border">
          {/* Main Content */}
          <div className="bg-background p-8">
            <div className="max-w-2xl">
              <h2 className="text-xl font-semibold">Set up your first team</h2>
              <p className="text-muted-text text-sm mb-8">
                Teams help you organize configurations, workflows, and
                notifications independently
              </p>

              <form className="space-y-8" onSubmit={handleSubmit}>
                <div className="space-y-4">
                  <div>
                    <Label
                      htmlFor="team-name"
                      className="text-sm font-medium flex items-center gap-1"
                    >
                      Team icon and name
                      <span className="text-red-500">*</span>
                    </Label>
                    <div className="flex flex-col gap-2 mt-2">
                      <div className="flex gap-2 flex-1">
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              className="w-[40px] h-[40px] p-0 flex-shrink-0 hover:bg-color-bg-subtle border border-border"
                              aria-label="Select icon"
                              style={{
                                borderRadius: "4px",
                                backgroundColor: formData.iconColor
                                  ? formData.iconColor
                                      .replace("rgb", "rgba")
                                      .replace(")", ", 0.2)")
                                  : ICON_COLORS.purple
                                      .replace("rgb", "rgba")
                                      .replace(")", ", 0.2)"),
                              }}
                            >
                              <IconPicker
                                iconColor={formData.iconColor}
                                name={formData.icon as IconName}
                              />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-[320px] p-3 h-[400px] overflow-y-auto">
                            <div className="space-y-3 h-full">
                              <div>
                                <div className="flex flex-wrap gap-2 mb-3">
                                  {Object.keys(ICON_COLORS).map((color) => (
                                    <Button
                                      key={color}
                                      style={{
                                        backgroundColor:
                                          ICON_COLORS[color as IconColor],
                                      }}
                                      className={cn(
                                        "h-[20px] w-[20px] p-0",
                                        formData.iconColor === color &&
                                          "border-primary",
                                      )}
                                      onClick={() => {
                                        // @ts-expect-error fix types
                                        setFormData((prev) => ({
                                          ...prev,
                                          iconColor:
                                            ICON_COLORS[color as IconColor],
                                        }));
                                      }}
                                    >
                                      <div
                                        className={cn(
                                          "w-4 h-4 rounded-full",
                                          ICON_COLORS[color as IconColor],
                                        )}
                                      />
                                    </Button>
                                  ))}
                                </div>
                                <Separator className="my-1" />
                                <Input
                                  placeholder="Search icons..."
                                  value={iconSearch}
                                  onChange={(e) =>
                                    setIconSearch(e.target.value)
                                  }
                                  className="h-7 my-3"
                                />
                                <div className="grid grid-cols-8 gap-2 mt-3">
                                  {filteredIcons.map((iconName) => (
                                    <TooltipWrapper
                                      key={iconName}
                                      tooltipContent={kebabCase(iconName)}
                                      asChild
                                    >
                                      <Button
                                        key={iconName}
                                        variant="ghost"
                                        className="h-[36px] w-[36px] p-0"
                                        onClick={() => {
                                          setFormData((prev) => ({
                                            ...prev,
                                            icon: iconName,
                                          }));
                                        }}
                                      >
                                        <IconPicker
                                          iconColor={formData.iconColor}
                                          name={iconName as IconName}
                                          showColor={false}
                                        />
                                      </Button>
                                    </TooltipWrapper>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                        <Input
                          id="team-name"
                          placeholder="Engineering"
                          className={`${errors.name ? "border-red-500" : ""}`}
                          value={formData.name}
                          onChange={(e) => {
                            const newName = e.target.value;
                            setFormData((prev) => ({
                              ...prev,
                              name: newName,
                              identifier: generateIdentifier(newName),
                            }));
                            if (errors.name) {
                              setErrors((prev) => ({ ...prev, name: "" }));
                            }
                          }}
                          required
                          aria-invalid={!!errors.name}
                          aria-describedby={
                            errors.name ? "name-error" : undefined
                          }
                        />
                      </div>
                      {errors.name && (
                        <p
                          id="name-error"
                          className="text-sm text-red-500 mt-1"
                        >
                          {errors.name}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* 32px spacer */}
                  <div style={{ height: "2px" }}></div>

                  <div>
                    <Label
                      htmlFor="identifier"
                      className="text-sm font-medium flex items-center"
                    >
                      Identifier
                      <span className="text-red-500">*</span>
                    </Label>
                    <p className="text-sm text-muted-text mb-2">
                      Set a unique identifier for easy reference of tickets from
                      this team.
                    </p>
                    <Input
                      id="identifier"
                      maxLength={7}
                      placeholder="E.g - ENG"
                      className={`${errors.identifier ? "border-red-500" : ""}`}
                      value={formData.identifier}
                      onChange={(e) => {
                        const upperValue = e.target.value.toUpperCase();
                        setFormData((prev) => ({
                          ...prev,
                          identifier: upperValue,
                        }));
                        if (errors.identifier) {
                          setErrors((prev) => ({ ...prev, identifier: "" }));
                        }
                      }}
                      required
                      aria-invalid={!!errors.identifier}
                      aria-describedby={
                        errors.identifier ? "identifier-error" : undefined
                      }
                    />
                    {errors.identifier && (
                      <p
                        id="identifier-error"
                        className="text-sm text-red-500 mt-1"
                      >
                        {errors.identifier}
                      </p>
                    )}
                  </div>

                  {/* 32px spacer */}
                  <div style={{ height: "2px" }}></div>

                  <div>
                    <Label className="text-sm font-medium">
                      Set team visibility
                    </Label>
                    <div className="text-sm text-muted-text mb-4">
                      <ul className="list-disc ml-4">
                        <li>
                          Public: Accessible to everyone in your organization.
                        </li>
                        <li>Private: Only accessible to invited members.</li>
                      </ul>
                    </div>
                    <RadioGroup
                      defaultValue="public"
                      className="grid grid-cols-2 gap-4"
                      value={formData.visibility}
                      onValueChange={(value) =>
                        setFormData((prev) => ({ ...prev, visibility: value }))
                      }
                    >
                      <Label
                        htmlFor="public"
                        className={cn(
                          "cursor-pointer flex items-center border rounded-sm px-4 py-3 flex-row-reverse justify-between hover:bg-accent transition-colors",
                          formData.visibility === "public" &&
                            "border border-primary",
                        )}
                      >
                        <RadioGroupItem
                          value="public"
                          id="public"
                          className="data-[state=checked]:border-primary"
                        />
                        <span className="font-normal flex items-center gap-2">
                          <Globe2 size={20} className="text-muted-foreground" />
                          Public
                        </span>
                      </Label>
                      <Label
                        htmlFor="private"
                        className={cn(
                          "cursor-pointer flex items-center border rounded-sm px-4 py-3 flex-row-reverse justify-between hover:bg-accent transition-colors",
                          formData.visibility === "private" &&
                            "border border-primary",
                        )}
                      >
                        <RadioGroupItem
                          value="private"
                          id="private"
                          className="data-[state=checked]:border-primary"
                        />
                        <span className="font-normal flex items-center gap-2">
                          <Lock size={20} className="text-muted-foreground" />
                          Private
                        </span>
                      </Label>
                    </RadioGroup>
                  </div>
                </div>

                <div className="flex justify-start">
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="group bg-gradient-to-r from-[var(--brand-gradient-start)] to-[var(--brand-gradient-end)] hover:from-[var(--brand-gradient-hover-start)] hover:to-[var(--brand-gradient-hover-end)] text-white"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin mr-2" />
                        Creating...
                      </>
                    ) : (
                      <>
                        Create team
                        <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
