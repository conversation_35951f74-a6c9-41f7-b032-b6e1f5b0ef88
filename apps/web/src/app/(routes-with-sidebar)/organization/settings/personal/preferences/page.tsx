"use client";

import CommonSelectWrapper from "@/components/common-select-wrapper";
import { useTheme, type Theme } from "@/components/theme-provider";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { HOURS } from "@/constants/working-hours";
import { cn } from "@/lib/utils";
import { validateWorkingHours } from "@/utils/settingsUtils";
import { format } from "date-fns";
import {
  Calendar as CalendarIcon,
  Check,
  Clock,
  Globe,
  SlackIcon as LucideSlackIcon,
  PaintbrushIcon,
  Plus,
  X,
} from "lucide-react";
import { DateTime } from 'luxon';
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { toast } from "sonner";
import WarningModal from "../../../../../../components/common/WarningModal";
import { Badge } from "../../../../../../components/ui/badge";
import { useApi } from "../../../../../../hooks/use-api";
import { useAppsSourcesStore } from "../../../../../../store/apps-sources-store";
import { useGlobalConfigPersistStore } from "../../../../../../store/globalConfigPersistStore";
import { getOrgDetails } from "../../../../../../utils/browserUtils";
import { getUserSessionDetails } from "../../../../../actions/auth";
import {
  deleteSlackAuth,
  generateSlackAuthUrl,
} from "../../../../../actions/generateSlackAuthUrl";
import { SourceDetails } from "../../../../dashboard/[teamId]/settings/sources/slack/page";

interface TimeSlot {
  start: string;
  end: string;
}

interface TimeOff {
  id: string;
  startDate: string;
  endDate: string;
  description: string;
  type: string;
  createdAt: string;
  updatedAt: string;
}

// Add interfaces for the API response and configurations
interface DailyConfig {
  isActive: boolean;
  slots: TimeSlot[];
}

interface PersonalPreferences {
  timezone?: string;
  commonDailyConfig?: boolean;
  commonSlots?: TimeSlot[];
  dailyConfig?: {
    [key: string]: DailyConfig;
  };
  holidays?: string[];
  slackAuthorized?: boolean;
}

interface DaySlots {
  [key: number]: TimeSlot[];
}

// Update the getSelectedDaysFromDailyConfig function with proper typing
const getSelectedDaysFromDailyConfig = (
  dailyConfig: Record<string, DailyConfig>,
) => {
  const daysMap: Record<string, number> = {
    monday: 0,
    tuesday: 1,
    wednesday: 2,
    thursday: 3,
    friday: 4,
    saturday: 5,
    sunday: 6,
  };

  return Object.entries(dailyConfig)

    .filter(([_, config]) => config.isActive)
    .map(([day]) => daysMap[day])
    .sort();
};

// Helper function to get styling info for preference types
const getPreferenceTypeInfo = (
  type: string,
): { label: string; icon: React.ReactNode } => {
  switch (type) {
    case "timezone":
      return {
        label: "Time zone",
        icon: <Globe className="w-4 h-4 text-muted-foreground" />,
      };
    case "working-hours":
      return {
        label: "Working hours",
        icon: <Clock className="w-4 h-4 text-muted-foreground" />,
      };
    case "time-off":
      return {
        label: "Holidays",
        icon: <CalendarIcon className="w-4 h-4 text-muted-foreground" />,
      };
    case "slack":
      return {
        label: "Slack authorization",
        icon: <LucideSlackIcon className="w-4 h-4 text-muted-foreground" />,
      };
    case "appearance":
      return {
        label: "Appearance",
        icon: <PaintbrushIcon className="w-4 h-4 text-muted-foreground" />,
      };
    default:
      return {
        label: type,
        icon: <Clock className="w-4 h-4 text-muted-foreground" />,
      };
  }
};

export default function PreferencesPage() {
  const { data: personalPreferences, loading } =
    useApi<PersonalPreferences>("/v1/users/details");
  // Time zone settings
  const [timezone, setTimezone] = useState(personalPreferences?.timezone || "");
  const [hasTimezoneChanges, setHasTimezoneChanges] = useState(false);

  // Working hours settings
  const [selectedDays, setSelectedDays] = useState<number[]>([0, 1, 2, 3, 4]);
  const [scheduleType, setScheduleType] = useState<"uniform" | "individual">(
    "individual",
  );
  const [daySlots, setDaySlots] = useState<DaySlots>({
    0: [{ start: "09:00", end: "17:00" }],
    1: [{ start: "09:00", end: "17:00" }],
    2: [{ start: "09:00", end: "17:00" }],
    3: [{ start: "09:00", end: "17:00" }],
    4: [{ start: "09:00", end: "17:00" }],
  });
  const [isWorkingHoursConfigured, setIsWorkingHoursConfigured] =
    useState(false);

  // Slack integration
  const sources = useAppsSourcesStore((state) => state.sources);
  const slackSource = sources.find((source) => source.type === "slack");
  const [slackDetails, setSlackDetails] = useState<SourceDetails | null>(null);
  const [slackUrl, setSlackUrl] = useState<string>(null);
  const [isLoading, setIsLoading] = useState(false);
  const currentUser = useGlobalConfigPersistStore((state) => state.currentUser);
  const dispatch = useGlobalConfigPersistStore((state) => state.dispatch);

  // Get all available timezones
  const timezones = Intl.supportedValuesOf("timeZone");

  const { theme, setTheme } = useTheme();

  // Memoize timezone options to avoid recreating on every render
  const timezoneOptions = useMemo(
    () =>
      Array.from(
        new Set(["UTC", ...timezones])
      ).map((tz) => ({ value: tz, label: tz })),
    [timezones],
  );

  // Time off and holidays state
  const [timeOffs, setTimeOffs] = useState<TimeOff[]>([]);
  const [isLoadingTimeOffs, setIsLoadingTimeOffs] = useState(true);
  const [_deletingTimeOffId, setDeletingTimeOffId] = useState<string | null>(
    null,
  );
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [totalTimeOffs, setTotalTimeOffs] = useState(0);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const observerTarget = useRef<HTMLDivElement>(null);

  const fetchTimeOffs = async (
    pageNum: number = 0,
    append: boolean = false,
  ) => {
    try {
      const response = await fetch(
        `/api/users/time-off?page=${pageNum}&limit=10`,
        {
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        },
      );
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch time off requests");
      }
      const data = await response.json();
      const newTimeOffs = data.data.timeOffs || [];
      setTotalTimeOffs(data.data.total);

      if (append) {
        setTimeOffs((prev) => [...prev, ...newTimeOffs]);
      } else {
        setTimeOffs(newTimeOffs);
      }

      setHasMore((pageNum + 1) * 2 < data.data.total);
    } catch (error) {
      console.error("Error fetching time off requests:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to load time off requests",
      );
    } finally {
      setIsLoadingTimeOffs(false);
      setIsLoadingMore(false);
    }
  };

  const loadMore = useCallback(() => {
    if (!isLoadingMore && hasMore) {
      setIsLoadingMore(true);
      const nextPage = page + 1;
      setPage(nextPage);
      fetchTimeOffs(nextPage, true);
    }
  }, [isLoadingMore, hasMore, page]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoadingMore) {
          loadMore();
        }
      },
      {
        root: null,
        rootMargin: "20px",
        threshold: 0.1,
      },
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => observer.disconnect();
  }, [hasMore, isLoadingMore, loadMore]);

  useEffect(() => {
    fetchTimeOffs();
  }, []);

  const handleTimeOffSubmit = async (date?: Date) => {
    if (!date) {
      toast.error("Please select a date");
      return;
    }

    try {
      
      // Convert Date to ISO string first, then create DateTime object
      const localStart = DateTime.fromJSDate(date, { zone: timezone }).startOf('day');
      const localEnd = localStart.endOf('day');
      const startDate = localStart.toUTC().toISO();
      const endDate = localEnd.toUTC().toISO();

      const response = await fetch("/api/users/time-off", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          startDate: startDate,
          endDate: endDate,
          description: "Time off request",
          type: "OTHER",
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create holiday");
      }

      await response.json();
      await fetchTimeOffs();
      toast.success("Holiday added successfully");
    } catch (error) {
      console.error("Error creating holiday:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to create holiday",
      );
    }
  };

  const handleTimezoneUpdate = async () => {
    try {
      const payload = {
        timezone: timezone === "" ? "UNSET" : timezone,
      };

      const response = await fetch("/api/users/business-hours", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error("Failed to update timezone");
      }

      toast.success("Timezone updated successfully");
      setHasTimezoneChanges(false);
    } catch (error) {
      console.error("Error updating timezone:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to update timezone",
      );
    }
  };

  const handleWorkingHoursUpdate = async () => {
    try {
      // Validate working hours before saving
      const validationError = validateWorkingHours({
        dayWiseHoursEnabled: true,
        workingDays: selectedDays.map((day) => {
          const days = [
            "monday",
            "tuesday",
            "wednesday",
            "thursday",
            "friday",
            "saturday",
            "sunday",
          ];
          return days[day];
        }),
        daySpecificHours: {
          monday: {
            slots: daySlots[0] || [],
            isActive: selectedDays.includes(0),
          },
          tuesday: {
            slots: daySlots[1] || [],
            isActive: selectedDays.includes(1),
          },
          wednesday: {
            slots: daySlots[2] || [],
            isActive: selectedDays.includes(2),
          },
          thursday: {
            slots: daySlots[3] || [],
            isActive: selectedDays.includes(3),
          },
          friday: {
            slots: daySlots[4] || [],
            isActive: selectedDays.includes(4),
          },
          saturday: {
            slots: daySlots[5] || [],
            isActive: selectedDays.includes(5),
          },
          sunday: {
            slots: daySlots[6] || [],
            isActive: selectedDays.includes(6),
          },
        },
        timezone: timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
        holidays: [],
        defaultHours: [{ start: "00:00", end: "23:59" }],
        commonDailyConfig: false,
        commonSlots: [{ start: "00:00", end: "23:59" }],
      });

      if (validationError) {
        toast.error(validationError);
        return;
      }

      //Removed commonDailyConfig and commonSlots for now
      const payload = {
        commonDailyConfig: false,
        dailyConfig: {
          monday: {
            isActive: selectedDays.includes(0),
            slots: selectedDays.includes(0) ? daySlots[0] : [],
          },
          tuesday: {
            isActive: selectedDays.includes(1),
            slots: selectedDays.includes(1) ? daySlots[1] : [],
          },
          wednesday: {
            isActive: selectedDays.includes(2),
            slots: selectedDays.includes(2) ? daySlots[2] : [],
          },
          thursday: {
            isActive: selectedDays.includes(3),
            slots: selectedDays.includes(3) ? daySlots[3] : [],
          },
          friday: {
            isActive: selectedDays.includes(4),
            slots: selectedDays.includes(4) ? daySlots[4] : [],
          },
          saturday: {
            isActive: selectedDays.includes(5),
            slots: selectedDays.includes(5) ? daySlots[5] : [],
          },
          sunday: {
            isActive: selectedDays.includes(6),
            slots: selectedDays.includes(6) ? daySlots[6] : [],
          },
        },
      };

      const response = await fetch("/api/users/business-hours", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error("Failed to update working hours");
      }

      toast.success("Working hours updated successfully");
      setIsWorkingHoursConfigured(selectedDays.length > 0);
    } catch (error) {
      console.error("Error updating working hours:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to update working hours",
      );
    }
  };

  const toggleWorkingDay = (dayIndex: number) => {
    setSelectedDays((prev) => {
      const newSelectedDays = prev.includes(dayIndex)
        ? prev.filter((index) => index !== dayIndex)
        : [...prev, dayIndex];

      // Sort the days
      newSelectedDays.sort((a, b) => a - b);

      // Update the day slots
      setDaySlots((prevSlots) => {
        const newSlots = { ...prevSlots };

        if (newSelectedDays.includes(dayIndex)) {
          // Initialize slots for newly selected day
          newSlots[dayIndex] = [{ start: "09:00", end: "17:00" }];
        } else {
          // Remove slots for deselected day
          delete newSlots[dayIndex];
        }

        return newSlots;
      });

      return newSelectedDays;
    });
  };

  const updateDaySlot = (
    dayIndex: number,
    slotIndex: number,
    property: keyof TimeSlot,
    value: string,
  ) => {
    setDaySlots((prev) => ({
      ...prev,
      [dayIndex]: prev[dayIndex].map((slot, i) =>
        i === slotIndex ? { ...slot, [property]: value } : slot,
      ),
    }));
  };

  const addDaySlot = (dayIndex: number) => {
    setDaySlots((prev) => ({
      ...prev,
      [dayIndex]: [...prev[dayIndex], { start: "09:00", end: "17:00" }],
    }));
  };

  const removeDaySlot = (dayIndex: number, slotIndex: number) => {
    setDaySlots((prev) => ({
      ...prev,
      [dayIndex]: prev[dayIndex].filter((_, i) => i !== slotIndex),
    }));
  };

  const generateSlackUrl = async (teamId: string) => {
    setIsLoading(true);
    const url = await generateSlackAuthUrl({
      slackId: teamId,
      authToken: slackDetails.key,
      email: currentUser.email,
    });

    setSlackUrl(url.url);
    setIsLoading(false);
  };

  useEffect(() => {
    const fetchSlackDetails = async () => {
      try {
        const response = await fetch(
          `/api/workspace/sources/${slackSource.id}?botUserId=${slackSource.botUserId}&type=slack`,
        );
        if (!response.ok) throw new Error("Failed to fetch source details");
        const data = await response.json();

        const userSessionDetails = await getUserSessionDetails(orgUid, orgId);
        const userData = userSessionDetails?.data?.user;
        dispatch({
          type: "SET_CURRENT_USER",
          payload: {
            currentUser: {
              ...currentUser,
              metadata: {
                ...userData?.metadata,
              },
            },
          },
        });
        setSlackDetails(data);
      } catch (error) {
        console.error("Error fetching source details:", error);
        toast.error("Failed to load source details");
      }
    };

    if (slackSource?.id && slackSource?.botUserId) {
      fetchSlackDetails();
    }
  }, [sources, slackSource?.id, slackSource?.botUserId]);
  const { orgId, orgUid } = getOrgDetails();
  const onWindowClosed = async () => {
    const result = await getUserSessionDetails(orgUid, orgId);
    const userData = result?.data?.user;
    dispatch({
      type: "SET_CURRENT_USER",
      payload: {
        currentUser: {
          id: userData.id,
          email: userData.email,
          name: userData.name,
          metadata: userData.metadata,
          uid: userData.currentUserUid || null,
          avatarUrl: userData.avatarUrl || null,
          allUserIds: userData.allUserIds || [],
        },
      },
    });
  };

  const [confirmationOpen, setConfirmationOpen] = useState(false);
  const [teamId, setTeamId] = useState(null);

  const handleSlackAuth = () => {
    const newWindow = window.open(
      slackUrl,
      "googleWindow",
      "left=400,top=100,width=600,height=620",
    );

    const checkWindowClosed = setInterval(() => {
      if (newWindow?.closed) {
        clearInterval(checkWindowClosed);
        onWindowClosed();
      }
    }, 500);
  };

  const isConfigured = useMemo(() => {
    return Boolean(
      selectedDays.length > 0 &&
        ((scheduleType === "uniform" && daySlots[0].length > 0) ||
          scheduleType === "individual"),
    );
  }, [selectedDays, scheduleType, daySlots]);

  useEffect(() => {
    if (personalPreferences) {
      // Update timezone
      setTimezone(
        personalPreferences.timezone ||
          Intl.DateTimeFormat().resolvedOptions().timeZone,
      );

      // Update working hours configuration with proper typing
      if (personalPreferences.dailyConfig) {
        setScheduleType("individual");

        const activeDays = getSelectedDaysFromDailyConfig(
          personalPreferences.dailyConfig,
        );
        setSelectedDays(activeDays);

        // Initialize daySlots with default values
        const newDaySlots: DaySlots = {
          0: [{ start: "09:00", end: "17:00" }],
          1: [{ start: "09:00", end: "17:00" }],
          2: [{ start: "09:00", end: "17:00" }],
          3: [{ start: "09:00", end: "17:00" }],
          4: [{ start: "09:00", end: "17:00" }],
          5: [{ start: "09:00", end: "17:00" }],
          6: [{ start: "09:00", end: "17:00" }],
        };

        // Update slots for each day from personalPreferences
        Object.entries(personalPreferences.dailyConfig).forEach(
          ([day, config]) => {
            const dayIndex = {
              monday: 0,
              tuesday: 1,
              wednesday: 2,
              thursday: 3,
              friday: 4,
              saturday: 5,
              sunday: 6,
            }[day.toLowerCase()];

            if (dayIndex !== undefined && config.slots) {
              newDaySlots[dayIndex] = config.slots || [
                { start: "09:00", end: "17:00" },
              ];
            } else {
              newDaySlots[dayIndex] = [{ start: "09:00", end: "17:00" }];
            }
          },
        );
        setDaySlots(newDaySlots);
      }

      // Update working hours configured status
      setIsWorkingHoursConfigured(Boolean(personalPreferences.dailyConfig));
    }
  }, [personalPreferences]);

  useEffect(() => {
    setIsWorkingHoursConfigured(isConfigured);
  }, [isConfigured]);

  const handleTimeOffDelete = async (id: string) => {
    try {
      setDeletingTimeOffId(id);
      const response = await fetch(`/api/users/time-off/${id}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete time off request");
      }

      // Refresh the time off list
      await fetchTimeOffs();
      toast.success("Selected time off deleted successfully");
    } catch (error) {
      console.error("Error deleting time off request:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to delete time off request",
      );
    } finally {
      setDeletingTimeOffId(null);
    }
  };

  const handleSlackDisconnect = async () => {
    await deleteSlackAuth({
      slackId: teamId,
      authToken: slackDetails.key,
      email: currentUser.email,
    });
    setConfirmationOpen(false);
    const result = await getUserSessionDetails(orgUid, orgId);
    const userData = result?.data?.user;

    dispatch({
      type: "SET_CURRENT_USER",
      payload: {
        currentUser: {
          id: userData.id,
          email: userData.email,
          name: userData.name,
          metadata: userData.metadata,
          uid: userData.currentUserUid || null,
          avatarUrl: userData.avatarUrl || null,
          allUserIds: userData.allUserIds || [],
        },
      },
    });
  };

  const nonAuthSlackChannels = slackDetails?.connections?.filter((team) => {
    return !(
      currentUser.metadata?.slackAuthSink &&
      currentUser.metadata.slackAuthSink[team?.teamId]
    );
  });

  return (
    <div className="h-full overflow-hidden">
      <div className="h-full p-6 pt-14 overflow-y-auto">
        <div className="max-w-[640px] mx-auto flex flex-col gap-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-medium">Preferences</h2>
              <p className="text-sm text-muted-foreground">
                Manage your personal preferences and working hours.
              </p>
            </div>
          </div>

          <div className="space-y-6">
            <Accordion
              type="multiple"
              defaultValue={["timezone"]}
              className="space-y-4"
            >
              {/* Time zone Section */}
              <AccordionItem value="timezone" className="border rounded-sm">
                <AccordionTrigger className="text-base font-normal px-4 py-3 data-[state=open]:border-b hover:no-underline">
                  <div className="flex items-center justify-between flex-1 pr-2">
                    <div className="flex items-center gap-2">
                      {getPreferenceTypeInfo("timezone").icon}
                      <span>{getPreferenceTypeInfo("timezone").label}</span>
                    </div>
                    {loading ? null : (
                      <span className="text-xs text-muted-foreground font-normal bg-muted px-2 py-0.5 rounded-sm">
                        {timezone}
                      </span>
                    )}
                  </div>
                </AccordionTrigger>
                <AccordionContent className="pt-4 px-4 py-3">
                  <div className="space-y-6">
                    <div className="flex flex-col space-y-2">
                      <h3 className="text-sm font-normal">
                        Select your timezone
                      </h3>
                      <div className="w-[500px]">
                        <style jsx global>{`
                          .timezone-select > div > div {
                            height: 32px !important;
                            border-radius: 4px !important;
                            min-height: 32px !important;
                          }
                        `}</style>
                        <CommonSelectWrapper
                          options={timezoneOptions}
                          value={{
                            value: timezone || "UTC",
                            label: timezone || "UTC",
                          }}
                          onChange={(option) => {
                            if (Array.isArray(option)) {
                              return;
                            }
                            setTimezone(option.value);
                            setHasTimezoneChanges(option.value !== timezone);
                          }}
                          wrapperClassname="w-full timezone-select"
                          hideClearIndicator={true}
                          isClearable={false}
                          disabled={loading}
                          minWidth={500}
                        />
                      </div>
                    </div>
                    {hasTimezoneChanges && (
                      <Button onClick={handleTimezoneUpdate}>
                        Update time zone
                      </Button>
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Working Hours Section */}
              <AccordionItem
                value="working-hours"
                className="border rounded-sm"
              >
                <AccordionTrigger className="text-base font-normal px-4 py-3 data-[state=open]:border-b hover:no-underline">
                  <div className="flex items-center justify-between flex-1 pr-2">
                    <div className="flex items-center gap-2">
                      {getPreferenceTypeInfo("working-hours").icon}
                      <span>
                        {getPreferenceTypeInfo("working-hours").label}
                      </span>
                    </div>
                    {isWorkingHoursConfigured ? (
                      <span className="text-xs text-green-700 font-normal bg-green-100 px-2 py-0.5 rounded-sm">
                        Configured
                      </span>
                    ) : (
                      <span className="text-xs text-muted-foreground font-normal bg-muted px-2 py-0.5 rounded-sm">
                        Not configured
                      </span>
                    )}
                  </div>
                </AccordionTrigger>
                <AccordionContent className="pt-4 px-4 py-3">
                  <div className="space-y-6">
                    {/* Working Days Selection */}
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-sm font-normal">Working days</h3>
                        <p className="text-sm text-muted-foreground">
                          Select the days you operate.
                        </p>
                      </div>
                      <div className="flex gap-2 mt-4">
                        {["M", "T", "W", "T", "F", "S", "S"].map(
                          (day, index) => (
                            <Button
                              key={day + index}
                              type="button"
                              variant="outline"
                              size="sm"
                              className={cn(
                                "w-10 h-10 p-0 text-sm font-medium",
                                selectedDays.includes(index) &&
                                  "bg-accent text-accent-foreground border-[var(--color-border)] border-2",
                              )}
                              onClick={() => toggleWorkingDay(index)}
                            >
                              {day}
                            </Button>
                          ),
                        )}
                      </div>
                    </div>

                    {/* Schedule Type Selection */}
                    {/* hide for now */}
                    {/* <div className="space-y-4">
                      <Label className="text-base">Schedule type</Label>
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-base font-medium">
                            Day-specific hours
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            Enable to set different working hours for each day.
                          </p>
                        </div>
                        <Switch
                          checked={scheduleType === "individual"}
                          onCheckedChange={(checked) => {
                            setScheduleType(checked ? "individual" : "uniform");
                          }}
                        />
                      </div>
                    </div> */}
                    {/* Individual Schedule */}
                    {scheduleType === "individual" &&
                      selectedDays.length > 0 && (
                        <div className="space-y-6">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="text-base font-normal">
                                Working hours
                              </h3>
                              <p className="text-sm text-muted-foreground">
                                Define the start and end times for your working
                                days.
                              </p>
                            </div>
                          </div>

                          <div className="space-y-6">
                            {selectedDays.map((dayIndex) => (
                              <div
                                key={dayIndex}
                                className="grid grid-cols-[120px_1fr] gap-4 items-start"
                              >
                                <span className="text-base">
                                  {
                                    [
                                      "Monday",
                                      "Tuesday",
                                      "Wednesday",
                                      "Thursday",
                                      "Friday",
                                      "Saturday",
                                      "Sunday",
                                    ][dayIndex]
                                  }
                                </span>
                                <div className="space-y-3">
                                  {daySlots[dayIndex].map((slot, slotIndex) => (
                                    <div
                                      key={slotIndex}
                                      className="flex items-center gap-2"
                                    >
                                      <div className="flex items-center gap-3">
                                        <Select
                                          value={slot.start}
                                          onValueChange={(value) =>
                                            updateDaySlot(
                                              dayIndex,
                                              slotIndex,
                                              "start",
                                              value,
                                            )
                                          }
                                        >
                                          <SelectTrigger className="w-[120px]">
                                            <SelectValue placeholder="00:00" />
                                          </SelectTrigger>
                                          <SelectContent
                                            className="max-h-[300px] overflow-y-auto"
                                            align="start"
                                            sideOffset={4}
                                          >
                                            {HOURS.map((hour) => (
                                              <SelectItem
                                                key={hour.value}
                                                value={hour.value}
                                                className="h-[36px]"
                                              >
                                                {hour.label}
                                              </SelectItem>
                                            ))}
                                          </SelectContent>
                                        </Select>
                                        <span className="text-sm text-gray-500">
                                          to
                                        </span>
                                        <Select
                                          value={slot.end}
                                          onValueChange={(value) =>
                                            updateDaySlot(
                                              dayIndex,
                                              slotIndex,
                                              "end",
                                              value,
                                            )
                                          }
                                        >
                                          <SelectTrigger className="w-[120px]">
                                            <SelectValue placeholder="23:59" />
                                          </SelectTrigger>
                                          <SelectContent
                                            className="max-h-[300px] overflow-y-auto"
                                            align="start"
                                            sideOffset={4}
                                          >
                                            {HOURS.map((hour) => (
                                              <SelectItem
                                                key={hour.value}
                                                value={hour.value}
                                                className="h-[36px]"
                                              >
                                                {hour.label}
                                              </SelectItem>
                                            ))}
                                          </SelectContent>
                                        </Select>
                                      </div>
                                      {daySlots[dayIndex].length > 1 && (
                                        <Button
                                          variant="ghost"
                                          size="icon"
                                          onClick={() =>
                                            removeDaySlot(dayIndex, slotIndex)
                                          }
                                          className="h-8 w-8 text-gray-400 hover:text-gray-500"
                                        >
                                          <X className="h-4 w-4" />
                                        </Button>
                                      )}

                                      {slotIndex ===
                                        daySlots[dayIndex].length - 1 &&
                                        daySlots[dayIndex].length < 6 && (
                                        <Button
                                          variant="ghost"
                                          className="flex items-center gap-2 text-primary hover:text-primary hover:bg-transparent px-2"
                                          onClick={() => addDaySlot(dayIndex)}
                                        >
                                          <Plus className="h-4 w-4" />
                                          <span>Add slot</span>
                                        </Button>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                    {/* Uniform Schedule */}
                    {scheduleType === "uniform" && selectedDays.length > 0 && (
                      <div className="space-y-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="text-base font-medium">
                              Working hours
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              Define the start and end times for your working
                              days.
                            </p>
                          </div>
                        </div>

                        <div className="space-y-3">
                          {daySlots[0].map((slot, index) => (
                            <div
                              key={index}
                              className="flex items-center gap-2"
                            >
                              <div className="flex items-center gap-3">
                                <Select
                                  value={slot.start}
                                  onValueChange={(value) =>
                                    updateDaySlot(index, 0, "start", value)
                                  }
                                >
                                  <SelectTrigger className="w-[120px]">
                                    <SelectValue placeholder="00:00" />
                                  </SelectTrigger>
                                  <SelectContent
                                    className="max-h-[300px] overflow-y-auto"
                                    align="start"
                                    sideOffset={4}
                                  >
                                    {HOURS.map((hour) => (
                                      <SelectItem
                                        key={hour.value}
                                        value={hour.value}
                                        className="h-[36px]"
                                      >
                                        {hour.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <span className="text-sm text-gray-500">
                                  to
                                </span>
                                <Select
                                  value={slot.end}
                                  onValueChange={(value) =>
                                    updateDaySlot(index, 0, "end", value)
                                  }
                                >
                                  <SelectTrigger className="w-[120px]">
                                    <SelectValue placeholder="23:59" />
                                  </SelectTrigger>
                                  <SelectContent
                                    className="max-h-[300px] overflow-y-auto"
                                    align="start"
                                    sideOffset={4}
                                  >
                                    {HOURS.map((hour) => (
                                      <SelectItem
                                        key={hour.value}
                                        value={hour.value}
                                        className="h-[36px]"
                                      >
                                        {hour.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                              {daySlots[0].length > 1 && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => removeDaySlot(0, index)}
                                  className="h-8 w-8 text-gray-400 hover:text-gray-500"
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              )}
                              {index === daySlots[0].length - 1 &&
                                daySlots[0].length < 6 && (
                                <Button
                                  variant="ghost"
                                  className="flex items-center gap-2 text-primary hover:text-primary hover:bg-transparent px-2"
                                  onClick={() => addDaySlot(0)}
                                >
                                  <Plus className="h-4 w-4" />
                                  <span>Add slot</span>
                                </Button>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="flex justify-end mt-4">
                    <Button onClick={handleWorkingHoursUpdate}>
                      Save working hours
                    </Button>
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Time off Section */}
              <AccordionItem value="timeOff" className="border rounded-sm">
                <AccordionTrigger className="text-base font-normal px-4 py-3 data-[state=open]:border-b hover:no-underline">
                  <div className="flex items-center justify-between flex-1 pr-2">
                    <div className="flex items-center gap-2">
                      {getPreferenceTypeInfo("time-off").icon}
                      <span>{getPreferenceTypeInfo("time-off").label}</span>
                    </div>
                    {timeOffs.length > 0 ? (
                      <span className="text-xs text-green-700 font-normal bg-green-100 px-2 py-0.5 rounded-sm">
                        {totalTimeOffs} selected
                      </span>
                    ) : (
                      <span className="text-xs text-muted-foreground font-normal bg-muted px-2 py-0.5 rounded-sm">
                        No time-off
                      </span>
                    )}
                  </div>
                </AccordionTrigger>
                <AccordionContent className="pt-4 px-4 py-3">
                  <div className="space-y-2">
                    <h3 className="text-sm font-normal">
                      Add holidays when you won&apos;t be available.
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Selected dates will be marked as unavailable for the full day in your local timezone ({timezone || Intl.DateTimeFormat().resolvedOptions().timeZone}).
                    </p>
                    {/* Add Time Off Form */}
                    <div className="flex flex-col sm:flex-row gap-3">
                      <div className="flex-1 space-y-3">
                        <Popover onOpenChange={(open) => {
                          if (!open) {
                            setSelectedDate(null);
                          }
                        }}>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="w-[172px] justify-start text-left font-normal text-muted-foreground"
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              <span>{selectedDate ? format(selectedDate, "PPP") : "Pick a date"}</span>
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent
                            className="w-auto p-0"
                            onOpenAutoFocus={(e) => e.preventDefault()}
                          >
                            <Calendar
                              mode="single"
                              selected={selectedDate ?? undefined}
                              onSelect={(date) => {
                                if (date) {
                                  setSelectedDate(date);
                                  handleTimeOffSubmit(date);
                                }
                              }}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>

                    {/* Time Off List */}
                    {isLoadingTimeOffs && timeOffs.length === 0 ? (
                      <div className="text-center py-4 text-muted-foreground">
                        Loading time off requests...
                      </div>
                    ) : timeOffs.length === 0 ? (
                      <div className="text-center py-4 text-muted-foreground">
                        No time off requests yet
                      </div>
                    ) : (
                      <div className="w-full flex flex-wrap gap-2">
                        {timeOffs.map((timeOff) => (
                          <Badge
                            key={timeOff.id}
                            variant="secondary"
                            className="font-medium py-1"
                          >
                            {format(
                              new Date(timeOff.startDate),
                              "MMMM d, yyyy",
                            )}{" "}

                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleTimeOffDelete(timeOff.id)}
                              className="h-4 w-4 ml-2 p-0"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Slack Authorization Section */}
              <AccordionItem value="slack" className="border rounded-sm">
                <AccordionTrigger className="text-base font-normal px-4 py-3 data-[state=open]:border-b hover:no-underline">
                  <div className="flex items-center justify-between flex-1 pr-2">
                    <div className="flex items-center gap-2">
                      {getPreferenceTypeInfo("slack").icon}
                      <span>{getPreferenceTypeInfo("slack").label}</span>
                    </div>
                    {slackDetails?.connections?.some(
                      (team) =>
                        currentUser.metadata?.slackAuthSink &&
                        currentUser.metadata.slackAuthSink[team?.teamId],
                    ) ? (
                      <span className="text-xs text-green-700 font-normal bg-green-100 px-2 py-0.5 rounded-sm">
                        Connected
                      </span>
                    ) : (
                      <span className="text-xs text-muted-foreground font-normal bg-muted px-2 py-0.5 rounded-sm">
                        Not connected
                      </span>
                    )}
                  </div>
                </AccordionTrigger>
                {slackDetails && (
                  <AccordionContent className="pt-4 px-4 py-3">
                    <div className="space-y-6">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <h4 className="text-sm font-medium">Connect Slack</h4>
                          <p className="text-sm text-muted-foreground">
                            Allow sending replies as you instead of a bot
                          </p>
                        </div>
                        {nonAuthSlackChannels?.length > 0 && (
                          <div className="flex items-center gap-2">
                            <Select
                              onValueChange={(value) => generateSlackUrl(value)}
                            >
                              <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Select a workspace" />
                              </SelectTrigger>
                              <SelectContent>
                                {nonAuthSlackChannels.map((team) => {
                                  return (
                                    <SelectItem
                                      key={team.teamId}
                                      value={team.teamId}
                                    >
                                      {team.workspaceName}
                                    </SelectItem>
                                  );
                                })}
                              </SelectContent>
                            </Select>
                            <Button
                              loading={isLoading}
                              disabled={!slackUrl || isLoading}
                              variant="default"
                              onClick={handleSlackAuth}
                            >
                              Connect
                            </Button>
                          </div>
                        )}
                      </div>
                      <div className="font-medium text-base">Workspaces: </div>
                      <div className="flex flex-col gap-2">
                        {slackDetails?.connections?.map((team) => {
                          const isAuthorized =
                            currentUser.metadata?.slackAuthSink &&
                            currentUser.metadata.slackAuthSink[team?.teamId];
                          return (
                            <div
                              key={team.teamId}
                              className="flex items-center justify-between"
                            >
                              <span>{team.workspaceName}</span>
                              <div className="flex items-center gap-2">
                                {isAuthorized && (
                                  <Button
                                    variant="destructive"
                                    className="h-8"
                                    onClick={() => {
                                      setTeamId(team.teamId);
                                      setConfirmationOpen(true);
                                    }}
                                  >
                                    Disconnect
                                  </Button>
                                )}
                                <span>
                                  {isAuthorized ? (
                                    <span className="text-sm text-green-700 font-normal bg-green-50 px-2 py-0.5 rounded-sm">
                                      Authorized
                                    </span>
                                  ) : (
                                    <span className="text-sm text-muted-foreground font-normal bg-muted px-2 py-0.5 rounded-sm">
                                      Not authorized
                                    </span>
                                  )}
                                </span>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </AccordionContent>
                )}
              </AccordionItem>

              {/* Appearance Section */}
              <AccordionItem value="appearance" className="border rounded-sm">
                <AccordionTrigger className="text-base font-normal px-4 py-3 data-[state=open]:border-b hover:no-underline">
                  <div className="flex items-center justify-between flex-1 pr-2">
                    <div className="flex items-center gap-2">
                      {getPreferenceTypeInfo("appearance").icon}
                      <span>{getPreferenceTypeInfo("appearance").label}</span>
                    </div>
                    <span className="text-xs text-muted-foreground font-normal bg-muted px-2 py-0.5 rounded-sm capitalize">
                      {theme}
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="pt-4 px-4 py-3">
                  <div className="space-y-6">
                    <div className="space-y-1">
                      <h4 className="text-sm font-normal">Theme</h4>
                      <p className="text-sm text-muted-foreground">
                        Choose a theme for your workspace
                      </p>
                    </div>
                    <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-3 mx-1">
                      {[
                        {
                          name: "mist",
                          label: "Mist",
                          outerColor: "bg-white border-zinc-200",
                          innerColor: "bg-[#adc3db]",
                          textColor: "text-zinc-950",
                          bgColor: "bg-white",
                          borderColor: "border-zinc-200",
                          order: 1,
                        },
                        {
                          name: "breeze",
                          label: "Breeze",
                          outerColor: "bg-white border-zinc-200",
                          innerColor: "bg-[#d3b6fb]",
                          textColor: "text-zinc-900",
                          bgColor: "bg-[hsl(230,60%,97%)]",
                          borderColor: "border-purple-100",
                          order: 2,
                        },
                        {
                          name: "lumen",
                          label: "Lumen",
                          outerColor: "bg-white border-zinc-200",
                          innerColor: "bg-[#71717a]",
                          textColor: "text-[hsl(220,10%,20%)]",
                          bgColor: "bg-white",
                          borderColor: "border-[hsl(220,10%,90%)]",
                          order: 3,
                        },
                        {
                          name: "carbon",
                          label: "Carbon",
                          outerColor: "bg-zinc-100 border-zinc-300",
                          innerColor: "bg-[#18181b]",
                          textColor: "text-zinc-50",
                          bgColor: "bg-[hsl(240,5%,4%)]",
                          borderColor: "border-zinc-800",
                          order: 4,
                        },
                        {
                          name: "midnight",
                          label: "Midnight",
                          outerColor: "bg-zinc-100 border-zinc-300",
                          innerColor: "bg-[#1e3a8a]",
                          textColor: "text-zinc-50",
                          bgColor: "bg-[hsl(229,84%,5%)]",
                          borderColor: "border-zinc-800",
                          order: 5,
                        },
                        {
                          name: "nebula",
                          label: "Nebula",
                          outerColor: "bg-zinc-100 border-zinc-300",
                          innerColor: "bg-[#50229f]",
                          textColor: "text-zinc-50",
                          bgColor: "bg-[hsl(245,15%,14%)]",
                          borderColor: "border-purple-900",
                          order: 6,
                        },
                      ]
                        .sort((a, b) => a.order - b.order)
                        .map((item) => (
                          <div
                            key={item.name}
                            className={cn(
                              "relative flex cursor-pointer flex-col items-center justify-between rounded-md border p-4",
                              item.bgColor,
                              item.borderColor,
                              {
                                "ring-2 ring-primary": theme === item.name,
                              },
                            )}
                            onClick={() => setTheme(item.name as Theme)}
                          >
                            <div className="flex items-center justify-center mb-3">
                              <div
                                className={cn(
                                  "h-6 w-6 rounded-full border flex items-center justify-center",
                                  item.outerColor,
                                )}
                              >
                                <div
                                  className={cn(
                                    "h-4 w-4 rounded-full",
                                    item.innerColor,
                                  )}
                                />
                              </div>
                            </div>
                            <span
                              className={cn(
                                "text-sm font-medium",
                                item.textColor,
                              )}
                            >
                              {item.label}
                            </span>
                            {theme === item.name && (
                              <div className="absolute right-2 top-2">
                                <Check className="h-4 w-4 text-primary" />
                              </div>
                            )}
                          </div>
                        ))}
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </div>

      <WarningModal
        setIsOpen={setConfirmationOpen}
        isOpen={confirmationOpen}
        title="Are you sure you want to disconnect slack?"
        okText="Disconnect"
        onOk={handleSlackDisconnect}
      />
    </div>
  );
}
