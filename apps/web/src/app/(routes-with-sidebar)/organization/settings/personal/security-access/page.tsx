"use client";

import { changePassword, signout } from "@/app/actions/auth";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Eye, EyeOff, Key, Lock } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { useApiMutation } from "../../../../../../hooks/use-api-mutation";
import { getOrgDetails } from "../../../../../../utils/browserUtils";

type ApiKeyData = {
  apiKey: {
    name: string;
    keyId: string;
    keyHash: string;
    type: string;
    expiresAt: string | null;
    userId: string;
    organizationId: string;
    metadata: {
      createdFromIp: string;
      lastUsedFromIp: string;
      userAgent: string;
    };
    createdBy: string;
    isActive: boolean;
    description: string | null;
    updatedBy: string | null;
    id: string;
    keyVersion: number;
    status: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
  };
  secretKey: string;
};

const SecurityAccessPage = () => {
  const [apiKey, setApiKey] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    currentPassword: false,
    newPassword: false,
    confirmPassword: false,
  });
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [passwordErrors, setPasswordErrors] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const { orgUid, orgId } = getOrgDetails();
  const { mutate } = useApiMutation<ApiKeyData>(
    `/v1/api-keys`,
    {},
    "POST",
    "auth",
  );

  const regenerateApiKey = () => {
    // In a real implementation, this would make an API call
    mutate({
      name: "API key",
    }).then((response: ApiKeyData) => {
      if (response) {
        setApiKey(response.secretKey);
        toast.success("API key regenerated successfully.");
      } else {
        toast.error("Failed to regenerate API key.");
      }
    });
    // setGeneratedAt(new Date());
  };

  const handlePasswordChange = (
    field: keyof typeof passwordForm,
    value: string,
  ) => {
    // Prevent spaces in password
    if (value.includes(" ")) {
      toast.error("Password cannot contain spaces");
      return;
    }

    setPasswordForm((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    setPasswordErrors((prev) => ({ ...prev, [field]: "" }));
  };

  const validatePassword = (password: string) => {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    if (password.length < minLength) {
      return "Password must be at least 8 characters long";
    }
    if (!hasUpperCase) {
      return "Password must contain at least one uppercase letter";
    }
    if (!hasLowerCase) {
      return "Password must contain at least one lowercase letter";
    }
    if (!hasNumbers) {
      return "Password must contain at least one number";
    }
    if (!hasSpecialChar) {
      return "Password must contain at least one special character";
    }
    return "";
  };

  const validatePasswordForm = () => {
    const errors = {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    };
    let isValid = true;

    if (!passwordForm.currentPassword) {
      errors.currentPassword = "Current password is required.";
      isValid = false;
    }

    if (!passwordForm.newPassword) {
      errors.newPassword = "New password is required.";
      isValid = false;
    } else {
      const passwordValidationError = validatePassword(
        passwordForm.newPassword,
      );
      if (passwordValidationError) {
        errors.newPassword = passwordValidationError;
        isValid = false;
      }
    }

    if (passwordForm.currentPassword === passwordForm.newPassword) {
      errors.newPassword =
        "New password must be different from current password";
      isValid = false;
    }

    if (!passwordForm.confirmPassword) {
      errors.confirmPassword = "Please confirm your new password.";
      isValid = false;
    } else if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      errors.confirmPassword = "Passwords do not match.";
      isValid = false;
    }

    setPasswordErrors(errors);
    return isValid;
  };

  const handlePasswordUpdate = async () => {
    if (!validatePasswordForm()) return;

    setIsLoading(true);
    try {
      const response = await changePassword(
        passwordForm.currentPassword,
        passwordForm.newPassword,
        orgUid,
        orgId,
      );

      if (!response.success) {
        toast.error(response.error);
        return;
      }

      toast.success(
        "Your password has been successfully updated. You will be logged out for security reasons.",
        {
          duration: 5000,
        },
      );

      // Clear form
      setPasswordForm({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });

      // Sign out after a short delay to allow the user to see the success message
      setTimeout(async () => {
        try {
          await signout();
          window.location.href = "/auth/login";
        } catch (error) {
          console.error("Error during sign out:", error);
        }
      }, 2000);
    } catch (error: unknown) {
      toast.error(
        error instanceof Error ? error.message : "Failed to update password.",
      );
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {
    setShowPasswords((prev) => ({ ...prev, [field]: !prev[field] }));
  };

  return (
    <div className="flex flex-col justify-center items-center w-full">
      <div className="flex-1 bg-background py-8 flex items-center justify-center w-[640px]">
        <div className="w-full pt-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-medium">Security and access</h2>
              <p className="text-sm text-gray-500 pb-6">
                Manage your security settings and API access credentials.
              </p>
            </div>
          </div>

          <form className="space-y-8" onSubmit={(e) => e.preventDefault()}>
            <div className="space-y-4">
              <div className="w-full">
                <Label
                  htmlFor="api-key"
                  className="text-sm font-medium flex items-center gap-2"
                >
                  <Key className="h-4 w-4" />
                  Personal API key.
                </Label>
                <p className="text-sm text-gray-500 mb-2">
                  Your personal API key for accessing Thena programmatically.
                </p>
                <div className="flex flex-col gap-2 mt-2">
                  <div className="flex gap-2 w-full">
                    <Input
                      id="api-key"
                      type={apiKey ? "text" : "password"}
                      value={apiKey ? apiKey : "*****************"}
                      readOnly
                      className="flex-1 font-mono bg-muted"
                    />
                    <Button variant="outline" onClick={regenerateApiKey}>
                      Regenerate
                    </Button>
                  </div>
                  {/* <p className="text-sm text-muted-foreground">
                    Generated on: {generatedAt.toLocaleString()}
                  </p> */}
                </div>
              </div>

              <div className="mt-4">
                <p className="text-sm text-gray-500">
                  Copy and keep your API key secure. If compromised, regenerate
                  it immediately. This is one time view only.
                </p>
              </div>
            </div>

            <Separator className="my-6" />

            <div className="space-y-4">
              <div className="w-full">
                <Label
                  htmlFor="current-password"
                  className="text-sm font-medium flex items-center gap-2"
                >
                  <Lock className="h-4 w-4" />
                  Change password.
                </Label>
                <p className="text-sm text-gray-500 mb-2">
                  Update your account password.
                </p>
                <div className="flex flex-col gap-4 mt-2">
                  <div className="space-y-4">
                    <div>
                      <Label
                        htmlFor="current-password"
                        className="text-sm font-medium"
                      >
                        Current password.{" "}
                        <span className="text-red-500">*</span>
                      </Label>
                      <div className="relative">
                        <Input
                          id="current-password"
                          type={
                            showPasswords.currentPassword ? "text" : "password"
                          }
                          value={passwordForm.currentPassword}
                          onChange={(e) =>
                            handlePasswordChange(
                              "currentPassword",
                              e.target.value,
                            )
                          }
                          className="mt-1 pr-10"
                        />
                        <button
                          type="button"
                          onClick={() =>
                            togglePasswordVisibility("currentPassword")
                          }
                          className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                        >
                          {showPasswords.currentPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                      {passwordErrors.currentPassword && (
                        <p className="text-sm text-red-500 mt-1">
                          {passwordErrors.currentPassword}
                        </p>
                      )}
                    </div>
                    <div>
                      <Label
                        htmlFor="new-password"
                        className="text-sm font-medium"
                      >
                        New password. <span className="text-red-500">*</span>
                      </Label>
                      <div className="relative">
                        <Input
                          id="new-password"
                          type={showPasswords.newPassword ? "text" : "password"}
                          value={passwordForm.newPassword}
                          onChange={(e) =>
                            handlePasswordChange("newPassword", e.target.value)
                          }
                          className="mt-1 pr-10"
                        />
                        <button
                          type="button"
                          onClick={() =>
                            togglePasswordVisibility("newPassword")
                          }
                          className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                        >
                          {showPasswords.newPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                      {passwordErrors.newPassword && (
                        <p className="text-sm text-red-500 mt-1">
                          {passwordErrors.newPassword}
                        </p>
                      )}
                    </div>
                    <div>
                      <Label
                        htmlFor="confirm-password"
                        className="text-sm font-medium"
                      >
                        Confirm new password.{" "}
                        <span className="text-red-500">*</span>
                      </Label>
                      <div className="relative">
                        <Input
                          id="confirm-password"
                          type={
                            showPasswords.confirmPassword ? "text" : "password"
                          }
                          value={passwordForm.confirmPassword}
                          onChange={(e) =>
                            handlePasswordChange(
                              "confirmPassword",
                              e.target.value,
                            )
                          }
                          className="mt-1 pr-10"
                        />
                        <button
                          type="button"
                          onClick={() =>
                            togglePasswordVisibility("confirmPassword")
                          }
                          className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                        >
                          {showPasswords.confirmPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                      {passwordErrors.confirmPassword && (
                        <p className="text-sm text-red-500 mt-1">
                          {passwordErrors.confirmPassword}
                        </p>
                      )}
                    </div>
                    <Button
                      onClick={handlePasswordUpdate}
                      disabled={
                        isLoading ||
                        !passwordForm.currentPassword ||
                        !passwordForm.newPassword ||
                        !passwordForm.confirmPassword
                      }
                    >
                      {isLoading ? "Updating..." : "Update password"}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default SecurityAccessPage;
