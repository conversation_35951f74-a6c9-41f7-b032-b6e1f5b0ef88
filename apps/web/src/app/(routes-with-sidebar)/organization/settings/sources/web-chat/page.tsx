"use client";

import { useQuery } from "@tanstack/react-query";
import { <PERSON><PERSON><PERSON><PERSON>, Clock, Loader2, <PERSON><PERSON>p<PERSON><PERSON>, Rocket } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { WebChatAgentBanner } from "../../../../../../components/agents/web-chat-agent-banner";
import CommonSelectWrapper from "../../../../../../components/common-select-wrapper";
import type { SelectOptionType } from "../../../../../../components/common-select-wrapper/types";
import { Button } from "../../../../../../components/ui/button";
import { AGENT_NAME } from "../../../../../../constants/web-chat";
import { getAgents } from "../../../../../../lib/api/agents";
import { useTicketMetaStore } from "../../../../../../store/ticket-meta-store";

interface App {
  uid: string;
  name: string;
  description: string;
  manifest: {
    app: {
      icons: {
        small: string;
        large: string;
      };
    };
    developer: {
      name: string;
      privacy_policy_url?: string;
      terms_url?: string;
    };
    metadata?: {
      template_id?: string;
    };
    activities?: Array<{
      name: string;
      description: string;
    }>;
  };
  isInstalled: boolean;
  template?: {
    id: string;
    title: string;
    category: string;
    description: string;
    metadata: {
      rating?: number;
      pricing?: {
        yearly?: number;
        monthly?: number;
      };
    };
    capabilities?: string[];
    configuration?: {
      goal?: string;
      role?: string;
      tone?: string;
      personality_traits?: string[];
    };
    avatar_url?: string;
    comingSoon?: boolean;
  };
}

const fetchApps = async ({
  category,
  page = 1,
  limit = 50, // Increased limit to ensure we get all the apps we need to transform
}: {
  category?: string;
  page?: number;
  limit?: number;
}): Promise<{
  data: App[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}> => {
  const params = new URLSearchParams();
  params.append("page", page.toString());
  params.append("limit", limit.toString());
  if (category && category !== "all") params.append("category", category);

  const response = await fetch(`/api/marketplace?${params.toString()}`);
  if (!response.ok) {
    throw new Error("Failed to fetch apps");
  }
  return response.json();
};

export default function WebChatSettingsPage() {
  const [selectedTeam, setSelectedTeam] = useState<SelectOptionType | null>(
    null,
  );
  const [isLoadingDeployments, setIsLoadingDeployments] = useState(true);
  const [isDeploymentInProgress, setIsDeploymentInProgress] = useState(false);
  const [deploymentStep, setDeploymentStep] = useState<
    "hiring" | "waiting-for-agent" | "deploying" | "completed" | null
  >(null);
  const [showProgressCard, setShowProgressCard] = useState(false);

  const [allowedDomains, setAllowedDomains] = useState<string[]>([]);
  const teams = useTicketMetaStore((state) => state.teams);
  const validDomains = allowedDomains.filter((domain) => domain.trim() !== "");
  const [currentDeployedTeam, setCurrentDeployedTeam] = useState(null);
  const { data: appsData, refetch } = useQuery<{
    data: App[];
    meta: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }>({
    queryKey: ["marketplace-apps", "all"],
    queryFn: () =>
      fetchApps({
        category: "all",
      }),
    staleTime: 1000 * 60 * 5, // 5 minutes
    enabled: typeof window !== "undefined",
  });

  const appDetails = useMemo(() => {
    return appsData?.data.find((app) => app.name === AGENT_NAME);
  }, [appsData]);

  const router = useRouter();

  const { data: agents } = useQuery({
    queryKey: ["agents"],
    queryFn: () => getAgents(),
    refetchInterval: deploymentStep === "waiting-for-agent" ? 2000 : false, // Poll more frequently when waiting for agent
    staleTime: 5000,
    enabled: !!appDetails?.uid,
  });
  const agentDetails = useMemo(() => {
    return agents?.find((agent) => agent.name === AGENT_NAME);
  }, [agents, appDetails, selectedTeam]);

  // Effect to handle deployment when agent becomes available after hiring
  useEffect(() => {
    if (deploymentStep === "waiting-for-agent" && agentDetails?.id) {
      setDeploymentStep("deploying");
      handleDeployment();
    }
  }, [agentDetails?.id, deploymentStep]);

  // Fetch existing deployments when component mounts
  useEffect(() => {
    async function fetchDeployments() {
      setIsLoadingDeployments(true);
      try {
        const response = await fetch(
          `/api/agents/${agentDetails?.id}/deployments`,
        );

        if (!response.ok) {
          const errorData = await response.json();
          console.error("Failed to fetch deployments", errorData);
          setIsLoadingDeployments(false);
          return;
        }

        const deployments = await response.json();
        // If we have deployments for this agent, use the first one
        if (deployments && deployments.length > 0) {
          const latestDeployment = deployments[0];
          // If the deployment has allowed origins, update state
          if (
            latestDeployment.allowed_origins &&
            latestDeployment.allowed_origins.length > 0
          ) {
            setAllowedDomains(latestDeployment.allowed_origins);
          }

          // Try to set selected team based on team_id if available
          if (latestDeployment.team_id) {
            // You might need to fetch team details or handle this differently
            // For now, we'll create a minimal team object
            setCurrentDeployedTeam(latestDeployment.team_id);
            setSelectedTeam({
              label: teams.find((item) => item.uid === latestDeployment.team_id)
                ?.name,
              value: latestDeployment.team_id,
              heroIcon: {
                name: teams.find(
                  (item) => item.uid === latestDeployment.team_id,
                )?.icon,
                color: teams.find(
                  (item) => item.uid === latestDeployment.team_id,
                )?.color,
              },
            });
          }
        }
      } catch (err) {
        console.error("Error fetching deployments:", err);
      } finally {
        setIsLoadingDeployments(false);
      }
    }
    if (agentDetails?.id) {
      fetchDeployments();
    }
  }, [agentDetails?.id]);

  const handleHiring = async () => {
    setIsDeploymentInProgress(true);
    setShowProgressCard(true);
    setDeploymentStep("hiring");
    try {
      const response = await fetch("/api/marketplace/install", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          appId: appDetails?.uid,
          teamIds: [selectedTeam?.value],
          appConfiguration: {
            required_settings: [],
            optional_settings: [],
          },
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to install app");
      }

      await refetch();

      // Set step to waiting for agent and let the useEffect handle the rest
      setDeploymentStep("waiting-for-agent");
    } catch (e) {
      console.error("Installation error:", e);
      if (
        e instanceof Error &&
        e.message.includes(
          "App is already installed for one or more of the specified teams.",
        )
      ) {
        setDeploymentStep("deploying");
        await handleDeployment();
        return;
      }
      setIsDeploymentInProgress(false);
      setDeploymentStep(null);
      setShowProgressCard(false);
      toast.error(
        `Failed to hire ${AGENT_NAME}: ${
          e instanceof Error ? e.message : "Unknown error"
        }`,
      );
    }
  };
  const handleDeployment = async () => {
    if (!agentDetails?.id) {
      console.error("Agent details not available for deployment");
      return;
    }
    try {
      setDeploymentStep("deploying");
      const response = await fetch(
        `/api/agents/${agentDetails.id}/deployments`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            team_id: selectedTeam?.value,
            allowed_origins: validDomains, // Send only valid domains
            deployment_type: "widget",
          }),
        },
      );

      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || "Deployment failed");
      }
      // Show completion state
      setDeploymentStep("completed");
      setCurrentDeployedTeam(selectedTeam?.value || null);

      // Wait a bit to show the completion state, then hide the progress card
      setTimeout(() => {
        setShowProgressCard(false);
        // Clean up states after the card is hidden
        setTimeout(() => {
          setIsDeploymentInProgress(false);
          setDeploymentStep(null);
        }, 500); // Clean up after transition completes
      }, 2000); // Show completed state for 2 seconds
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An unexpected error occurred.";
      toast.error(errorMessage);
      setShowProgressCard(false);
      setTimeout(() => {
        setIsDeploymentInProgress(false);
        setDeploymentStep(null);
      }, 300);
    }
  };
  const handleBtnClick = async () => {
    // If no team is currently deployed, we need to install the app first
    if (!currentDeployedTeam) {
      await handleHiring();
      return;
    }

    // If the selected team is different from the currently deployed team,
    // we need to deploy the widget for the new team
    if (currentDeployedTeam !== selectedTeam?.value) {
      setIsDeploymentInProgress(true);
      await handleDeployment();
      return;
    }

    // If we're here, it means we're trying to deploy to the same team
    // that's already deployed, so we should just update the deployment
    setIsDeploymentInProgress(true);
    setShowProgressCard(true);
    await handleDeployment();
  };
  const getButtonText = () => {
    if (!isDeploymentInProgress) {
      return "Add to team";
    }

    switch (deploymentStep) {
      case "hiring":
        return "Hiring agent";
      case "waiting-for-agent":
        return "Waiting for agent";
      case "deploying":
        return "Deploying";
      default:
        return "Processing";
    }
  };

  const getStepIcon = () => {
    switch (deploymentStep) {
      case "hiring":
        return <Rocket className="h-4 w-4" />;
      case "waiting-for-agent":
        return <Clock className="h-4 w-4" />;
      case "deploying":
        return <Loader2 className="h-4 w-4 animate-spin" />;
      default:
        return <Loader2 className="h-4 w-4 animate-spin" />;
    }
  };
  const getProgressSteps = () => [
    {
      id: "hiring",
      label: "Hiring agent",
      completed:
        deploymentStep &&
        ["waiting-for-agent", "deploying", "completed"].includes(
          deploymentStep,
        ),
      active: deploymentStep === "hiring",
    },
    {
      id: "waiting-for-agent",
      label: "Initializing agent",
      completed:
        deploymentStep && ["deploying", "completed"].includes(deploymentStep),
      active: deploymentStep === "waiting-for-agent",
    },
    {
      id: "deploying",
      label: "Setting up deployment",
      completed: deploymentStep === "completed",
      active: deploymentStep === "deploying",
    },
  ];

  return (
    <div className="h-[calc(100vh-4rem)] flex flex-col">
      <div className="px-6 pt-14 h-full">
        <div className="grid gap-4 mx-auto max-w-[640px]">
          <div>
            <h2 className="text-2xl font-medium">Web chat</h2>
            <p className="text-sm text-[var(--color-text-muted)]">
              Embed our AI-native widget in your product for instant support.
            </p>
          </div>
          <div className="border-border border border-solid rounded-sm w-full p-4">
            <p className="text-sm text-[var(--color-text-muted)] mb-3">
              Choose which team will handle web chat conversation.
            </p>
            <div className="flex items-center justify-between">
              <CommonSelectWrapper
                key="Select team"
                placeholder="Select team"
                hideClearIndicator
                isClearable={false}
                options={teams?.map((option) => ({
                  value: option.uid,
                  label: option.name,
                  heroIcon: {
                    name: option.icon || "RocketLaunchIcon",
                    color: option.color || "purple",
                  },
                }))}
                name="Select team"
                onCancelClick={() => setSelectedTeam(null)}
                value={selectedTeam}
                isVirtualized={true}
                isOptionsMemoized
                onChange={(value) => setSelectedTeam(value as SelectOptionType)}
                maxVirtuosoHeight={600}
                triggerClassname="w-full h-6"
                wrapperClassname="!max-w-[80%] w-full"
                labelClass="!rounded-sm"
                isLoading={
                  !!selectedTeam?.value &&
                  (isLoadingDeployments || isDeploymentInProgress)
                }
                disabled={
                  !!selectedTeam?.value &&
                  (isLoadingDeployments || isDeploymentInProgress)
                }
              />
              {(agentDetails === undefined ||
                currentDeployedTeam !== selectedTeam?.value) && (
                <Button
                  variant="default"
                  size="sm"
                  className="gap-2"
                  onClick={handleBtnClick}
                  disabled={!selectedTeam || isDeploymentInProgress}
                >
                  {isDeploymentInProgress && getStepIcon()}
                  {getButtonText()}
                </Button>
              )}
            </div>

            {/* Progress Indicator */}
            {showProgressCard && (
              <div
                className={`mt-6 p-4 rounded-lg border transition-all duration-500 ease-in-out transform ${
                  deploymentStep === "completed"
                    ? "bg-green-50 dark:bg-green-950/20 border-gray-200 dark:border-gray-950"
                    : "bg-blue-50 dark:bg-blue-950/20 border-gray-200 dark:border-gray-950"
                } opacity-100 translate-y-0`}
              >
                <div className="flex items-center gap-2 mb-3">
                  {deploymentStep === "completed" ? (
                    <CircleCheck className="h-4 w-4 text-green-600" />
                  ) : (
                    <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                  )}
                  <span
                    className={`text-sm font-medium ${
                      deploymentStep === "completed"
                        ? "text-green-900 dark:text-green-100"
                        : "text-blue-900 dark:text-blue-100"
                    }`}
                  >
                    {deploymentStep === "completed"
                      ? "Deployment complete"
                      : "Deployment in progress"}
                  </span>
                </div>

                {deploymentStep === "completed" ? (
                  <div className="text-sm text-green-700 dark:text-green-300">
                    Your widget configuration is ready and deployed
                    successfully.
                  </div>
                ) : (
                  <div className="space-y-3">
                    {getProgressSteps().map((step, index) => (
                      <div key={step.id} className="flex items-center gap-2">
                        <div className="flex items-center justify-center w-4 h-4 rounded-full transition-all duration-300">
                          {step.completed ? (
                            <div className="w-4 h-4 bg-green-600 rounded-full flex items-center justify-center transform transition-transform duration-300 scale-100">
                              <CircleCheck className="h-4 w-4 text-white" />
                            </div>
                          ) : step.active ? (
                            <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                              <Loader2 className="h-3 w-3 text-white animate-spin" />
                            </div>
                          ) : (
                            <div className="w-4 h-4 border border-gray-300 dark:border-gray-600 rounded-full bg-white dark:bg-gray-800">
                              <span className="w-full h-full rounded-full text-[10px] flex items-center justify-center text-gray-500 dark:text-gray-400">
                                {index + 1}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="flex-1">
                          <div
                            className={`text-sm font-medium transition-colors duration-300 ${
                              step.completed
                                ? "text-green-700 dark:text-green-300"
                                : step.active
                                ? "text-blue-700 dark:text-blue-300"
                                : "text-gray-500 dark:text-gray-400"
                            }`}
                          >
                            {step.label}
                          </div>
                          {step.active && (
                            <div className="text-xs text-gray-600 dark:text-gray-400 mt-1 transition-opacity duration-300">
                              {deploymentStep === "hiring" &&
                                "Installing agent for your team..."}
                              {deploymentStep === "waiting-for-agent" &&
                                "Waiting for agent to be ready..."}
                              {deploymentStep === "deploying" &&
                                "Configuring widget deployment..."}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {selectedTeam &&
              agentDetails !== undefined &&
              !isDeploymentInProgress && (
                <Button
                  variant="default"
                  size="sm"
                  className="mt-3 gap-2 group"
                  onClick={() => {
                    router.push(
                      `/dashboard/${selectedTeam.value}/settings/sources/web-chat?agentId=${agentDetails?.id}`,
                    );
                  }}
                >
                  Configure deployment in team <MoveUpRight className="transition-transform duration-200 group-hover:translate-x-1" />
                </Button>
              )}
          </div>
          {selectedTeam && (
            <WebChatAgentBanner
              AGENT_NAME={AGENT_NAME}
              agentDetails={agentDetails}
            />
          )}
        </div>
      </div>
    </div>
  );
}
