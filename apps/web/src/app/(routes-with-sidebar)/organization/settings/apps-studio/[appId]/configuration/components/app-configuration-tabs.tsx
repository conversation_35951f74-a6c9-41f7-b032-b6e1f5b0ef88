"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { useAppStudioStore } from "@/store/app-studio-store";
import { useEffect, useState } from "react";
import { APP_HOMES, SupportedAppSlug } from "./apps";

interface AppConfigurationTabsProps {
  children: React.ReactNode;
}

// Extend the app manifest type to include the slug
interface AppManifestWithSlug {
  app: {
    name: string;
    icons?: {
      large: string;
      small: string;
    };
    category: string;
    description: string;
    supported_locales: string[];
    slug?: string;
  };
}

interface ExtendedApp {
  appId: string;
  appManifest: AppManifestWithSlug;
  // ... other properties
}

// Shimmer skeleton component for app home loading
const AppHomeShimmer = () => (
  <div className="max-w-[640px] mx-auto space-y-6 animate-pulse">
    {/* Companies Section Shimmer */}
    <div className="border rounded-[4px] px-4 shadow-none">
      <div className="flex items-center justify-between py-4">
        <div className="flex items-center gap-3">
          <div className="w-5 h-5 bg-muted/60 rounded"></div>
          <div>
            <div className="h-4 bg-muted/60 rounded w-20 mb-1"></div>
            <div className="h-3 bg-muted/40 rounded w-32"></div>
          </div>
        </div>
        <div className="w-4 h-4 bg-muted/60 rounded"></div>
      </div>
    </div>

    {/* Contacts Section Shimmer */}
    <div className="border rounded-[4px] px-4 shadow-none">
      <div className="flex items-center justify-between py-4">
        <div className="flex items-center gap-3">
          <div className="w-5 h-5 bg-muted/60 rounded"></div>
          <div>
            <div className="h-4 bg-muted/60 rounded w-16 mb-1"></div>
            <div className="h-3 bg-muted/40 rounded w-28"></div>
          </div>
        </div>
        <div className="w-4 h-4 bg-muted/60 rounded"></div>
      </div>
    </div>

    {/* Sync Status Section Shimmer */}
    <div className="border rounded-[4px] px-4 shadow-none">
      <div className="flex items-center justify-between py-4">
        <div className="flex items-center gap-3">
          <div className="w-5 h-5 bg-muted/60 rounded"></div>
          <div>
            <div className="h-4 bg-muted/60 rounded w-24 mb-1"></div>
            <div className="h-3 bg-muted/40 rounded w-36"></div>
          </div>
        </div>
        <div className="w-4 h-4 bg-muted/60 rounded"></div>
      </div>
    </div>
  </div>
);

export function AppConfigurationTabs({ children }: AppConfigurationTabsProps) {
  const [isAppHomeLoading, setIsAppHomeLoading] = useState(true);

  const app = useAppStudioStore((state) => {
    const selectedApp = state.selectedApp;
    if (!selectedApp) return null;

    // Type guard to ensure the app has the expected structure
    const hasSlug = selectedApp.appManifest?.app?.slug;
    if (!hasSlug) return null;

    return selectedApp as ExtendedApp;
  });

  // Initialize loading state when app changes
  useEffect(() => {
    if (app?.appId) {
      // Set loading to true when app changes
      setIsAppHomeLoading(true);

      // Use a reasonable timeout that allows for data fetching
      // This is a fallback in case the app has issues loading
      const timer = setTimeout(() => {
        setIsAppHomeLoading(false);
      }, 4000); // 2 seconds is usually enough for most API calls

      return () => clearTimeout(timer);
    }
  }, [app?.appId]);

  // Check if the app's slug is in our supported apps list
  if (!app?.appManifest.app.slug || !(app.appManifest.app.slug in APP_HOMES)) {
    return children;
  }

  const AppHome = APP_HOMES[app.appManifest.app.slug as SupportedAppSlug];

  return (
    <Tabs defaultValue="app-home" className="w-full">
      <TabsList className="w-full justify-start bg-background border-b rounded-none p-0 shadow-none h-min">
        <TabsTrigger
          value="app-home"
          className="gap-2 rounded-none data-[state=active]:shadow-none data-[state=active]:border-b-[2px] data-[state=active]:border-b-primary py-3"
        >
          App home
        </TabsTrigger>
        <TabsTrigger
          value="thena-config"
          className="gap-2 rounded-none data-[state=active]:shadow-none data-[state=active]:border-b-[2px] data-[state=active]:border-b-primary py-3"
        >
          Thena configuration
        </TabsTrigger>
      </TabsList>

      <TabsContent value="app-home" className="mt-6">
        {isAppHomeLoading ? <AppHomeShimmer /> : <AppHome />}
      </TabsContent>

      <TabsContent value="thena-config" className="mt-6">
        {children}
      </TabsContent>
    </Tabs>
  );
}
