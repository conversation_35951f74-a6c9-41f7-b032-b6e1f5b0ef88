"use client";

import { AppSettingsForm } from "@/components/app-studio/configuration/app-settings-form";
import { EmptyState } from "@/components/app-studio/empty-state";
import ThenaLoader from "@/components/thena-loader";
import TooltipWrapper from "@/components/tooltip-wrapper";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAppStudioStore } from "@/store/app-studio-store";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { AppScope, InstalledAppDto } from "@/types/app-studio";
import { MoreVertical, Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { AppConfigurationTabs } from "./components/app-configuration-tabs";
import { APP_HOMES } from "./components/apps/index";

interface AppManifestScopes {
  required: {
    platform: AppScope[];
  };
  optional?: {
    platform: AppScope[];
  };
}

// Removed unused interfaces

async function uninstallApp(appId: string) {
  try {
    const response = await fetch("/api/app-studio/uninstall", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ appId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(
        error.message || error.error || "Failed to uninstall app",
      );
    }

    return true;
  } catch (error) {
    console.error("Error uninstalling app:", error);
    throw error;
  }
}

export default function AppConfigurationPage() {
  const { selectedApp: app } = useAppStudioStore();
  const router = useRouter();
  const [isUninstalling, setIsUninstalling] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isResyncingAuth, setIsResyncingAuth] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showUnsavedChangesDialog, setShowUnsavedChangesDialog] =
    useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(
    null,
  );
  const [openAccordion, setOpenAccordion] = useState<string | undefined>(
    undefined,
  );
  const [isLoadingApp, setIsLoadingApp] = useState(false);

  // Reference to the form's methods
  const formRef = useRef<{
    submitForm: () => Promise<Record<string, unknown>>;
    getValues: () => Record<string, unknown>;
    hasChanged?: () => boolean;
  } | null>(null);

  const handleAccordionChange = (value: string | undefined) => {
    if (value === openAccordion) {
      setOpenAccordion(undefined);
    } else {
      setOpenAccordion(value);
    }
  };

  const handleUninstall = async () => {
    if (!app) return;

    try {
      setIsUninstalling(true);
      await uninstallApp(app.appId);
      toast.success("App uninstalled successfully");
      router.push("/organization/settings/apps-studio");
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to uninstall app",
      );
    } finally {
      setIsUninstalling(false);
    }
  };

  // Removed unused handleBeforeNavigate function

  // Handle navigation after saving or discarding changes
  const handleNavigate = () => {
    if (pendingNavigation) {
      // Use window.location for external navigation in client components
      window.location.href = pendingNavigation;
    }
  };

  // Handle saving changes and then navigating
  const handleSaveAndNavigate = async () => {
    if (formRef.current?.submitForm) {
      setIsSaving(true);
      try {
        const formValues = await formRef.current.submitForm();

        if (formValues) {
          const response = await fetch("/api/app-studio/update-settings", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              appId: app.appId,
              installationId: app.appId,
              settings: formValues,
              required_settings:
                app.appManifest.configuration.required_settings,
              optional_settings:
                app.appManifest.configuration.optional_settings,
            }),
          });

          if (!response.ok) {
            let errorMessage = "Failed to update app settings";
            try {
              const errorData = await response.json();
              if (errorData && (errorData.message || errorData.error)) {
                errorMessage = errorData.message || errorData.error;
              }
            } catch (parseError) {
              console.error("Error parsing error response:", parseError);
            }
            throw new Error(errorMessage);
          }
        }

        toast.success("Settings saved successfully");
        setIsEditMode(false);
        setHasUnsavedChanges(false);
        setShowUnsavedChangesDialog(false);
        handleNavigate();
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to save settings";
        toast.error(errorMessage);
        console.error("Error saving settings:", error);
      } finally {
        setIsSaving(false);
      }
    }
  };

  // Handle discarding changes and navigating
  const handleDiscardAndNavigate = () => {
    setIsEditMode(false);
    setHasUnsavedChanges(false);
    setShowUnsavedChangesDialog(false);
    handleNavigate();
  };

  useEffect(() => {
    const fetchAppData = async (appId: string) => {
      try {
        setIsLoadingApp(true);
        const response = await fetch("/api/app-studio/installed-apps");
        if (!response.ok) {
          throw new Error("Failed to fetch installed apps");
        }
        const data = await response.json();
        const foundApp = data.apps?.find(
          (app: InstalledAppDto) => app.appId === appId,
        );

        if (foundApp) {
          // Set the app in the store so it's available for the component
          const { setSelectedApp } = useAppStudioStore.getState();
          setSelectedApp(foundApp);
        } else {
          // App not found in installed apps, redirect to apps studio
          window.location.href = "/organization/settings/apps-studio";
        }
      } catch (error) {
        console.error("Error fetching app data:", error);
        // On error, redirect to apps studio
        window.location.href = "/organization/settings/apps-studio";
      } finally {
        setIsLoadingApp(false);
      }
    };

    if (!app) {
      // Get the appId from the URL
      const pathSegments = window.location.pathname.split("/");
      const appIdIndex =
        pathSegments.findIndex((segment) => segment === "apps-studio") + 1;
      const appId = pathSegments[appIdIndex];

      if (appId) {
        fetchAppData(appId);
      } else {
        // No appId in URL, redirect to apps studio
        window.location.href = "/organization/settings/apps-studio";
      }
    }
  }, [app]);

  // Add window beforeunload event listener for navigation attempts
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      // Only show browser popup if our custom dialog is not already showing
      if (isEditMode && hasUnsavedChanges && !showUnsavedChangesDialog) {
        // Standard way to show a confirmation dialog when closing/navigating
        const message =
          "You have unsaved changes. Are you sure you want to leave?";
        e.preventDefault();
        e.returnValue = message;
        return message;
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [isEditMode, hasUnsavedChanges, showUnsavedChangesDialog]);

  // Handle browser back button and history navigation
  useEffect(() => {
    // Save the current location to detect back/forward navigation
    const currentPath = window.location.pathname;

    // Create a history state object to detect popstate events
    const pushState = () => {
      const state = { currentPath };
      window.history.pushState(state, "", window.location.href);
    };

    // Push initial state
    pushState();

    const handlePopState = (e: PopStateEvent) => {
      if (isEditMode && hasUnsavedChanges) {
        // Prevent navigation
        e.preventDefault();
        // Push state again to prevent navigation
        pushState();
        // Show dialog
        setShowUnsavedChangesDialog(true);
        // Store the URL we were trying to navigate to
        setPendingNavigation(
          document.referrer || "/organization/settings/apps-studio",
        );
        return false;
      }
      return true;
    };

    window.addEventListener("popstate", handlePopState);

    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [isEditMode, hasUnsavedChanges]);

  // We don't need to intercept link clicks for breadcrumb navigation
  // Only the browser back button will be intercepted

  if (isLoadingApp) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-75px)] w-full">
        <ThenaLoader loaderText="Loading app configuration..." />
      </div>
    );
  }

  if (!app) {
    return null;
  }

  // Get required scopes
  const platformScopes = app.appManifest.scopes.required.platform;

  // The scopes are already in the correct format, no need to transform
  const requiredScopes = platformScopes;

  // Check if app home is available for this app
  const hasAppHome =
    app?.appManifest?.app?.slug && app.appManifest.app.slug in APP_HOMES;

  return (
    <div
      className="flex flex-col w-full h-full overflow-y-auto"
      style={{ height: "calc(100vh - 75px)" }}
    >
      {/* Unsaved Changes Dialog */}
      <Dialog
        open={showUnsavedChangesDialog}
        onOpenChange={(open) => {
          // When closing the dialog (X button), just close it and stay on the page
          setShowUnsavedChangesDialog(open);
          // Reset pending navigation when closing with X button
          if (!open) {
            setPendingNavigation(null);
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Unsaved changes</DialogTitle>
            <DialogDescription>
              You have unsaved changes. What would you like to do?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex justify-end gap-2">
            <Button variant="outline" onClick={handleDiscardAndNavigate}>
              Discard changes
            </Button>
            <Button onClick={handleSaveAndNavigate} disabled={isSaving}>
              {isSaving ? "Saving..." : "Save changes"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <div className="flex-1 w-full">
        <div className="max-w-[640px] w-full mx-auto py-6 space-y-6">
          {/* Header space */}
          <div className="h-[8px]" />
          {/* Header */}
          <div className="flex items-center justify-between pb-6">
            <div className="flex items-center gap-4">
              <div className="flex h-12 w-12 items-center justify-center rounded-sm bg-background overflow-hidden">
                <img
                  src={
                    app.appManifest.app.icons?.small ||
                    "/placeholder-app-icon.png"
                  }
                  alt={app.appManifest.app.name}
                  crossOrigin="anonymous"
                  className="h-full w-full object-cover"
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold">
                  {app.appManifest.app.name}
                </h1>
                <p className="text-muted-foreground">
                  {app.appManifest.metadata?.title || "App configuration"}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {/* Note: Edit settings button temporarily hidden - will be implemented later when the UPDATE backend API is ready */}
              {/* 
              <Button
                variant={isEditMode ? "default" : "outline"}
                size="sm"
                onClick={async () => {
                  if (isEditMode) {
                    // Save mode - submit the form
                    if (formRef.current?.submitForm) {
                      setIsSaving(true);
                      try {
                        // First trigger form submission to validate and get the latest values
                        const formValues = await formRef.current.submitForm();

                        if (formValues) {
                          const response = await fetch(
                            "/api/app-studio/update-settings",
                            {
                              method: "POST",
                              headers: {
                                "Content-Type": "application/json",
                              },
                              body: JSON.stringify({
                                appId: app.appId,
                                // For apps platform, the installation ID is the same as the appId
                                // This is what the backend expects for the update-config endpoint
                                installationId: app.appId,
                                settings: formValues,
                                required_settings:
                                  app.appManifest.configuration
                                    .required_settings,
                                optional_settings:
                                  app.appManifest.configuration
                                    .optional_settings,
                              }),
                            },
                          );

                          if (!response.ok) {
                            let errorMessage = "Failed to update app settings";
                            try {
                              const errorData = await response.json();
                              if (errorData && (errorData.message || errorData.error)) {
                                errorMessage = errorData.message || errorData.error;
                              }
                            } catch (parseError) {
                              console.error("Error parsing error response:", parseError);
                            }
                            throw new Error(errorMessage);
                          }
                        } else {
                        }

                        toast.success("Settings saved successfully");
                        setIsEditMode(false);
                        setHasUnsavedChanges(false);
                      } catch (error) {
                        const errorMessage = error instanceof Error ? error.message : "Failed to save settings";
                        toast.error(errorMessage);
                        console.error("Error saving settings:", error);
                      } finally {
                        setIsSaving(false);
                      }
                    }
                  } else {
                    // Edit mode - just toggle
                    setIsEditMode(true);
                    
                    // Mark as having unsaved changes when entering edit mode
                    setHasUnsavedChanges(true);
                    
                    // Always open the settings accordion when Edit settings is clicked
                    setOpenAccordion("settings");
                  }
                }}
                disabled={isSaving}
              >
                {isSaving
                  ? "Saving..."
                  : isEditMode
                  ? "Save settings"
                  : "Edit settings"}
              </Button>
              */}
              {app?.appManifest?.integration?.entry_points?.oauth_redirect &&
                !hasAppHome && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setIsResyncingAuth(true);
                      try {
                        // Get the organization ID from the global store
                        const currentOrgId =
                          useGlobalConfigPersistStore.getState().currentOrgId;
                        // Find the current organization to get its UID
                        const currentOrg = useGlobalConfigPersistStore
                          .getState()
                          .orgs.find((org) => org.id === currentOrgId);
                        const orgUid = currentOrg?.orgId;

                        // Replace placeholder with actual organization ID
                        let redirectUrl =
                          app.appManifest.integration.entry_points
                            .oauth_redirect;
                        // Handle various formats of organization ID in Liquid tags
                        redirectUrl = redirectUrl.replace(
                          /\{\{\s*organizationId\s*\}\}/g,
                          orgUid || "",
                        );
                        // Also handle {{orgId}} format
                        redirectUrl = redirectUrl.replace(
                          /\{\{\s*orgId\s*\}\}/g,
                          orgUid || "",
                        );
                        // Also handle {{orgUid}} format
                        redirectUrl = redirectUrl.replace(
                          /\{\{\s*orgUid\s*\}\}/g,
                          orgUid || "",
                        );

                        // Redirect to the OAuth URL
                        window.location.href = redirectUrl;
                      } catch (error) {
                        console.error("Error redirecting to OAuth URL:", error);
                        toast.error(
                          "Failed to redirect to authentication page",
                        );
                        setIsResyncingAuth(false);
                      }
                    }}
                    disabled={isResyncingAuth}
                  >
                    {isResyncingAuth ? "Redirecting..." : "Resync"}
                  </Button>
                )}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon" className="h-8 w-8">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="min-w-[120px] p-1">
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <DropdownMenuItem
                        className="text-destructive focus:text-destructive text-xs py-1.5 h-7"
                        onSelect={(e) => e.preventDefault()}
                        disabled={isUninstalling}
                      >
                        {isUninstalling ? "Uninstalling..." : "Uninstall"}
                      </DropdownMenuItem>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>
                          Are you sure you want to uninstall this app?
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                          This action cannot be undone. The app and all its data
                          will be removed from your workspace.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleUninstall}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          Uninstall
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Wrap the existing content in AppConfigurationTabs */}
          <AppConfigurationTabs>
            <div className="space-y-4">
              {/* Main Accordion */}
              <Accordion
                type="single"
                value={openAccordion}
                onValueChange={handleAccordionChange}
                collapsible
                className="w-full"
              >
                {/* Teams Accordion Item */}
                <AccordionItem value="teams" className="border rounded-sm">
                  <AccordionTrigger className="px-4 py-3 hover:no-underline">
                    <div>
                      <h3 className="text-base font-medium text-left h-6">
                        Teams
                      </h3>
                      <p className="text-sm text-muted-foreground text-left">
                        Teams that have access to this app.
                      </p>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 py-3 pb-4">
                    {/* TODO: Team management functionality is temporarily disabled 
                         while we finalize the team access control system */}
                    <EmptyState
                      message="No teams have access to this app yet."
                      actionButton={{
                        label: "Add to a team",
                        icon: <Plus className="h-4 w-4" />,
                        onClick: () => {},
                        disabled: true, // Temporarily disabled for the time being
                      }}
                    />
                  </AccordionContent>
                </AccordionItem>

                {/* Settings Accordion Item */}
                <AccordionItem
                  value="settings"
                  className="border rounded-sm mt-4"
                >
                  <AccordionTrigger className="px-4 py-3 hover:no-underline">
                    <div>
                      <h3 className="text-base font-medium text-left h-6">
                        Settings
                      </h3>
                      <p className="text-sm text-muted-foreground text-left">
                        Configure app settings and parameters.
                      </p>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 py-3 pb-4">
                    {/* Check if app has any settings configured */}
                    {!app?.appManifest?.configuration?.required_settings?.length &&
                    !app?.appManifest?.configuration?.optional_settings?.length ? (
                      <EmptyState message="No settings available for this app." />
                    ) : (
                      <div className="space-y-4">
                        <hr className="-mx-4 border-t border-border" />
                        <AppSettingsForm
                          app={app}
                          readOnly={!isEditMode}
                          formRef={formRef}
                        />
                      </div>
                    )}
                  </AccordionContent>
                </AccordionItem>

                {/* Required Scopes Accordion Item */}
                {requiredScopes.length > 0 && (
                  <AccordionItem
                    value="requiredScopes"
                    className="border rounded-sm mt-4"
                  >
                    <AccordionTrigger className="px-4 py-3 hover:no-underline">
                      <div>
                        <h3 className="text-base font-medium text-left h-6">
                          Required scopes
                        </h3>
                        <p className="text-sm text-muted-foreground text-left">
                          API permissions granted to this app.
                        </p>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-4 py-3 pb-4">
                      <div className="space-y-2">
                        {requiredScopes.map((scope, index) => (
                          <div key={`${scope.scope}-${index}`} className="py-1">
                            <div className="flex items-center gap-2">
                              <Checkbox
                                checked
                                disabled
                                className="shadow-none"
                              />
                              <TooltipWrapper
                                tooltipContent={scope.description}
                              >
                                <span className="text-sm font-medium">
                                  {scope.scope}
                                </span>
                              </TooltipWrapper>
                            </div>
                          </div>
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                )}

                {/* Optional Scopes Accordion Item */}
                <AccordionItem
                  value="optionalScopes"
                  className="border rounded-sm mt-4"
                >
                  <AccordionTrigger className="px-4 py-3 hover:no-underline">
                    <div>
                      <h3 className="text-base font-medium text-left h-6">
                        Optional scopes
                      </h3>
                      <p className="text-sm text-muted-foreground text-left">
                        Additional API permissions that can be granted.
                      </p>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 py-3 pb-4">
                    {(app.appManifest.scopes as AppManifestScopes).optional
                      ?.platform?.length ? (
                      <div className="space-y-2">
                        {(
                          (app.appManifest.scopes as AppManifestScopes).optional
                            ?.platform || []
                        ).map((scope, index) => (
                          <div key={`${scope.scope}-${index}`} className="py-1">
                            <div className="flex items-center gap-2">
                              <Checkbox
                                checked={false}
                                disabled
                                className="shadow-none"
                              />
                              <TooltipWrapper
                                tooltipContent={scope.description}
                              >
                                <span className="text-sm font-medium">
                                  {scope.scope}
                                </span>
                              </TooltipWrapper>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <EmptyState message="No optional scopes available for this app." />
                    )}
                  </AccordionContent>
                </AccordionItem>

                {/* Event Subscriptions */}
                <AccordionItem
                  value="eventSubscriptions"
                  className="border rounded-sm mt-4"
                >
                  <AccordionTrigger className="px-4 py-3 hover:no-underline">
                    <div>
                      <h3 className="text-base font-medium text-left h-6">
                        Event subscriptions
                      </h3>
                      <p className="text-sm text-muted-foreground text-left">
                        Subscribe to events from this app.
                      </p>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 py-3 pb-4">
                    {app.appManifest.events?.subscribe &&
                    app.appManifest.events.subscribe.length > 0 ? (
                      <div className="space-y-2">
                        {app.appManifest.events.subscribe.map(
                          (event, index) => (
                            <div
                              key={`${event.event}-${index}`}
                              className="py-1"
                            >
                              <div className="flex items-center gap-2">
                                <Checkbox
                                  checked
                                  disabled
                                  className="shadow-none"
                                />
                                <TooltipWrapper
                                  tooltipContent={event.description}
                                >
                                  <span className="text-sm font-medium">
                                    {event.event}
                                  </span>
                                </TooltipWrapper>
                              </div>
                            </div>
                          ),
                        )}
                      </div>
                    ) : (
                      <EmptyState message="No event subscriptions configured yet." />
                    )}
                  </AccordionContent>
                </AccordionItem>

                {/* Events Registered */}
                <AccordionItem
                  value="eventsRegistered"
                  className="border rounded-sm mt-4"
                >
                  <AccordionTrigger className="px-4 py-3 hover:no-underline">
                    <div>
                      <h3 className="text-base font-medium text-left h-6">
                        Events registered
                      </h3>
                      <p className="text-sm text-muted-foreground text-left">
                        Events registered by this app.
                      </p>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 py-3 pb-4">
                    {app.appManifest.events?.publish &&
                    app.appManifest.events.publish.length > 0 ? (
                      <div className="space-y-2">
                        {app.appManifest.events.publish.map((event, index) => (
                          <div key={`${event.event}-${index}`} className="py-1">
                            <div className="flex items-center gap-2">
                              <Checkbox
                                checked
                                disabled
                                className="shadow-none"
                              />
                              <TooltipWrapper
                                tooltipContent={event.description}
                              >
                                <span className="text-sm font-medium">
                                  {event.event}
                                </span>
                              </TooltipWrapper>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <EmptyState message="No events registered yet." />
                    )}
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          </AppConfigurationTabs>
        </div>
      </div>
    </div>
  );
}
