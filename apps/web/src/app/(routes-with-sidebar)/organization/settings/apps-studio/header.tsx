"use client";

import { Breadcrumb } from "@/components/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { useSidebar } from "@/components/ui/sidebar";
import { AppResponseDto } from "@/types/app-studio";
import { PanelLeft } from "lucide-react";
import { usePathname } from "next/navigation";

interface AppsStudioHeaderProps {
  app?: AppResponseDto;
}

const formatBreadcrumb = (path: string, app?: AppResponseDto) => {
  return path
    .split("/")
    .filter(Boolean)
    .map((segment, index, segments) => {
      if (segment === "apps-studio") {
        return {
          label: "Apps Studio",
          href: "/organization/settings/apps-studio",
        };
      }
      if (app && segment === app.uid) {
        // Check if this is part of an install flow
        const isInstallFlow = segments[index + 1] === "install";
        return {
          label: app.name,
          href: isInstallFlow ? undefined : `/organization/settings/apps-studio/${app.uid}`,
        };
      }
      if (segment === "install") {
        return {
          label: "Install",
          href: undefined,
        };
      }
      return {
        label: segment
          .split("-")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" "),
        href: `/${segment}`,
      };
    });
};

export function AppsStudioHeader({ app }: AppsStudioHeaderProps) {
  const pathname = usePathname();
  const { toggleSidebar } = useSidebar();
  const breadcrumbs = formatBreadcrumb(pathname, app);

  return (
    <div className="px-4 py-2 h-[60px] flex justify-between items-center bg-[var(--color-bg-subtle)]">
      <div className="flex items-center gap-2 w-full">
        <PanelLeft
          size={16}
          onClick={toggleSidebar}
          className="cursor-pointer"
          role="button"
        />
        <Separator orientation="vertical" className="h-[14px]" />
        <Breadcrumb items={breadcrumbs} className="!border-none" />
      </div>
    </div>
  );
}
