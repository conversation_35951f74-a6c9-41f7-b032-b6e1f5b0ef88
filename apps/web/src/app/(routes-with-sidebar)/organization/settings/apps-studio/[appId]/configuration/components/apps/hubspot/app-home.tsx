"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
} from "@/components/ui/accordion";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { SimpleAccordionTrigger } from "@/components/ui/simple-accordion-trigger";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAppStudioStore } from "@/store/app-studio-store";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { Activity, AlertCircle, Building, Plus, Users } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import { CustomFieldsSelector } from "./custom-fields-selector";
import { FilterRow } from "./filter-row";
import {
  HUBSPOT_FIELD_OPERATORS,
  HubSpotFilter,
  HubSpotSettingsResponseDto,
  HubSpotSyncStatusResponse,
  TriggerSyncResponse,
  UpdateHubSpotSettingsDto,
} from "./types";

// Add interface for audit log response
interface AuditLogEntry {
  id: string;
  orgId: string;
  teamId?: string;
  entityType:
    | "HUBSPOT_WEBHOOK"
    | "HUBSPOT_ACCOUNT"
    | "HUBSPOT_CONTACT"
    | "THENA_ACCOUNT"
    | "THENA_CONTACT"
    | "THENA_CUSTOM_FIELD"
    | "THENA_USER";
  entityId?: string;
  op:
    | "info"
    | "created"
    | "updated"
    | "deleted"
    | "archived"
    | "restored"
    | "fetched"
    | "searched"
    | "received";
  metadata: {
    error?: string;
    [key: string]: unknown;
  };
  status: "PENDING" | "SUCCESS" | "FAILED";
  description: string;
  activity: string;
  createdAt: string;
}

interface AuditLogsResponse {
  data: AuditLogEntry[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Function to format dates
const formatDate = (dateString: string) => {
  if (!dateString) return "Unknown date";
  const date = new Date(dateString);
  return date.toLocaleString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  });
};

// Initial filter template
const createInitialFilter = (field = ""): HubSpotFilter => ({
  field,
  operator: HUBSPOT_FIELD_OPERATORS.EQ,
  value: [] as string[],
});

export default function AppHome() {
  const { selectedApp: app } = useAppStudioStore();
  const [originalSettings, setOriginalSettings] =
    useState<HubSpotSettingsResponseDto | null>(null);
  const [settings, setSettings] = useState<HubSpotSettingsResponseDto | null>(
    null,
  );
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [activeAccordion, setActiveAccordion] = useState<string | undefined>(
    "companies",
  );
  const [syncStatus, setSyncStatus] =
    useState<HubSpotSyncStatusResponse | null>(null);
  const [isSyncStatusLoading, setIsSyncStatusLoading] = useState(false);
  const [isTriggeringSync, setIsTriggeringSync] = useState(false);
  const [auditLogs, setAuditLogs] = useState<AuditLogsResponse | null>(null);
  const [isLoadingAuditLogs, setIsLoadingAuditLogs] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(100);

  // Function to fetch settings
  const fetchSettings = useCallback(async () => {
    try {
      setError(false);
      const response = await fetch(
        `/api/app-studio/apps/${app?.appId}/hubspot/settings`,
      );
      if (!response.ok) {
        throw new Error("Failed to fetch settings");
      }
      const data = await response.json();
      setSettings(data);
      setOriginalSettings(data);
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error("Error fetching settings:", error);
      toast.error("Failed to load HubSpot settings");
      setError(true);
    }
  }, [app?.appId]);

  // Function to fetch sync status
  const fetchSyncStatus = useCallback(async () => {
    try {
      setIsSyncStatusLoading(true);
      const response = await fetch(
        `/api/app-studio/apps/${app?.appId}/hubspot/sync-status`,
      );
      if (!response.ok) {
        throw new Error("Failed to fetch sync status");
      }
      const data = await response.json();
      setSyncStatus(data);
    } catch (error) {
      console.error("Error fetching sync status:", error);
      // Don't show error toast for sync status as it's not critical
    } finally {
      setIsSyncStatusLoading(false);
    }
  }, [app?.appId]);

  // Add function to fetch audit logs
  const fetchAuditLogs = useCallback(
    async (page: number) => {
      if (!app?.appId) return;

      try {
        setIsLoadingAuditLogs(true);
        const response = await fetch(
          `/api/app-studio/apps/${app.appId}/hubspot/audit-logs?page=${page}&limit=${itemsPerPage}`,
        );
        if (!response.ok) {
          throw new Error("Failed to fetch audit logs");
        }
        const data = await response.json();
        setAuditLogs(data);
        setCurrentPage(page);
      } catch (error) {
        console.error("Error fetching audit logs:", error);
        toast.error("Failed to load audit logs");
      } finally {
        setIsLoadingAuditLogs(false);
      }
    },
    [app?.appId, itemsPerPage],
  );

  // Function to trigger sync
  const triggerSync = useCallback(
    async (syncType?: "companies" | "contacts" | "both") => {
      if (!app?.appId || !settings) return;

      try {
        setIsTriggeringSync(true);

        // Determine what to sync based on the syncType parameter or active accordion
        let syncAccounts = false;
        let syncContacts = false;

        if (syncType === "companies") {
          syncAccounts = settings.companies.enabled;
        } else if (syncType === "contacts") {
          syncContacts = settings.contacts.enabled;
        } else if (syncType === "both") {
          syncAccounts = settings.companies.enabled;
          syncContacts = settings.contacts.enabled;
        } else {
          // Default behavior: sync based on active accordion
          if (activeAccordion === "companies") {
            syncAccounts = settings.companies.enabled;
          } else if (activeAccordion === "contacts") {
            syncContacts = settings.contacts.enabled;
          } else {
            // If on sync status tab, sync both enabled types
            syncAccounts = settings.companies.enabled;
            syncContacts = settings.contacts.enabled;
          }
        }

        if (!syncAccounts && !syncContacts) {
          const typeText =
            syncType === "companies"
              ? "company"
              : syncType === "contacts"
              ? "contact"
              : "sync";
          toast.error(
            `${
              typeText.charAt(0).toUpperCase() + typeText.slice(1)
            } sync is not enabled`,
          );
          return;
        }

        const response = await fetch(
          `/api/app-studio/apps/${app.appId}/hubspot/trigger-sync`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              syncAccounts,
              syncContacts,
            }),
          },
        );

        if (!response.ok) {
          throw new Error("Failed to trigger sync");
        }

        const result: TriggerSyncResponse = await response.json();

        if (result.success) {
          const syncTypeText =
            syncAccounts && syncContacts
              ? "sync"
              : syncAccounts
              ? "company sync"
              : "contact sync";
          toast.success(
            result.message ||
              `${
                syncTypeText.charAt(0).toUpperCase() + syncTypeText.slice(1)
              } triggered successfully`,
          );
          // Refresh sync status after a short delay to show the new jobs
          setTimeout(() => {
            fetchSyncStatus();
          }, 1000);
        } else {
          throw new Error("Failed to trigger sync");
        }
      } catch (error) {
        console.error("Error triggering sync:", error);
        toast.error("Failed to trigger sync");
      } finally {
        setIsTriggeringSync(false);
      }
    },
    [app?.appId, settings, activeAccordion, fetchSyncStatus],
  );

  // Fetch settings on mount
  useEffect(() => {
    if (app?.appId) {
      fetchSettings();
      fetchSyncStatus();
    }
  }, [app?.appId, fetchSettings, fetchSyncStatus]);

  // Add effect to fetch audit logs when tab changes
  useEffect(() => {
    if (activeAccordion === "sync-status") {
      fetchAuditLogs(1);
    }
  }, [activeAccordion, fetchAuditLogs]);

  // Check for unsaved changes
  useEffect(() => {
    if (originalSettings && settings) {
      const hasChanges =
        JSON.stringify(originalSettings) !== JSON.stringify(settings);
      setHasUnsavedChanges(hasChanges);
    }
  }, [originalSettings, settings]);

  const handleToggle = (type: "companies" | "contacts") => {
    if (!settings) return;

    setSettings({
      ...settings,
      [type]: {
        ...settings[type],
        enabled: !settings[type].enabled,
      },
    });
  };

  const handleFieldsChange = (
    type: "companies" | "contacts",
    fields: string[],
  ) => {
    if (!settings) return;

    setSettings({
      ...settings,
      [type]: {
        ...settings[type],
        selectedFields: fields,
      },
    });
  };

  const addFilter = (type: "companies" | "contacts") => {
    if (!settings) return;

    setSettings({
      ...settings,
      [type]: {
        ...settings[type],
        filters: [...settings[type].filters, createInitialFilter()],
      },
    });
  };

  const updateFilter = (
    type: "companies" | "contacts",
    index: number,
    filter: HubSpotFilter,
  ) => {
    if (!settings) return;

    setSettings({
      ...settings,
      [type]: {
        ...settings[type],
        filters: settings[type].filters.map((f, i) => {
          if (i !== index) return f;
          return filter;
        }),
      },
    });
  };

  const deleteFilter = (type: "companies" | "contacts", index: number) => {
    if (!settings) return;

    setSettings({
      ...settings,
      [type]: {
        ...settings[type],
        filters: settings[type].filters.filter((_, i) => i !== index),
      },
    });
  };

  const handleReconnect = async () => {
    if (!app?.appManifest?.integration?.entry_points?.oauth_redirect) {
      toast.error("OAuth redirect URL not found");
      return;
    }

    try {
      // Get the organization ID from the global store
      const currentOrgId = useGlobalConfigPersistStore.getState().currentOrgId;
      // Find the current organization to get its UID
      const currentOrg = useGlobalConfigPersistStore
        .getState()
        .orgs.find((org) => org.id === currentOrgId);
      const orgUid = currentOrg?.orgId;

      // Get the current user's email
      const currentUser = useGlobalConfigPersistStore.getState().currentUser;
      const userEmail = currentUser?.email;

      if (!orgUid) {
        toast.error("Organization ID not found");
        return;
      }

      // Get the base OAuth URL (no liquid tag replacement)
      let redirectUrl = app.appManifest.integration.entry_points.oauth_redirect;

      // Add query parameters using string concatenation
      const separator = redirectUrl.includes("?") ? "&" : "?";

      // Always add orgID and email as query parameters
      if (orgUid) {
        redirectUrl += `${separator}orgId=${encodeURIComponent(orgUid)}`;
      }
      if (userEmail) {
        const nextSeparator = redirectUrl.includes("?") ? "&" : "?";
        redirectUrl += `${nextSeparator}userEmail=${encodeURIComponent(
          userEmail,
        )}`;
      }

      // Redirect to the OAuth URL
      window.location.href = redirectUrl;
    } catch (error) {
      console.error("Error redirecting to OAuth URL:", error);
      toast.error("Failed to redirect to authentication page");
    }
  };

  const handleSave = async () => {
    if (!settings || !app?.appId) return;

    setIsSaving(true);
    try {
      // Create the update payload by comparing with original settings
      const updatedSettings: UpdateHubSpotSettingsDto = {};

      if (
        originalSettings?.integration.enabled !== settings.integration.enabled
      ) {
        updatedSettings.integration = { enabled: settings.integration.enabled };
      }

      // Helper function to normalize filter values to arrays
      const normalizeFilters = (filters: HubSpotFilter[]): HubSpotFilter[] => {
        return filters.map((filter) => {
          // Always ensure value is an array of strings for the API
          if (Array.isArray(filter.value)) {
            return {
              ...filter,
              value: filter.value.map((v) => v.toString()),
            };
          } else if (typeof filter.value === "string" && filter.value) {
            // Split comma-separated string into array
            return {
              ...filter,
              value: filter.value
                .split(",")
                .map((v) => v.trim())
                .filter(Boolean),
            };
          } else {
            return {
              ...filter,
              value: [],
            };
          }
        });
      };

      if (
        JSON.stringify(originalSettings?.companies) !==
        JSON.stringify(settings.companies)
      ) {
        updatedSettings.companies = {
          ...settings.companies,
          filters: normalizeFilters(settings.companies.filters),
        };
      }

      if (
        JSON.stringify(originalSettings?.contacts) !==
        JSON.stringify(settings.contacts)
      ) {
        updatedSettings.contacts = {
          ...settings.contacts,
          filters: normalizeFilters(settings.contacts.filters),
        };
      }

      // Only make API call if there are changes
      if (Object.keys(updatedSettings).length === 0) {
        toast.info("No changes to save");
        setHasUnsavedChanges(false);
        return;
      }

      const response = await fetch(
        `/api/app-studio/apps/${app.appId}/hubspot/settings`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updatedSettings),
        },
      );

      if (!response.ok) {
        throw new Error("Failed to update settings");
      }

      const data = await response.json();
      setSettings(data);
      setOriginalSettings(data);
      setHasUnsavedChanges(false);
      toast.success("Settings updated successfully");
    } catch (error) {
      console.error("Error saving settings:", error);
      toast.error("Failed to save settings");
    } finally {
      setIsSaving(false);
      setShowConfirmDialog(false);
    }
  };

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      setShowConfirmDialog(true);
    }
  };

  const handleConfirmCancel = () => {
    if (originalSettings) {
      setSettings(originalSettings);
      setHasUnsavedChanges(false);
    }
    setShowConfirmDialog(false);
  };

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="text-center space-y-4">
          <div className="text-sm text-muted-foreground">
            Failed to load HubSpot settings
          </div>
          <Button onClick={fetchSettings} variant="outline" size="sm">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  if (!settings) {
    return null;
  }

  const renderSyncInfo = (type: "companies" | "contacts") => {
    const { enabled } = settings[type];
    if (!enabled) return "Sync disabled";

    // Get the latest sync operation for this type
    const syncOperations =
      type === "companies" ? syncStatus?.accountSync : syncStatus?.contactSync;
    const latestSync = syncOperations?.[0]; // Assuming the array is sorted by most recent first

    if (!latestSync) {
      return "Sync enabled";
    }

    // Show status with additional info
    switch (latestSync.status) {
      case "active":
        return "Syncing...";
      case "completed":
        const completedTime = formatDate(
          latestSync.finishedAt || latestSync.createdAt,
        );
        return `Last synced: ${completedTime}`;
      case "failed":
        return "Sync failed";
      case "waiting":
        return "Sync queued";
      case "delayed":
        return "Sync delayed";
      default:
        return "Sync enabled";
    }
  };

  return (
    <div className="max-w-[640px] mx-auto space-y-6">
      {/* Settings Accordion */}
      <Accordion
        type="single"
        collapsible
        value={activeAccordion}
        onValueChange={setActiveAccordion}
        defaultValue="companies"
        className="space-y-4"
      >
        {/* Companies Section */}
        <AccordionItem
          value="companies"
          className="border rounded-[4px] px-4 shadow-none"
        >
          <SimpleAccordionTrigger
            title="Companies"
            subtext={renderSyncInfo("companies")}
            icon={<Building className="w-5 h-5 text-muted-foreground" />}
            value="companies"
            activeAccordion={activeAccordion}
          />
          <AccordionContent className="pb-6 pt-2">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <h4 className="text-sm font-medium">Enable sync</h4>
                  <p className="text-sm text-muted-foreground">
                    Enable or disable syncing of HubSpot companies
                  </p>
                </div>
                <Switch
                  checked={settings.companies.enabled}
                  onCheckedChange={() => handleToggle("companies")}
                />
              </div>

              {settings.companies.enabled && (
                <div className="space-y-6">
                  <div>
                    <h4 className="text-sm font-medium mb-1">Custom fields</h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      Select which custom fields to sync from HubSpot to Thena
                    </p>
                    <CustomFieldsSelector
                      value={settings.companies.selectedFields}
                      onChange={(fields) =>
                        handleFieldsChange("companies", fields)
                      }
                      placeholder="Search and select company fields"
                      disabled={!settings.companies.enabled}
                      appId={app?.appId}
                      fieldType="company"
                    />
                  </div>

                  {settings.companies.selectedFields.length > 0 && (
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h4 className="text-sm font-medium">Filters</h4>
                          <p className="text-sm text-muted-foreground">
                            Add filters to sync only specific companies
                          </p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => addFilter("companies")}
                          disabled={
                            settings.companies.selectedFields.length === 0
                          }
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add filter
                        </Button>
                      </div>

                      <div className="space-y-3">
                        {settings.companies.filters.map((filter, index) => (
                          <FilterRow
                            key={index}
                            filter={filter}
                            availableFields={settings.companies.selectedFields}
                            onUpdate={(updatedFilter) =>
                              updateFilter("companies", index, updatedFilter)
                            }
                            onDelete={() => deleteFilter("companies", index)}
                          />
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Trigger Sync Button for Companies */}
                  <div className="pt-4 border-t">
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => triggerSync("companies")}
                      disabled={
                        isTriggeringSync || !settings?.integration.enabled
                      }
                      className="text-xs"
                    >
                      {isTriggeringSync
                        ? "Triggering..."
                        : "Trigger company sync"}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Contacts Section */}
        <AccordionItem
          value="contacts"
          className="border rounded-[4px] px-4 shadow-none"
        >
          <SimpleAccordionTrigger
            title="Contacts"
            subtext={renderSyncInfo("contacts")}
            icon={<Users className="w-5 h-5 text-muted-foreground" />}
            value="contacts"
            activeAccordion={activeAccordion}
          />
          <AccordionContent className="pb-6 pt-2">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <h4 className="text-sm font-medium">Enable sync</h4>
                  <p className="text-sm text-muted-foreground">
                    Enable or disable syncing of HubSpot contacts
                  </p>
                </div>
                <Switch
                  checked={settings.contacts.enabled}
                  onCheckedChange={() => handleToggle("contacts")}
                />
              </div>

              {settings.contacts.enabled && (
                <div className="space-y-6">
                  <div>
                    <h4 className="text-sm font-medium">Default fields</h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      These fields are automatically synced from HubSpot to
                      Thena
                    </p>
                    <CustomFieldsSelector
                      value={[]}
                      onChange={() => {}}
                      placeholder=""
                      disabled={true}
                      appId={app?.appId}
                      fieldType="contact"
                    />
                  </div>

                  {/* Trigger Sync Button for Contacts */}
                  <div className="pt-4 border-t">
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => triggerSync("contacts")}
                      disabled={
                        isTriggeringSync || !settings?.integration.enabled
                      }
                      className="text-xs"
                    >
                      {isTriggeringSync
                        ? "Triggering..."
                        : "Trigger contact sync"}
                    </Button>
                  </div>

                  {/* TODO: Add filter support for contacts in the future */}
                  {/*
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h4 className="text-sm font-medium">Filters</h4>
                        <p className="text-sm text-muted-foreground">
                          Add filters to sync only specific contacts
                        </p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => addFilter("contacts")}
                        disabled={settings.contacts.selectedFields.length === 0}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add Filter
                      </Button>
                    </div>

                    <div className="space-y-3">
                      {settings.contacts.filters.map((filter, index) => (
                        <FilterRow
                          key={index}
                          filter={filter}
                          availableFields={settings.contacts.selectedFields}
                          onUpdate={(updatedFilter) =>
                            updateFilter("contacts", index, updatedFilter)
                          }
                          onDelete={() => deleteFilter("contacts", index)}
                        />
                      ))}
                    </div>
                  </div>
                  */}
                </div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Sync Status Section */}
        <AccordionItem
          value="sync-status"
          className="border rounded-[4px] px-4 shadow-none"
        >
          <SimpleAccordionTrigger
            title="Sync status"
            subtext="View sync operations and status"
            icon={<Activity className="w-5 h-5 text-muted-foreground" />}
            value="sync-status"
            activeAccordion={activeAccordion}
          />
          <AccordionContent className="pb-6 pt-2">
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Complete sync operations from HubSpot to Thena
              </p>

              <Tabs defaultValue="completesync" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="completesync">Complete sync</TabsTrigger>
                  <TabsTrigger value="updates">
                    Updates from Hubspot
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="completesync" className="mt-4">
                  <div className="max-h-80 overflow-y-auto space-y-2">
                    {/* Companies Syncs */}
                    {syncStatus?.accountSync?.map((sync) => (
                      <div
                        key={sync.id}
                        className="flex items-start justify-between p-3 border rounded-sm bg-background/50"
                      >
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="capitalize font-medium">
                              {sync.status}
                            </span>
                            {sync.status === "active" && sync.progress > 0 && (
                              <span className="text-muted-foreground">
                                ({sync.progress}%)
                              </span>
                            )}
                            <span className="text-xs px-2 py-0.5 bg-background border rounded-sm">
                              Companies
                            </span>
                          </div>
                          {sync.status === "failed" && (
                            <div className="text-xs text-red-600 mt-1">
                              Unable to complete sync
                            </div>
                          )}
                        </div>
                        <div className="text-right text-xs text-muted-foreground">
                          <div>Started: {formatDate(sync.createdAt)}</div>
                          {sync.finishedAt && (
                            <div>Finished: {formatDate(sync.finishedAt)}</div>
                          )}
                        </div>
                      </div>
                    ))}

                    {/* Contacts Syncs */}
                    {syncStatus?.contactSync?.map((sync) => (
                      <div
                        key={sync.id}
                        className="flex items-start justify-between p-3 border rounded-sm bg-background/50"
                      >
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="capitalize font-medium">
                              {sync.status}
                            </span>
                            {sync.status === "active" && sync.progress > 0 && (
                              <span className="text-muted-foreground">
                                ({sync.progress}%)
                              </span>
                            )}
                            <span className="text-xs px-2 py-0.5 bg-background border rounded-sm">
                              Contacts
                            </span>
                          </div>
                          {sync.status === "failed" && (
                            <div className="text-xs text-red-600 mt-1">
                              Unable to complete sync
                            </div>
                          )}
                        </div>
                        <div className="text-right text-xs text-muted-foreground">
                          <div>Started: {formatDate(sync.createdAt)}</div>
                          {sync.finishedAt && (
                            <div>Finished: {formatDate(sync.finishedAt)}</div>
                          )}
                        </div>
                      </div>
                    ))}

                    {!syncStatus?.accountSync?.length &&
                      !syncStatus?.contactSync?.length && (
                        <div className="text-center py-8 text-muted-foreground">
                          No sync operations found
                        </div>
                      )}

                    {/* Action Buttons */}
                    <div className="pt-2 border-t">
                      <div className="flex gap-2">
                        <Button
                          variant="default"
                          size="sm"
                          onClick={() => triggerSync("both")}
                          disabled={
                            isTriggeringSync || !settings?.integration.enabled
                          }
                          className="text-xs"
                        >
                          {isTriggeringSync ? "Triggering..." : "Trigger sync"}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={fetchSyncStatus}
                          disabled={isSyncStatusLoading}
                          className="text-xs"
                        >
                          {isSyncStatusLoading
                            ? "Refreshing..."
                            : "Refresh status"}
                        </Button>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="updates" className="mt-4">
                  <div className="max-h-80 overflow-y-auto">
                    {isLoadingAuditLogs ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="text-sm text-muted-foreground">
                          Loading audit logs...
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {auditLogs?.data?.map((log) => (
                          <div
                            key={log.id}
                            className="p-3 border rounded-sm bg-background/50"
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                  <span
                                    className={`text-xs px-2 py-0.5 rounded-sm capitalize ${
                                      log.status === "SUCCESS"
                                        ? "bg-green-100 text-green-700"
                                        : log.status === "FAILED"
                                        ? "bg-red-100 text-red-700"
                                        : "bg-yellow-100 text-yellow-700"
                                    }`}
                                  >
                                    {log.status.toLowerCase()}
                                  </span>
                                  <span className="text-xs text-muted-foreground">
                                    {log.entityType.replace("HUBSPOT_", "")}
                                  </span>
                                </div>
                                <div className="text-sm font-medium">
                                  {log.activity}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {log.description}
                                </div>
                                {log.metadata.error && (
                                  <div className="text-xs text-red-600 mt-1">
                                    Error: {log.metadata.error}
                                  </div>
                                )}
                              </div>
                              <div className="text-xs text-muted-foreground text-right">
                                {formatDate(log.createdAt)}
                              </div>
                            </div>
                          </div>
                        ))}

                        {(!auditLogs?.data || auditLogs.data.length === 0) && (
                          <div className="text-center py-8 text-muted-foreground">
                            No audit logs found
                          </div>
                        )}

                        {/* Pagination */}
                        {auditLogs && auditLogs.totalPages > 1 && (
                          <div className="flex justify-center gap-2 pt-4">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                fetchAuditLogs(Math.max(1, currentPage - 1))
                              }
                              disabled={currentPage === 1 || isLoadingAuditLogs}
                            >
                              Previous
                            </Button>
                            <span className="flex items-center text-sm text-muted-foreground">
                              Page {currentPage} of {auditLogs.totalPages}
                            </span>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                fetchAuditLogs(
                                  Math.min(
                                    auditLogs.totalPages,
                                    currentPage + 1,
                                  ),
                                )
                              }
                              disabled={
                                currentPage === auditLogs.totalPages ||
                                isLoadingAuditLogs
                              }
                            >
                              Next
                            </Button>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Integration Info - Moved to the end */}
      <div>
        {settings.integration.enabled ? (
          <div className="text-sm text-muted-foreground">
            HubSpot integrated on{" "}
            {formatDate(settings.integration.integratedOn || "")} by{" "}
            {settings.integration.integratedBy || "Unknown user"}
          </div>
        ) : (
          <div className="flex items-center gap-2 p-4 text-sm border rounded-sm bg-destructive/10 text-destructive">
            <AlertCircle className="h-4 w-4" />
            <span>Integration is disconnected</span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleReconnect}
              className="ml-auto"
            >
              Reconnect
            </Button>
          </div>
        )}
      </div>

      {/* Save/Cancel Actions */}
      {hasUnsavedChanges && (
        <div className="flex items-center justify-end gap-3 pt-4 border-t">
          <Button variant="outline" onClick={handleCancel} disabled={isSaving}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      )}

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Unsaved changes</AlertDialogTitle>
            <AlertDialogDescription>
              You have unsaved changes. Are you sure you want to cancel without
              saving?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>No, keep editing</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmCancel}>
              Yes, discard changes
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
