"use server";

import { AUTH_URL } from "@/config/constant";
import { createClient } from "@/lib/supabase-auth-server";
import { cookies } from "next/headers";
import {
  fetchOrganizations,
  formatUserResponse,
  getDefaultOrg,
  getErrorMessage,
  isValidEmail,
} from "../../utils/auth-utils";
import { findOrganizationByEmail } from "./organization";

export async function login(email: string, password: string) {
  try {
    if (!isValidEmail(email)) {
      throw new Error("Invalid email format.");
    }

    const supabase = await createClient();
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      throw new Error(getErrorMessage(error));
    }

    const orgs = await fetchOrganizations(data.session.access_token);

    await setOrgCookies(data.session.access_token);

    if (!orgs.length) {
      return {
        success: true,
        data: {
          user: {
            id: data.user.id,
            name: data.user.user_metadata.name,
            avatarUrl: data.user.user_metadata.avatar_url || null,
            email: data.user.email,
            orgId: null,
            userMetadata: {
              ...data.user.user_metadata,
              userPk: data.user.id,
            },
            allOrgs: [],
            currentOrgId: null,
            currentUserUid: data.user.id,
            allUserIds: [],
          },
        },
      };
    }
    const defaultOrg = getDefaultOrg(orgs);
    const { data: allPersonas } = await supabase
      .from("user")
      .select("*")
      .eq("auth_id", data.user.id);

    const user = allPersonas.find(
      (persona) => persona.organization_id == defaultOrg.id,
    );

    const response = formatUserResponse({
      data,
      orgs,
      user,
      allPersonas,
      defaultOrg,
    });
    return {
      success: true,
      data: response,
    };
  } catch (error) {
    return {
      success: false,
      error: "Authentication failed",
      message: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

export async function signout() {
  try {
    const supabase = await createClient();
    const { error } = await supabase.auth.signOut();
    const cookieStore = await cookies();

    // Clear the session cookie
    cookieStore.delete("base-access");
    cookieStore.delete("orgUid");
    cookieStore.delete("orgId");

    if (error) {
      throw new Error("Failed to sign out");
    }
  } catch (error) {
    console.error("Error signing out:", error);
    return {
      success: false,
      error: "signout_failed",
    };
  }
}

export async function signup(
  email: string,
  password: string,
  name: string = "", // Make name optional with default empty string
  metadata: {
    signupComplete: boolean;
  },
  slug?: string,
) {
  try {
    const supabase = await createClient();
    // First check if the email domain is authorized
    const domain = email.split("@")[1].toLowerCase();

    // Call our security definer function to check if domain is authorized
    const { data: isDomainAuthorized, error: domainCheckError } =
      await supabase.rpc("check_authorized_domain", { user_email: email });

    if (domainCheckError) {
      console.error("Error checking domain authorization:", domainCheckError);
      return {
        success: false,
        error: "domain_check_failed",
        message: "Failed to verify email domain. Please try again later.",
      };
    }

    if (!isDomainAuthorized) {
      return {
        success: false,
        error: "unauthorized_domain",
        message:
          "Registration with this email domain is not allowed. Please use an authorized email domain.",
      };
    }

    // Now check if user exists
    const { data: existingUser } = await supabase
      .from("user")
      .select("id")
      .eq("email", email);

    if (existingUser?.length > 0) {
      return {
        success: false,
        error: "user_exists",
        message:
          "An account with this email already exists. Please login instead.",
      };
    }
    // Check if organization exists for the domain

    //public domain check
    // if (PUBLIC_EMAIL_DOMAINS.has(domain)) {
    //   return {
    //     success: false,
    //     error: "public_domain",
    //     message:
    //       "Registration with public email domains is not allowed. Please use your company email.",
    //   };
    // }

    const organizations = await findOrganizationByEmail(domain);
    const updatedMetadata = {
      ...metadata,
      name: name || "", // Ensure name is never undefined
      domainExists: organizations.length > 0,
      user_type: organizations.length > 0 ? "user" : "org_admin",
    };

    // Todo : Add this back when org is private vs public
    //  if (!organization.metadata?.auto_add_domain_users) {
    //     return {
    //       error: {
    //         message:
    //           "This email domain is already registered but not accepting new members. Please contact your organization administrator.",
    //       },
    //     };
    //   }

    const redirectTo = `${
      process.env.NEXT_PUBLIC_REDIRECT_URL
    }/auth/callback?next=/getting-started${slug ? `&slug=${slug}` : ""}`;
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: updatedMetadata,
        emailRedirectTo: redirectTo,
      },
    });

    // Log the signup response
    console.log("Signup response:", { data, error });

    if (error) {
      console.error("Signup error:", error);
      return {
        success: false,
        error: error.message,
        message: "Failed to sign up",
      };
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error("Signup error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      message: "Failed to sign up",
    };
  }
}

export async function updateSignupProgress(progressData: {
  signupComplete: boolean;
}) {
  try {
    const supabase = await createClient();
    await getCurrentUser();
    const { error: updateError } = await supabase.auth.updateUser({
      data: progressData,
    });

    if (updateError) {
      console.error("Update error:", updateError);
      throw new Error(`Failed to update user metadata: ${updateError.message}`);
    }
  } catch (error) {
    console.error("Update error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

export async function getCurrentUser() {
  try {
    const supabase = await createClient();
    const cookieStore = await cookies();
    const accessToken = cookieStore.get("base-access")?.value;
    const { data, error: getUserError } = await supabase.auth.getUser(
      accessToken,
    );

    if (getUserError || !data.user) {
      console.error("Get user error:", getUserError);
      throw new Error(
        `Failed to get user: ${getUserError?.message || "User not found"}`,
      );
    }

    return data.user;
  } catch (error) {
    console.error("Get user error:", error);
    return null;
  }
}

export async function signInWithGoogleIdToken(
  token: string,
  email?: string | null,
) {
  try {
    const supabase = await createClient();
    const domain = email?.split("@")[1].toLowerCase() || "";
    // Call our security definer function to check if domain is authorized
    const { data: isDomainAuthorized, error: domainCheckError } =
      await supabase.rpc("check_authorized_domain", { user_email: email });

    if (domainCheckError) {
      console.error("Error checking domain authorization:", domainCheckError);
      return {
        success: false,
        error: {
          message: "domain_check_failed",
        },
      };
    }

    if (!isDomainAuthorized) {
      return {
        success: false,
        error: {
          message: "unauthorized_domain",
        },
      };
    }
    const { data: googleResponse, error } =
      await supabase.auth.signInWithIdToken({
        provider: "google",
        token,
      });
    await setOrgCookies(googleResponse.session.access_token);

    const organizations = await findOrganizationByEmail(domain);
    if (organizations.length > 0) {
      await supabase.auth.updateUser({
        data: { domainExists: true },
      });
    }
    const { data: existingUser } = await supabase
      .from("user")
      .select("id")
      .eq("email", email);
    const { data: allPersonas } = await supabase
      .from("user")
      .select("*")
      .eq("auth_id", googleResponse.user.id);
    const orgs = await fetchOrganizations(googleResponse.session.access_token);
    const defaultOrg = getDefaultOrg(orgs);
    const user = allPersonas.find(
      (persona) => persona.organization_id == defaultOrg.id,
    );
    if (existingUser?.length > 0) {
      await updateSignupProgress({ signupComplete: true });

      const response = formatUserResponse({
        data: {
          ...googleResponse,
          user_metadata: {
            ...googleResponse.user.user_metadata,
            domainExists: organizations.length > 0,
            signupComplete: true,
          },
        },
        orgs,
        user,
        allPersonas,
        defaultOrg,
      });

      return {
        success: true,
        data: { response },
        error,
      };
    } else {
      const response = formatUserResponse({
        data: {
          ...googleResponse,
          user_metadata: {
            ...googleResponse.user.user_metadata,
            domainExists: organizations.length > 0,
            signupComplete: false,
          },
        },
        orgs,
        user,
        allPersonas,
        defaultOrg,
      });
      await updateSignupProgress({ signupComplete: false });
      return {
        success: true,
        data: { response },
        error,
      };
    }
  } catch (error) {
    console.error("Sign in with google id token error:", error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : "Unknown error",
      },
    };
  }
}

export async function inviteUserByEmail(
  email: string,
  accessLevel: string,
  currentOrgUId: string,
  currentOrgId: string,
  teamId?: string,
) {
  try {
    const supabase = await createClient();
    const redirectTo = `${process.env.NEXT_PUBLIC_REDIRECT_URL}/auth/callback?next=/set-password`;
    const currentUser = await getCurrentUser();
    const { data: usersList } = await supabase
      .from("user")
      .select("*")
      .eq("auth_id", currentUser?.id)
      .eq("organization_id", currentOrgId);
    const metadata = {
      user_type: accessLevel,
      invitedOrgId: currentOrgUId,
      invitingUserId: usersList[0].uid,
      isInvitedUser: true,
      ...(teamId && { invite_team_id: teamId }),
    };

    const { data: existingUser } = await supabase
      .from("user")
      .select("*")
      .eq("email", email);

    // Get all users across pages
    let allUsers = [];
    let page = 0;
    const perPage = 1000;
    let hasMore = true;

    while (hasMore) {
      const { data: users, error: listError } =
        await supabase.auth.admin.listUsers({
          page: page,
          perPage: perPage,
        });

      if (listError) {
        console.error("Error fetching users:", listError);
        return {
          success: false,
          error: "fetch_error",
          message: "Failed to verify user account.",
        };
      }

      if (!users.users.length) {
        hasMore = false;
      } else {
        allUsers = [...allUsers, ...users.users];
        page++;
      }
    }

    const userInAuthTable = allUsers.find((user) => user.email === email);

    // Check if user exists in auth table but not in user table (new user)
    if (userInAuthTable && (!existingUser || existingUser.length === 0)) {
      // New user - delete from auth table if exists and reinvite with /set-password redirect
      const { error: deleteError } = await supabase.auth.admin.deleteUser(
        userInAuthTable.id,
      );
      if (deleteError) {
        throw deleteError;
      }
      const { data, error } = await supabase.auth.admin.inviteUserByEmail(
        email,
        {
          data: metadata,
          redirectTo: redirectTo, // /set-password redirect for new users
        },
      );
      return { data, error };
    }
    // User exists in the user table (existing user)
    else if (existingUser && existingUser.length > 0) {
      const emailRedirectTo = teamId
        ? `${process.env.NEXT_PUBLIC_REDIRECT_URL}/auth/callback?invitedOrgId=${currentOrgUId}&inviteTeamId=${teamId}&invitingUserId=${usersList[0].uid}&next=/dashboard/${teamId}?fetchOrgs=${currentOrgUId}`
        : `${process.env.NEXT_PUBLIC_REDIRECT_URL}/auth/callback?invitedOrgId=${currentOrgUId}&invitingUserId=${usersList[0].uid}&next=/dashboard?fetchOrgs=${currentOrgUId}`;

      // Re-invite with dashboard redirect for existing users
      const { data, error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: false,
          emailRedirectTo,
        },
      });
      return { data, error };
    }
    // New user without any existing entry
    else {
      const { data, error } = await supabase.auth.admin.inviteUserByEmail(
        email,
        {
          data: metadata,
          redirectTo, // /set-password redirect for new users
        },
      );

      if (error) {
        throw error;
      }
      return { data, error };
    }
  } catch (error) {
    console.error("Error inviting user:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

export async function refreshSession(refreshToken: string) {
  try {
    const supabase = await createClient();

    const { data, error } = await supabase.auth.refreshSession({
      refresh_token: refreshToken,
    });

    if (error) {
      throw error;
    }
    const cookieStore = await cookies();
    cookieStore.set("base-access", data.session.access_token, {
      httpOnly: true,
      secure: true,
      sameSite: "strict",
    });
    return data;
  } catch (error) {
    console.error("Error refreshing session:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

export const updateUserPassword = async (password: string) => {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase.auth.updateUser({
      password,
    });
    if (error) {
      throw error;
    }
    return { data, error };
  } catch (err) {
    console.log(err);
    const { error } = await supabase.auth.updateUser({
      password,
    });
    return {
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
};

export const changePassword = async (
  currentPassword: string,
  newPassword: string,
  orgUid: string,
  orgId: string,
) => {
  try {
    const supabase = await createClient();
    // First verify the current password
    const sessionDetails = await getUserSessionDetails(orgUid, orgId);

    if (!sessionDetails.success || !sessionDetails.data.user.email) {
      return {
        success: false,
        error: "Failed to get user session details"
      };
    }

    const { error: signInError } = await supabase.auth.signInWithPassword({
      email: sessionDetails.data.user.email,
      password: currentPassword,
    });

    if (signInError) {
      return {
        success: false,
        error: "Current password is incorrect"
      };
    }

    // If current password is correct, update to new password
    const { error: updateError } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (updateError) {
      return {
        success: false,
        error: "Failed to update password"
      };
    }
    return {
      success: true,
      message: "Password updated successfully",
    };
  } catch (error) {
    console.error("Error changing password:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unexpected error occurred"
    };
  }
};

export const resendEmail = async (email: string) => {
  try {
    const supabase = await createClient();
    const redirectTo = `${process.env.NEXT_PUBLIC_REDIRECT_URL}/auth/callback?next=/getting-started`;
    const { data: existingUser } = await supabase
      .from("user")
      .select("id")
      .eq("email", email);
    if (existingUser?.length > 0) {
      return {
        success: false,
        error: {
          message: "User already exists.",
        },
      };
    }
    const { data, error } = await supabase.auth.resend({
      type: "signup",
      email: email,
      options: {
        emailRedirectTo: redirectTo,
      },
    });

    if (error) {
      throw error;
    }

    return { data, error };
  } catch (error) {
    console.error("Error resending email:", error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : "Unknown error",
      },
    };
  }
};

export async function getUserSessionDetails(currentOrgUid, currentOrgId) {
  try {
    const cookieStore = await cookies();
    const accessToken = cookieStore.get("base-access")?.value;
    const supabase = await createClient();
    const currentUser = await getCurrentUser();
    const orgResponse = await fetch(
      `${AUTH_URL}/v1/authentication/organizations`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );

    const orgs = await orgResponse.json();

    if (!orgs.length) {
      throw new Error("Failed to fetch organizations");
    }

    let defaultOrg = orgs.find((org) => org.metadata?.is_default);

    if (!defaultOrg && !currentOrgId) {
      defaultOrg = orgs[orgs.length - 1];
    } else {
      defaultOrg = orgs.find((org) => org.id === currentOrgId);
    }

    const { data: allPersonas } = await supabase
      .from("user")
      .select("*")
      .eq("auth_id", currentUser.id);

    const user = allPersonas.find(
      (persona) => persona.organization_id == defaultOrg.id,
    );

    const response = {
      user: {
        id: currentUser.id,
        name: currentUser.user_metadata.name,
        email: currentUser.email,
        metadata: user.metadata,
        avatarUrl: user?.avatar_url || null,
        orgId: currentOrgUid,
        userMetadata: currentUser.user_metadata,
        allOrgs: orgs || [],
        currentOrgId: currentOrgId,
        currentUserUid: user?.uid,
        allUserIds:
          allPersonas?.map((persona) => ({
            uid: persona.uid,
            orgId: persona.organization_id,
            avatarUrl: persona.avatar_url,
            name: persona.name,
          })) || [],
      },
    };
    return {
      success: true,
      data: response,
    };
  } catch (error) {
    return {
      success: false,
      error: "Authentication failed",
      message: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

export async function forgetPasswordLink(email: string) {
  try {
    const supabase = await createClient();
    const redirectTo = `${process.env.NEXT_PUBLIC_REDIRECT_URL}/auth/callback?next=/reset-password`;
    // Get all users across pages
    let allUsers = [];
    let page = 0;
    const perPage = 1000;
    let hasMore = true;

    while (hasMore) {
      const { data: users, error: listError } =
        await supabase.auth.admin.listUsers({
          page: page,
          perPage: perPage,
        });

      if (listError) {
        console.error("Error fetching users:", listError);
        return {
          success: false,
          error: "fetch_error",
          message: "Failed to verify user account.",
        };
      }

      if (!users.users.length) {
        hasMore = false;
      } else {
        allUsers = [...allUsers, ...users.users];
        page++;
      }
    }

    const userExists = allUsers.some((user) => user.email === email);

    if (!userExists) {
      return {
        success: false,
        error: "user_not_found",
        message: "For registered users password reset link will be sent to registered email",
      };
    }

    const { data, error: resetError } =
      await supabase.auth.resetPasswordForEmail(email, {
        redirectTo,
      });

    if (resetError) {
      console.error("Password reset error:", resetError);
      return {
        success: false,
        error: "reset_failed",
        message: resetError.message || "Failed to send password reset email.",
      };
    }

    return {
      success: true,
      data,
      message: "Password reset email sent successfully.",
    };
  } catch (error) {
    console.error("Exception in password reset:", error);
    return {
      success: false,
      error: "unexpected_error",
      message:
        error instanceof Error
          ? error.message
          : "An unexpected error occurred.",
    };
  }
}

export async function getRealtimeToken() {
  const cookieStore = await cookies();
  const accessToken = cookieStore.get("base-access")?.value;

  if (!accessToken) {
    throw new Error("No access token found");
  }

  return accessToken;
}

export async function setOrgCookies(token: string) {
  const cookieStore = await cookies();
  cookieStore.set("base-access", token, {
    httpOnly: true,
    secure: true,
    sameSite: "strict",
  });
}
