import { WORKFLOWS_URL } from "@/config/constant";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import { HUBSPOT_ACTIVITIES, generateHubspotActivityId } from "../constants";

type RouteParams = Promise<{ appId: string }>;

interface SearchFieldsRequest {
  query: string;
  type: "company" | "contact";
}

interface HubSpotFieldOption {
  name: string;
  label: string;
  type: string;
  hubspotDefined: boolean;
  order: number;
}

export async function POST(
  request: Request,
  context: { params: RouteParams },
): Promise<Response> {
  try {
    const params = await context.params;
    const { appId: _appId } = params;
    const body: SearchFieldsRequest = await request.json();

    const cookieStore = await cookies();
    const session = cookieStore.get("base-access");
    const orgUid = request.headers.get("x-org-uid");

    if (!session?.value) {
      return NextResponse.json(
        { error: "Unauthorized: No session token found" },
        { status: 401 },
      );
    }

    if (!orgUid) {
      return NextResponse.json(
        { error: "Unauthorized: No organization ID found" },
        { status: 401 },
      );
    }

    // Validate request body
    if (!body.query || body.query.length < 2) {
      return NextResponse.json(
        { error: "Query must be at least 2 characters long" },
        { status: 400 },
      );
    }

    if (!body.type || !["company", "contact"].includes(body.type)) {
      return NextResponse.json(
        { error: "Type must be either 'company' or 'contact'" },
        { status: 400 },
      );
    }

    if (!HUBSPOT_ACTIVITIES.SEARCH_CUSTOM_FIELDS) {
      return NextResponse.json(
        {
          error:
            "HubSpot search custom fields workflow activity not configured",
        },
        { status: 500 },
      );
    }

    if (!WORKFLOWS_URL) {
      return NextResponse.json(
        { error: "Workflows service URL not configured" },
        { status: 500 },
      );
    }

    // Generate activity name using the utility function
    const activityName = generateHubspotActivityId(
      HUBSPOT_ACTIVITIES.SEARCH_CUSTOM_FIELDS,
      _appId,
      orgUid,
    );
    const workflowUrl = `${WORKFLOWS_URL}/api/v1/workflows/${activityName}/execute`;

    const workflowResponse = await fetch(workflowUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.value}`,
        "x-org-id": orgUid,
      },
      body: JSON.stringify({
        query: body.query,
        type: body.type,
      }),
    });

    if (!workflowResponse.ok) {
      const errorText = await workflowResponse.text();
      console.error("Workflow API error:", errorText);
      return NextResponse.json(
        { error: "Failed to search HubSpot custom fields" },
        { status: workflowResponse.status },
      );
    }

    const result = await workflowResponse.json();

    // Extract the options from the workflow response
    // The API returns: { success: true, data: { options: [...], searchType: "...", totalResults: N } }
    const options = result.data?.options || [];

    // Map the options to the expected format
    const fields = options.map((option: HubSpotFieldOption) => ({
      value: option.name,
      label: option.label,
    }));

    return NextResponse.json({ fields });
  } catch (error) {
    console.error("Error searching HubSpot custom fields:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
