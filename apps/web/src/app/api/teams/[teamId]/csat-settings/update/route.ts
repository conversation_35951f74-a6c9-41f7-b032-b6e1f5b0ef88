import { cookies } from "next/headers";
import { NextResponse } from "next/server";

type RouteParams = Promise<{ teamId: string }>;

export async function PATCH(
  request: Request,
  context: { params: RouteParams }
): Promise<Response> {
  const cookieStore = await cookies();
  const session = cookieStore.get("base-access");
  const orgUid = request.headers.get("x-org-uid");

  if (!session?.value || !orgUid) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const params = await context.params;
    const { teamId: _teamId } = params;
    const { cooldownPeriodDays, emailConfigId, userCooldownPeriodDays, closedStatusIds } = await request.json();
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/v1/csat/team/${_teamId}/update`, {
      method: "PATCH",
      headers: {
        "Authorization": `Bearer ${session.value}`,
        "x-org-id": orgUid,
        "Content-Type": "application/json"
      },
      body: JSON.stringify({ cooldownPeriodDays, emailConfigId, userCooldownPeriodDays, closedStatusIds })
    });

    if (!response.ok) {
      return NextResponse.json({ error: "Something went wrong" }, { status: response.status });
    }

    const responseData = await response.json();
    return NextResponse.json(responseData);

  } catch {
    return NextResponse.json(
      { error: "Failed to update CSAT cooldown period" },
      { status: 500 }
    );
  }
}
