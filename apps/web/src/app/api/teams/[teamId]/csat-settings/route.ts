import { cookies } from "next/headers";
import { NextResponse } from "next/server";

type RouteParams = Promise<{ teamId: string }>;

export async function GET(
  _request: Request,
  context: { params: RouteParams }
): Promise<Response> {
  const cookieStore = await cookies();
  const session = cookieStore.get("base-access");
  const orgUid = _request.headers.get("x-org-uid");

  if (!session?.value || !orgUid) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const params = await context.params;
    const { teamId: _teamId } = params;

    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/v1/csat/team/${_teamId}`, {
      headers: {
        Authorization: `Bearer ${session.value}`,
        "x-org-id": orgUid,
      },
    });

    if (response.status === 404) {
      return NextResponse.json({ rules: [] });
    }

    if (!response.ok) {
      return NextResponse.json({ error: "Something went wrong" }, { status: response.status });
    }

    const responseData = await response.json();

    return NextResponse.json({
      emailConfigId: responseData.emailConfigId,
      cooldownPeriodDays: responseData.cooldownPeriodDays,
      rules: responseData.rules,
      closedStatusIds: responseData.closedStatusIds,
      userCooldownPeriodDays: responseData.userCooldownPeriodDays,
    });
  } catch {
    return NextResponse.json(
      { error: "Failed to fetch CSAT settings" },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: Request,
  context: { params: RouteParams }
): Promise<Response> {
  const cookieStore = await cookies();
  const session = cookieStore.get("base-access");
  const orgUid = request.headers.get("x-org-uid");

  if (!session?.value || !orgUid) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const params = await context.params;
    const { teamId: _teamId } = params;
    const body = await request.json();
    const reorderRules = body;

    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/v1/csat/team/${_teamId}/rules/reorder`, {
      method: "PATCH",
      headers: {
        "Authorization": `Bearer ${session.value}`,
        "x-org-id": orgUid,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(reorderRules),
    });


    if (!response.ok) {
      return NextResponse.json({ error: "Something went wrong" }, { status: response.status });
    }

    return NextResponse.json({ message: "CSAT rules reordered successfully" });
  } catch {
    return NextResponse.json(
      { error: "Failed to reorder CSAT rules" },
      { status: 500 }
    );
  }
}
