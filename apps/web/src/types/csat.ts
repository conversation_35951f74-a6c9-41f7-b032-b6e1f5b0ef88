export enum CsatFilterOperator {
  EQUALS = "=",
  NOT_EQUALS = "!=",
  GREATER_THAN = ">",
  LESS_THAN = "<",
  GREATER_THAN_EQUAL = ">=",
  LESS_THAN_EQUAL = "<=",
  CONTAINS = "contains",
  NOT_CONTAINS = "not_contains",
  IN = "in",
  NOT_IN = "not_in",
  IS_EMPTY = "is_empty",
  IS_NOT_EMPTY = "is_not_empty"
}

export interface StandardFilterDto {
  field: string;
  operator: CsatFilterOperator;
  value: string | string[] | boolean | number | Date | null;
}

export interface CustomFieldFilterDto {
  field: string;
  customFieldId: string;
  operator: CsatFilterOperator;
  value: string | string[] | boolean | number | Date | null;
}

export interface EntityFilters {
  standardFields: StandardFilterDto[];
  customFields: CustomFieldFilterDto[];
}

export interface CSATRuleFilters {
  ticket: EntityFilters;
  account: EntityFilters;
  contact: EntityFilters;
}

export const createEmptyEntityFilters = (): EntityFilters => ({
  standardFields: [],
  customFields: []
});

export const createEmptyRuleFilters = (): CSATRuleFilters => ({
  ticket: createEmptyEntityFilters(),
  account: createEmptyEntityFilters(),
  contact: createEmptyEntityFilters()
});

export interface CSATRule {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  allFilters: CSATRuleFilters;
  anyFilters: CSATRuleFilters;
  priority: number;
  triggerConfig: {
    triggerType: 'always' | 'random';
    randomPercentage?: 10 | 20;
    delayMinutes?: number;
  };
  feedbackConfig: {
    enabled: boolean;
    feedbackType: 'star' | 'thumbs';
    customTitle?: string;
    customMessage?: string;
    customThankYouMessage?: string;
    includeCommentField: boolean;
    commentFieldLabel?: string;
    commentFieldPlaceholder?: string;
    brandingColor?: string;
    deliveryChannel?: 'source' | 'email';
  };
  createdAt: string;
  updatedAt: string;
}

export interface CSATSettings {
  cooldownPeriodDays?: number;
  userCooldownPeriodDays?: number;
  emailConfigId?: string;
  closedStatusIds?: string[];
  rules: CSATRule[];
}


export type EmailDataType = {
  id: string;
  email: string;
  domain?: string;
};

export type DefaultEmailType = {
  id: string;
  email: string;
};

export type EmailResponseItemType = {
  id?: string;
  uid?: string;
  domain?: string;
  customEmail?: string;
  isEmailForwardingVerified: boolean;
};

export type DomainType = {
  isDnsVerified: boolean;
  domain: string;
};