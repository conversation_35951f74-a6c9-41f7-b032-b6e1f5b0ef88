"use client";

import {
  AlertD<PERSON>og,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import { deleteCSATRule, updateCSATRule } from "@/services/csat-service";
import { CSATRule } from "@/types/csat";
import { DraggableProvidedDragHandleProps } from "@hello-pangea/dnd";
import { GripVertical, MoreHorizontal, Pencil, Trash2 } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface CSATRuleCardProps {
  rule: CSATRule;
  teamId: string;
  onUpdate: (updatedRule: CSATRule) => void;
  onDelete: (ruleId: string) => void;
  onEdit: (rule: CSATRule) => void;
  isReordering?: boolean;
  dragHandleProps?: DraggableProvidedDragHandleProps | null;
}

export function CSATRuleCard({
  rule,
  teamId,
  onUpdate,
  onDelete,
  onEdit,
  isReordering = false,
  dragHandleProps
}: CSATRuleCardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const handleToggleActive = async () => {
    setIsLoading(true);
    try {
      const { id: _id, createdAt: _createdAt, updatedAt: _updatedAt, ...rest } = rule;
      const updatedRuleData = {
        ...rest,
        isActive: !rule.isActive,
      };

      await updateCSATRule(teamId, rule.id, updatedRuleData);

      const updatedRule = {
        ...rule,
        isActive: !rule.isActive,
      };

      toast(rule.isActive ? "Rule deactivated." : "Rule activated.", {
        description: `The rule "${rule.name}" has been ${rule.isActive ? "deactivated" : "activated"}.`,
      });

      onUpdate(updatedRule);
    } catch (_error) {
      toast("Failed to update rule.", {
        description: "An error occurred while updating the rule. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    setIsLoading(true);
    try {
      await deleteCSATRule(teamId, rule.id);
      toast("Rule deleted.", {
        description: `The rule "${rule.name}" has been deleted.`,
      });

      onDelete(rule.id);
    } catch (_error) {
      toast("Failed to delete rule.", {
        description: "An error occurred while deleting the rule. Please try again.",
      });
    } finally {
      setIsLoading(false);
      setShowDeleteDialog(false);
    }
  };

  return (
    <>
      <div className="border rounded-sm">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between px-4 py-2">
          {isReordering && (
            <div {...dragHandleProps} className="mr-3 cursor-grab active:cursor-grabbing">
              <GripVertical className="h-5 w-5 text-muted-foreground" />
            </div>
          )}
          <div className="flex-1">
            <h3 className="text-base font-medium">{rule.name}</h3>
            {rule.description && (
              <p className="text-sm text-muted-foreground mt-1">{rule.description}</p>
            )}
          </div>
          <div className="flex items-center gap-3 mt-2 sm:mt-0">
            <Switch
              checked={rule.isActive}
              onCheckedChange={handleToggleActive}
              disabled={isLoading || isReordering}
            />
            {!isReordering && (
              <DropdownMenu modal={false}>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    disabled={isLoading}
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onEdit(rule)}>
                    <Pencil className="h-4 w-4 mr-2" />
                    Edit rule
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => setShowDeleteDialog(true)}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete rule
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </div>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete rule</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete &quot;{rule.name}&quot;? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isLoading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isLoading ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}