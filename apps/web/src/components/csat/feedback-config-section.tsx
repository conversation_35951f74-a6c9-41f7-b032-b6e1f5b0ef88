"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { CSATRule } from "@/types/csat";
import { Eye, Mail, Slack, Star, ThumbsUp } from "lucide-react";
import Image from "next/image";
import React from "react";

interface FeedbackConfigSectionProps {
  feedbackConfig: CSATRule["feedbackConfig"];
  onFeedbackConfigChange: (feedbackConfig: CSATRule["feedbackConfig"]) => void;
}

export function FeedbackConfigSection({
  feedbackConfig,
  onFeedbackConfigChange,
}: FeedbackConfigSectionProps) {
  const [isSlackPreview, setIsSlackPreview] = React.useState(false);

  // Get current organization data
  const currentOrgId = useGlobalConfigPersistStore((state) => state.currentOrgId);
  const allOrgs = useGlobalConfigPersistStore((state) => state.orgs);
  const currentOrg = allOrgs.find(org => org.id === currentOrgId);
  
  const orgName = currentOrg?.name || "Support team";
  const orgLogoUrl = currentOrg?.logoUrl;

  // State for CSAT email configuration
  const [csatEmail, setCsatEmail] = React.useState<string>("<EMAIL>");

  // Get teamId from URL params 
  const [teamId, setTeamId] = React.useState<string | null>(null);

  // Set default values based on feedback type
  React.useEffect(() => {
    const defaults = {
      customTitle: feedbackConfig.feedbackType === "star" ? "How would you rate your experience?" : "How was your experience?",
      commentFieldLabel: "Any additional comments?",
      commentFieldPlaceholder: "Tell us more about your experience..."
    };

    // Only set defaults if fields are empty
    const updates: Partial<CSATRule["feedbackConfig"]> = {};
    
    if (!feedbackConfig.customTitle) {
      updates.customTitle = defaults.customTitle;
    }
    if (!feedbackConfig.commentFieldLabel) {
      updates.commentFieldLabel = defaults.commentFieldLabel;
    }
    if (!feedbackConfig.commentFieldPlaceholder) {
      updates.commentFieldPlaceholder = defaults.commentFieldPlaceholder;
    }

    if (Object.keys(updates).length > 0) {
      onFeedbackConfigChange({
        ...feedbackConfig,
        ...updates,
      });
    }
  }, [feedbackConfig.feedbackType, feedbackConfig.customTitle, feedbackConfig.commentFieldLabel, feedbackConfig.commentFieldPlaceholder, onFeedbackConfigChange]);

  React.useEffect(() => {
    // Extract teamId from URL
    const path = window.location.pathname;
    const teamIdMatch = path.match(/\/dashboard\/([^\/]+)/);
    if (teamIdMatch) {
      setTeamId(teamIdMatch[1]);
    }
  }, []);

  // Fetch CSAT email configuration
  React.useEffect(() => {
    if (!teamId) return;

    const fetchCSATEmailConfig = async () => {
      try {
        // First, get CSAT settings to get the emailConfigId
        const csatResponse = await fetch(`/api/teams/${teamId}/csat-settings`);
        if (csatResponse.ok) {
          const csatData = await csatResponse.json();
          
          if (csatData.emailConfigId) {
            // Try to get the custom email configuration
            try {
              const emailResponse = await fetch(`/api/organization/teams/email-config/custom-email-config?teamId=${teamId}`);
              if (emailResponse.ok) {
                const emailData = await emailResponse.json();
                const emailsArray = Array.isArray(emailData) ? emailData : emailData.data || [];
                const configuredEmail = emailsArray.find((email: { id: string; uid: string; customEmail: string }) => email.id === csatData.emailConfigId || email.uid === csatData.emailConfigId);
                
                if (configuredEmail && configuredEmail.customEmail) {
                  setCsatEmail(configuredEmail.customEmail);
                  return;
                }
              }
            } catch {
              // Fall back to default email if custom email fetch fails
            }
          }

          // Fall back to default email
          try {
            const defaultEmailResponse = await fetch(`/api/organization/teams/email-config/default-email?teamId=${teamId}`, {
              method: "POST",
            });
            if (defaultEmailResponse.ok) {
              const defaultEmailData = await defaultEmailResponse.json();
              setCsatEmail(defaultEmailData.email);
            }
          } catch {
            // Keep the default fallback
          }
        }
      } catch (error) {
        console.error("Error fetching CSAT email config:", error);
        // Keep the default fallback email
      }
    };

    fetchCSATEmailConfig();
  }, [teamId]);

  const handleFeedbackTypeChange = (value: "star" | "thumbs") => {
    const newTitle = value === "star" ? "How would you rate your experience?" : "How was your experience?";
    onFeedbackConfigChange({
      ...feedbackConfig,
      feedbackType: value,
      customTitle: newTitle, // Update title when feedback type changes
    });
  };

  const handleFieldChange = (
    field: keyof CSATRule["feedbackConfig"],
    value: string | boolean
  ) => {
    onFeedbackConfigChange({
      ...feedbackConfig,
      [field]: value,
    });
  };

  // Validation function
  const validateField = (field: keyof CSATRule["feedbackConfig"], value: string) => {
    if (!value || value.trim() === "") {
      return false;
    }
    return true;
  };

  // Sample ticket data for preview
  const sampleTicketData = {
    ticketId: "CS-123",
    ticketTitle: "Issue with payment processing",
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    closedAt: new Date()
  };

  // Helper function to format dates
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Feedback type selection */}
      <div className="space-y-4">
        <div className="space-y-1">
          <Label className="text-sm font-medium">Feedback type</Label>
          <p className="text-sm text-muted-foreground">
            Choose how customers will rate their experience.
          </p>
        </div>

        <RadioGroup
          value={feedbackConfig.feedbackType}
          onValueChange={(value) => handleFeedbackTypeChange(value as "star" | "thumbs")}
          className="space-y-4"
        >
          <div className="flex items-start space-x-3">
            <RadioGroupItem value="star" id="star" className="mt-0.5" />
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <Star className="h-4 w-4 text-yellow-500" />
                <Label htmlFor="star" className="font-medium cursor-pointer">
                  5-star rating
                </Label>
              </div>
              <p className="text-sm text-muted-foreground">
                Ask customers to rate their experience on a scale of 1-5 stars.
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <RadioGroupItem value="thumbs" id="thumbs" className="mt-0.5" />
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <ThumbsUp className="h-4 w-4 text-blue-500" />
                <Label htmlFor="thumbs" className="font-medium cursor-pointer">
                  Thumbs up/down
                </Label>
              </div>
              <p className="text-sm text-muted-foreground">
                Ask customers for a simple thumbs up or thumbs down.
              </p>
            </div>
          </div>
        </RadioGroup>
      </div>

      <div>
        <Label htmlFor="deliveryChannel" className="text-sm font-medium">Delivery channel</Label>
        <p className="text-sm text-muted-foreground mt-1">
          Choose how the survey will be delivered to customers.
        </p>
        <Select
          value={feedbackConfig.deliveryChannel || "email"}
          onValueChange={(value) => handleFieldChange("deliveryChannel", value)}
        >
          <SelectTrigger className="w-full mt-2">
            <SelectValue placeholder="Select delivery channel" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="email">Email</SelectItem>
            <SelectItem value="source">Source (Slack, Email tickets)</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground mt-1">
          Selecting &apos;Source&apos; sends the survey through the original channel if it&apos;s Slack or email, otherwise by email.
        </p>
      </div>

      <Tabs defaultValue="content" className="mt-6">
        <TabsList className="mb-4">
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="appearance">Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="content" className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="customTitle" className="text-sm">Survey title</Label>
            <Input
              id="customTitle"
              value={feedbackConfig.customTitle || ""}
              onChange={(e) => handleFieldChange("customTitle", e.target.value)}
              placeholder={feedbackConfig.feedbackType === "star" ? "How would you rate your experience?" : "How was your experience?"}
              className={!validateField("customTitle", feedbackConfig.customTitle || "") ? "border-red-500" : ""}
            />
            {!validateField("customTitle", feedbackConfig.customTitle || "") && (
              <p className="text-xs text-red-500">Survey title is required</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="customMessage" className="text-sm">Survey message (optional)</Label>
            <Textarea
              id="customMessage"
              value={feedbackConfig.customMessage || ""}
              onChange={(e) => handleFieldChange("customMessage", e.target.value)}
              placeholder="Please let us know how we did with your recent support request."
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="customThankYouMessage" className="text-sm">Thank you message (optional)</Label>
            <Textarea
              id="customThankYouMessage"
              value={feedbackConfig.customThankYouMessage || ""}
              onChange={(e) => handleFieldChange("customThankYouMessage", e.target.value)}
              placeholder="Thank you for your feedback! We appreciate your input."
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <div>
              <Label htmlFor="brandingColor" className="text-sm">Branding color</Label>
              <p className="text-xs text-muted-foreground mt-0.5">
                This color will be used for buttons and active elements in your survey.
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <Input
                id="brandingColor"
                type="color"
                value={feedbackConfig.brandingColor || "#6366f1"}
                onChange={(e) => handleFieldChange("brandingColor", e.target.value)}
                className="w-12 h-10 p-1 cursor-pointer border"
              />
              <div className="flex-1">
                <Input
                  value={feedbackConfig.brandingColor || "#6366f1"}
                  onChange={(e) => handleFieldChange("brandingColor", e.target.value)}
                  placeholder="#6366f1"
                  className="font-mono text-sm"
                />
              </div>
            </div>
          </div>

          <div className="pt-6 border-t">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Include comment field</Label>
                <p className="text-sm text-muted-foreground mt-1">
                  Allow customers to provide additional comments with their rating.
                </p>
              </div>
              <Switch
                id="includeCommentField"
                checked={feedbackConfig.includeCommentField}
                onCheckedChange={(checked) => handleFieldChange("includeCommentField", checked)}
              />
            </div>

            {feedbackConfig.includeCommentField && (
              <div className="space-y-4 mt-4">
                <div className="space-y-2">
                  <Label htmlFor="commentFieldLabel" className="text-sm">Comment field label</Label>
                  <Input
                    id="commentFieldLabel"
                    value={feedbackConfig.commentFieldLabel || ""}
                    onChange={(e) => handleFieldChange("commentFieldLabel", e.target.value)}
                    placeholder="Any additional comments?"
                    className={!validateField("commentFieldLabel", feedbackConfig.commentFieldLabel || "") ? "border-red-500" : ""}
                  />
                  {!validateField("commentFieldLabel", feedbackConfig.commentFieldLabel || "") && (
                    <p className="text-xs text-red-500">Comment field label is required</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="commentFieldPlaceholder" className="text-sm">Comment field placeholder</Label>
                  <Input
                    id="commentFieldPlaceholder"
                    value={feedbackConfig.commentFieldPlaceholder || ""}
                    onChange={(e) => handleFieldChange("commentFieldPlaceholder", e.target.value)}
                    placeholder="Tell us more about your experience..."
                    className={!validateField("commentFieldPlaceholder", feedbackConfig.commentFieldPlaceholder || "") ? "border-red-500" : ""}
                  />
                  {!validateField("commentFieldPlaceholder", feedbackConfig.commentFieldPlaceholder || "") && (
                    <p className="text-xs text-red-500">Comment field placeholder is required</p>
                  )}
                </div>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="appearance" className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-base font-medium">Preview</h4>
              <p className="text-sm text-muted-foreground mt-1">
                See how your survey will appear to customers.
              </p>
            </div>
            <div className="flex items-center rounded-sm border">
              <button
                type="button"
                onClick={() => setIsSlackPreview(false)}
                className={`flex items-center gap-1.5 px-2 py-1 text-xs font-medium transition-colors ${
                  !isSlackPreview ? 'bg-muted text-foreground' : 'hover:bg-muted/50'
                }`}
              >
                <Mail className="w-3.5 h-3.5" />
                Email
              </button>
              <button
                type="button"
                onClick={() => setIsSlackPreview(true)}
                className={`flex items-center gap-1.5 px-2 py-1 text-xs font-medium transition-colors ${
                  isSlackPreview ? 'bg-muted text-foreground' : 'hover:bg-muted/50'
                }`}
              >
                <Slack className="w-3.5 h-3.5" />
                Slack
              </button>
            </div>
          </div>

          <div>
            {/* Email Preview */}
            {!isSlackPreview && (
              <div className="block" data-preview="email">
                {/* Email Container */}
                <div className="bg-background border border-border rounded-sm">
                  {/* Email Header */}
                  <div className="px-4 py-3 border-b bg-gray-50 dark:bg-gray-800">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                          S
                        </div>
                        <span className="font-medium">{orgName}</span>
                        <span className="text-muted-foreground">&lt;{csatEmail}&gt;</span>
                      </div>
                      <span className="text-sm text-muted-foreground">2 min ago</span>
                    </div>
                  </div>
                  <div className="px-4 py-3 border-b">
                    <div className="text-base font-medium text-foreground">
                      {feedbackConfig.customTitle || (feedbackConfig.feedbackType === "star" ? "How would you rate your experience?" : "How was your experience?")}
                    </div>
                  </div>

                  {/* Email Content */}
                  <div className="p-6 space-y-6">
                    {/* Minimal but Complete Ticket Details */}
                    <div className="bg-muted/50 rounded-sm p-4 space-y-2">
                      <div className="text-center">
                        <div className="font-medium text-foreground">
                          Ticket #{sampleTicketData.ticketId} - {sampleTicketData.ticketTitle}
                        </div>
                      </div>
                      <div className="flex justify-center gap-6 text-xs text-muted-foreground">
                        <span>Created: {formatDate(sampleTicketData.createdAt)}</span>
                        <span>Resolved: {formatDate(sampleTicketData.closedAt)}</span>
                      </div>
                    </div>

                    {/* Survey content */}
                    <div className="text-center space-y-3 pt-4">
                      <h3 className="text-lg font-medium text-foreground leading-tight">
                        {feedbackConfig.customTitle || (feedbackConfig.feedbackType === "star" ? "How would you rate your experience?" : "How was your experience?")}
                      </h3>
                      {feedbackConfig.customMessage && (
                        <p className="text-muted-foreground leading-relaxed">
                          {feedbackConfig.customMessage}
                        </p>
                      )}
                    </div>

                    {/* Rating section */}
                    <div className="flex justify-center pb-4">
                      {feedbackConfig.feedbackType === "star" ? (
                        <div className="flex space-x-2">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <div
                              key={star}
                              className="relative group cursor-pointer transition-transform hover:scale-110"
                            >
                              <Star
                                className="h-8 w-8 text-muted-foreground group-hover:text-yellow-400 transition-colors duration-200"
                              />
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="flex space-x-8">
                          <div className="flex flex-col items-center group cursor-pointer">
                            <div className="p-3 rounded-sm bg-muted group-hover:bg-green-50 dark:group-hover:bg-green-900/20 transition-colors duration-200">
                              <ThumbsUp className="h-5 w-5 text-muted-foreground group-hover:text-green-600 transition-colors duration-200" />
                            </div>
                            <span className="text-sm font-medium text-muted-foreground mt-2">Great</span>
                          </div>
                          <div className="flex flex-col items-center group cursor-pointer">
                            <div className="p-3 rounded-sm bg-muted group-hover:bg-red-50 dark:group-hover:bg-red-900/20 transition-colors duration-200">
                              <ThumbsUp className="h-5 w-5 text-muted-foreground group-hover:text-red-600 rotate-180 transition-colors duration-200" />
                            </div>
                            <span className="text-sm font-medium text-muted-foreground mt-2">Not great</span>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Comment field */}
                    {feedbackConfig.includeCommentField && (
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-muted-foreground">
                          {feedbackConfig.commentFieldLabel || "Any additional comments?"}
                        </Label>
                        <Textarea
                          placeholder={feedbackConfig.commentFieldPlaceholder || "Tell us more about your experience..."}
                          className="resize-none h-20 border-border rounded-sm transition-all duration-200 bg-background"
                          style={{
                            borderColor: feedbackConfig.brandingColor ? `${feedbackConfig.brandingColor}20` : undefined
                          }}
                        />
                      </div>
                    )}

                    {/* Submit button */}
                    <button
                      type="button"
                      className="w-full py-2.5 px-4 rounded-sm text-white font-medium transition-all duration-200 hover:opacity-90"
                      style={{ backgroundColor: feedbackConfig.brandingColor || "#6366f1" }}
                    >
                      Submit feedback
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Slack Preview */}
            {isSlackPreview && (
              <div className="space-y-4">
                {/* Slack Message */}
                <div className="bg-muted/30 border border-border rounded-sm p-4">
                  <div className="flex items-center gap-1.5 text-xs text-muted-foreground mb-2">
                    <Eye className="w-3.5 h-3.5 flex-shrink-0" />
                    <span>Only visible to you</span>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-9 h-9 rounded-sm flex items-center justify-center text-white p-2">
                      {orgLogoUrl ? (
                        <Image 
                          src={orgLogoUrl} 
                          alt={`${orgName} logo`}
                          width={36}
                          height={36}
                          className="w-full h-full object-contain rounded-sm"
                        />
                      ) : (
                        <div style={{ width: '20px', height: '16px' }}>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="100%"
                            height="100%"
                            viewBox="0 0 12 10"
                            fill="none"
                          >
                            <path
                              fillRule="evenodd"
                              clipRule="evenodd"
                              d="M9.64432 9.99511C9.64432 7.38623 7.80011 5.22715 5.32617 4.77734V9.99511H9.64432Z"
                              fill="currentColor"
                            />
                            <path
                              fillRule="evenodd"
                              clipRule="evenodd"
                              d="M6.7207 0.503906V4.28229H10.0493H11.0389C11.0389 2.30314 10.2292 0.503906 6.7207 0.503906Z"
                              fill="currentColor"
                            />
                            <path
                              fillRule="evenodd"
                              clipRule="evenodd"
                              d="M5.23606 4.28229V0.503906H1.95247H0.962891C0.962891 2.48306 1.77254 4.28229 5.23606 4.28229Z"
                              fill="currentColor"
                            />
                          </svg>
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-foreground">{orgName}</span>
                        <span className="bg-muted text-xs px-1.5 py-0.5 rounded text-muted-foreground">APP</span>
                        <span className="text-xs text-muted-foreground">15:50</span>
                      </div>
                      <div className="text-sm text-foreground">
                        <span className="text-blue-600 font-medium">@Requester</span> Your ticket{' '}
                        <a href="#" className="text-blue-600 hover:underline">
                          #{sampleTicketData.ticketId}
                        </a>{' '}
                        has been closed. {feedbackConfig.customTitle || (feedbackConfig.feedbackType === "star" ? "How would you rate your experience?" : "How was your experience?")}
                      </div>
                      <div className="mt-3">
                        <button className="bg-background border border-border rounded px-3 py-1.5 text-sm font-medium hover:bg-muted transition-colors">
                          Share feedback
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Slack Survey Modal/Card */}
                <div className="bg-background border border-border rounded-sm p-6 space-y-6">
                  {/* Survey content */}
                  <div className="text-center space-y-3">
                    <h3 className="text-lg font-medium text-foreground leading-tight">
                      {feedbackConfig.customTitle || (feedbackConfig.feedbackType === "star" ? "How would you rate your experience?" : "How was your experience?")}
                    </h3>
                    {feedbackConfig.customMessage && (
                      <p className="text-muted-foreground leading-relaxed">
                        {feedbackConfig.customMessage}
                      </p>
                    )}
                  </div>

                  {/* Rating section */}
                  <div className="flex justify-center pb-4">
                    {feedbackConfig.feedbackType === "star" ? (
                      <div className="flex space-x-2">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <div
                            key={star}
                            className="relative group cursor-pointer transition-transform hover:scale-110"
                          >
                            <Star
                              className="h-8 w-8 text-muted-foreground group-hover:text-yellow-400 transition-colors duration-200"
                            />
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="flex space-x-8">
                        <div className="flex flex-col items-center group cursor-pointer">
                          <div className="p-3 rounded-sm bg-muted group-hover:bg-green-50 dark:group-hover:bg-green-900/20 transition-colors duration-200">
                            <ThumbsUp className="h-5 w-5 text-muted-foreground group-hover:text-green-600 transition-colors duration-200" />
                          </div>
                          <span className="text-sm font-medium text-muted-foreground mt-2">Great</span>
                        </div>
                        <div className="flex flex-col items-center group cursor-pointer">
                          <div className="p-3 rounded-sm bg-muted group-hover:bg-red-50 dark:group-hover:bg-red-900/20 transition-colors duration-200">
                            <ThumbsUp className="h-5 w-5 text-muted-foreground group-hover:text-red-600 rotate-180 transition-colors duration-200" />
                          </div>
                          <span className="text-sm font-medium text-muted-foreground mt-2">Not great</span>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Comment field */}
                  {feedbackConfig.includeCommentField && (
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-muted-foreground">
                        {feedbackConfig.commentFieldLabel || "Any additional comments?"}
                      </Label>
                      <Textarea
                        placeholder={feedbackConfig.commentFieldPlaceholder || "Tell us more about your experience..."}
                        className="resize-none h-20 border-border rounded-sm transition-all duration-200 bg-background"
                        style={{
                          borderColor: feedbackConfig.brandingColor ? `${feedbackConfig.brandingColor}20` : undefined
                        }}
                      />
                    </div>
                  )}

                  {/* Submit button */}
                  <button
                    type="button"
                    className="w-full py-2.5 px-4 rounded-sm text-white font-medium transition-all duration-200 hover:opacity-90"
                    style={{ backgroundColor: feedbackConfig.brandingColor || "#6366f1" }}
                  >
                    Submit feedback
                  </button>
                </div>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}