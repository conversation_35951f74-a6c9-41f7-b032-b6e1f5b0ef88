"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  CSATRuleFilters,
  CsatFilterOperator,
  CustomFieldFilterDto,
  StandardFilterDto,
  createEmptyRuleFilters
} from "@/types/csat";
import { PlusIcon, Trash2 } from "lucide-react";
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { CSATFieldSelector, CSATFieldValue } from "./csat-field-selector";

interface EnhancedFilterSectionProps {
  allFilters: CSATRuleFilters;
  anyFilters: CSATRuleFilters;
  onAllFiltersChange: (filters: CSATRuleFilters) => void;
  onAnyFiltersChange: (filters: CSATRuleFilters) => void;
  teamId: string;
  onFilterAction?: () => void;
  onAllFiltersRemoved?: () => void;
}

interface FilterCondition {
  id: string;
  entity: 'ticket' | 'account' | 'contact';
  filter: 'all' | 'any';
  field: string;
  operator: string;
  value: string | number | boolean | Date | null;
  customFieldId?: string;
}

type EntityType = 'ticket' | 'account' | 'contact';
type FilterType = 'all' | 'any';

function extractCustomFieldId(fieldName: string): string | null {
  if (!fieldName) return null;
  const match = fieldName.match(/^custom_(.+)$/);
  return match ? match[1] : null;
}

function generateStableId(
  filterType: FilterType,
  entity: EntityType,
  fieldType: 'standard' | 'custom',
  fieldId: string,
  index: number
): string {
  return `${filterType}-${entity}-${fieldType}-${fieldId}-${index}`;
}

function areFiltersEqual(filters1: CSATRuleFilters, filters2: CSATRuleFilters): boolean {
  return JSON.stringify(filters1) === JSON.stringify(filters2);
}

function isFilterObjectEmpty(filters: CSATRuleFilters): boolean {
  for (const entity of ['ticket', 'account', 'contact'] as const) {
    if (!filters[entity]) continue;

    if (Array.isArray(filters[entity].standardFields) &&
      filters[entity].standardFields.length > 0) {
      return false;
    }

    if (Array.isArray(filters[entity].customFields) &&
      filters[entity].customFields.length > 0) {
      return false;
    }
  }

  return true;
}

const FilterCondition = React.memo(function FilterCondition({
  condition,
  onRemove,
  onFieldChange,
  teamId,
  onFilterAction,
  number
}: {
  condition: FilterCondition;
  onRemove: (id: string) => void;
  onFieldChange: (id: string, value: CSATFieldValue) => void;
  teamId: string;
  onFilterAction?: () => void;
  number: number;
}) {
  useEffect(() => {
    const scrollY = window.scrollY;
    return () => {
      window.scrollTo({ top: scrollY, behavior: 'auto' });
    };
  });

  const handleFieldChange = useCallback((value: CSATFieldValue) => {
    onFieldChange(condition.id, value);

    if (onFilterAction && value.field) {
      onFilterAction();
    }
  }, [condition.id, onFieldChange, onFilterAction]);

  return (
    <div className="filter-item">
      <div className="flex gap-3 items-start">
        {/* Number badge */}
        <div className="flex-shrink-0 mt-2">
          <div className="w-6 h-6 rounded-full bg-muted flex items-center justify-center text-xs font-medium text-muted-foreground">
            {number}
          </div>
        </div>
        
        <div className="flex-1">
          <CSATFieldSelector
            entity={condition.entity}
            onChange={handleFieldChange}
            initialValue={condition.field ? {
              field: condition.field,
              operator: condition.operator,
              value: condition.value,
              customFieldId: condition.customFieldId
            } : undefined}
            teamId={teamId}
            excludeCustomFields={false}
          />
        </div>

        <Button
          variant="ghost"
          size="icon"
          onClick={() => onRemove(condition.id)}
          className="h-10 w-10 text-muted-foreground hover:text-destructive flex-shrink-0"
          type="button"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
});

function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return function (...args: Parameters<T>): void {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

function CsatFilterSectionComponent({
  allFilters,
  anyFilters,
  onAllFiltersChange,
  onAnyFiltersChange,
  teamId,
  onFilterAction,
  onAllFiltersRemoved
}: EnhancedFilterSectionProps) {
  const [conditions, setConditions] = useState<FilterCondition[]>([]);

  const initializedRef = useRef(false);

  const prevFiltersRef = useRef({
    all: createEmptyRuleFilters(),
    any: createEmptyRuleFilters()
  });

  const updatingRef = useRef(false);

  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const filterAddedRef = useRef(false);

  const filtersRemovedRef = useRef(false);

  const notifyFilterAction = useCallback(() => {
    if (onFilterAction && !filterAddedRef.current) {
      onFilterAction();
      filterAddedRef.current = true;
    }
  }, [onFilterAction]);

  const notifyAllFiltersRemoved = useCallback(() => {
    if (onAllFiltersRemoved && !filtersRemovedRef.current) {
      onAllFiltersRemoved();
      filtersRemovedRef.current = true;
    }
  }, [onAllFiltersRemoved]);

  useEffect(() => {
    if (typeof CSATFieldSelector.prefetchAllEntityTypes === 'function') {
      CSATFieldSelector.prefetchAllEntityTypes();
    }

    window.history.scrollRestoration = 'manual';

    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  const convertFiltersToConditions = useCallback((
    filters: CSATRuleFilters,
    filterType: FilterType
  ): FilterCondition[] => {
    const result: FilterCondition[] = [];

    for (const entity of ['ticket', 'account', 'contact'] as const) {
      if (!filters[entity]) continue;

      if (filters[entity].standardFields) {
        filters[entity].standardFields.forEach((filter: StandardFilterDto, index) => {
          if (filter && filter.field) {
            const stableId = generateStableId(filterType, entity, 'standard', filter.field, index);

            result.push({
              id: stableId,
              entity,
              filter: filterType,
              field: filter.field,
              operator: filter.operator || '=',
              value: filter.value as string | number | boolean | Date | null
            });
          }
        });
      }

      if (filters[entity].customFields) {
        filters[entity].customFields.forEach((filter: CustomFieldFilterDto, index) => {
          if (filter && filter.field && filter.customFieldId) {
            const stableId = generateStableId(filterType, entity, 'custom', filter.customFieldId, index);

            result.push({
              id: stableId,
              entity,
              filter: filterType,
              field: filter.field,
              operator: filter.operator || '=',
              value: filter.value as string | number | boolean | Date | null,
              customFieldId: filter.customFieldId
            });
          }
        });
      }
    }

    if (result.length > 0 && !filterAddedRef.current) {
      notifyFilterAction();
    }

    return result;
  }, [notifyFilterAction]);

  const convertConditionsToFilters = useCallback((): {
    newAllFilters: CSATRuleFilters;
    newAnyFilters: CSATRuleFilters;
  } => {
    const newAllFilters = createEmptyRuleFilters();
    const newAnyFilters = createEmptyRuleFilters();

    conditions.forEach(condition => {
      if (!condition.field || !condition.operator) return;

      const { entity, field, operator, value, filter: filterType } = condition;
      if (!field.trim()) return;

      const targetFilters = filterType === 'all' ? newAllFilters : newAnyFilters;

      const isCustomField = condition.customFieldId || field.startsWith('custom_');

      if (isCustomField) {
        let customFieldId = condition.customFieldId;

        if (!customFieldId) {
          const extractedId = extractCustomFieldId(field);
          if (extractedId) {
            customFieldId = extractedId;
          } else {
            return;
          }
        }

        targetFilters[entity].customFields.push({
          field,
          customFieldId,
          operator: operator as CsatFilterOperator,
          value
        });
      } else {
        targetFilters[entity].standardFields.push({
          field,
          operator: operator as CsatFilterOperator,
          value
        });
      }
    });

    if (conditions.length === 0 ||
      (isFilterObjectEmpty(newAllFilters) && isFilterObjectEmpty(newAnyFilters))) {
      notifyAllFiltersRemoved();
    } else {
      filtersRemovedRef.current = false;
    }

    return { newAllFilters, newAnyFilters };
  }, [conditions, notifyAllFiltersRemoved]);


  const checkIfAllFiltersRemoved = useCallback(() => {
    if (conditions.length === 0) {
      if (onAllFiltersRemoved) {
        onAllFiltersRemoved();
      }
      return true;
    }

    const hasValidConditions = conditions.some(c =>
      c.field && c.operator && c.field.trim() !== ''
    );

    if (!hasValidConditions) {
      if (onAllFiltersRemoved) {
        onAllFiltersRemoved();
      }
      return true;
    }

    return false;
  }, [conditions, onAllFiltersRemoved]);

  const debouncedUpdateFilters = useCallback(
    debounce(() => {
      if (updatingRef.current) return;

      updatingRef.current = true;

      try {
        const scrollY = window.scrollY;

        const allRemoved = checkIfAllFiltersRemoved();

        const { newAllFilters, newAnyFilters } = convertConditionsToFilters();

        const hasValidConditions = conditions.some(c => c.field && c.operator);

        if (hasValidConditions && !allRemoved) {
          notifyFilterAction();
        }

        if (!areFiltersEqual(newAllFilters, prevFiltersRef.current.all)) {
          onAllFiltersChange(newAllFilters);
          prevFiltersRef.current.all = newAllFilters;
        }

        if (!areFiltersEqual(newAnyFilters, prevFiltersRef.current.any)) {
          onAnyFiltersChange(newAnyFilters);
          prevFiltersRef.current.any = newAnyFilters;
        }

        setTimeout(() => {
          window.scrollTo({ top: scrollY, behavior: 'auto' });
        }, 0);
      } finally {
        setTimeout(() => {
          updatingRef.current = false;
        }, 50);
      }
    }, 100),
    [convertConditionsToFilters, onAllFiltersChange, onAnyFiltersChange, conditions, notifyFilterAction, checkIfAllFiltersRemoved]
  );

  useEffect(() => {
    if (updatingRef.current) return;

    if (
      initializedRef.current &&
      areFiltersEqual(allFilters, prevFiltersRef.current.all) &&
      areFiltersEqual(anyFilters, prevFiltersRef.current.any)
    ) {
      return;
    }

    const scrollY = window.scrollY;

    prevFiltersRef.current = {
      all: { ...allFilters },
      any: { ...anyFilters }
    };

    try {
      const allConditions = convertFiltersToConditions(allFilters, 'all');
      const anyConditions = convertFiltersToConditions(anyFilters, 'any');

      setConditions([...allConditions, ...anyConditions]);
      initializedRef.current = true;

      if (allConditions.length === 0 && anyConditions.length === 0) {
        notifyAllFiltersRemoved();
      }

      setTimeout(() => {
        window.scrollTo({ top: scrollY, behavior: 'auto' });
      }, 0);
    } catch {
      setConditions([]);
      initializedRef.current = true;

      notifyAllFiltersRemoved();
    }
  }, [allFilters, anyFilters, convertFiltersToConditions, notifyAllFiltersRemoved]);

  useEffect(() => {
    if (!initializedRef.current) return;

    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }

    debouncedUpdateFilters();

    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, [conditions, debouncedUpdateFilters]);

  const handleAddCondition = useCallback((filterType: FilterType, entityType: EntityType): void => {
    const scrollY = window.scrollY;

    const newCondition: FilterCondition = {
      id: `${filterType}-${entityType}-new-${Date.now()}`,
      entity: entityType,
      filter: filterType,
      field: '',
      operator: '=',
      value: ''
    };

    setConditions(prev => [...prev, newCondition]);

    filtersRemovedRef.current = false;

    setTimeout(() => {
      window.scrollTo({ top: scrollY, behavior: 'auto' });
    }, 0);
  }, []);

  const handleRemoveCondition = useCallback((id: string): void => {
    const scrollY = window.scrollY;

    setConditions(prev => {
      const newConditions = prev.filter(c => c.id !== id);

      if (newConditions.length === 0 && prev.length > 0) {
        notifyAllFiltersRemoved();
      }

      return newConditions;
    });

    setTimeout(() => {
      window.scrollTo({ top: scrollY, behavior: 'auto' });
    }, 0);
  }, [notifyAllFiltersRemoved]);

  const handleFieldChange = useCallback((id: string, value: CSATFieldValue): void => {
    const scrollY = window.scrollY;

    setConditions(prev => {
      if (value.field) {
        filtersRemovedRef.current = false;
      }

      return prev.map(condition => {
        if (condition.id !== id) return condition;

        return {
          ...condition,
          field: value.field,
          operator: value.operator,
          value: value.value,
          customFieldId: value.customFieldId
        };
      });
    });

    setTimeout(() => {
      window.scrollTo({ top: scrollY, behavior: 'auto' });
    }, 0);
  }, []);

  const filterCounts = useMemo(() => {
    return {
      all: conditions.filter(c => c.filter === 'all').length,
      any: conditions.filter(c => c.filter === 'any').length
    };
  }, [conditions]);

  const organizedConditions = useMemo(() => {
    return {
      all: conditions.filter(c => c.filter === 'all'),
      any: conditions.filter(c => c.filter === 'any')
    };
  }, [conditions]);

  useEffect(() => {
    const handleBeforeUnload = (): void => {
      window.history.scrollRestoration = 'auto';
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-sm font-medium mb-2">Trigger conditions</h3>
        <p className="text-sm text-muted-foreground">
          Define when the CSAT survey should be sent by setting up filter conditions.
        </p>
      </div>

      {/* Match ALL section */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="text-sm text-muted-foreground">
              Match <span className="font-medium text-foreground">ALL</span> of the below conditions
            </div>
            {filterCounts.all > 0 && (
              <Badge variant="secondary" className="h-5 min-w-5 px-1 bg-primary/10 text-xs">
                {filterCounts.all}
              </Badge>
            )}
          </div>
        </div>

        <div className="space-y-4">
          {organizedConditions.all.map((condition, index) => (
            <FilterCondition
              key={condition.id}
              condition={condition}
              onRemove={handleRemoveCondition}
              onFieldChange={handleFieldChange}
              teamId={teamId}
              onFilterAction={onFilterAction}
              number={index + 1}
            />
          ))}
        </div>

        <div className="mt-4">
          <div className={`flex gap-2 ${organizedConditions.all.length > 0 ? 'ml-9' : ''}`}>
            <Button
              variant="outline"
              className="h-8 px-3 text-xs"
              onClick={() => handleAddCondition('all', 'ticket')}
              type="button"
            >
              <PlusIcon className="mr-1 h-3 w-3" />
              Ticket filter
            </Button>
            <Button
              variant="outline"
              className="h-8 px-3 text-xs"
              onClick={() => handleAddCondition('all', 'account')}
              type="button"
            >
              <PlusIcon className="mr-1 h-3 w-3" />
              Account filter
            </Button>
            <Button
              variant="outline"
              className="h-8 px-3 text-xs"
              onClick={() => handleAddCondition('all', 'contact')}
              type="button"
            >
              <PlusIcon className="mr-1 h-3 w-3" />
              Contact filter
            </Button>
          </div>
        </div>
      </div>

      {/* Match ANY section */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="text-sm text-muted-foreground">
              Match <span className="font-medium text-foreground">ANY</span> of the below conditions
            </div>
            {filterCounts.any > 0 && (
              <Badge variant="secondary" className="h-5 min-w-5 px-1 bg-primary/10 text-xs">
                {filterCounts.any}
              </Badge>
            )}
          </div>
        </div>

        <div className="space-y-4">
          {organizedConditions.any.map((condition, index) => (
            <FilterCondition
              key={condition.id}
              condition={condition}
              onRemove={handleRemoveCondition}
              onFieldChange={handleFieldChange}
              teamId={teamId}
              onFilterAction={onFilterAction}
              number={index + 1}
            />
          ))}
        </div>

        <div className="mt-4">
          <div className={`flex gap-2 ${organizedConditions.any.length > 0 ? 'ml-9' : ''}`}>
            <Button
              variant="outline"
              className="h-8 px-3 text-xs"
              onClick={() => handleAddCondition('any', 'ticket')}
              type="button"
            >
              <PlusIcon className="mr-1 h-3 w-3" />
              Ticket filter
            </Button>
            <Button
              variant="outline"
              className="h-8 px-3 text-xs"
              onClick={() => handleAddCondition('any', 'account')}
              type="button"
            >
              <PlusIcon className="mr-1 h-3 w-3" />
              Account filter
            </Button>
            <Button
              variant="outline"
              className="h-8 px-3 text-xs"
              onClick={() => handleAddCondition('any', 'contact')}
              type="button"
            >
              <PlusIcon className="mr-1 h-3 w-3" />
              Contact filter
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export const CsatFilterSection = React.memo(CsatFilterSectionComponent);