"use client";

import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { CSATRule } from "@/types/csat";

interface TriggerConfigSectionProps {
  triggerConfig: CSATRule["triggerConfig"];
  onTriggerConfigChange: (triggerConfig: CSATRule["triggerConfig"]) => void;
  teamId: string;
}

export function TriggerConfigSection({
  triggerConfig,
  onTriggerConfigChange,
  teamId: _teamID,
}: TriggerConfigSectionProps) {
  const handleFieldChange = (field: keyof CSATRule["triggerConfig"], value) => {
    onTriggerConfigChange({
      ...triggerConfig,
      [field]: value
    });
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-1">
          <Label className="text-sm font-medium">When to send CSAT surveys</Label>
          <p className="text-sm text-muted-foreground">
            Choose whether to send surveys for all eligible tickets or only a random sample.
          </p>
        </div>

        <RadioGroup
          value={triggerConfig.triggerType}
          onValueChange={(value) => handleFieldChange('triggerType', value)}
          className="space-y-4"
        >
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <RadioGroupItem value="random" id="random" className="mt-1" />
              <div className="space-y-1">
                <Label htmlFor="random" className="font-medium cursor-pointer">
                  Random sampling
                </Label>
                <p className="text-sm text-muted-foreground">
                  Only send CSAT surveys for a random sample of eligible tickets.
                </p>
              </div>
            </div>

            {triggerConfig.triggerType === "random" && (
              <div className="ml-7 pl-4 border-l-2 border-muted space-y-3">
                <Label className="text-sm font-medium text-muted-foreground">
                  Sample size
                </Label>
                <RadioGroup
                  value={triggerConfig.randomPercentage?.toString()}
                  onValueChange={(value) => handleFieldChange('randomPercentage', parseInt(value))}
                  className="space-y-2"
                >
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="10" id="10percent" />
                    <Label htmlFor="10percent" className="font-normal cursor-pointer">
                      1 out of 10 tickets (10%)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="20" id="20percent" />
                    <Label htmlFor="20percent" className="font-normal cursor-pointer">
                      1 out of 5 tickets (20%)
                    </Label>
                  </div>
                </RadioGroup>
              </div>
            )}
          </div>

          <div className="flex items-start space-x-3">
            <RadioGroupItem value="always" id="always" className="mt-1" />
            <div className="space-y-1">
              <Label htmlFor="always" className="font-medium cursor-pointer">
                Always send
              </Label>
              <p className="text-sm text-muted-foreground">
                Send CSAT surveys for every eligible ticket. No random sampling.
              </p>
            </div>
          </div>
        </RadioGroup>
      </div>
    </div>
  );
}