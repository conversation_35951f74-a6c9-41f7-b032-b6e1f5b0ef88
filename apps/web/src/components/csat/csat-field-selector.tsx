"use client";

import { FieldInput } from "@/components/field-input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { getAnnotatorMetadata } from "@/types/annotators";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";

interface Operator {
  name: string;
  value: string;
}

interface ConstraintOption {
  label: string;
  value: string;
}

interface AnnotatorField {
  label: string;
  type: string;
  supportedOperators: Operator[];
  constraints?: {
    options?: ConstraintOption[];
    dynamicChoices?: ConstraintOption[];
    relatedEntityType?: string;
    lookupType?: string;
  };
  fields?: Record<string, AnnotatorField>;
}

interface CustomField {
  id: string;
  name: string;
  fieldType: string;
  description: string;
  options: Array<{
    id: string;
    value: string;
    isDisabled: boolean;
    order: number;
  }>;
  placeholderText?: string;
}

export interface EntityData {
  fields: Record<string, AnnotatorField>;
  customFields: CustomField[];
}

export interface CSATFieldValue {
  field: string;
  operator: string;
  value: string | number | boolean | Date | null;
  customFieldId?: string;
}

interface CSATFieldSelectorProps {
  entity: 'ticket' | 'account' | 'contact';
  onChange: (value: CSATFieldValue) => void;
  teamId: string;
  initialValue?: CSATFieldValue;
  excludeCustomFields?: boolean;
}

const ENTITY_TYPE_MAP: Record<'ticket' | 'account' | 'contact', string> = {
  'ticket': 'Ticket',
  'account': 'Account',
  'contact': 'CustomerContact'
};

const STANDARD_FIELDS: Record<'ticket' | 'account' | 'contact', Record<string, { label: string; type: string }>> = {
  ticket: {
    'status': { label: 'Status', type: 'lookup' },
    'tags': { label: 'Tags', type: 'lookup' },
    'priority': { label: 'Priority', type: 'lookup' },
    'sentiment': { label: 'Sentiment', type: 'lookup' },
    'type': { label: 'Type', type: 'lookup' },
    'requestorEmail': { label: 'Requester email', type: 'string' },
    'submitterEmail': { label: 'Submitter email', type: 'string' },
    'title': { label: 'Title', type: 'string' },
    'aiGeneratedTitle': { label: 'AI generated title', type: 'string' },
    'description': { label: 'Description', type: 'text' },
    'aiGeneratedSummary': { label: 'AI generated summary', type: 'string' },
    'createdAt': { label: 'Created at', type: 'date' },
    'dueDate': { label: 'Due date', type: 'date' },
    'isEscalated': { label: 'Is escalated', type: 'boolean' },
    'updatedAt': { label: 'Updated at', type: 'date' },
    'isPrivate': { label: 'Private', type: 'boolean' },
    'storyPoints': { label: 'Story points', type: 'number' },
    'isDraft': { label: 'Is draft', type: 'boolean' },
    'source': { label: 'Source', type: 'choice' },
  },
  account: {
    'status': { label: 'Status', type: 'lookup' },
    'classification': { label: 'Classification', type: 'lookup' },
    'health': { label: 'Health', type: 'lookup' },
    'industry': { label: 'Industry', type: 'lookup' },
    'id': { label: 'Id', type: 'string' },
    'name': { label: 'Name', type: 'string' },
    'description': { label: 'Description', type: 'text' },
    'primaryDomain': { label: 'Primary Domain', type: 'string' },
    'secondaryDomain': { label: 'Secondary Domain', type: 'string' },
    'annualRevenue': { label: 'Annual Revenue', type: 'currency' },
    'employees': { label: 'Employees Count', type: 'number' },
    'website': { label: 'Website', type: 'string' },
    'billingAddress': { label: 'Billing Address', type: 'string' },
    'shippingAddress': { label: 'Shipping Address', type: 'string' },
    'createdAt': { label: 'Created At', type: 'date' },
    'updatedAt': { label: 'Updated At', type: 'date' },
  },
  contact: {
    'id': { label: 'Id', type: 'string' },
    'firstName': { label: 'First Name', type: 'string' },
    'lastName': { label: 'Last Name', type: 'string' },
    'email': { label: 'Email', type: 'string' },
    'phoneNumber': { label: 'Phone Number', type: 'string' },
    'isMarketingContact': { label: 'Is Marketing Contact', type: 'boolean' },
    'isActive': { label: 'Is Active', type: 'boolean' },
    'createdAt': { label: 'Created At', type: 'date' },
    'updatedAt': { label: 'Updated At', type: 'date' }
  }
};

const OPERATOR_DISPLAY_NAMES: Record<string, string> = {
  '=': 'equals',
  '!=': 'not equals',
  '>': 'greater than',
  '<': 'less than',
  '>=': 'greater than or equal',
  '<=': 'less than or equal',
  'contains': 'contains',
  'not_contains': 'does not contain',
  'starts_with': 'starts with',
  'in': 'in',
  'not_in': 'not in',
  'is_empty': 'is empty',
  'is_not_empty': 'is not empty'
};

function getOperatorDisplayName(operator: string): string {
  return OPERATOR_DISPLAY_NAMES[operator] || operator;
}

const CUSTOM_FIELD_TYPE_MAP: Record<string, string> = {
  'checkbox': 'boolean',
  'multi_choice': 'choice',
  'radio': 'choice',
  'select': 'choice',
  'rich_text': 'text',
  'integer': 'number',
  'decimal': 'number'
};

function mapCustomFieldType(fieldType: string): string {
  return CUSTOM_FIELD_TYPE_MAP[fieldType] || 'string';
}

const STRING_OPERATORS: Operator[] = [
  { name: 'equals', value: '=' },
  { name: 'not equals', value: '!=' },
  { name: 'contains', value: 'contains' },
  { name: 'starts with', value: 'starts_with' }
];

const CHOICE_OPERATORS: Operator[] = [
  { name: 'equals', value: '=' },
  { name: 'not equals', value: '!=' },
  { name: 'in', value: 'in' },
  { name: 'not in', value: 'not_in' }
];

const NUMBER_OPERATORS: Operator[] = [
  { name: 'equals', value: '=' },
  { name: 'not equals', value: '!=' },
  { name: 'greater than', value: '>' },
  { name: 'less than', value: '<' },
  { name: 'greater than or equal', value: '>=' },
  { name: 'less than or equal', value: '<=' }
];

const BOOLEAN_OPERATORS: Operator[] = [
  { name: 'equals', value: '=' },
  { name: 'not equals', value: '!=' }
];

function getCustomFieldOperators(fieldType: string): Operator[] {
  if (['multi_choice', 'radio', 'select', 'choice'].includes(fieldType)) {
    return CHOICE_OPERATORS;
  }

  if (['text', 'rich_text', 'string'].includes(fieldType)) {
    return STRING_OPERATORS;
  }

  if (['integer', 'decimal', 'number', 'currency'].includes(fieldType)) {
    return NUMBER_OPERATORS;
  }

  return BOOLEAN_OPERATORS;
}

function extractCustomFieldId(fieldName: string): string | null {
  if (!fieldName) return null;
  const match = fieldName.match(/^custom_(.+)$/);
  return match ? match[1] : null;
}

class EntityDataCache {
  private cache = new Map<string, { data: EntityData, timestamp: number }>();
  private fetchPromises = new Map<string, Promise<EntityData>>();
  private static CACHE_TTL = 5 * 60 * 1000;

  get(key: string): EntityData | null {
    const cached = this.cache.get(key);
    if (cached && (Date.now() - cached.timestamp) < EntityDataCache.CACHE_TTL) {
      return cached.data;
    }
    return null;
  }

  set(key: string, data: EntityData): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  setFetchPromise(key: string, promise: Promise<EntityData>): void {
    this.fetchPromises.set(key, promise);
  }

  getFetchPromise(key: string): Promise<EntityData> | null {
    return this.fetchPromises.get(key) || null;
  }

  clearFetchPromise(key: string): void {
    this.fetchPromises.delete(key);
  }
}

const globalEntityDataCache = new EntityDataCache();

function CSATFieldSelectorComponent({
  entity,
  onChange,
  teamId,
  initialValue,
  excludeCustomFields = false
}: CSATFieldSelectorProps) {

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [entityData, setEntityData] = useState<EntityData | null>(null);
  const [nameFieldOperators, setNameFieldOperators] = useState<Record<string, Operator[]>>({});
  const [fieldChoices, setFieldChoices] = useState<ConstraintOption[] | null>(null);
  const [selectedField, setSelectedField] = useState<string>('');
  const [selectedOperator, setSelectedOperator] = useState<string>('');
  const [selectedValue, setSelectedValue] = useState<string | number | boolean | Date | null>('');
  const [fieldType, setFieldType] = useState<string>('string');
  const [customFieldId, setCustomFieldId] = useState<string | undefined>(undefined);

  const isComponentMounted = useRef(true);
  const isChangingFieldRef = useRef(false);
  const isChangingOperatorRef = useRef(false);
  const initialValueRef = useRef(initialValue);
  const initializedRef = useRef(false);
  const selectRef = useRef<HTMLDivElement>(null);

  const cacheKey = useMemo(() => `${entity}-${teamId}`, [entity, teamId]);

  const fetchEntityData = useCallback(async (): Promise<EntityData | null> => {
    const cachedData = globalEntityDataCache.get(cacheKey);
    if (cachedData) {
      setEntityData(cachedData);
      setIsLoading(false);
      return cachedData;
    }

    const existingPromise = globalEntityDataCache.getFetchPromise(cacheKey);
    if (existingPromise) {
      try {
        const data = await existingPromise;
        if (isComponentMounted.current) {
          setEntityData(data);
          setIsLoading(false);
        }
        return data;
      } catch {
        if (isComponentMounted.current) {
          setError(`Failed to load field data. Please try again.`);
          setIsLoading(false);
        }
        return null;
      }
    }

    setIsLoading(true);
    setError(null);

    const fetchPromise = (async (): Promise<EntityData> => {
      try {
        const apiEntityType = ENTITY_TYPE_MAP[entity];
        let relations: string[] = [];

        if (entity === 'ticket') {
          relations = ['GROUP', 'FORM', 'STATUS', 'PRIORITY', 'TYPE', 'SENTIMENT', 'TAGS'];
        } else if (entity === 'account') {
          relations = ['STATUS', 'CLASSIFICATION', 'HEALTH', 'INDUSTRY'];
        } else if (entity === 'contact') {
          relations = ['CONTACT_TYPE'];
        }

        const response = await getAnnotatorMetadata(apiEntityType, relations, teamId);

        const nameOperators: Record<string, Operator[]> = {};
        const allowedFieldKeys = Object.keys(STANDARD_FIELDS[entity]);
        const filteredFields: Record<string, AnnotatorField> = {};

        if (response.data && response.data.fields) {
          Object.entries(response.data.fields).forEach(([key, fieldValue]) => {
            if (allowedFieldKeys.includes(key)) {
              const field = fieldValue as AnnotatorField;
              filteredFields[key] = field;

              if (field.type === 'lookup' && field.fields && field.fields.name &&
                Array.isArray(field.fields.name.supportedOperators) &&
                field.fields.name.supportedOperators.length > 0) {
                nameOperators[key] = field.fields.name.supportedOperators;
              }
            }
          });
        }

        allowedFieldKeys.forEach(key => {
          if (!filteredFields[key]) {
            filteredFields[key] = {
              label: STANDARD_FIELDS[entity][key].label,
              type: STANDARD_FIELDS[entity][key].type,
              supportedOperators: getCustomFieldOperators(STANDARD_FIELDS[entity][key].type)
            };
          }
        });

        const fetchedData: EntityData = {
          fields: filteredFields,
          customFields: response.data.customFields || []
        };

        if (isComponentMounted.current) {
          setEntityData(fetchedData);
          setNameFieldOperators(nameOperators);
          setIsLoading(false);
        }

        globalEntityDataCache.set(cacheKey, fetchedData);

        return fetchedData;
      } catch (error) {
        if (isComponentMounted.current) {
          setError(`Failed to load field data. Please try again.`);
          setIsLoading(false);
        }
        throw error;
      } finally {
        globalEntityDataCache.clearFetchPromise(cacheKey);
      }
    })();

    globalEntityDataCache.setFetchPromise(cacheKey, fetchPromise);

    try {
      return await fetchPromise;
    } catch {
      return null;
    }
  }, [entity, teamId, cacheKey]);

  const fetchRelatedFields = useCallback(async (fieldValue: string): Promise<void> => {
    if (!fieldValue) return;

    const _fetchKey = `${cacheKey}-${fieldValue}`;

    try {
      setFieldChoices(null);

      const apiEntityType = ENTITY_TYPE_MAP[entity];
      const response = await getAnnotatorMetadata(apiEntityType, [fieldValue], teamId);

      const possibleFields = ["name", "value", "label", "displayName"];

      if (response.data.fields &&
        response.data.fields[fieldValue] &&
        response.data.fields[fieldValue].type === 'lookup' &&
        response.data.fields[fieldValue].fields) {

        const fieldData = response.data.fields[fieldValue];
        const fieldOps: Record<string, Operator[]> = {};

        for (const field of possibleFields) {
          if (fieldData.fields[field]?.supportedOperators &&
            Array.isArray(fieldData.fields[field].supportedOperators)) {
            fieldOps[field] = fieldData.fields[field].supportedOperators;
          }
        }

        if (Object.keys(fieldOps).length > 0) {
          const primaryField = Object.keys(fieldOps)[0];

          if (isComponentMounted.current) {
            setNameFieldOperators(prev => ({
              ...prev,
              [fieldValue]: fieldOps[primaryField]
            }));
          }
        }
      }

      let extractedOptions: ConstraintOption[] = [];
      const fieldData = response.data.fields?.[fieldValue] as AnnotatorField | undefined;

      if (fieldData?.fields) {
        for (const field of possibleFields) {
          if (fieldData.fields[field]?.constraints) {
            const constraints = fieldData.fields[field].constraints;

            if (constraints.dynamicChoices?.length > 0) {
              extractedOptions = constraints.dynamicChoices;
              break;
            } else if (constraints.options?.length > 0) {
              extractedOptions = constraints.options;
              break;
            }
          }
        }

        if (extractedOptions.length === 0) {
          for (const fieldName of Object.keys(fieldData.fields)) {
            const field = fieldData.fields[fieldName];
            if (field.constraints) {
              if (field.constraints.dynamicChoices?.length > 0) {
                extractedOptions = field.constraints.dynamicChoices;
                break;
              } else if (field.constraints.options?.length > 0) {
                extractedOptions = field.constraints.options;
                break;
              }
            }
          }
        }
      }

      if (extractedOptions.length === 0 && fieldData?.constraints) {
        if (fieldData.constraints.dynamicChoices?.length > 0) {
          extractedOptions = fieldData.constraints.dynamicChoices;
        } else if (fieldData.constraints.options?.length > 0) {
          extractedOptions = fieldData.constraints.options;
        }
      }

      if (extractedOptions.length === 0) {
        extractedOptions = [
          { label: "No options available", value: "" }
        ];
      }

      if (isComponentMounted.current) {
        setFieldChoices(extractedOptions);
      }

    } catch {
      if (isComponentMounted.current) {
        setFieldChoices([
          { label: "Error loading options", value: "" }
        ]);
      }
    }
  }, [entity, teamId, cacheKey]);

  useEffect(() => {
    isComponentMounted.current = true;
    fetchEntityData();

    return () => {
      isComponentMounted.current = false;
    };
  }, [fetchEntityData]);

  useEffect(() => {
    const initialValueChanged = JSON.stringify(initialValue) !== JSON.stringify(initialValueRef.current);
    initialValueRef.current = initialValue;

    if (initializedRef.current || !entityData || !initialValue?.field) {
      return;
    }

    const initializeFromValue = async () => {
      initializedRef.current = true;

      try {
        setSelectedField(initialValue.field);
        setSelectedOperator(initialValue.operator);
        setSelectedValue(initialValue.value);

        const extractedId = extractCustomFieldId(initialValue.field);
        const cfId = initialValue.customFieldId || extractedId;
        setCustomFieldId(cfId || undefined);

        const field = entityData.fields[initialValue.field];
        if (field) {
          setFieldType(field.type);

          if (field.type === 'lookup') {
            await fetchRelatedFields(initialValue.field);
          }
          else if (field.constraints?.options && field.constraints.options.length > 0) {
            setFieldChoices(field.constraints.options);
          } else if (field.constraints?.dynamicChoices && field.constraints.dynamicChoices.length > 0) {
            setFieldChoices(field.constraints.dynamicChoices);
          } else {
            setFieldChoices(null);
          }
        }
      } finally {
        setTimeout(() => {
          if (isComponentMounted.current) {
            initializedRef.current = false;
          }
        }, 50);
      }
    };

    if ((initialValueChanged || (initialValue?.field && !selectedField)) && entityData) {
      initializeFromValue();
    }
  }, [initialValue, selectedField, entityData, fetchRelatedFields]);

  const getFieldInfo = useCallback((fieldName: string): AnnotatorField | null => {
    if (!entityData || !fieldName) return null;

    if (fieldName in entityData.fields) {
      return entityData.fields[fieldName];
    }

    if (!excludeCustomFields) {
      const customFieldId = extractCustomFieldId(fieldName);
      if (customFieldId && entityData.customFields) {
        const customField = entityData.customFields.find(cf => cf.id === customFieldId);
        if (customField) {
          const mappedType = mapCustomFieldType(customField.fieldType);
          return {
            label: customField.name,
            type: mappedType,
            supportedOperators: getCustomFieldOperators(mappedType),
            constraints: customField.options?.length ? {
              options: customField.options.map(opt => ({
                label: opt.value,
                value: opt.value
              }))
            } : undefined
          };
        }
      }
    }

    return null;
  }, [entityData, excludeCustomFields]);

  const getDefaultValueForType = useCallback((type: string): string | number | boolean | null => {
    switch (type) {
      case 'boolean':
        return false;
      case 'number':
      case 'currency':
        return 0;
      case 'date':
        return '';
      default:
        return '';
    }
  }, []);

  const handleFieldChange = useCallback((value: string): void => {
    if (initializedRef.current || isChangingFieldRef.current || !value) return;

    isChangingFieldRef.current = true;

    try {
      setFieldChoices(null);

      const extractedId = extractCustomFieldId(value);
      setCustomFieldId(extractedId || undefined);

      const field = getFieldInfo(value);
      if (!field) {
        setSelectedField(value);
        setSelectedOperator('');
        setSelectedValue('');

        onChange({
          field: value,
          operator: '',
          value: ''
        });
        return;
      }

      setFieldType(field.type);
      setSelectedField(value);

      if (field.type === 'lookup') {
        fetchRelatedFields(value);

        let operators: Operator[];
        if (nameFieldOperators[value]) {
          operators = nameFieldOperators[value];
        } else {
          operators = field.supportedOperators && field.supportedOperators.length > 0
            ? field.supportedOperators
            : getCustomFieldOperators('string');
        }

        let defaultOperator: string;
        const equalsOp = operators.find(op => op.value === '=');
        if (equalsOp) {
          defaultOperator = equalsOp.value;
        } else if (operators.length > 0) {
          defaultOperator = operators[0].value;
        } else {
          defaultOperator = 'contains';
        }

        setSelectedOperator(defaultOperator);
        setSelectedValue('');

        onChange({
          field: value,
          operator: defaultOperator,
          value: '',
          ...(extractedId && { customFieldId: extractedId })
        });
      } else {
        if (field.constraints?.options && field.constraints.options.length > 0) {
          setFieldChoices(field.constraints.options);
        } else if (field.constraints?.dynamicChoices && field.constraints.dynamicChoices.length > 0) {
          setFieldChoices(field.constraints.dynamicChoices);
        } else {
          setFieldChoices(null);
        }

        const operators = field.supportedOperators && field.supportedOperators.length > 0
          ? field.supportedOperators
          : getCustomFieldOperators(field.type);

        let defaultOperator: string;
        const equalsOp = operators.find(op => op.value === '=');
        if (equalsOp) {
          defaultOperator = equalsOp.value;
        } else if (operators.length > 0) {
          defaultOperator = operators[0].value;
        } else {
          defaultOperator = '=';
        }

        const defaultValue = getDefaultValueForType(field.type);

        setSelectedOperator(defaultOperator);
        setSelectedValue(defaultValue);

        onChange({
          field: value,
          operator: defaultOperator,
          value: defaultValue,
          ...(extractedId && { customFieldId: extractedId })
        });
      }
    } finally {
      setTimeout(() => {
        if (isComponentMounted.current) {
          isChangingFieldRef.current = false;
        }
      }, 50);
    }
  }, [getFieldInfo, onChange, fetchRelatedFields, nameFieldOperators, getDefaultValueForType]);

  const handleOperatorChange = useCallback((value: string): void => {
    if (initializedRef.current || isChangingOperatorRef.current || !value) return;

    isChangingOperatorRef.current = true;

    try {
      setSelectedOperator(value);

      const isEmptyOperator = ['is_empty', 'is_not_empty'].includes(value);

      let newValue: string | number | boolean | null = isEmptyOperator
        ? null
        : getDefaultValueForType(fieldType);

      if (['in', 'not_in'].includes(value)) {
        newValue = '';
      }

      setSelectedValue(newValue);

      const fieldValue: CSATFieldValue = {
        field: selectedField,
        operator: value,
        value: newValue,
      };

      if (customFieldId) {
        fieldValue.customFieldId = customFieldId;
      }

      onChange(fieldValue);
    } finally {
      setTimeout(() => {
        if (isComponentMounted.current) {
          isChangingOperatorRef.current = false;
        }
      }, 50);
    }
  }, [customFieldId, onChange, selectedField, fieldType, getDefaultValueForType]);

  const handleValueChange = useCallback((value: string | number | boolean | Date | null): void => {
    if (initializedRef.current) return;

    setSelectedValue(value);

    const fieldValue: CSATFieldValue = {
      field: selectedField,
      operator: selectedOperator,
      value,
    };

    if (customFieldId) {
      fieldValue.customFieldId = customFieldId;
    }

    onChange(fieldValue);
  }, [customFieldId, onChange, selectedField, selectedOperator]);

  const operatorsForField = useMemo(() => {
    if (!selectedField || !entityData) return [];

    if (entityData.fields[selectedField]?.type === 'lookup') {
      if (nameFieldOperators[selectedField]) {
        return nameFieldOperators[selectedField];
      }
    }

    const field = entityData.fields[selectedField];
    if (!field) return [];

    if (field.supportedOperators && field.supportedOperators.length > 0) {
      return field.supportedOperators;
    }

    return getCustomFieldOperators(field.type);
  }, [selectedField, entityData, nameFieldOperators]);

  const availableFields = useMemo(() => {
    if (!entityData) return [];

    const standardFields = Object.entries(entityData.fields)
      .filter(([key]) => !['deletedAt', 'customFieldValues'].includes(key))
      .map(([key, field]) => ({
        label: field.label || key,
        value: key,
        type: field.type
      }));

    let customFields: Array<{ label: string; value: string; type: string }> = [];
    if (!excludeCustomFields && entityData.customFields) {
      customFields = entityData.customFields.map(field => ({
        label: `${field.name} (Custom)`,
        value: `custom_${field.id}`,
        type: field.fieldType
      }));
    }

    return [...standardFields, ...customFields].sort((a, b) => a.label.localeCompare(b.label));
  }, [entityData, excludeCustomFields]);

  useEffect(() => {
    const scrollY = window.scrollY;

    return () => {
      window.scrollTo({
        top: scrollY,
        behavior: 'auto'
      });
    };
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-10 text-sm text-muted-foreground">
        <span className="animate-spin mr-2">⌛</span>
        Loading fields...
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-10 text-sm text-destructive">
        <span className="mr-2">⚠️</span>
        {error}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div ref={selectRef}>
        <Select
          value={selectedField}
          onValueChange={handleFieldChange}
          onOpenChange={(open) => {
            if (open) {
              window.history.scrollRestoration = 'manual';
            }
          }}
        >
          <SelectTrigger className="w-full h-10 border bg-background px-3 py-2">
            <SelectValue placeholder="Select a field" />
          </SelectTrigger>
          <SelectContent className="max-h-80 overflow-auto">
            {availableFields.length > 0 ? (
              availableFields.map(field => (
                <SelectItem key={`${entity}-${field.value}`} value={field.value}>
                  {field.label}
                </SelectItem>
              ))
            ) : (
              <SelectItem value="no-fields" disabled>
                No fields available for this entity
              </SelectItem>
            )}
          </SelectContent>
        </Select>
      </div>

      {selectedField && (
        <div>
          <Select
            value={selectedOperator}
            onValueChange={handleOperatorChange}
            onOpenChange={(open) => {
              if (open) {
                // Save scroll position
                window.history.scrollRestoration = 'manual';
              }
            }}
          >
            <SelectTrigger className="w-full h-10 border bg-background px-3 py-2">
              <SelectValue placeholder="Select an operator" />
            </SelectTrigger>
            <SelectContent>
              {operatorsForField.length > 0 ? (
                operatorsForField.map(op => (
                  <SelectItem key={`${entity}-${selectedField}-${op.value}`} value={op.value}>
                    {getOperatorDisplayName(op.value)}
                  </SelectItem>
                ))
              ) : (
                <SelectItem value="=" disabled={false}>
                  equals
                </SelectItem>
              )}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Value input */}
      {selectedField && selectedOperator && !['is_empty', 'is_not_empty'].includes(selectedOperator) && (
        <div>
          <FieldInput
            type={fieldType}
            operator={selectedOperator}
            value={selectedValue as string || ''}
            onChange={handleValueChange}
            {...(fieldChoices?.length ? { choices: fieldChoices } : {})}
            className="w-full h-10 border bg-background px-3 py-2"
          />
        </div>
      )}
    </div>
  );
}

async function prefetchAllEntityTypes(teamId?: string): Promise<void> {
  try {
    const fetchEntityData = async (entityType: 'ticket' | 'account' | 'contact'): Promise<void> => {
      const cacheKey = `${entityType}-${teamId || ''}`;

      if (globalEntityDataCache.get(cacheKey)) {
        return;
      }

      const apiEntityType = ENTITY_TYPE_MAP[entityType];
      let relations: string[] = [];

      if (entityType === 'ticket') {
        relations = ['GROUP', 'FORM', 'STATUS', 'PRIORITY', 'TYPE', 'SENTIMENT', 'TAGS'];
      } else if (entityType === 'account') {
        relations = ['STATUS', 'CLASSIFICATION', 'HEALTH', 'INDUSTRY'];
      } else if (entityType === 'contact') {
        relations = ['CONTACT_TYPE'];
      }

      try {
        const response = await getAnnotatorMetadata(apiEntityType, relations, teamId);

        const allowedFieldKeys = Object.keys(STANDARD_FIELDS[entityType]);
        const filteredFields: Record<string, AnnotatorField> = {};

        if (response.data && response.data.fields) {
          Object.entries(response.data.fields).forEach(([key, fieldValue]) => {
            if (allowedFieldKeys.includes(key)) {
              filteredFields[key] = fieldValue as AnnotatorField;
            }
          });
        }

        allowedFieldKeys.forEach(key => {
          if (!filteredFields[key]) {
            filteredFields[key] = {
              label: STANDARD_FIELDS[entityType][key].label,
              type: STANDARD_FIELDS[entityType][key].type,
              supportedOperators: getCustomFieldOperators(STANDARD_FIELDS[entityType][key].type)
            };
          }
        });

        const fetchedData: EntityData = {
          fields: filteredFields,
          customFields: response.data.customFields || []
        };

        globalEntityDataCache.set(cacheKey, fetchedData);
      } catch (error) {
        console.error(`[prefetchAllEntityTypes] Error fetching ${entityType} metadata:`, error);
      }
    };

    // Prefetch all entity types
    await Promise.all([
      fetchEntityData('ticket'),
      fetchEntityData('account'),
      fetchEntityData('contact')
    ]);
  } catch (error) {
    console.error('[prefetchAllEntityTypes] Error:', error);
  }
}

const CSATFieldSelectorMemo = React.memo(CSATFieldSelectorComponent);

export const CSATFieldSelector = Object.assign(CSATFieldSelectorMemo, {
  prefetchAllEntityTypes
});