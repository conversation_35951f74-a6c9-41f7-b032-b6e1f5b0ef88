"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ReactNode } from "react";

interface EmptyStateProps {
  message: string;
  actionButton?: {
    label: string;
    icon?: ReactNode;
    onClick: () => void;
    disabled?: boolean;
  };
  minHeight?: string;
}

export function EmptyState({
  message,
  actionButton,
  minHeight = "120px",
}: EmptyStateProps) {
  return (
    <div
      className="flex flex-col items-center justify-center rounded-sm border border-dashed"
      style={{ minHeight }}
    >
      <div className="mx-auto flex max-w-[420px] flex-col items-center justify-center text-center">
        <p className="text-sm text-muted-foreground">{message}</p>
        {actionButton && (
          <Button
            variant="outline"
            className="mt-4"
            onClick={actionButton.onClick}
            disabled={actionButton.disabled}
          >
            {actionButton.icon && <span className="mr-2">{actionButton.icon}</span>}
            {actionButton.label}
          </Button>
        )}
      </div>
    </div>
  );
}
