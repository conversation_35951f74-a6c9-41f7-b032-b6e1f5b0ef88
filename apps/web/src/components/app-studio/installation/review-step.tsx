"use client";

import TooltipWrapper from "@/components/tooltip-wrapper";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useInstallationFormStore } from "@/store/installation-form-store";
import { AppResponseDto } from "@/types/app-studio";
import { ArrowRight, ChevronLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface ReviewStepProps {
  app: AppResponseDto;
}

export function ReviewStep({ app }: ReviewStepProps) {
  const router = useRouter();
  const { clearForm } = useInstallationFormStore();
  const [optionalScopes, setOptionalScopes] = useState<Record<string, boolean>>(
    {},
  );
  const [openAccordion, setOpenAccordion] = useState<string | undefined>(
    undefined,
  );

  const handleAccordionChange = (value: string | undefined) => {
    if (value === openAccordion) {
      setOpenAccordion(undefined);
    } else {
      setOpenAccordion(value);
    }
  };
  const handleCancel = () => {
    clearForm();
    router.push("/organization/settings/apps-studio");
  };

  const handleNext = () => {
    router.push(
      `/organization/settings/apps-studio/${app.uid}/install?step=settings`,
    );
  };

  return (
    <div className="space-y-8">
      <div>
        {/* Header space */}
        <div className="h-6" />
        {/* Header */}
        <div className="flex items-center gap-4">
          <div className="flex h-12 w-12 items-center justify-center rounded-sm bg-background overflow-hidden">
            <img
              src={app.manifest.app.icons.small}
              alt={app.manifest.app.name}
              crossOrigin="anonymous"
              className="h-full w-full object-cover"
            />
          </div>
          <div>
            <h1 className="text-2xl font-bold">{app.manifest.app.name}</h1>
            <p className="text-muted-foreground">
              {app.manifest.metadata.title}
            </p>
          </div>
        </div>

        {/* Installation Steps */}
        <div className="flex items-center justify-center gap-4 text-sm mt-8">
          <div className="flex items-center gap-2">
            <Badge variant="default">1</Badge>
            <span className="font-medium">Review scopes</span>
          </div>
          <div className="h-px w-8 bg-border" />
          <div className="flex items-center gap-2">
            <Badge variant="secondary">2</Badge>
            <span className="text-muted-foreground">Configure settings</span>
          </div>
          <div className="h-px w-8 bg-border" />
          <div className="flex items-center gap-2">
            <Badge variant="secondary">3</Badge>
            <span className="text-muted-foreground">Complete</span>
          </div>
        </div>
      </div>

      <div>
        <Accordion
          type="single"
          value={openAccordion}
          onValueChange={handleAccordionChange}
          collapsible
          className="space-y-4"
        >
          {/* Scopes */}
          <AccordionItem value="scopes" className="border rounded-sm">
            <AccordionTrigger className="px-4 py-3 hover:no-underline">
              <div>
                <h3 className="text-base font-medium text-left h-6">
                  Scopes
                </h3>
                <p className="text-sm text-muted-foreground text-left">
                  Permissions required for the integration
                </p>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-4 py-3 pb-4">
              <div className="space-y-6">
                {/* Required Platform Scopes */}
                <div>
                  <TooltipWrapper tooltipContent="These permissions are required for the integration to function properly.">
                    <h4 className="font-medium mb-2">
                      Required platform scopes
                    </h4>
                  </TooltipWrapper>
                  <div className="space-y-2">
                    {app.manifest?.scopes.required.platform.map((scope) => (
                      <div key={scope.scope} className="py-1">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Checkbox
                              checked
                              disabled
                              className="shadow-none"
                            />
                            <TooltipWrapper tooltipContent={scope.description}>
                              <span className="text-sm font-medium">
                                {scope.scope}
                              </span>
                            </TooltipWrapper>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Optional Scopes */}
                {app.manifest?.scopes.optional.platform.length > 0 && (
                  <div>
                    <TooltipWrapper tooltipContent="Additional permissions that enhance the integration's functionality.">
                      <h4 className="font-medium mb-2">
                        Optional platform scopes
                      </h4>
                    </TooltipWrapper>
                    <div className="space-y-2">
                      {app.manifest.scopes.optional.platform.map((scope) => (
                        <div key={scope.scope} className="py-1">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Checkbox
                                checked={optionalScopes[scope.scope] || false}
                                onCheckedChange={(checked) =>
                                  setOptionalScopes((prev) => ({
                                    ...prev,
                                    [scope.scope]: checked === true,
                                  }))
                                }
                                className="shadow-none"
                              />
                              <TooltipWrapper
                                tooltipContent={scope.description}
                              >
                                <span className="text-sm font-medium">
                                  {scope.scope}
                                </span>
                              </TooltipWrapper>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </AccordionContent>
          </AccordionItem>
          {/* Events */}
          <AccordionItem value="events" className="border rounded-sm">
            <AccordionTrigger className="px-4 py-3 hover:no-underline">
              <div>
                <h3 className="text-base font-medium text-left h-6">
                  Events
                </h3>
                <p className="text-sm text-muted-foreground text-left">
                  Events that this app can subscribe to or publish
                </p>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-4 py-3 pb-4">
              <div className="space-y-6">
                {/* Subscribe Events */}
                {app.manifest?.events.subscribe.length > 0 && (
                  <div>
                    <TooltipWrapper tooltipContent="Events that this app listens to">
                      <h4 className="font-medium mb-2">Subscribe events</h4>
                    </TooltipWrapper>
                    <div className="space-y-2">
                      {app.manifest.events.subscribe.map((event) => (
                        <div key={event.event} className="py-1">
                          <div className="flex items-center gap-2">
                            <Checkbox
                              checked
                              disabled
                              className="shadow-none"
                            />
                            <TooltipWrapper tooltipContent={event.description}>
                              <span className="text-sm font-medium">
                                {event.event}
                              </span>
                            </TooltipWrapper>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Publish Events */}
                {app.manifest?.events.publish.length > 0 && (
                  <div>
                    <TooltipWrapper tooltipContent="Events that this app can trigger">
                      <h4 className="font-medium mb-2">Publish events</h4>
                    </TooltipWrapper>
                    <div className="space-y-2">
                      {app.manifest.events.publish.map((event) => (
                        <div key={event.event} className="py-1">
                          <div className="flex items-center gap-2">
                            <Checkbox
                              checked
                              disabled
                              className="shadow-none"
                            />
                            <TooltipWrapper tooltipContent={event.description}>
                              <span className="text-sm font-medium">
                                {event.event}
                              </span>
                            </TooltipWrapper>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </AccordionContent>
          </AccordionItem>
          {/* Activities */}
          <AccordionItem value="activities" className="border rounded-sm">
            <AccordionTrigger className="px-4 py-3 hover:no-underline">
              <div>
                <h3 className="text-base font-medium text-left h-6">
                  Activities
                </h3>
                <p className="text-sm text-muted-foreground text-left">
                  Actions that this app can perform
                </p>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-4 py-3 pb-4">
              <div className="space-y-2">
                {app.manifest?.activities.map((activity) => (
                  <div key={activity.name} className="py-1">
                    <div className="flex items-center gap-2">
                      <Checkbox checked disabled className="shadow-none" />
                      <TooltipWrapper tooltipContent={activity.description}>
                        <span className="text-sm font-medium">
                          {activity.name}
                        </span>
                      </TooltipWrapper>
                    </div>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      <div className="bg-background pb-6">
        <div className="flex justify-between gap-3">
          <Button
            onClick={handleCancel}
            variant="outline"
            size="lg"
            className="px-3 group"
          >
            <ChevronLeft className="mr-1 h-4 w-4 transition-transform duration-200 group-hover:-translate-x-1" />
            Go back
          </Button>
          <Button onClick={handleNext} size="lg" className="px-3 group">
            Next step
            <ArrowRight className="ml-1 h-4 w-4 transition-transform duration-200 group-hover:translate-x-1" />
          </Button>
        </div>
      </div>
    </div>
  );
}
