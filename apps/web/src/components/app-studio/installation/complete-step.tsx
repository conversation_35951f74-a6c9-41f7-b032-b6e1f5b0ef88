"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useGlobalConfigPersistStore } from "@/store/globalConfigPersistStore";
import { useInstallationFormStore } from "@/store/installation-form-store";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { CheckCircle2, Loader2, XCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { InstallationSteps } from "./installation-steps";

export function CompleteStep() {
  const router = useRouter();
  const { clearForm, settings, app, setApp } = useInstallationFormStore();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isComplete, setIsComplete] = useState(false);
  const [selectedTeams, setSelectedTeams] = useState<string[]>([]);
  const [isOrgOnly, setIsOrgOnly] = useState(false);

  // Get teams from store
  const teams = useTicketMetaStore((state) => state.teams) || [];

  // Get app name from form state
  const appName = app?.name || "App";

  const handleInstallation = async () => {
    // Fetch fresh app data
    const appResponse = await fetch(
      `/api/app-studio/available-apps/${app?.uid}`,
    );
    const appData = await appResponse.json();

    setIsLoading(true);
    setError(null);

    try {
      if (!isOrgOnly && selectedTeams.length === 0) {
        throw new Error(
          "Please select at least one team or choose org-only installation",
        );
      }

      // Use the fresh app data for configuration
      if (!appData?.manifest?.configuration) {
        throw new Error("App configuration not found. Please try again.");
      }

      const { required_settings = [], optional_settings = [] } =
        appData.manifest.configuration;

      // Validate all required settings are present
      const missingRequiredSettings = required_settings
        .filter((setting) => {
          const value = settings?.[setting.key];
          return value === undefined || value === "";
        })
        .map((setting) => setting.label || setting.key);

      if (missingRequiredSettings.length > 0) {
        throw new Error(
          `Missing required settings: ${missingRequiredSettings.join(", ")}`,
        );
      }

      // Prepare settings arrays
      const requiredSettings = required_settings.map((setting) => ({
        [setting.key]: settings?.[setting.key],
      }));

      const optionalSettings = optional_settings
        .filter((setting) => {
          const value = settings?.[setting.key];
          return value !== undefined && value !== "";
        })
        .map((setting) => ({
          [setting.key]: settings?.[setting.key],
        }));

      const response = await fetch("/api/app-studio/install", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          appId: app?.uid,
          teamIds: isOrgOnly ? [] : selectedTeams,
          appConfiguration: {
            required_settings: requiredSettings,
            optional_settings: optionalSettings,
          },
        }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || "Failed to install app");
      }

      setIsComplete(true);
      // Update app in store with manifest data
      if (app) {
        setApp({
          ...app,
          appManifest: appData.manifest,
        });
      }
      toast.success(`${appName} installed successfully`);
    } catch (err) {
      const message =
        err instanceof Error ? err.message : "Failed to install app";
      setError(message);
      toast.error(message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleReturn = () => {
    clearForm();
    router.push("/organization/settings/apps-studio");
  };

  const renderAppHeader = () => (
    <div>
      {/* Header space */}
      <div className="h-6" />
      {/* Header */}
      <div className="flex items-center gap-4">
        <div className="flex h-12 w-12 items-center justify-center rounded-sm bg-background overflow-hidden">
          {app?.appManifest?.app?.icons?.small ? (
            <img
              src={app.appManifest.app.icons.small}
              alt={appName}
              crossOrigin="anonymous"
              className="h-full w-full object-cover"
            />
          ) : (
            <span className="text-xs text-muted-foreground">No icon</span>
          )}
        </div>
        <div>
          <h1 className="text-2xl font-bold">{appName}</h1>
          <p className="text-muted-foreground">
            {app?.appManifest?.metadata?.title || ""}
          </p>
        </div>
      </div>

      {/* Installation Steps */}
      <InstallationSteps currentStep="complete" />
    </div>
  );

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-8">
        {renderAppHeader()}
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="rounded-full bg-primary/10 p-3">
            <Loader2 className="h-12 w-12 text-primary animate-spin" />
          </div>
          <h2 className="text-2xl font-medium">Installing {appName}</h2>
          <p className="text-muted-foreground">
            Please wait while we install the app in your workspace
          </p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-8">
        {renderAppHeader()}
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="rounded-full bg-destructive/10 p-3">
            <XCircle className="h-12 w-12 text-destructive" />
          </div>
          <h2 className="text-2xl font-medium">Installation Failed</h2>
          <p className="text-muted-foreground">{error}</p>
          <Button size="lg" onClick={handleReturn} className="mt-4">
            Return to Apps Studio
          </Button>
        </div>
      </div>
    );
  }

  // Show initial state with team selection and install button
  if (!isComplete) {
    return (
      <div className="space-y-8">
        {renderAppHeader()}
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <h2 className="text-2xl font-medium">Complete installation</h2>
          <p className="text-muted-foreground">
            Select the teams where you want to install {appName}, or install for
            the entire organization.
          </p>

          <div className="w-full max-w-xs space-y-4">
            <div className="space-y-4">
              {!isOrgOnly && selectedTeams.length < teams.length ? (
                <Select
                  onValueChange={(value) => {
                    if (value === "org-only") {
                      setIsOrgOnly(true);
                      setSelectedTeams([]);
                    } else if (!selectedTeams.includes(value)) {
                      setSelectedTeams([...selectedTeams, value]);
                    }
                  }}
                >
                  <SelectTrigger className="text-muted-foreground">
                    <SelectValue placeholder="Select team / org" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="org-only">Entire organization</SelectItem>
                    {teams?.map(
                      (team) =>
                        !selectedTeams.includes(team.uid) && (
                          <SelectItem key={team.uid} value={team.uid}>
                            {team.name}
                          </SelectItem>
                        ),
                    )}
                  </SelectContent>
                </Select>
              ) : !isOrgOnly ? (
                <Button
                  variant="outline"
                  className="w-full text-muted-foreground"
                  disabled
                >
                  All teams selected
                </Button>
              ) : null}

              {isOrgOnly ? (
                <div className="flex flex-wrap gap-2">
                  <Badge
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    Entire organization
                    <button
                      onClick={() => setIsOrgOnly(false)}
                      className="ml-1 rounded-full hover:bg-muted/50 p-1"
                    >
                      ×
                    </button>
                  </Badge>
                </div>
              ) : (
                <div className="flex flex-wrap gap-2">
                  {selectedTeams.map((teamId) => {
                    const team = teams.find((t) => t.uid === teamId);
                    return (
                      team && (
                        <Badge
                          key={team.uid}
                          variant="secondary"
                          className="flex items-center gap-1"
                        >
                          {team.name}
                          <button
                            onClick={() =>
                              setSelectedTeams(
                                selectedTeams.filter((id) => id !== team.uid),
                              )
                            }
                            className="ml-1 rounded-full hover:bg-muted/50 p-1"
                          >
                            ×
                          </button>
                        </Badge>
                      )
                    );
                  })}
                </div>
              )}
            </div>

            <Button
              size="lg"
              onClick={handleInstallation}
              className="w-full"
              disabled={!isOrgOnly && selectedTeams.length === 0}
            >
              Install
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Show success state
  return (
    <div className="space-y-8">
      {renderAppHeader()}
      <div className="flex flex-col items-center justify-center space-y-4 text-center">
        <div className="rounded-full bg-primary/5 p-3">
          <CheckCircle2 className="h-8 w-8 text-green-600" />
        </div>
        <h2 className="text-2xl font-medium">Installation complete</h2>
        <p className="text-muted-foreground">
          {appName} has been successfully installed in your workspace
          {app?.appManifest?.integration?.entry_points?.oauth_redirect && (
            <>
              <br />
              To complete the {appName} integration, you need to authorize
              access to your {appName} account.
            </>
          )}
        </p>

        {app?.appManifest?.integration?.entry_points?.oauth_redirect ? (
          <Button
            size="lg"
            onClick={() => {
              try {
                // Get the organization ID from the global store
                const currentOrgId =
                  useGlobalConfigPersistStore.getState().currentOrgId;
                // Find the current organization to get its UID
                const currentOrg = useGlobalConfigPersistStore
                  .getState()
                  .orgs.find((org) => org.id === currentOrgId);
                const orgUid = currentOrg?.orgId;

                // Get the current user's email
                const currentUser =
                  useGlobalConfigPersistStore.getState().currentUser;
                const userEmail = currentUser?.email;

                // Get the base OAuth URL (no liquid tag replacement)
                let redirectUrl =
                  app.appManifest.integration.entry_points.oauth_redirect;

                // Add query parameters using string concatenation
                const separator = redirectUrl.includes("?") ? "&" : "?";

                // Always add orgID and email as query parameters
                if (orgUid) {
                  redirectUrl += `${separator}orgId=${encodeURIComponent(
                    orgUid,
                  )}`;
                }
                if (userEmail) {
                  const nextSeparator = redirectUrl.includes("?") ? "&" : "?";
                  redirectUrl += `${nextSeparator}userEmail=${encodeURIComponent(
                    userEmail,
                  )}`;
                }

                // Redirect to the OAuth URL
                window.location.href = redirectUrl;
              } catch (error) {
                console.error("Error redirecting to OAuth URL:", error);
              }
            }}
            className="mt-4"
          >
            Complete {appName} authorization
          </Button>
        ) : (
          <Button size="lg" onClick={handleReturn} className="mt-4">
            Return to Apps Studio
          </Button>
        )}
      </div>
    </div>
  );
}
