import { AccordionTrigger } from "./accordion";

interface SimpleAccordionTriggerProps {
  title: string;
  subtext?: string;
  icon?: React.ReactNode;
  value: string;
  activeAccordion: string | undefined;
}

export const SimpleAccordionTrigger = ({
  title,
  subtext,
  icon
}: SimpleAccordionTriggerProps) => {
  return (
    <AccordionTrigger className="py-4 hover:no-underline">
      <div className="flex items-start gap-3">
        {icon && <div className="mt-1">{icon}</div>}
        <div className="text-left">
          <h3 className="text-base">{title}</h3>
          {subtext && (
            <p className="text-sm text-muted-foreground">{subtext}</p>
          )}
        </div>
      </div>
    </AccordionTrigger>
  );
}; 