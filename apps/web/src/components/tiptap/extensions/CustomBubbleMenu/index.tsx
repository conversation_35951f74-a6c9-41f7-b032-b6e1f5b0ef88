"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Editor } from "@tiptap/react";
import { memo } from "react";

import { LucideIcon } from "@/components/lucide-icon";
import { Language } from "@tiptap-pro/extension-ai";
import { AIDropdown } from "./components/AIDropdown";
import BubbleMenuButton from "./components/BubbleMenuButton";
import { ContentTypePicker } from "./components/ContentTypePicker";
import EditLinkPopover from "./components/EditLinkPopover";
import { useTextmenuAlignTypes } from "./hooks/useTextmenuAlignTypes";
import { useTextmenuCommands } from "./hooks/useTextmenuCommands";
import { useTextmenuContentTypes } from "./hooks/useTextmenuContentTypes";
import { useTextmenuStates } from "./hooks/useTextmenuStates";

const Divider = () => {
  return <div className="w-[1px] h-6 bg-color-strong" />;
};

type Props = {
  editor: Editor;
  onComment?: () => void;
  enableAI?: boolean;
  isNotFromSlack?: boolean;
};

const MemoContentTypePicker = memo(ContentTypePicker);
const MemoButton = memo(BubbleMenuButton);

const CustomBubbleMenu = ({
  editor,
  onComment,
  enableAI,
  isNotFromSlack = false,
}: Props) => {
  const commands = useTextmenuCommands(editor);
  const states = useTextmenuStates(editor);
  const blockOptions = useTextmenuContentTypes(editor);
  const alignOptions = useTextmenuAlignTypes(editor);

  if (!editor) return null;

  const toExclude = ["heading2", "heading3", "paragraph"];

  const contentOptions = isNotFromSlack
    ? blockOptions.filter((option) => !toExclude.includes(option.id))
    : blockOptions;

  return (
    <BubbleMenu
      tippyOptions={{
        popperOptions: {
          placement: "top-start",
          modifiers: [
            {
              name: "preventOverflow",
              options: {
                boundary: "viewport",
                padding: 8,
              },
            },
            {
              name: "flip",
              options: {
                fallbackPlacements: ["bottom-start", "top-end", "bottom-end"],
              },
            },
          ],
        },
        maxWidth: "calc(100vw - 16px)",
        appendTo: () => document.body,
        onMount: (instance) => {
          instance.popper.style.position = "fixed";
        },
        zIndex: 9999
      }}
      editor={editor}
      pluginKey="textMenu"
      shouldShow={states.shouldShow}
      updateDelay={100}
    >
      <div className="inline-flex h-full leading-none gap-0.5 bg-background rounded-sm shadow-lg border py-1 px-2 items-center">
        {enableAI && (
          <AIDropdown
            onFixSpelling={commands.onFixSpelling}
            onSummarize={commands.onSummarize}
            onTone={commands.onTone}
            onAutoComplete={commands.onAutoComplete}
            onTldr={commands.onTldr}
            onLanguageChange={(lang) =>
              commands.onLanguageChange(lang as Language)
            }
          />
        )}

        {onComment && (
          <>
            <Divider />
            <MemoButton tooltip="" onClick={onComment}>
              <LucideIcon name="MessageSquareText" size={12} className="mr-1" />
              Comment
            </MemoButton>
          </>
        )}

        <Divider />

        <MemoContentTypePicker options={contentOptions} defaultIcon="Text" />

        <MemoButton
          tooltip="Bold"
          tooltipShortcut={["Mod", "B"]}
          onClick={commands.onBold}
          active={states.isBold}
        >
          <LucideIcon name="Bold" />
        </MemoButton>
        <MemoButton
          tooltip="Italic"
          tooltipShortcut={["Mod", "I"]}
          onClick={commands.onItalic}
          active={states.isItalic}
        >
          <LucideIcon name="Italic" />
        </MemoButton>

        <MemoButton
          tooltip="Strikethrough"
          tooltipShortcut={["Mod", "Shift", "S"]}
          onClick={commands.onStrike}
          active={states.isStrike}
        >
          <LucideIcon name="Strikethrough" />
        </MemoButton>

        <EditLinkPopover onSetLink={commands.onLink} />

        {/* <Popover.Root>
          <Popover.Trigger asChild>
            <MemoButton
              active={!!states.currentHighlight}
              tooltip="Highlight text"
            >
              <LucideIcon name="Highlighter" />
            </MemoButton>
          </Popover.Trigger>
          <Popover.Content side="top" sideOffset={8} asChild>
            <Surface className="p-1">
              <MemoColorPicker
                color={states.currentHighlight}
                onChange={commands.onChangeHighlight}
                onClear={commands.onClearHighlight}
              />
            </Surface>
          </Popover.Content>
        </Popover.Root> */}
        {/* <Popover.Root>
          <Popover.Trigger asChild>
            <MemoButton active={!!states.currentColor} tooltip="Text color">
              <LucideIcon name="Palette" />
            </MemoButton>
          </Popover.Trigger>
          <Popover.Content side="top" sideOffset={8} asChild>
            <Surface className="p-1">
              <MemoColorPicker
                color={states.currentColor}
                onChange={commands.onChangeColor}
                onClear={commands.onClearColor}
              />
            </Surface>
          </Popover.Content>
        </Popover.Root> */}

        {!isNotFromSlack && (
          <ContentTypePicker
            options={alignOptions}
            defaultIcon="AlignLeft"
            className="!flex-row"
            showActive
          />
        )}
      </div>
    </BubbleMenu>
  );
};

export default CustomBubbleMenu;
