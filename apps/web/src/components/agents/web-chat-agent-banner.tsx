"use client";

import { ArrowRight, SparklesIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import { Button } from "../ui/button";

const AgentInfoWrapper = styled.div`
  padding: 16px;
`;
export const WebChatAgentBanner = ({ AGENT_NAME, agentDetails }) => {
  const router = useRouter();
  return (
    <AgentInfoWrapper className="flex items-start justify-between gap-2 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-950/30 dark:to-blue-950/30 border border-purple-200 dark:border-purple-800/50 rounded-sm">
      <SparklesIcon className="w-6 h-6 text-purple-500 dark:text-purple-400 mt-0.5" />
      <p className="text-sm text-gray-700 dark:text-gray-200 font-normal flex-1">
        Web chat is managed by {AGENT_NAME}, your AI agent. Customize{" "}
        {AGENT_NAME}&apos;s knowledge in Agent settings.
      </p>
      <Button
        size="sm"
        variant="ghost"
        className="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 hover:bg-purple-100/50 dark:hover:bg-purple-900/20 group"
        onClick={() => {
          router.push(
            `/organization/settings/agent-studio/agents?agentId=${agentDetails?.id}`,
          );
        }}
      >
        Explore agent <ArrowRight className="ml-1 transition-transform duration-200 group-hover:translate-x-1" />
      </Button>
    </AgentInfoWrapper>
  );
};
