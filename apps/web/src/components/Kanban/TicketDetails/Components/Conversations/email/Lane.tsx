import { truncate } from "lodash";
import { X } from "lucide-react";

import TooltipWrapper from "@/components/tooltip-wrapper";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

type Props = {
  emails: string[];
  setEmails: React.Dispatch<React.SetStateAction<string[]>>;
  defaultCC?: {
    Name: string;
    Value: string;
  };
};

export const Email = ({
  email,
  setEmails,
  defaultCC,
}: {
  email: string;
  setEmails: React.Dispatch<React.SetStateAction<string[]>>;
  defaultCC?: {
    Name: string;
    Value: string;
  };
}) => {
  return (
    <TooltipWrapper tooltipContent={email}>
      <div className="text-[var(--color-text)] text-xs border px-2 py-1 rounded-md flex items-center">
        <span>{truncate(email, { length: 20 })}</span>
        {email !== defaultCC?.Value && (
          <X
            size={12}
            className="text-[var(--color-icon-muted)] font-bold ml-1 cursor-pointer"
            onClick={() => {
              setEmails((prev) => prev.filter((item) => item !== email));
            }}
          />
        )}
      </div>
    </TooltipWrapper>
  );
};

const renderChips = ({
  emails,
  setEmails,
  defaultCC,
}: {
  emails: string[];
  setEmails: React.Dispatch<React.SetStateAction<string[]>>;
  defaultCC?: {
    Name: string;
    Value: string;
  };
}) => {
  return (
    <div className="flex gap-1">
      {emails
        .filter(Boolean)
        .slice(0, 2)
        .map((item, id) => {
          // if (item === defaultCC?.Value) return null;
          return (
            <Email
              email={item}
              key={item + id}
              setEmails={setEmails}
              defaultCC={defaultCC}
            />
          );
        })}
    </div>
  );
};

const renderSurplusChips = ({
  emails,
  setEmails,
}: {
  emails: string[];
  setEmails: React.Dispatch<React.SetStateAction<string[]>>;
}) => {
  const count = emails.length - 2;
  if (count < 1) return null;

  const content = () => {
    return emails
      .filter(Boolean)
      .slice(2)
      .map((item) => {
        return <Email email={item} key={item} setEmails={setEmails} />;
      });
  };

  return (
    <div className="text-[var(--color-text)] text-xs border p-0.5 px-1 rounded-md flex items-center ml-1">
      <Popover>
        <PopoverTrigger className="">{` +${count} `}</PopoverTrigger>
        <PopoverContent className="max-w-[170px] px-2 py-1">
          <div className="space-y-1">{content()}</div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

const Lane = ({ emails, setEmails, defaultCC }: Props) => {
  return (
    <>
      {renderChips({ emails, setEmails, defaultCC })}
      {renderSurplusChips({ emails, setEmails })}
    </>
  );
};

export { Lane };
