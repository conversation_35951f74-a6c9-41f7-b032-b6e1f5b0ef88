import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useInternalUsersStore } from "@/store/internal-users-store";
import { useTicketMetaStore } from "@/store/ticket-meta-store";
import { isEmpty } from "@/utils/kanban";
import { debounce } from "lodash";
import { Loader2 } from "lucide-react";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { ThreadType } from "../../../../../../constants/thread";
import { useAppsSourcesStore } from "../../../../../../store/apps-sources-store";
import { useGlobalConfigPersistStore } from "../../../../../../store/globalConfigPersistStore";
import { useTicketDrawerStore } from "../../../../../../store/ticketDrawerStore";
import type { ThenaTicket } from "../../../../../../types/kanban";
import { fetchInternalUsers } from "../../../../../../utils/internal-user";
import { getSourceIcon } from "../../TicketDetailsLeftside";
import "../style.css";

const getFirstLetter = (name = ""): string => {
  return name.charAt(0).toUpperCase();
};
const formatName = (name = ""): string => {
  const capitalizedName = name.charAt(0).toUpperCase() + name.slice(1);
  return capitalizedName;
};

export const MentionList = forwardRef(
  (
    props: {
      threadType: ThreadType;
      query: string;
      command: (command: { label: string; id: string; email: string }) => void;
      items: { label: string; id: string; email: string }[];
    },
    ref,
  ) => {
    const [selectedIndex, setSelectedIndex] = useState(0);
    const teamMembers = useTicketMetaStore((state) => state.teamMembers);
    const internalUsers = useInternalUsersStore((state) => state.internalUsers);
    const [customerContacts, setCustomerContacts] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const ticketId = useTicketDrawerStore((state) => state.ticketId);
    const sources = useAppsSourcesStore((state) => state.sources);
    const threadType = props.threadType || ThreadType.PUBLIC;
    const listRef = useRef<HTMLDivElement>(null);
    const selectedItemRef = useRef<HTMLDivElement>(null);

    const tickets = useTicketMetaStore((state) => state.tickets);
    const { currentOrgId, orgs } = useGlobalConfigPersistStore(
      (state) => state,
    );
    const ticket: ThenaTicket = tickets.find(
      (ticket) => ticket.uid === ticketId,
    );

    const accountId = ticket?.account?.id || 0;

    const handleFetchInternalUsers = useCallback(async () => {
      setIsLoading(true);

      try {
        const fetchedUsers = await fetchInternalUsers(
          sources,
          orgs.find((org) => org.id === currentOrgId)?.orgId,
        );

        useInternalUsersStore.getState().setInternalUsers(fetchedUsers);
      } catch (error) {
        console.error("Error fetching internal users:", error);
      } finally {
        setIsLoading(false);
      }
    }, [sources, currentOrgId]);

    const fetchCustomerContacts = useCallback(
      debounce(async (query: string) => {
        if (!query || query.trim() === "" || threadType !== ThreadType.PUBLIC) {
          setCustomerContacts([]);
          return;
        }

        setIsLoading(true);
        try {
          const response = await fetch(
            `/api/customer-contacts/search?account_id=${accountId}&search=${encodeURIComponent(
              query,
            )}`,
          );
          const result = await response.json();

          if (result.data) {
            const formattedContacts = result.data.map((contact) => ({
              label: `${contact.first_name} ${contact.last_name}`.trim(),
              id: contact.uid,
              email: contact.email,
              avatar_url: contact.avatar_url,
              isCustomer: true,
            }));
            setCustomerContacts(formattedContacts);
          }
        } catch (error) {
          console.error("Error fetching customer contacts:", error);
          setCustomerContacts([]);
        } finally {
          setIsLoading(false);
        }
      }, 300),
      [accountId, threadType],
    );

    useEffect(() => {
      fetchCustomerContacts(props.query);
    }, [props.query, fetchCustomerContacts]);

    useEffect(() => {
      if (internalUsers.length === 0) {
        handleFetchInternalUsers();
      }
    }, [internalUsers]);

    const filteredTeamMembers = teamMembers
      .filter(
        (user) =>
          user?.name?.toLowerCase()?.includes(props.query.toLowerCase()),
      )
      .map((user) => ({
        ...user,
        label: user.name,
        isCustomer: false,
        source: "Member",
      }));

    const filteredInternalUsers = internalUsers
      .filter(
        (user) =>
          user?.name?.toLowerCase()?.includes(props.query.toLowerCase()),
      )
      .map((user) => ({
        ...user,
        id: user.uid,
        label: user.name,
        isCustomer: false,
      }));

    const deduplicateByEmail = (users) => {
      const seen = new Map();
      return users.filter((user) => {
        if (!user.email || seen.has(user.email)) {
          return false;
        }
        seen.set(user.email, true);
        return true;
      });
    };

    // Business logic: Only show customer contacts in public threads
    // For internal threads, we only want to show team members and internal users to prevent
    // accidentally mentioning customers in internal communications, and also remove duplicates
    const filteredUsers =
      threadType === ThreadType.PUBLIC
        ? deduplicateByEmail([
            ...filteredTeamMembers,
            ...filteredInternalUsers,
            ...customerContacts,
          ])
        : deduplicateByEmail([
            ...filteredTeamMembers,
            ...filteredInternalUsers,
          ]);

    const selectItem = (index) => {
      const item = filteredUsers[index];
      if (!isEmpty(item)) {
        if (
          !item.id ||
          item.id === "null" ||
          !item.email ||
          item.email === "null"
        ) {
          console.error("Invalid mention data:", item);
          return;
        }

        props.command({
          label: item.label || "Unknown User",
          id: item.id,
          email: item.email,
        });
      }
    };

    const upHandler = () => {
      setSelectedIndex(
        (selectedIndex + filteredUsers.length - 1) % filteredUsers.length,
      );
    };

    const downHandler = () => {
      setSelectedIndex((selectedIndex + 1) % filteredUsers.length);
    };

    useEffect(() => {
      if (selectedItemRef.current && listRef.current) {
        const list = listRef.current;
        const item = selectedItemRef.current;

        const itemTop = item.offsetTop;
        const itemBottom = itemTop + item.offsetHeight;
        const listTop = list.scrollTop;
        const listBottom = listTop + list.offsetHeight;

        if (itemTop < listTop) {
          list.scrollTop = itemTop;
        } else if (itemBottom > listBottom) {
          list.scrollTop = itemBottom - list.offsetHeight;
        }
      }
    }, [selectedIndex, filteredUsers]);

    const enterHandler = () => {
      selectItem(selectedIndex);
    };

    useEffect(() => setSelectedIndex(0), [props.items]);

    useImperativeHandle(ref, () => ({
      onKeyDown: ({ event }) => {
        if ((event.metaKey || event.ctrlKey) && event.key === "Enter") {
          return false;
        }

        if (event.key === "ArrowUp") {
          upHandler();
          return true;
        }

        if (event.key === "ArrowDown") {
          downHandler();
          return true;
        }

        if (event.key === "Enter") {
          enterHandler();
          return true;
        }

        return false;
      },
    }));

    return (
      <div
        ref={listRef}
        className="items max-h-[300px] overflow-y-auto rounded-md shadow-md border border-gray-200 dark:border-gray-700"
      >
        {filteredUsers.length ? (
          filteredUsers.map((item, index) => {
            return (
              <div
                key={index}
                ref={index === selectedIndex ? selectedItemRef : null}
                onClick={() => selectItem(index)}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    selectItem(index);
                    e.preventDefault();
                  }
                }}
                tabIndex={0}
                role="option"
                aria-selected={index === selectedIndex}
                className={`item flex items-center p-2 rounded-md cursor-pointer ${
                  index === selectedIndex ? "bg-gray-100 dark:bg-gray-800" : ""
                }`}
              >
                <Avatar className="w-5 h-5 flex-shrink-0">
                  {item.avatar_url && (
                    <AvatarImage src={item.avatar_url} alt={item.label} />
                  )}
                  <AvatarFallback>{getFirstLetter(item.label)}</AvatarFallback>
                </Avatar>
                <button
                  key={index}
                  type="button"
                  className="ml-2 text-sm truncate max-w-[200px] text-left"
                  title={item.label}
                >
                  {formatName(item.label)}
                </button>
                {item.isCustomer && (
                  <span className="text-[0.65rem] px-1 py-0.5 rounded-sm bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 ml-1 whitespace-nowrap flex-shrink-0">
                    Customer contact
                  </span>
                )}
                {!item.isCustomer && item.source && (
                  <span className="flex gap-1 items-center text-[0.65rem] px-1 py-0.5 rounded-sm bg-muted text-muted-foreground ml-1 whitespace-nowrap flex-shrink-0">
                    {item.source == "Slack member" &&
                      getSourceIcon("slack", 12)}
                    {item.source}
                  </span>
                )}
              </div>
            );
          })
        ) : isLoading ? (
          <div className="flex justify-center py-2">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">Loading...</span>
          </div>
        ) : (
          <div className="p-2">No result</div>
        )}
      </div>
    );
  },
);

MentionList.displayName = "MentionList";
