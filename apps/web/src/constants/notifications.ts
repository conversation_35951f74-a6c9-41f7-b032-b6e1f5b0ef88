import SlackColoredIcon from "@/components/icons/SlackColoredIcon";
import {
  Alert<PERSON>riangle,
  ArrowUpCircle,
  AtSign,
  BarChart2,
  Bell,
  BellRing,
  FileText,
  Mail,
  MessageCircle,
  MessageSquare,
  Star,
  UserCheck,
} from "lucide-react";
import React from "react";

// This is a copy of the enum from shared interfaces to avoid bundling NestJS dependencies
export enum NotificationEventType {
  TICKET_CREATED = "ticket_created",
  TICKET_ASSIGNED = "ticket_assigned",
  TICKET_STATUS_CHANGED = "ticket_status_changed",
  TICKET_PRIORITY_CHANGED = "ticket_priority_changed",
  TICKET_ESCALATED = "ticket_escalated",
  TICKET_ARCHIVED = "ticket_archived",
  TICKET_UNARCHIVED = "ticket_unarchived",
  TICKET_CUSTOMER_THREAD_REPLY = "ticket_customer_thread_reply",
  TICKET_CUSTOMER_THREAD_MENTION = "ticket_customer_thread_mention",
  TICKET_INTERNAL_THREAD_REPLY = "ticket_internal_thread_reply",
  TICKET_INTERNAL_THREAD_MENTION = "ticket_internal_thread_mention",
  TICKET_NOTE_REPLY = "ticket_note_reply",
  TICKET_NOTE_MENTION = "ticket_note_mention",
  TICKET_CUSTOM_FIELD_UPDATED = "ticket_custom_field_updated",
  TICKET_TAG_UPDATED = "ticket_tag_updated",
  TICKET_SLA_BREACH_WARNING = "ticket_sla_breach_warning",
  TICKET_SLA_BREACHED = "ticket_sla_breached",
  TICKET_CSAT_RECEIVED = "ticket_csat_received",
  TICKET_CSAT_UPDATED = "ticket_csat_updated",
}

// Type for notification event configuration
type NotificationEventConfig = {
  id: NotificationEventType;
  label: string;
  icon: typeof AlertTriangle; // Using one of the icon types as they're all the same type
};

// Define and export the notification events with their UI configuration
export const NOTIFICATION_EVENTS: NotificationEventConfig[] = [
  {
    id: NotificationEventType.TICKET_CREATED,
    label: "New ticket",
    icon: MessageSquare,
  },
  {
    id: NotificationEventType.TICKET_ASSIGNED,
    label: "Assigned to ticket",
    icon: UserCheck,
  },
  {
    id: NotificationEventType.TICKET_STATUS_CHANGED,
    label: "Status change",
    icon: BarChart2,
  },
  {
    id: NotificationEventType.TICKET_PRIORITY_CHANGED,
    label: "Priority change",
    icon: ArrowUpCircle,
  },
  {
    id: NotificationEventType.TICKET_ESCALATED,
    label: "Ticket escalated",
    icon: AlertTriangle,
  },
  {
    id: NotificationEventType.TICKET_ARCHIVED,
    label: "Ticket archived",
    icon: FileText,
  },
  {
    id: NotificationEventType.TICKET_UNARCHIVED,
    label: "Ticket unarchived",
    icon: FileText,
  },
  {
    id: NotificationEventType.TICKET_SLA_BREACH_WARNING,
    label: "SLA breach warning",
    icon: AlertTriangle,
  },
  {
    id: NotificationEventType.TICKET_SLA_BREACHED,
    label: "SLA breached",
    icon: AlertTriangle,
  },
  {
    id: NotificationEventType.TICKET_CSAT_RECEIVED,
    label: "CSAT received",
    icon: Star,
  },
  {
    id: NotificationEventType.TICKET_CSAT_UPDATED,
    label: "CSAT updated",
    icon: Star,
  },
  {
    id: NotificationEventType.TICKET_CUSTOMER_THREAD_REPLY,
    label: "Customer thread reply",
    icon: MessageCircle,
  },
  {
    id: NotificationEventType.TICKET_CUSTOMER_THREAD_MENTION,
    label: "Customer thread mention",
    icon: AtSign,
  },
  {
    id: NotificationEventType.TICKET_INTERNAL_THREAD_REPLY,
    label: "Internal thread reply",
    icon: MessageCircle,
  },
  {
    id: NotificationEventType.TICKET_INTERNAL_THREAD_MENTION,
    label: "Internal thread mention",
    icon: AtSign,
  },
  {
    id: NotificationEventType.TICKET_NOTE_REPLY,
    label: "Note reply",
    icon: MessageCircle,
  },
  {
    id: NotificationEventType.TICKET_NOTE_MENTION,
    label: "Note mention",
    icon: AtSign,
  },
  {
    id: NotificationEventType.TICKET_CUSTOM_FIELD_UPDATED,
    label: "Custom field updated",
    icon: FileText,
  },
  {
    id: NotificationEventType.TICKET_TAG_UPDATED,
    label: "Tag updated",
    icon: FileText,
  },
];

// Type for notification channel configuration
export type NotificationChannelConfig = {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string; size?: number }>;
  color: string;
};

// Define and export the notification channels
export const NOTIFICATION_CHANNELS: NotificationChannelConfig[] = [
  {
    id: "toast",
    label: "Toast",
    icon: BellRing,
    color: "text-purple-500",
  },
  {
    id: "in-app",
    label: "Inbox",
    icon: Bell,
    color: "text-blue-500",
  },
  {
    id: "slack",
    label: "Slack",
    icon: SlackColoredIcon,
    color: "text-emerald-500",
  },
  {
    id: "email",
    label: "Email",
    icon: Mail,
    color: "text-red-500",
  },
];

// Export the channel type for use in other files
export type NotificationChannel = (typeof NOTIFICATION_CHANNELS)[number]["id"];
