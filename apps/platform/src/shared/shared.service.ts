import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import {
  AccountRepository,
  BusinessHoursConfig,
  BusinessSlot,
  CustomerContactRepository,
  CustomObjectRecordsRepository,
  CustomObjectRepository,
  DayConfig,
  Form,
  FormFieldEventRepository,
  FormRepository,
  Organization,
  OrganizationRepository,
  Tag,
  TagRepository,
  Team,
  TeamMember,
  TeamMemberRepository,
  TeamMemberRole,
  TeamRepository,
  ThenaLookupEntities,
  TicketPriority,
  TicketPriorityRepository,
  TicketRepository,
  TicketSentiment,
  TicketSentimentRepository,
  TicketStatus,
  TicketStatusRepository,
  TicketType,
  TicketTypeRepository,
  TransactionContext,
  TransactionService,
  UserRepository,
  UserType,
} from "@repo/thena-platform-entities";
import { DateTime, DateTimeOptions } from "luxon";
import { DeepPartial, FindOptionsWhere, In, IsNull } from "typeorm";
import { CurrentUser } from "../common/decorators";
import { GetAllTagsQuery } from "../tags/dto";
import { priorities, statuses, ticketSentiments, types } from "./constants";

@Injectable()
export class SharedService {
  constructor(
    // Team Repositories
    private readonly teamRepository: TeamRepository,

    // Team Member Repositories
    private readonly teamMemberRepository: TeamMemberRepository,

    // Tag Repositories
    private readonly tagRepository: TagRepository,

    // Ticket utility repositories
    private readonly ticketStatusRepository: TicketStatusRepository,
    private readonly ticketPriorityRepository: TicketPriorityRepository,
    private readonly ticketTypeRepository: TicketTypeRepository,
    private readonly ticketSentimentRepository: TicketSentimentRepository,

    // Injected services
    private readonly transactionService: TransactionService,

    // Organization Repository
    private readonly organizationRepository: OrganizationRepository,

    // Form Repository
    private readonly formRepository: FormRepository,
    private readonly formFieldEventRepository: FormFieldEventRepository,

    // Custom Object Repository
    private readonly customObjectRepository: CustomObjectRepository,

    // Custom Object Record Repository
    private readonly customObjectRecordsRepository: CustomObjectRecordsRepository,

    // Ticket Repository
    private readonly ticketRepository: TicketRepository,

    // User Repository
    private readonly userRepository: UserRepository,

    // Account Repository
    private readonly accountRepository: AccountRepository,

    // Contact Repository
    private readonly customerContactRepository: CustomerContactRepository,
  ) { }

  //TODO : REMOVE THIS LATER
  async findOrganization(orgId: string) {
    return await this.organizationRepository.findByCondition({
      where: {
        uid: orgId,
      },
    });
  }

  /**
   * Finds a tag by its ID.
   * @param tagId The ID of the tag to find.
   * @param user The current user.
   * @returns The found tag.
   */
  async findOneTag(tagId: string, user: CurrentUser): Promise<Tag> {
    // Find the tag by its ID
    const tag = await this.tagRepository.findByCondition({
      where: {
        uid: tagId,
        organizationId: user.orgId,
        isActive: true,
      },
    });

    // If tag not found, throw an error
    if (!tag) {
      throw new NotFoundException("Tag not found");
    }

    return tag;
  }

  /**
   * Finds all tags for an organization.
   * @param user The current user.
   * @param type Optional tag type filter.
   * @returns Array of tags.
   */
  async findAllTags(user: CurrentUser, query: GetAllTagsQuery): Promise<Tag[]> {
    const whereClause: FindOptionsWhere<Tag> = {
      organizationId: user.orgId,
      tagType: query.type,
      isActive: true,
    };

    const limit = Math.min(query.limit ?? 10, 100);
    const { results: tags } = await this.tagRepository.fetchPaginatedResults(
      { page: query.page ?? 0, limit },
      {
        where: whereClause,
        order: { createdAt: "DESC" },
      },
    );

    return tags;
  }

  /**
   * Finds all teams for an organization.
   * @param teamIds The IDs of the teams to find.
   * @param orgId The ID of the organization.
   * @returns Array of teams.
   */
  async findAllTeams(
    teamIds: Array<string>,
    orgId: string,
  ): Promise<Array<Team>> {
    // If no team IDs are provided, return an empty array
    if (!teamIds || teamIds.length === 0) {
      return [];
    }

    let orgQuery: FindOptionsWhere<Organization> = {
      id: orgId,
    };

    if (orgId.startsWith("E")) {
      orgQuery = {
        uid: orgId,
      };
    }

    // Find the teams by their IDs
    const teams = await this.teamRepository.findAll({
      where: {
        uid: In(teamIds),
        organization: orgQuery,
      },
    });

    return teams;
  }

  async findAllTicketTypesForTeam(orgId: string, teamId: string) {
    if (!orgId || !teamId?.trim()) {
      throw new BadRequestException("Organization ID and team ID are required");
    }

    // Find all ticket statuses for the organization
    const ticketTypes = await this.ticketTypeRepository.findAll({
      where: {
        organizationId: orgId,
        teamId: teamId,
      },
    });

    return ticketTypes;
  }

  async findAllTicketStatusesForTeam(orgId: string, teamId: string) {
    if (!orgId || !teamId?.trim()) {
      throw new BadRequestException("Organization ID and team ID are required");
    }

    const ticketStatuses = await this.ticketStatusRepository.findAll({
      where: {
        organizationId: orgId,
        teamId: teamId,
      },
    });

    return ticketStatuses;
  }

  async findStatusById(statusId: string) {
    const status = await this.ticketStatusRepository.findByCondition({
      where: {
        id: statusId,
      },
      relations: ["parentStatus"],
    });

    return status;
  }

  async findAllTicketPrioritiesForTeam(orgId: string, teamId: string) {
    if (!orgId || !teamId?.trim()) {
      throw new BadRequestException("Organization ID and team ID are required");
    }

    const ticketPriorities = await this.ticketPriorityRepository.findAll({
      where: {
        organizationId: orgId,
        teamId: teamId,
      },
    });

    return ticketPriorities;
  }

  /**
   * Checks if a user is part of any teams.
   * @param userId The ID of the user.
   * @param orgId The ID of the organization.
   * @returns True if the user is part of any teams, false otherwise.
   */
  async belongsToTeams(userId: string, orgId: string, count: boolean = true) {
    // If count is false, return the team members
    if (!count) {
      const teamMembers = await this.teamMemberRepository.findAll({
        where: { userId, organizationId: orgId },
      });

      return teamMembers;
    }

    // If count is true, return the count of team members
    const teamMembersCount = await this.teamMemberRepository.count({
      where: { userId, organizationId: orgId },
    });

    return teamMembersCount;
  }

  /**
   * Adds a team member as a bot.
   * @param teamId The ID of the team.
   * @param userId The ID of the user.
   * @param orgId The ID of the organization.
   * @param txnContext The transaction context.
   * @returns The created team member.
   */
  async addTeamMemberAsBot(
    teamId: string | Array<string>,
    userId: string,
    orgId: string,
    txnContext?: TransactionContext,
  ) {
    const teamIds = Array.isArray(teamId) ? teamId : [teamId];
    const writes: DeepPartial<TeamMember>[] = teamIds.map((teamId) => ({
      teamId,
      userId,
      organizationId: orgId,
      isActive: true,
      isBot: true,
      role: TeamMemberRole.MEMBER,
    }));

    let teamMembers: TeamMember[] = [];
    try {
      // Run the transaction
      teamMembers = await this.transactionService.runInTransaction(
        async (txnContext) => {
          return await this.teamMemberRepository.saveManyWithTxn(
            txnContext,
            writes,
          );
        },
        txnContext,
      );
    } catch (error) {
      if (error.code === "23505") {
        teamMembers = await this.teamMemberRepository.findAll({
          where: {
            teamId: In(teamIds),
            userId,
            organizationId: orgId,
          },
        });
      }
    }

    return teamMembers;
  }

  async removeTeamMembers(
    teamIds: Array<string>,
    userId: string,
    orgId: string,
    txnContext?: TransactionContext,
  ) {
    // Find all team members
    const teamMembers = await this.teamMemberRepository.findAll({
      where: {
        teamId: In(teamIds),
        userId,
        organizationId: orgId,
      },
    });

    // If no team members are found, return
    if (!teamMembers || teamMembers.length === 0) {
      return;
    }

    // Run the transaction
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Delete the team members records
      await this.teamMemberRepository.removeManyWithTxn(
        txnContext,
        teamMembers,
      );
    }, txnContext);
  }

  /**
   * Seeds default statuses, priorities, and types for a team.
   * @param team The team to seed.
   * @param existingTransaction The existing transaction context.
   */
  async seedDefaultStatusPriorityAndType(
    team: Team,
    existingTransaction: TransactionContext,
  ) {
    // Seed statuses
    const statusesRecords: DeepPartial<Array<TicketStatus>> = statuses.map(
      (status) => {
        return {
          teamId: team.id,
          organizationId: team.organizationId,
          name: status.name,
          displayName: status.name,
          description: status.description,
          isDefault: status.default,
          order: status.order,
        };
      },
    );

    // Seed priorities
    const prioritiesRecords: DeepPartial<Array<TicketPriority>> =
      priorities.map((priority) => {
        return {
          teamId: team.id,
          organizationId: team.organizationId,
          name: priority.name,
          displayName: priority.name,
          description: priority.description,
          isDefault: priority.default,
        };
      });

    // Seed types
    const typesRecords: DeepPartial<Array<TicketType>> = types.map((type) => {
      return {
        teamId: team.id,
        organizationId: team.organizationId,
        name: type.name,
        description: type.description,
        isDefault: type.default,
        icon: type.icon,
        color: type.color,
      };
    });

    // Seed sentiments
    const sentimentsRecords: DeepPartial<Array<TicketSentiment>> =
      ticketSentiments.map((sentiment) => {
        return {
          name: sentiment.name,
          team: { id: team.id },
          isDefault: sentiment.default,
          description: sentiment.description,
          organization: { id: team.organizationId },
        };
      });

    // Seed data on team creation inside a transaction
    await this.transactionService.runInTransaction(async (txnContext) => {
      // Seed statuses
      await this.ticketStatusRepository.saveManyWithTxn(
        txnContext,
        statusesRecords,
      );

      // Seed priorities
      await this.ticketPriorityRepository.saveManyWithTxn(
        txnContext,
        prioritiesRecords,
      );

      // Seed sentiments
      await this.ticketSentimentRepository.saveManyWithTxn(
        txnContext,
        sentimentsRecords,
      );

      // Seed types
      await this.ticketTypeRepository.saveManyWithTxn(txnContext, typesRecords);
    }, existingTransaction);
  }

  /**
   * Finds all forms that use any of the specified field IDs.
   * @param fieldIds The list of field IDs to search for.
   * @returns Array of forms that use the specified field IDs.
   */
  async findFormsByFieldIds(fieldIds: Array<string>): Promise<Array<Form>> {
    if (!fieldIds || fieldIds.length === 0) {
      return [];
    }

    const forms = await this.formRepository.queryRaw(
      `SELECT *
		FROM forms
		WHERE EXISTS (
		  SELECT 1
		  FROM jsonb_array_elements(fields) AS field_data
		  WHERE field_data->>'field' = ANY($1)
		);`,
      [fieldIds],
    );

    return forms;
  }

  async saveFormFieldEvents(formFieldEvents: any) {
    const fieldEvent =
      this.formFieldEventRepository.createMany(formFieldEvents);
    await this.formFieldEventRepository.saveMany(fieldEvent);
  }

  async isValidCustomObjectOrThenaEntity(objectId: string, teamId: string) {
    if (!objectId) {
      return false;
    }

    const customObject = await this.customObjectRepository.findByCondition({
      where: {
        uid: objectId,
        deletedAt: IsNull(),
        teamId: teamId ? teamId : IsNull(),
      },
    });

    const validThenaObjects = Object.values(ThenaLookupEntities).map((object) =>
      object.toLowerCase(),
    );

    return customObject || validThenaObjects.includes(objectId);
  }

  async getTeam(teamId: string, orgId: string): Promise<Team> {
    const team = await this.teamRepository.findByCondition({
      where: { uid: teamId, organizationId: orgId },
    });

    if (!team) {
      throw new NotFoundException("Team not found");
    }

    return team;
  }

  async getCustomObjectRecord(recordId: string, teamId: string) {
    const customObjectRecord =
      await this.customObjectRecordsRepository.findByCondition({
        where: {
          uid: recordId,
          teamId: teamId,
        },
      });

    if (!customObjectRecord) {
      throw new NotFoundException("Custom object record not found");
    }

    return customObjectRecord;
  }

  async getTicket(ticketId: string, teamId: string) {
    const ticket = await this.ticketRepository.findByCondition({
      where: {
        uid: ticketId,
        teamId: teamId,
      },
    });

    if (!ticket) {
      throw new NotFoundException("Ticket not found");
    }

    return ticket;
  }

  async getTicketByPrimaryKey(ticketId: string) {
    try {
      const ticket = await this.ticketRepository.findByCondition({
        where: {
          id: ticketId,
        },
        relations: [
          // Core entities
          "account",
          "customerContact",
          "organization",

          // Team relations
          "team",
          "team.organization",

          // Essential ticket fields for CSAT
          "status",
          "priority",
          "type",
          "sentiment",
          "tags",
          "assignedAgent",

          // Custom fields
          "customFieldValues",
          "account.customFieldValues",
          "customerContact.customFieldValues",

          // Account relations
          "account.organization",
          "account.statusAttribute",
          "account.classificationAttribute",
          "account.healthAttribute",
          "account.industryAttribute",

          // Contact relations
          "customerContact.organization",
          "customerContact.contactTypeAttribute",
        ],
      });

      if (!ticket) {
        throw new NotFoundException("Ticket not found");
      }

      return ticket;
    } catch (error) {
      console.error(`Error fetching ticket with ID ${ticketId}:`, error);
      throw error;
    }
  }

  async getUser(userId: string, orgId: string) {
    const user = await this.userRepository.findByCondition({
      where: {
        uid: userId,
        organizationId: orgId,
      },
    });

    if (!user) {
      throw new NotFoundException("User not found");
    }

    return user;
  }

  // not using "findOrganization" because it can be removed as mentioned in the TODO
  async getOrganization(orgId: string) {
    const organization = await this.organizationRepository.findByCondition({
      where: { uid: orgId },
    });

    if (!organization) {
      throw new NotFoundException("Organization not found");
    }

    return organization;
  }

  async getAccount(accountId: string, orgId: string) {
    const account = await this.accountRepository.findByCondition({
      where: { uid: accountId, organizationId: orgId },
    });

    if (!account) {
      throw new NotFoundException("Account not found");
    }

    return account;
  }

  async getContact(contactId: string, orgId: string) {
    const contact = await this.customerContactRepository.findByCondition({
      where: { uid: contactId, organizationId: orgId },
    });

    if (!contact) {
      throw new NotFoundException("Contact not found");
    }

    return contact;
  }

  async getBotUserFromOrganization(orgId: string) {
    const user = await this.userRepository.findByCondition({
      where: { organizationId: orgId, userType: UserType.BOT_USER },
    });

    if (!user) {
      throw new NotFoundException("Bot user not found");
    }

    return user;
  }
  /**
   * Checks if the current time falls within the business hours for a given timezone.
   * @param businessHoursConfig The business hours configuration.
   * @param timezone The timezone to check.
   * @returns True if the current time is within the business hours, false otherwise.
   */
  checkBusinessHoursAvailability(
    businessHoursConfig: BusinessHoursConfig,
    timezone: string,
  ): boolean {
    // If business hours config is not set, team / user is always available
    if (!businessHoursConfig) {
      return true;
    }

    const now = DateTime.now().setZone(timezone);
    // Get current day of week and its business hours
    const currentDay = now.weekdayLong.toLowerCase();
    const businessHours = (
      businessHoursConfig[currentDay as keyof BusinessHoursConfig] as
      | DayConfig
      | undefined
    )?.slots as BusinessSlot[] | undefined;

    // If no business hours set for current day, team is unavailable
    if (!businessHours || businessHours.length === 0) {
      return false;
    }

    // Get current time in HH:mm format
    const currentTime = now.toFormat("HH:mm");

    // Check if current time falls within any business hours slot
    return businessHours.some((slot) => {
      const zone: DateTimeOptions = { zone: timezone };
      const slotStart = DateTime.fromFormat(slot.start, "HH:mm", zone);
      const slotEnd = DateTime.fromFormat(slot.end, "HH:mm", zone);

      // Handle slots spanning midnight
      if (slotEnd < slotStart) {
        // If current time is before midnight, compare with start time
        if (currentTime >= slot.start) {
          return true;
        }
        // If current time is after midnight, compare with end time
        if (currentTime <= slot.end) {
          return true;
        }
        return false;
      }

      // Normal slot comparison
      return currentTime >= slot.start && currentTime <= slot.end;
    });
  }
}
