<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>
    <%= title %>
  </title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    /* Custom styles beyond Tailwind */
    .star-btn {
      width: 50px;
      height: 50px;
      line-height: 50px;
      border-radius: 50%;
      text-align: center;
      font-size: 24px;
      cursor: pointer;
      transition: all 0.2s;
      background-color: #d1d5db;
      color: white;
      user-select: none;
      margin: 0 4px;
      -webkit-tap-highlight-color: transparent;
    }

    .star-btn:hover {
      transform: scale(1.1);
      background-color: #e5a73b;
    }

    .star-btn.selected {
      background-color: #f59e0b !important;
      color: white !important;
    }

    .thumb-btn {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 30px;
      cursor: pointer;
      background-color: #d1d5db;
      color: white;
      transition: all 0.2s;
      margin: 0 10px;
    }

    .thumb-btn:hover {
      transform: scale(1.1);
    }

    /* Use CSS variable for the branding color that we'll set in the body */
    .thumb-btn.selected {
      background-color: var(--branding-color, #0284c7);
    }

    /* Smooth transitions */
    .fade-in {
      animation: fadeIn 0.5s ease-in;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
      }

      to {
        opacity: 1;
      }
    }
  </style>
</head>

<body class="bg-gray-50 min-h-screen flex flex-col" <% if(typeof brandingColor !=='undefined' ) { %>
  style="--branding-color: <%= brandingColor %>"<% } %>>
      <main class="flex-grow flex items-center justify-center py-10 px-4 sm:px-6 lg:px-8">
        <!-- Error State -->
        <% if (typeof isError !=='undefined' && isError) { %>
          <div class="max-w-md w-full bg-white shadow-lg rounded-lg overflow-hidden">
            <div class="px-6 py-8">
              <div class="text-center">
                <svg class="mx-auto h-16 w-16 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z">
                  </path>
                </svg>
                <h2 class="mt-4 text-xl font-bold text-gray-900">
                  <%= title %>
                </h2>
                <p class="mt-2 text-gray-600">
                  <%= message %>
                </p>
              </div>
            </div>
          </div>

          <!-- Thank You State -->
          <% } else if (typeof isThankYou !=='undefined' && isThankYou) { %>
            <div class="max-w-md w-full bg-white shadow-lg rounded-lg overflow-hidden fade-in">
              <div class="px-6 py-10">
                <!-- Ticket Details Section for Thank You Page -->
                <% if (typeof ticketDetails !=='undefined' && ticketDetails) { %>
                  <div class="bg-gray-50 rounded-lg p-4 border border-gray-200 mb-6">
                    <div class="mb-3">
                      <div class="text-sm text-gray-600 mb-1">
                        Ticket #<%= ticketDetails.ticketId %>
                      </div>
                      <div class="font-semibold text-gray-900">
                        <%= ticketDetails.ticketTitle %>
                      </div>
                    </div>
                    <div class="grid grid-cols-2 gap-3 text-sm">
                      <div class="text-gray-600">
                        <span class="text-gray-500">Created:</span>
                        <%= ticketDetails.createdAt %>
                      </div>
                      <div class="text-gray-600">
                        <span class="text-gray-500">Resolved:</span>
                        <%= ticketDetails.closedAt %>
                      </div>
                    </div>
                  </div>
                  <% } %>

                    <div class="text-center">
                      <div class="bg-green-500 rounded-full mx-auto w-16 h-16 flex items-center justify-center">
                        <svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7">
                          </path>
                        </svg>
                      </div>

                      <h2 class="mt-4 text-2xl font-bold text-gray-900">
                        <%= title %>
                      </h2>
                      <p class="mt-3 text-base text-gray-600">
                        <%= message %>
                      </p>

                      <div class="mt-8 border-t border-gray-200 pt-6">
                        <p class="text-sm text-gray-500">
                          Your feedback helps us improve our service. Thank you for taking the time to share your
                          thoughts.
                        </p>
                      </div>
                    </div>
              </div>
            </div>

            <!-- Feedback Form State -->
            <% } else { %>
              <div class="max-w-xl w-full bg-white shadow-lg rounded-lg overflow-hidden fade-in">
                <div class="px-6 py-8">
                  <!-- Ticket Details Section -->
                  <% if (typeof ticketDetails !=='undefined' && ticketDetails) { %>
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200 mb-6">
                      <div class="mb-3">
                        <div class="text-sm text-gray-600 mb-1">
                          Ticket #<%= ticketDetails.ticketId %>
                        </div>
                        <div class="font-semibold text-gray-900">
                          <%= ticketDetails.ticketTitle %>
                        </div>
                      </div>
                      <div class="grid grid-cols-2 gap-3 text-sm">
                        <div class="text-gray-600">
                          <span class="text-gray-500">Created:</span>
                          <%= ticketDetails.createdAt %>
                        </div>
                        <div class="text-gray-600">
                          <span class="text-gray-500">Resolved:</span>
                          <%= ticketDetails.closedAt %>
                        </div>
                      </div>
                    </div>
                    <% } %>

                      <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-900">
                          <%= title %>
                        </h2>
                        <p class="mt-2 text-gray-600">
                          <%= message %>
                        </p>
                      </div>

                      <form id="feedbackForm" action="<%= apiUrl %>" method="POST">
                        <input type="hidden" name="ratingType" value="<%= feedbackType %>">

                        <div class="mb-8">
                          <label class="block text-sm font-medium text-gray-700 mb-3 text-center">Your Rating</label>

                          <% if (feedbackType==='star' ) { %>
                            <div class="flex justify-center space-x-2" id="starRatingContainer">
                              <% for (let i=1; i <=5; i++) { %>
                                <div class="star-btn" data-value="<%= i %>">★</div>
                                <% } %>
                            </div>

                            <div class="flex justify-between mt-2 text-xs text-gray-500 px-2">
                              <span>Poor</span>
                              <span>Excellent</span>
                            </div>
                            <% } else { %>
                              <div class="flex justify-center">
                                <div class="flex flex-col items-center mx-4">
                                  <div class="thumb-btn" data-value="up">👍</div>
                                  <span class="mt-2 text-sm text-gray-600">Good</span>
                                </div>

                                <div class="flex flex-col items-center mx-4">
                                  <div class="thumb-btn" data-value="down">👎</div>
                                  <span class="mt-2 text-sm text-gray-600">Needs Improvement</span>
                                </div>
                              </div>
                              <% } %>
                                <input type="hidden" id="ratingValue" name="rating" value="">
                        </div>

                        <% if (typeof includeComment !=='undefined' && includeComment) { %>
                          <div class="mb-6">
                            <label for="comment" class="block text-sm font-medium text-gray-700 mb-2">
                              <%= commentLabel %>
                            </label>
                            <textarea id="comment" name="comment" rows="4"
                              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="<%= commentPlaceholder %>"></textarea>
                          </div>
                          <% } %>

                            <div class="mt-8">
                              <button type="submit" id="submitBtn"
                                class="w-full py-3 px-4 text-white font-medium rounded-md shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                                style="background-color: <%= typeof brandingColor !== 'undefined' ? brandingColor : '#0284c7' %>;"
                                disabled>
                                Submit Feedback
                              </button>
                            </div>
                      </form>
                </div>
              </div>
              <% } %>
      </main>

      <footer class="bg-white border-t border-gray-200 py-6">
        <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-gray-500 text-sm">
          &copy; <%= new Date().getFullYear() %> Your Company. All rights reserved.
        </div>
      </footer>

      <% if (!(typeof isThankYou !=='undefined' && isThankYou) && !(typeof isError !=='undefined' && isError)) { %>
        <script>
          document.addEventListener('DOMContentLoaded', function () {
            // Get the form element
            const form = document.getElementById('feedbackForm');

            // Debugging - check if we found star buttons
            const starButtons = document.querySelectorAll('.star-btn');

            // Explicitly bind click events to stars as a backup method
            starButtons.forEach(btn => {
              const value = parseInt(btn.getAttribute('data-value'));
              btn.addEventListener('click', function () {
                selectStar(value);
              });
            });

            // Similarly for thumb buttons
            const thumbButtons = document.querySelectorAll('.thumb-btn');
            thumbButtons.forEach(btn => {
              const value = btn.getAttribute('data-value');
              btn.addEventListener('click', function () {
                selectThumb(value);
              });
            });

            // Add event listener for form submission
            form.addEventListener('submit', function (event) {
              event.preventDefault();

              // Get the rating value
              const ratingValue = document.getElementById('ratingValue').value;

              // Validate the form - ensure rating is selected
              if (!ratingValue) {
                alert('Please select a rating before submitting.');
                return;
              }

              // If validation passes, prepare to submit the form
              const formData = new FormData(form);

              // Create a regular object from FormData for easier debugging
              const formObject = {};
              formData.forEach((value, key) => {
                formObject[key] = value;
              });


              // Try alternate submission method using JSON
              fetch(form.action, {
                method: 'POST',
                body: JSON.stringify(formObject),
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                }
              })
                .then(response => {
                  return response.text().then(text => {
                    // Try to parse as JSON, but fall back to text if it's not valid JSON
                    try {
                      return JSON.parse(text);
                    } catch (e) {
                      return { success: response.ok, responseText: text };
                    }
                  });
                })
                .then(data => {
                  // On successful submission, redirect to thank you page or reload
                  window.location.reload();
                })
                .catch(error => {
                  alert('There was an error submitting your feedback. Please try again.');
                });
            });
          });

          // Star rating selection
          function selectStar(value) {
            // Set the hidden input value
            document.getElementById('ratingValue').value = value;


            // Clear all selections first
            const allStars = document.querySelectorAll('.star-btn');
            allStars.forEach(star => {
              star.classList.remove('selected');
            });

            // Then add 'selected' class to this star and all stars before it
            allStars.forEach(star => {
              const starValue = parseInt(star.getAttribute('data-value'));
              if (starValue <= value) {
                star.classList.add('selected');
              }
            });

            // Enable submit button
            document.getElementById('submitBtn').disabled = false;
          }

          // Thumbs rating selection
          function selectThumb(value) {
            document.getElementById('ratingValue').value = value;

            // Clear all selections
            document.querySelectorAll('.thumb-btn').forEach(btn => {
              btn.classList.remove('selected');
            });

            // Select chosen thumb
            document.querySelector(`.thumb-btn[data-value="${value}"]`).classList.add('selected');

            // Enable submit button
            document.getElementById('submitBtn').disabled = false;
          }
        </script>
        <% } %>
</body>

</html>