import { InjectQueue } from "@nestjs/bullmq";
import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { SentryService } from "@repo/nestjs-commons/filters";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  Organization,
  OrganizationDomainsRepository,
  OrganizationInvitationsRepository,
  OrganizationRepository,
  TransactionService,
  UserType,
} from "@repo/thena-platform-entities";
import { OrganizationEvents } from "@repo/thena-shared-interfaces";
import { Queue } from "bullmq";
import * as _ from "lodash";
import { nanoid } from "nanoid";
import tldts from "tldts";
import { IsNull } from "typeorm";
import { POSTGRES_ERROR_CODES } from "../../common/constants/postgres-errors.constants";
import { CurrentUser } from "../../common/decorators";
import { IdGeneratorUtils } from "../../common/utils";
import { QueueNames } from "../../constants/queue.constants";
import { UsersService } from "../../users/services/users.service";
import {
  CreateOrganizationDto,
  InviteUserDto,
  JoinOrganizationDto,
  UpdateOrganizationDto,
} from "../dto/organization.dto";
import { OrganizationEventsFactory } from "../events/organization-events.factory";
import { OrganizationSNSEventsFactory } from "../events/organization-sns-events.factory";
import { EmittableOrganizationEvents } from "../events/organization.events";
import { SNSEvent } from "../interfaces/sns-events.interface";
import { CreateOrgAndOrgAdminSaga } from "../sagas";
@Injectable()
export class OrganizationService {
  constructor(
    // Sentry and logger
    @Inject("Sentry") private readonly sentryService: SentryService,
    @Inject("CustomLogger") private readonly logger: ILogger,

    // Services
    private readonly transactionService: TransactionService,

    // Repositories
    private readonly organizationRepository: OrganizationRepository,
    private readonly organizationDomainsRepository: OrganizationDomainsRepository,
    private readonly createOrgAndOrgAdminSaga: CreateOrgAndOrgAdminSaga,
    private readonly usersService: UsersService,
    private readonly organizationInvitationsRepository: OrganizationInvitationsRepository,

    // SNS Publisher Queue
    @InjectQueue(QueueNames.ORGANIZATION_SNS_PUBLISHER)
    private readonly organizationSNSPublisherQueue: Queue,

    // SNS Events Factory
    private readonly organizationSNSEventsFactory: OrganizationSNSEventsFactory,

    // Local event emitter
    private readonly eventEmitter: EventEmitter2,

    // Events Factory
    private readonly organizationEventsFactory: OrganizationEventsFactory,
  ) { }

  /**
   * Publishes an account event to SNS.
   * @param event The event to publish.
   * @param eventData The data to publish.
   * @param user The user associated with the event.
   */
  async publishEventToSNSQueue<Q, T extends SNSEvent<Q>>(
    event: T["eventType"],
    eventData: T,
    user: { uid: string; email: string; userType: UserType; orgUid: string },
  ) {
    try {
      await this.organizationSNSPublisherQueue.add(
        QueueNames.ORGANIZATION_SNS_PUBLISHER,
        {
          event,
          eventData,
          user,
        },
        {
          attempts: 3,
          backoff: {
            type: "exponential",
            delay: 1000, // 1 second
          },
        },
      );
    } catch (error) {
      this.logger.error(
        `Error encountered while publishing organization event - ${event} for organization ${eventData.orgId} to SNS: ${error?.message}`,
        error.stack,
      );

      this.sentryService.captureException(error, {
        tag: "ORGANIZATION_EVENTS",
        fn: "publishEventToSNSQueue",
        organizationId: eventData.orgId,
        user,
        event,
        eventData,
      });
    }
  }

  async getUserType(email: string, orgId: string) {
    const { domain } = tldts.parse(email);
    if (!domain) {
      throw new Error("Invalid email domain");
    }

    let orgLookup: { id?: string; uid?: string };
    if (orgId.startsWith("E")) {
      orgLookup = { uid: orgId };
    } else {
      orgLookup = { id: orgId };
    }

    // Organization domains
    const orgDomains = await this.organizationDomainsRepository.findAll({
      where: { organization: orgLookup, deletedAt: IsNull() },
      select: { domain: true },
    });

    const domains = orgDomains.map((orgDomain) => orgDomain.domain);
    if (domains.length === 0) {
      throw new ForbiddenException("Forbidden");
    }

    // Check if the domain is in the domains array
    const isDomainAllowed = domains.includes(domain);
    if (!isDomainAllowed) {
      return "CUSTOMER";
    }

    return "USER";
  }

  /**
   * Finds all organizations.
   * @returns All organizations.
   */
  findAll(user: CurrentUser): Promise<Organization[]> {
    this.logger.log(`Finding all organizations for user ${user.uid}`);
    return this.usersService.findAllOrgsForUser(user);
  }

  async findOneBySlug(slug: string, user: CurrentUser): Promise<Organization> {
    this.logger.log(`Finding organization by slug: ${slug}`);

    // Check if the slug is valid
    const organization = await this.organizationRepository.findByCondition({
      where: { slug },
    });

    // Check if the user is authorized to access this organization
    if (!organization) {
      this.logger.warn(`Organization with slug ${slug} not found`);
      throw new NotFoundException("Organization not found");
    }

    // Check if the user is authorized to access this organization
    if (user.orgUid !== organization.uid) {
      this.logger.warn(
        `User ${user.uid} is not authorized to access organization ${organization.uid}`,
      );
      throw new ForbiddenException("Invalid organization slug provided!");
    }

    return organization;
  }

  /**
   * Finds an organization by ID.
   * @param id The ID of the organization to find.
   * @returns The organization.
   */
  async findOne(id: string): Promise<Organization> {
    this.logger.log(`Finding organization by ID: ${id}`);
    try {
      const orgByIdentifier = await this.findOneByIdentifier(id);

      // If the organization was found by identifier, return it
      if (!_.isEmpty(orgByIdentifier)) {
        return orgByIdentifier;
      }

      const organization = await this.organizationRepository.findOneById(id);
      if (!organization) {
        this.logger.warn(`Organization with ID ${id} not found`);
        throw new NotFoundException(
          "Organization with provided id was not found!",
        );
      }

      return organization;
    } catch (error) {
      if (error?.code === POSTGRES_ERROR_CODES.INVALID_TEXT_REPRESENTATION) {
        this.logger.warn(`Invalid organization ID format: ${id}`);
        throw new NotFoundException(
          "Organization with provided id was not found!",
        );
      }

      this.logger.error(`Error finding organization ${id}:`, error);
      throw error;
    }
  }

  /**
   * Finds an organization by identifier.
   * @param identifier The identifier of the organization to find.
   * @returns The organization.
   */
  async findOneByIdentifier(orgIdentifier: string): Promise<Organization> {
    this.logger.log(`Finding organization by identifier: ${orgIdentifier}`);
    const organization = await this.organizationRepository.findByCondition({
      where: { uid: orgIdentifier },
    });

    return organization;
  }

  /**
   * Joins an organization.
   * @param joinOrgDto The DTO containing the organization ID and the user ID.
   * @returns The organization.
   */
  async joinOrganization(joinOrgDto: JoinOrganizationDto, authToken: string) {
    this.logger.log(
      `Processing join organization request for org ${joinOrgDto.orgId}`,
    );

    // Find and check if the organization exists
    const organization = await this.findOne(joinOrgDto.orgId);

    // Get the token from the auth token
    const [_type, token] = authToken?.split(" ") || [];
    if (!token) {
      this.logger.warn(
        "Invalid auth token provided for join organization request",
      );
      throw new BadRequestException("Invalid auth token provided!");
    }

    // Get the current user
    const currentUser = await this.usersService.getSupabaseUser(token);

    // Check if the user is already a member of the organization
    const isMember = await this.usersService.isUserPartOfOrganization(
      { email: currentUser.email },
      organization.id,
      { exists: true },
    );

    // If the user is already a member, throw an error
    if (isMember) {
      this.logger.warn(
        `User ${currentUser.email} is already a member of organization ${organization.id}`,
      );
      throw new ConflictException(
        "You are already a member of this organization.",
      );
    }

    // Check if the invite is a link invite or is a domain organization join
    const isLinkInvite = joinOrgDto.inviteeEmail && joinOrgDto.invitingUserId;

    this.logger.log(
      `Processing ${isLinkInvite ? "link invite" : "domain"} join for user ${currentUser.email
      }`,
    );

    // Check if the invite is valid
    if (isLinkInvite) {
      // Get the inviting user
      const invitingUser = await this.usersService.getUserByUserId(
        joinOrgDto.invitingUserId,
        organization.id,
      );

      // If the inviting user is not found, throw an error
      if (!invitingUser || invitingUser.length === 0) {
        this.logger.warn(
          `Invalid invite: Inviting user ${joinOrgDto.invitingUserId} not found`,
        );
        throw new ForbiddenException("Invalid invite!");
      }

      // Check if the invitation exists
      const invitation =
        await this.organizationInvitationsRepository.findByCondition({
          where: {
            organization: { id: organization.id },
            inviteeEmail: joinOrgDto.inviteeEmail,
            inviter: { id: invitingUser[0].id },
          },
        });

      // If the invitation is not found, throw an error
      if (!invitation) {
        this.logger.warn(
          `Invalid invite: No invitation found for email ${joinOrgDto.inviteeEmail}`,
        );
        throw new ForbiddenException("Invalid invite!");
      }
    } else {
      const { domain } = tldts.parse(currentUser.email);

      this.logger.log(`Checking domain access for ${domain}`);

      // Get all organizations with matching domains
      const orgsWithMatchingDomains =
        await this.organizationDomainsRepository.findAll({
          where: { domain },
          relations: { organization: true },
          select: { organization: { id: true, uid: true } },
        });

      // Get the organizations with matching domains
      const joinableOrganizations = orgsWithMatchingDomains.map(
        (org) => org.organization,
      );

      // If there are no joinable organizations, throw an error
      if (joinableOrganizations.length === 0) {
        this.logger.warn(`No organizations found for domain ${domain}`);
        throw new ForbiddenException("Forbidden");
      }

      // Check if the organization is in the joinable organizations
      const foundOrganization = joinableOrganizations.find(
        (org) => org.uid === organization.uid,
      );

      // If the organization is not in the joinable organizations, throw an error
      if (!foundOrganization) {
        this.logger.warn(
          `Organization ${organization.uid} not found in joinable organizations for domain ${domain}`,
        );
        throw new ForbiddenException("Forbidden");
      }
    }

    try {
      // At this point we know the user can join the above organization
      const userPersona = await this.usersService.createUserPersona({
        email: currentUser.email,
        name: currentUser.email.split("@")?.[0] || currentUser.email,
        organizationId: organization.id,
        authId: currentUser.id,
        userType: UserType.ORG_ADMIN,
      });

      this.logger.log(
        `Created user persona ${userPersona.uid} for user ${currentUser.email}`,
      );

      // Publish the user joined organization event
      const userJoinedOrganizationEvent =
        this.organizationSNSEventsFactory.createUserJoinedOrganizationSNSEvent(
          {
            uid: currentUser.id,
            email: currentUser.email,
            userType: UserType.ORG_ADMIN,
            orgUid: organization.uid,
          },
          organization,
          userPersona,
        );

      await this.publishEventToSNSQueue(
        OrganizationEvents.MEMBER_JOINED,
        userJoinedOrganizationEvent,
        {
          uid: currentUser.id,
          email: currentUser.email,
          userType: UserType.ORG_ADMIN,
          orgUid: organization.uid,
        },
      );

      this.logger.log(
        `Successfully processed join organization request for user ${currentUser.email}`,
      );

      // Return the user persona
      return { userPersona, organization };
    } catch (error) {
      this.logger.error(
        `Failed to process join organization request for user ${currentUser.email}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Creates an invitation for a user to join an organization.
   * @param inviteDto The DTO containing the email of the user to invite.
   * @param user The current user.
   * @returns The invitation.
   */
  async createInvite(inviteDto: InviteUserDto, user: CurrentUser) {
    this.logger.log(
      `Creating invite for email ${inviteDto.email} by user ${user.uid}`,
    );

    // Check if the user is an organization admin
    if (user.userType !== UserType.ORG_ADMIN) {
      this.logger.warn(
        `User ${user.uid} attempted to create invite without admin privileges`,
      );
      throw new ForbiddenException("You are not authorized to invite users!");
    }

    try {
      const invite = await this.organizationInvitationsRepository.save({
        inviteeEmail: inviteDto.email,
        inviter: { id: user.sub },
        organization: { id: user.orgId },
        // TODO: expiresAt: addDays(new Date(), 7),
      });

      this.logger.log(
        `Successfully created invite for email ${inviteDto.email}`,
      );
      return invite;
    } catch (error) {
      this.logger.error(
        `Failed to create invite for email ${inviteDto.email}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Creates a new organization.
   * @param createOrganizationDto The organization data to create.
   * @returns The created organization.
   */
  async create(
    createOrganizationDto: CreateOrganizationDto,
    authToken?: string,
  ): Promise<Organization> {
    let { email } = createOrganizationDto;
    const {
      password,
      name,
      logoUrl = "",
      allowSameDomainJoin,
    } = createOrganizationDto;

    // Generate a unique identifier for the organization
    const identifier = this.generateOrganizationIdentifier();
    const slug = this.generateOrganizationSlug(name);

    // Tracks if we should skip user creation
    let skipUserCreation = false;
    let authId = "";

    // If the email and password are not provided, the user
    // was already created, we'll go with create org flow with this
    // user and treating this user as the org admin
    if (!email && !password && authToken) {
      // Get the token from the auth token
      const [_type, token] = authToken.split(" ") || [];
      if (!token) {
        throw new BadRequestException("Invalid auth token provided!");
      }

      // Get the supabase user
      const supabaseUser = await this.usersService.getSupabaseUser(token);

      // If the supabase user is not found, throw an error
      if (!supabaseUser) {
        throw new BadRequestException(
          "User attached in the auth token is not found in the database!",
        );
      }

      // Set the email and password to the supabase user details
      email = supabaseUser.email.toLowerCase();
      authId = supabaseUser.id;
      skipUserCreation = true;
    } else if (!email && !password) {
      // If the email and password are not provided with no auth token
      throw new BadRequestException(
        "You need to provide user details to create an organization.",
      );
    }

    // Validate the email domain
    const { domain } = tldts.parse(email);
    if (!domain) {
      throw new BadRequestException("Invalid email domain");
    }

    // Get all organizations for the user, just their names
    const userMemberOfOrgs = await this.usersService.findAllOrgsForUserEmail(
      email,
      { name: true },
    );

    // Check if the organization name is already taken
    const conflictWithJoinedOrg = userMemberOfOrgs.find(
      (org) => org.name.toLowerCase() === name.toLowerCase(),
    );

    // If the organization name is already taken, throw an error
    if (conflictWithJoinedOrg) {
      throw new ConflictException(
        "Organization with this name already exists!",
      );
    }

    // Get all organizations with matching domains
    const orgsWithMatchingDomains =
      await this.organizationDomainsRepository.findAll({
        where: { domain },
        relations: { organization: true },
        select: { organization: { id: true, uid: true, name: true } },
      });

    // Get the organizations with matching domains
    const foundOrgs = orgsWithMatchingDomains.map((org) => org.organization);
    const matchedOrg = foundOrgs.find(
      (org) => org.name.toLowerCase() === name.toLowerCase(),
    );

    // If the organization name is already taken, throw an error
    if (matchedOrg) {
      throw new ConflictException(
        "Organization with this name already exists!",
      );
    }

    try {
      // Execute the saga
      const orgInput = {
        identifier,
        email: email.toLowerCase(),
        password,
        logoUrl,
        authId,
        organizationName: name,
        slug,
        allowSameDomainJoin,
      };

      // Execute the saga
      const { organization } = await this.createOrgAndOrgAdminSaga.execute(
        orgInput,
        { createUser: skipUserCreation },
      );

      // Return the organization
      return organization;
    } catch (error) {
      if (error?.code === 6) {
        throw new ConflictException(error?.details);
      }

      throw error;
    }
  }

  /**
   * Updates an existing organization.
   * @param id The ID of the organization to update.
   * @param updateOrganizationDto The organization data to update.
   * @returns The updated organization.
   */
  async update(
    id: string,
    updateOrganizationDto: UpdateOrganizationDto,
    currentUser: CurrentUser,
  ): Promise<Organization> {
    this.logger.log(`Updating organization ${id}`);
    try {
      // Check if the organization exists
      const organization = await this.organizationRepository.findByCondition({
        where: { uid: id },
      });

      const previousOrganization = _.cloneDeep(organization);

      // If the organization was not found, throw an error
      if (_.isEmpty(organization)) {
        this.logger.warn(`Organization ${id} not found`);
        throw new NotFoundException(
          "Organization with provided id was not found!",
        );
      }

      // Update the organization name if provided
      if (updateOrganizationDto.name) {
        this.logger.log(
          `Updating organization name from "${organization.name}" to "${updateOrganizationDto.name}"`,
        );
        organization.name = updateOrganizationDto.name;
        organization.slug = this.generateOrganizationSlug(
          updateOrganizationDto.name,
        );
      }

      // Update the organization logo URL if provided
      if (updateOrganizationDto.logoUrl) {
        this.logger.log("Updating organization logo URL");
        organization.logoUrl = updateOrganizationDto.logoUrl;
      }

      // Update the organization isActive status if provided
      if (updateOrganizationDto.isActive !== undefined) {
        this.logger.log(
          `Updating organization active status to: ${updateOrganizationDto.isActive}`,
        );
        organization.isActive = updateOrganizationDto.isActive;
      }

      // Update the organization allowSameDomainJoin status if provided
      if (updateOrganizationDto.allowSameDomainJoin !== undefined) {
        this.logger.log(
          `Updating organization allowSameDomainJoin status to: ${updateOrganizationDto.allowSameDomainJoin}`,
        );
        organization.allowSameDomainJoin =
          updateOrganizationDto.allowSameDomainJoin;
      }

      // Update the organization
      const updatedOrg = await this.transactionService.runInTransaction(
        async (txnContext) => {
          const updated = await this.organizationRepository.saveWithTxn(
            txnContext,
            organization,
          );

          // Publish the organization updated event
          const organizationUpdatedEvent =
            this.organizationSNSEventsFactory.createOrganizationUpdatedSNSEvent(
              currentUser,
              previousOrganization,
              organization,
            );

          // Publish the organization updated event
          await this.publishEventToSNSQueue(
            OrganizationEvents.UPDATED,
            organizationUpdatedEvent,
            {
              uid: currentUser.uid,
              email: currentUser.email,
              userType: currentUser.userType,
              orgUid: organization.uid,
            },
          );

          return updated;
        },
      );

      // Emit the organization updated event locally
      this.eventEmitter.emit(
        EmittableOrganizationEvents.ORGANIZATION_UPDATED,
        this.organizationEventsFactory.createOrganizationUpdatedEvent(
          organization,
        ),
      );

      this.logger.log(`Successfully updated organization ${id}`);
      return updatedOrg;
    } catch (error) {
      if (error?.code === POSTGRES_ERROR_CODES.INVALID_TEXT_REPRESENTATION) {
        this.logger.warn(`Invalid organization ID format: ${id}`);
        throw new NotFoundException(
          "Organization with provided id was not found!",
        );
      }

      this.logger.error(`Failed to update organization ${id}:`, error);
      throw error;
    }
  }

  /**
   * Removes an organization by ID.
   * @param id The ID of the organization to remove.
   */
  async remove(id: string, currentUser: CurrentUser): Promise<void> {
    this.logger.log(`Removing organization ${id}`);
    try {
      const organization = await this.organizationRepository.findByCondition({
        where: { uid: id },
      });

      // If the organization was not found, throw an error
      if (_.isEmpty(organization)) {
        this.logger.warn(`Organization ${id} not found`);
        throw new NotFoundException(
          "Organization with provided id was not found!",
        );
      }

      // Publish the organization updated event
      const organizationDeletedEvent =
        this.organizationSNSEventsFactory.createOrganizationDeletedSNSEvent(
          currentUser,
          organization,
        );

      await this.publishEventToSNSQueue(
        OrganizationEvents.DELETED,
        organizationDeletedEvent,
        {
          uid: currentUser.uid,
          email: currentUser.email,
          userType: currentUser.userType,
          orgUid: organization.uid,
        },
      );

      await this.organizationRepository.remove(organization);
      this.logger.log(`Successfully removed organization ${id}`);
    } catch (error) {
      if (error?.code === POSTGRES_ERROR_CODES.INVALID_TEXT_REPRESENTATION) {
        this.logger.warn(`Invalid organization ID format: ${id}`);
        throw new NotFoundException(
          "Organization with provided id was not found!",
        );
      }

      this.logger.error(`Failed to remove organization ${id}:`, error);
      throw error;
    }
  }

  async getDomains(orgUid: string) {
    try {
      this.logger.log(`Getting domains for organization ${orgUid}`);
      const domains = await this.organizationRepository.findByCondition({
        where: { uid: orgUid },
        relations: { domains: true },
      });

      return domains?.domains || [];
    } catch (error) {
      this.logger.error(
        `Failed to get domains for organization ${orgUid}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Generates an organization identifier.
   * @returns The generated organization identifier.
   */
  private generateOrganizationIdentifier(): string {
    this.logger.log("Generating organization identifier");
    const identifier = IdGeneratorUtils.generate("E");
    return identifier;
  }

  private generateOrganizationSlug(name: string) {
    this.logger.log(`Generating organization slug for name: ${name}`);
    let slug = name.toLowerCase();

    // Construct the slug's name by replacing special characters
    slug = slug.replace(/ /g, "-"); // Replace spaces with hyphens
    slug = slug.replace(/[^a-z0-9-]/g, ""); // Remove all non-alphanumeric characters except hyphens
    slug = slug.replace(/-+/g, "-"); // Replace multiple hyphens with a single hyphen
    slug = slug.replace(/^-|-$/g, ""); // Remove leading and trailing hyphens

    // If the slug is empty, return the identifier
    if (slug.length === 0) {
      this.logger.warn(`Invalid organization name: ${name}`);
      throw new BadRequestException("Invalid organization name");
    }

    // Create a new nanoid and suffix it to the slug
    const slugSuffix = nanoid(8);
    const finalSlug = `${slug}-${slugSuffix}`;

    this.logger.log(`Generated slug: ${finalSlug}`);
    return finalSlug;
  }
}
