import { Injectable } from '@nestjs/common';
import { CsatTriggerConfig, CsatTriggerConfigRepository, TransactionContext } from '@repo/thena-platform-entities';
import { TriggerConfigDto } from '../dto/csat-trigger-config.dto';

@Injectable()
export class CsatTriggerConfigService {
  constructor(
    private readonly csatTriggerConfigRepository: CsatTriggerConfigRepository,
  ) { }

  /**
   * Creates a trigger configuration
   * @param ruleId - The ID of the rule to create the trigger config for
   * @param configDto - The trigger configuration data
   * @param txn - Transaction context
   * @returns Created trigger config entity
   */
  async createTriggerConfig(
    ruleId: string,
    configDto: TriggerConfigDto,
    txn: TransactionContext
  ): Promise<CsatTriggerConfig> {
    const config = this.csatTriggerConfigRepository.create({
      ruleId,
      triggerType: configDto.triggerType,
      randomPercentage: configDto.randomPercentage,
    });

    return await this.csatTriggerConfigRepository.saveWithTxn(txn, config);
  }

  /**
   * Updates a trigger configuration
   * @param configId - The ID of the config to update
   * @param configDto - The new trigger configuration data
   * @param txn - Transaction context
   * @returns Updated trigger config entity
   */
  async updateTriggerConfig(
    configId: string,
    configDto: TriggerConfigDto,
    txn: TransactionContext
  ): Promise<CsatTriggerConfig> {
    const existingConfig = await this.csatTriggerConfigRepository.findByCondition({
      where: { id: configId },
    });

    if (!existingConfig) {
      throw new Error(`Trigger config with ID ${configId} not found`);
    }

    existingConfig.triggerType = configDto.triggerType;
    existingConfig.randomPercentage = configDto.randomPercentage;

    await this.csatTriggerConfigRepository.saveWithTxn(txn, existingConfig);

    return await this.csatTriggerConfigRepository.saveWithTxn(txn, existingConfig);
  }

  /**
   * Gets a trigger configuration by rule ID
   * @param ruleId - The ID of the rule
   * @returns Trigger config entity or null if not found
   */
  async getTriggerConfigByRuleId(ruleId: string): Promise<CsatTriggerConfig | null> {
    return await this.csatTriggerConfigRepository.findByCondition({
      where: { ruleId },
    });
  }

  /**
   * Maps a trigger config entity to DTO format
   * @param config - The trigger config entity
   * @returns Trigger config DTO
   */
  mapToDto(config: CsatTriggerConfig): TriggerConfigDto {
    return {
      triggerType: config.triggerType as any,
      randomPercentage: config.randomPercentage,
    };
  }
}
