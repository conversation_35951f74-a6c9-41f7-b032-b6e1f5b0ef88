import { Injectable } from '@nestjs/common';
import { ConfigKeys, ConfigService } from '../../../config/config.service';
import { CsatSurveyService } from '../csat-survey/csat-survey.service';
import { EncodingDecodingService } from './encoding-decoding-service';

interface SurveyDetails {
  teamId: bigint | string;
  orgId: bigint | string;
  ticketId: string;
}

interface TicketDetails {
  ticketUid: string;
  ticketTitle: string;
  createdAt: string;
  closedAt: string;
}

interface CsatFeedbackConfig {
  customTitle?: string;
  customMessage?: string;
  feedbackType?: 'star' | 'thumbs';
  includeCommentField?: boolean;
  commentFieldLabel?: string;
  commentFieldPlaceholder?: string;
  brandingColor?: string;
  customThankYouMessage?: string;
}

interface SlackMessage {
  blocks: any[];
  text?: string;
}

@Injectable()
export class CsatTemplateService {
  constructor(
    private readonly encodingDecodingService: EncodingDecodingService,
    private readonly csatSurveyService: CsatSurveyService,
    private readonly configService: ConfigService
  ) { }

  generateSlackBlocksForNewSurvey(
    details: SurveyDetails,
    ticketDetails: TicketDetails,
    config: CsatFeedbackConfig,
    user: string
  ): SlackMessage {
    const encodedDetails = this.encodingDecodingService.encrypt(details);
    const webUrl = `${this.configService.get(ConfigKeys.BASE_URL)}/csat-feedback`;
    return {
      blocks: [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `<@${user}> ${config.customTitle || `Your ticket *#${ticketDetails.ticketUid}* has been closed. We'd love to hear your feedback!`}`,
          },
        },
        {
          type: "actions",
          elements: [
            {
              type: "button",
              text: {
                type: "plain_text",
                text: "Share Feedback",
              },
              url: `${webUrl}/${encodedDetails}`,
              action_id: "ack-no-op",
            },
          ],
        },
      ],
    };
  }
}
