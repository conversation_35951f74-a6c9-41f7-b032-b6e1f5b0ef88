import { Injectable, Logger } from '@nestjs/common';
import { email } from '@repo/shared-proto';
import { CsatDeliveryStatus, CsatMappingStatus, CsatSurvey, CsatSurveyRepository, Ticket } from '@repo/thena-platform-entities';
import { In, Not } from 'typeorm';
import { EmailGrpcClient } from '../../../common/grpc/email.client.grpc';
import { SharedService } from '../../../shared/shared.service';
import { CsatSettingsService } from '../csat-settings.service';
@Injectable()
export class CsatSurveyService {
  private readonly logger = new Logger(CsatSurveyService.name);
  constructor(
    private readonly csatSurveyRepository: CsatSurveyRepository,
    private readonly csatSettingsService: CsatSettingsService,
    private readonly emailGrpcClient: EmailGrpcClient,
    private readonly sharedService: SharedService,
  ) { }

  /**
   * Initializes a CSAT survey for a closed ticket
   * @param ticket The ticket to create the survey for
   * @returns The created CSAT survey
   */
  public async initializeCsatSurvey(ticket: Ticket) {
    try {
      const csatSettings = await this.csatSettingsService.getSettingsByTeamId(
        ticket.teamId,
      );

      if (!csatSettings) {
        this.logger.warn(`No CSAT settings found for team ${ticket.teamId}`);
        return;
      }

      const isClosed = ticket.status.name.toLowerCase() === 'closed' || csatSettings.closedStatusIds.includes(ticket.statusId.toString());

      if (!isClosed) {
        return;
      }

      const existingSurvey = await this.csatSurveyRepository.findByCondition({
        where: {
          ticketId: ticket.id,
        },
      });

      if (existingSurvey) {
        return;
      }

      const surveyTicketMapping = new CsatSurvey();
      surveyTicketMapping.ticketId = ticket.id;
      surveyTicketMapping.teamId = BigInt(ticket.teamId);
      surveyTicketMapping.organizationId = BigInt(ticket.organizationId);
      surveyTicketMapping.ticketClosedAt = ticket.updatedAt;
      surveyTicketMapping.mappingStatus = CsatMappingStatus.CREATED;
      surveyTicketMapping.ticketMetadata = {};
      surveyTicketMapping.ticketTitle = ticket.title || null;
      surveyTicketMapping.requestorEmail = ticket.requestorEmail || null;

      const createdSurvey = await this.csatSurveyRepository.save(
        surveyTicketMapping,
      );

      return createdSurvey;
    } catch (error) {
      this.logger.error(
        `Error initializing CSAT survey for ticket ${ticket.uid}: ${error.message}`,
        error?.stack,
      );
    }
  }

  /**
   * Gets all pending CSAT ticket mappings
   * @returns Array of pending CsatTicketMapping entities
   */
  public async getPendingCsatSurveys(): Promise<CsatSurvey[]> {
    try {
      return await this.csatSurveyRepository.findAll({
        where: {
          mappingStatus: Not(In([CsatMappingStatus.PROCESSED])),
        },
      });
    } catch (error) {
      this.logger.error("Error fetching pending CSAT surveys:", error);
      throw error;
    }
  }

  /**
   * Submits a CSAT survey
   * @param survey The CSAT survey to submit
   */
  public async submitSurvey(survey: CsatSurvey) {
    try {
      survey.deliveryStatus = CsatDeliveryStatus.SENT;
      await this.csatSurveyRepository.save(survey);
    } catch (error) {
      this.logger.error("Error submitting CSAT survey:", error);
      throw error;
    }
  }

  /**
   * Gets a CSAT survey by ticket ID
   * @param ticketId The ID of the ticket to get the survey for
   * @returns The CSAT survey
   */
  public async getSurveyByTicketId(ticketId: string) {
    return await this.csatSurveyRepository.findByCondition({
      where: {
        ticketId: ticketId,
      },
    });
  }

  /**
   * Sends an email
   * @param email The email to send
   */
  public async sendAnEmail({
    from,
    to,
    subject,
    htmlBody,
    textBody = "",
    cc = "",
    bcc = "",
  }: {
    from: string;
    to: string;
    subject: string;
    htmlBody: string;
    textBody?: string;
    cc?: string;
    bcc?: string;
  }) {
    try {
      const response = await this.emailGrpcClient.sendEmail({
        from,
        to,
        subject,
        htmlBody,
        textBody: textBody || "",
        cc: cc || "",
        bcc: bcc || "",
      });

      if (response.errorCode !== 0) {
        throw new Error(response.message);
      }

      return response;
    } catch (error) {
      this.logger.error("Error sending email:", error);
      throw error;
    }
  }

  /**
   * Gets an email config by ID
   * @param emailConfig The email config to get
   * @returns The email config
   */
  public async getEmailConfigById(
    emailConfig: email.GetEmailConfigByIdRequest,
  ) {
    try {
      return await this.emailGrpcClient.getEmailConfigById(emailConfig);
    } catch (error) {
      this.logger.error("Error getting email config:", error);
      throw error;
    }
  }
}
