import { Injectable, NotFoundException } from '@nestjs/common';
import { CsatAuditAction, CsatSettingsRepository, TicketStatus, TransactionContext } from '@repo/thena-platform-entities';
import { CreateCsatRuleDto } from '../dto/create-csat-rule.dto';
import { CsatSettingsResponseDto } from '../dto/csat-settings-response.dto';
import { UpdateCsatSettingsDto } from '../dto/update-csat-settings.dto';
import { organizationToDto, teamToDto, toIdString } from '../utils/helper';
import { CsatAuditLogService } from './csat-audit-logs.service';
import { CsatRuleService } from './csat-rule.service';
@Injectable()
export class CsatSettingsService {
  constructor(
    private readonly csatSettingsRepository: CsatSettingsRepository,
    private readonly csatRuleService: CsatRuleService,
    private readonly csatAuditLogService: CsatAuditLogService,
  ) { }

  /**
   * Updates the csat setting for a team
   * @param teamId - The ID of the team
   * @param cooldownPeriodDays - The new cooldown period in days
   * * @param  emailConfigId- The id of emailconfig 
   * @param userCooldownPeriodDays - The new user cooldown period in days
   * @param closedStatusIds - The ids of the closed statuses
   * @returns The updated settings with all rules
   */
  async updateCsatSetting(updateCsatSettingDto: UpdateCsatSettingsDto, teamId: bigint | string, orgId: bigint | string): Promise<CsatSettingsResponseDto> {
    const teamIdString = toIdString(teamId);
    const orgIdString = toIdString(orgId);
    const settings = await this.csatSettingsRepository.findByCondition({
      where: { team: { id: teamIdString }, organization: { id: orgIdString } },
    });

    if (!settings) {
      const newSettings = this.csatSettingsRepository.create({
        team: { id: teamIdString },
        organization: { id: orgIdString },
        cooldownPeriodDays: updateCsatSettingDto.cooldownPeriodDays,
        userCooldownPeriodDays: updateCsatSettingDto.userCooldownPeriodDays,
        closedStatuses: updateCsatSettingDto.closedStatusIds.map(id => ({ id: Number(id) } as unknown as TicketStatus)),
        emailConfigId: updateCsatSettingDto.emailConfigId,
      });
      const savedSettings = await this.csatSettingsRepository.save(newSettings);
      return this.getSettingsById(savedSettings.id);
    }

    settings.cooldownPeriodDays = updateCsatSettingDto.cooldownPeriodDays;
    settings.emailConfigId = updateCsatSettingDto.emailConfigId;
    settings.userCooldownPeriodDays = updateCsatSettingDto.userCooldownPeriodDays;
    settings.closedStatuses = [];
    await this.csatSettingsRepository.save(settings);
    if (updateCsatSettingDto.closedStatusIds && updateCsatSettingDto.closedStatusIds.length > 0) {
      settings.closedStatuses = updateCsatSettingDto.closedStatusIds.map(id => ({ id: Number(id) } as unknown as TicketStatus));
    }

    const updatedSettings = await this.csatSettingsRepository.save(settings);

    return this.getSettingsById(updatedSettings.id);
  }

  /**
   * Creates or adds a rule to existing CSAT settings
   * @param teamId - The ID of the team
   * @param ruleDto - The rule creation data
   * @param userId - The ID of the user creating the settings/rule
   * @param orgId - The ID of the organization
   * @param txn - Transaction context
   * @returns Created or updated settings with all rules
   */
  async createOrUpdateRule(
    teamId: bigint | string,
    ruleDto: CreateCsatRuleDto,
    userId: string,
    orgId: string,
    txn: TransactionContext
  ): Promise<CsatSettingsResponseDto> {
    const teamIdString = toIdString(teamId);
    const orgIdString = toIdString(orgId);

    // Find existing settings with relations
    let settings = await this.csatSettingsRepository.findByCondition({
      where: {
        team: { id: teamIdString },
        organization: { id: orgIdString },
      },
      relations: ['team', 'organization'],
    });

    if (settings) {
      // Create rule for existing settings
      await this.csatRuleService.createRule(settings.id, ruleDto, userId, txn);

      // Instead of looking up from database again, construct the response using the data we have
      const rules = await this.csatRuleService.getRulesByCsatSettingsId(settings.id);

      return {
        id: settings.id,
        team: teamToDto(settings.team),
        organization: organizationToDto(settings.organization),
        isEnabled: settings.isEnabled,
        cooldownPeriodDays: settings.cooldownPeriodDays,
        rules: rules,
        createdAt: settings.createdAt.toISOString(),
        updatedAt: settings.updatedAt.toISOString(),
        userCooldownPeriodDays: settings.userCooldownPeriodDays,
        closedStatusIds: settings.closedStatuses?.map(status => status.id.toString()),
      };
    }

    // Create new settings (always enabled)
    settings = this.csatSettingsRepository.create({
      team: { id: teamIdString },
      organization: { id: orgIdString },
      isEnabled: true,
    });

    // Save with transaction
    const savedSettings = await this.csatSettingsRepository.saveWithTxn(txn, settings);

    // Log the creation
    const auditLogData = {
      teamId: teamId,
      organizationId: orgId,
      entityType: 'csat_settings',
      entityId: savedSettings.id,
      action: CsatAuditAction.CREATED,
      previousState: {},
      newState: {
        teamId: teamId.toString(),
        organizationId: orgId,
        isEnabled: true,
      },
      metadata: {},
    };
    await this.csatAuditLogService.logAudit(auditLogData, txn);

    // Create rule for new settings
    const createdRule = await this.csatRuleService.createRule(savedSettings.id, ruleDto, userId, txn);

    // Construct the response using saved settings and created rule
    // This avoids the need to query from database again
    return {
      id: savedSettings.id,
      team: {
        id: teamIdString,
      },
      organization: {
        id: orgIdString,
      },
      isEnabled: savedSettings.isEnabled,
      cooldownPeriodDays: savedSettings.cooldownPeriodDays,
      rules: [createdRule],
      createdAt: savedSettings.createdAt.toISOString(),
      updatedAt: savedSettings.updatedAt.toISOString(),
      userCooldownPeriodDays: savedSettings.userCooldownPeriodDays,
      closedStatusIds: [],
    };
  }

  /**
   * Gets CSAT settings by team ID and organization ID with all rules
   * @param teamId - The ID of the team
   * @param organizationId - The ID of the organization
   * @returns Settings with all rules
   */
  async getSettingsByTeamAndOrgId(teamId: bigint | string, organizationId: bigint | string): Promise<CsatSettingsResponseDto> {
    // Convert to string IDs for entity references
    const teamIdString = toIdString(teamId);
    const orgIdString = toIdString(organizationId);

    // Use findByCondition from the base repository with both teamId and organizationId
    const settings = await this.csatSettingsRepository.findByCondition({
      where: {
        team: { id: teamIdString },
        organization: { id: orgIdString },
      },
      relations: ['team', 'organization'],
    });

    if (!settings) {
      throw new NotFoundException(`CSAT settings for team with ID ${teamId} in organization ${organizationId} not found`);
    }

    return this.getSettingsById(settings.id);
  }

  /**
   * Gets CSAT settings by team ID with all rules
   * @param teamId - The ID of the team
   * @returns Settings with all rules
   */
  async getSettingsByTeamId(teamId: bigint | string): Promise<CsatSettingsResponseDto> {
    // Convert to string ID for entity reference
    const teamIdString = toIdString(teamId);

    // Use findByCondition from the base repository
    const settings = await this.csatSettingsRepository.findByCondition({
      where: { team: { id: teamIdString } },
      relations: ['team', 'organization'],
    });

    if (!settings) {
      throw new NotFoundException(`CSAT settings for team with ID ${teamId} not found`);
    }

    return this.getSettingsById(settings.id);
  }

  /**
   * Gets CSAT settings by ID with all rules
   * @param id - The ID of the settings to retrieve
   * @returns Settings with all rules
   */
  async getSettingsById(id: string): Promise<CsatSettingsResponseDto> {

    const settings = await this.csatSettingsRepository.findByCondition({
      where: { id },
      relations: ['team', 'organization', 'closedStatuses'],
    });

    if (!settings) {
      throw new NotFoundException(`CSAT settings with ID ${id} not found`);
    }

    // Get all rules for these settings`
    const rules = await this.csatRuleService.getRulesByCsatSettingsId(id);

    const response = {
      id: settings.id,
      cooldownPeriodDays: settings.cooldownPeriodDays,
      team: teamToDto(settings.team),
      emailConfigId: settings.emailConfigId,
      organization: organizationToDto(settings.organization),
      isEnabled: settings.isEnabled,
      rules,
      createdAt: settings.createdAt.toISOString(),
      updatedAt: settings.updatedAt.toISOString(),
      userCooldownPeriodDays: settings.userCooldownPeriodDays,
      closedStatusIds: settings.closedStatuses.map(status => status.id.toString()),
    };

    return response;
  }

  /**
   * Reorders the priority of CSAT rules for a team
   * @param teamId - The ID of the team
   * @param reorderRules - An array of objects containing ruleId and priority
   * @param txn - Transaction context
   * @returns The updated settings with reordered rules
   */
  async reorderRules(
    teamId: string | bigint,
    reorderRules: Array<{ ruleId: string; priority: number }>,
    txn: TransactionContext
  ): Promise<CsatSettingsResponseDto> {
    const existingSettings = await this.csatSettingsRepository.findByCondition({
      where: { team: { id: toIdString(teamId) } },
      relations: ['organization'],
    });

    if (!existingSettings) {
      throw new NotFoundException(`CSAT settings for team with ID ${teamId} not found`);
    }

    await this.csatRuleService.reorderRules(existingSettings.id, reorderRules, txn);

    return await this.getSettingsById(existingSettings.id);
  }
}
