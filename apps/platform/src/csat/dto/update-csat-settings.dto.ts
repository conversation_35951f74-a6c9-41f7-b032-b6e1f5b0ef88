import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>A<PERSON><PERSON>, IsNumber, IsString } from 'class-validator';

export class UpdateCsatSettingsDto {
  @ApiProperty({
    description: 'Cooldown period in days',
    example: 14,
  })
  @IsNumber()
  cooldownPeriodDays: number;

  @ApiProperty({
    description: 'The Email Config Id associated with the CSAT settings',
    type: 'string',
  })
  @IsString()
  emailConfigId: string;

  @ApiProperty({
    description: 'The user cooldown period in days',
    example: 14,
  })
  @IsNumber()
  userCooldownPeriodDays: number;

  @ApiProperty({
    description: 'The closed statuses associated with the CSAT settings',
    type: 'string',
    isArray: true,
  })
  @IsArray()
  @IsString({ each: true })
  closedStatusIds: string[];
}
