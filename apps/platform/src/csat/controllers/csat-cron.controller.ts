import { Controller, Get } from '@nestjs/common';
import { Public } from '../../auth/decorators/auth.decorator';
import { CsatCronService } from '../services/csat-cron/cron.service';
@Public()
@Controller('csat-cron')
export class CsatCronController {
  constructor(
    private readonly csatCronService: CsatCronService,
  ) { }

  @Get()
  async triggerCron(): Promise<void> {
    await this.csatCronService.handleCsatProcessing();
  }
}
