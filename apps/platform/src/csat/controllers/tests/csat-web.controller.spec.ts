import { Test, TestingModule } from '@nestjs/testing';
import {
  CsatDeliveryChannel,
  CsatDeliveryStatus,
  CsatFeedbackType,
  CsatMappingStatus,
  CsatResponseStatus,
  CsatSamplingStatus,
  Organization,
  Team,
} from '@repo/thena-platform-entities';
import { CsatSurveyService } from '../../services/csat-survey/csat-survey.service';
import { EncodingDecodingService } from '../../services/csat-template/encoding-decoding-service';
import { CsatWebController } from '../csat-web.controller';

describe('CsatWebController', () => {
  let controller: CsatWebController;
  let encodingDecodingService: jest.Mocked<EncodingDecodingService>;
  let csatSurveyService: jest.Mocked<CsatSurveyService>;

  beforeEach(async () => {
    const mockEncodingDecodingService = {
      decrypt: jest.fn(),
    };

    const mockCsatSurveyService = {
      getSurveyByTicketId: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [CsatWebController],
      providers: [
        {
          provide: EncodingDecodingService,
          useValue: mockEncodingDecodingService,
        },
        {
          provide: CsatSurveyService,
          useValue: mockCsatSurveyService,
        },
      ],
    }).compile();

    controller = module.get<CsatWebController>(CsatWebController);
    encodingDecodingService = module.get(EncodingDecodingService);
    csatSurveyService = module.get(CsatSurveyService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getSurveyPage', () => {
    const mockEncodedDetails = 'encoded-details';
    const mockDecodedDetails = { ticketId: 'ticket123' };

    it('should return error page when survey is not found', async () => {
      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(null);

      const result = await controller.getSurveyPage(mockEncodedDetails);

      expect(result).toEqual({
        isError: true,
        isThankYou: false,
        isForm: false,
        title: 'Survey Not Found',
        message: 'The survey you are looking for does not exist or has expired.',
      });
    });

    it('should return thank you page for completed survey', async () => {
      const mockSurvey = {
        id: 'survey123',
        responseStatus: CsatResponseStatus.COMPLETED,
        ruleConfig: {
          feedbackConfig: {
            customThankYouMessage: 'Thank you for your feedback!',
          },
        },
        surveyConfig: {},
        team: {} as Team,
        teamId: BigInt(1),
        organization: {} as Organization,
        organizationId: BigInt(1),
        ticketId: 'ticket123',
        ticketTitle: 'Test Ticket',
        ticketClosedAt: new Date(),
        ticketMetadata: {},
        mappingStatus: CsatMappingStatus.CREATED,
        samplingStatus: CsatSamplingStatus.PENDING,
        deliveryStatus: CsatDeliveryStatus.NOT_APPLICABLE,
        surveyToken: 'token123',
        requestorEmail: '<EMAIL>',
        deliveryChannel: CsatDeliveryChannel.EMAIL,
        deliveryDetails: {},
        responseMetadata: {},
        hasResponse: false,
        hasComment: false,
        attemptNumber: 0,
        followUpCount: 0,
        previousAttempts: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(mockSurvey);

      const result = await controller.getSurveyPage(mockEncodedDetails);

      expect(result).toEqual({
        isError: false,
        isThankYou: true,
        isForm: false,
        title: 'Thank You!',
        message: 'Thank you for your feedback!',
      });
    });

    it('should return form page for pending survey', async () => {
      const mockSurvey = {
        id: 'survey123',
        responseStatus: CsatResponseStatus.NO_RESPONSE,
        ruleConfig: {
          feedbackConfig: {
            customTitle: 'How was your experience?',
            customMessage: 'Please share your feedback',
            feedbackType: CsatFeedbackType.STAR,
            includeCommentField: true,
            commentFieldLabel: 'Additional comments',
            commentFieldPlaceholder: 'Share your thoughts...',
            brandingColor: '#FF0000',
          },
        },
        surveyConfig: {},
        team: {} as Team,
        teamId: BigInt(1),
        organization: {} as Organization,
        organizationId: BigInt(1),
        ticketId: 'ticket123',
        ticketTitle: 'Test Ticket',
        ticketClosedAt: new Date(),
        ticketMetadata: {},
        mappingStatus: CsatMappingStatus.CREATED,
        samplingStatus: CsatSamplingStatus.PENDING,
        deliveryStatus: CsatDeliveryStatus.NOT_APPLICABLE,
        surveyToken: 'token123',
        requestorEmail: '<EMAIL>',
        deliveryChannel: CsatDeliveryChannel.EMAIL,
        deliveryDetails: {},
        responseMetadata: {},
        hasResponse: false,
        hasComment: false,
        attemptNumber: 0,
        followUpCount: 0,
        previousAttempts: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(mockSurvey);

      const result = await controller.getSurveyPage(mockEncodedDetails);

      expect(result).toEqual({
        isError: false,
        isThankYou: false,
        isForm: true,
        title: 'How was your experience?',
        message: 'Please share your feedback',
        feedbackType: 'star',
        includeComment: true,
        commentLabel: 'Additional comments',
        commentPlaceholder: 'Share your thoughts...',
        brandingColor: '#FF0000',
        encodedDetails: mockEncodedDetails,
        apiUrl: `/csat/submissions?details=${mockEncodedDetails}`,
      });
    });

    it('should return default values when feedback config is not provided', async () => {
      const mockSurvey = {
        id: 'survey123',
        responseStatus: CsatResponseStatus.NO_RESPONSE,
        ruleConfig: {
          feedbackConfig: {},
        },
        surveyConfig: {},
        team: {} as Team,
        teamId: BigInt(1),
        organization: {} as Organization,
        organizationId: BigInt(1),
        ticketId: 'ticket123',
        ticketTitle: 'Test Ticket',
        ticketClosedAt: new Date(),
        ticketMetadata: {},
        mappingStatus: CsatMappingStatus.CREATED,
        samplingStatus: CsatSamplingStatus.PENDING,
        deliveryStatus: CsatDeliveryStatus.NOT_APPLICABLE,
        surveyToken: 'token123',
        requestorEmail: '<EMAIL>',
        deliveryChannel: CsatDeliveryChannel.EMAIL,
        deliveryDetails: {},
        responseMetadata: {},
        hasResponse: false,
        hasComment: false,
        attemptNumber: 0,
        followUpCount: 0,
        previousAttempts: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(mockSurvey);

      const result = await controller.getSurveyPage(mockEncodedDetails);

      expect(result).toEqual({
        isError: false,
        isThankYou: false,
        isForm: true,
        title: 'How was your experience?',
        message: 'Please let us know how we did with your recent support request.',
        feedbackType: 'star',
        includeComment: false,
        commentLabel: 'Any additional comments?',
        commentPlaceholder: 'Tell us more about your experience...',
        brandingColor: '#0284c7',
        encodedDetails: mockEncodedDetails,
        apiUrl: `/csat/submissions?details=${mockEncodedDetails}`,
      });
    });

    it('should handle decryption errors', async () => {
      encodingDecodingService.decrypt.mockImplementation(() => {
        throw new Error('Decryption failed');
      });

      const result = await controller.getSurveyPage(mockEncodedDetails);

      expect(result).toEqual({
        isError: true,
        isThankYou: false,
        isForm: false,
        title: 'Error',
        message: 'An error occurred while processing your feedback request. Please try again later.',
      });
    });
  });

  describe('Additional edge cases', () => {
    const mockEncodedDetails = 'encoded-details';
    const mockDecodedDetails = { ticketId: 'ticket123' };

    it('should handle malformed encoded details', async () => {
      const malformedEncoded = 'invalid-base64-@#$%';

      encodingDecodingService.decrypt.mockImplementation(() => {
        throw new Error('Invalid encoding');
      });

      const result = await controller.getSurveyPage(malformedEncoded);

      expect(result.isError).toBe(true);
      expect(result.title).toBe('Error');
    });

    it('should handle missing ticketId in decoded details', async () => {
      encodingDecodingService.decrypt.mockReturnValue({
        // Missing ticketId
        teamId: 'team123',
        orgId: 'org123',
      });

      const result = await controller.getSurveyPage(mockEncodedDetails);

      expect(result.isError).toBe(true);
    });

    it('should handle survey with missing ruleConfig', async () => {
      const mockSurvey = {
        id: 'survey123',
        responseStatus: CsatResponseStatus.NO_RESPONSE,
        ruleConfig: null, // Missing config
        surveyConfig: {},
        team: {} as Team,
        teamId: BigInt(1),
        organization: {} as Organization,
        organizationId: BigInt(1),
        ticketId: 'ticket123',
        ticketTitle: 'Test Ticket',
        ticketClosedAt: new Date(),
        ticketMetadata: {},
        mappingStatus: CsatMappingStatus.CREATED,
        samplingStatus: CsatSamplingStatus.PENDING,
        deliveryStatus: CsatDeliveryStatus.NOT_APPLICABLE,
        surveyToken: 'token123',
        requestorEmail: '<EMAIL>',
        deliveryChannel: CsatDeliveryChannel.EMAIL,
        deliveryDetails: {},
        responseMetadata: {},
        hasResponse: false,
        hasComment: false,
        attemptNumber: 0,
        followUpCount: 0,
        previousAttempts: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(mockSurvey);

      const result = await controller.getSurveyPage(mockEncodedDetails);

      // Should still work with default values
      expect(result.isForm).toBe(true);
      expect(result.title).toBe('How was your experience?');
      expect(result.feedbackType).toBe('star');
    });

    it('should handle expired surveys', async () => {
      const expiredSurvey = {
        id: 'survey123',
        responseStatus: CsatResponseStatus.EXPIRED, // If you have this status
        ruleConfig: {
          feedbackConfig: {},
        },
        surveyConfig: {},
        team: {} as Team,
        teamId: BigInt(1),
        organization: {} as Organization,
        organizationId: BigInt(1),
        ticketId: 'ticket123',
        ticketTitle: 'Test Ticket',
        ticketClosedAt: new Date(),
        ticketMetadata: {},
        mappingStatus: CsatMappingStatus.CREATED,
        samplingStatus: CsatSamplingStatus.PENDING,
        deliveryStatus: CsatDeliveryStatus.NOT_APPLICABLE,
        surveyToken: 'token123',
        requestorEmail: '<EMAIL>',
        deliveryChannel: CsatDeliveryChannel.EMAIL,
        deliveryDetails: {},
        responseMetadata: {},
        hasResponse: false,
        hasComment: false,
        attemptNumber: 0,
        followUpCount: 0,
        previousAttempts: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        expiresAt: new Date('2020-01-01'), // Expired
      };

      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(expiredSurvey);

      const result = await controller.getSurveyPage(mockEncodedDetails);

      // Depending on your business logic, this might show an error or the form
      expect(result).toBeDefined();
    });

    it('should handle special characters in feedback config', async () => {
      const mockSurvey = {
        id: 'survey123',
        responseStatus: CsatResponseStatus.NO_RESPONSE,
        ruleConfig: {
          feedbackConfig: {
            customTitle: 'How\'s your experience? 😊',
            customMessage: 'Please rate & share your "feedback"',
            commentFieldLabel: '<script>alert("XSS")</script>', // Should be escaped in view
            brandingColor: 'rgb(255, 0, 0)', // Different color format
          },
        },
        surveyConfig: {},
        team: {} as Team,
        teamId: BigInt(1),
        organization: {} as Organization,
        organizationId: BigInt(1),
        ticketId: 'ticket123',
        ticketTitle: 'Test Ticket',
        ticketClosedAt: new Date(),
        ticketMetadata: {},
        mappingStatus: CsatMappingStatus.CREATED,
        samplingStatus: CsatSamplingStatus.PENDING,
        deliveryStatus: CsatDeliveryStatus.NOT_APPLICABLE,
        surveyToken: 'token123',
        requestorEmail: '<EMAIL>',
        deliveryChannel: CsatDeliveryChannel.EMAIL,
        deliveryDetails: {},
        responseMetadata: {},
        hasResponse: false,
        hasComment: false,
        attemptNumber: 0,
        followUpCount: 0,
        previousAttempts: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(mockSurvey);

      const result = await controller.getSurveyPage(mockEncodedDetails);

      expect(result.title).toBe('How\'s your experience? 😊');
      expect(result.commentLabel).toContain('script'); // The view should handle escaping
      expect(result.brandingColor).toBe('rgb(255, 0, 0)');
    });

    it('should handle database connection errors', async () => {
      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockRejectedValue(
        new Error('Database connection failed')
      );

      const result = await controller.getSurveyPage(mockEncodedDetails);

      expect(result.isError).toBe(true);
      expect(result.message).toContain('error occurred');
    });

    it('should handle survey with partial response', async () => {
      const mockSurvey = {
        id: 'survey123',
        responseStatus: CsatResponseStatus.PARTIAL, // If you have this status
        ruleConfig: {
          feedbackConfig: {
            customTitle: 'How was your experience?',
            feedbackType: CsatFeedbackType.STAR,
          },
        },
        surveyConfig: {},
        team: {} as Team,
        teamId: BigInt(1),
        organization: {} as Organization,
        organizationId: BigInt(1),
        ticketId: 'ticket123',
        ticketTitle: 'Test Ticket',
        ticketClosedAt: new Date(),
        ticketMetadata: {},
        mapplingStatus: CsatMappingStatus.CREATED,
        samplingStatus: CsatSamplingStatus.PENDING,
        deliveryStatus: CsatDeliveryStatus.NOT_APPLICABLE,
        surveyToken: 'token123',
        requestorEmail: '<EMAIL>',
        deliveryChannel: CsatDeliveryChannel.EMAIL,
        deliveryDetails: {},
        responseMetadata: {},
        hasResponse: false,
        hasComment: false,
        attemptNumber: 0,
        followUpCount: 0,
        previousAttempts: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockResolvedValue({ ...mockSurvey, mappingStatus: CsatMappingStatus.CREATED });

      const result = await controller.getSurveyPage(mockEncodedDetails);

      // Should allow resuming the survey
      expect(result.isForm).toBe(true);
    });

    it('should handle surveys with viewed but not completed status', async () => {
      const mockSurvey = {
        id: 'survey123',
        responseStatus: CsatResponseStatus.VIEWED, // If you have this status
        viewedAt: new Date(),
        ruleConfig: {
          feedbackConfig: {
            customTitle: 'How was your experience?',
          },
        },
        surveyConfig: {},
        team: {} as Team,
        teamId: BigInt(1),
        organization: {} as Organization,
        organizationId: BigInt(1),
        ticketId: 'ticket123',
        ticketTitle: 'Test Ticket',
        ticketClosedAt: new Date(),
        ticketMetadata: {},
        mappingStatus: CsatMappingStatus.CREATED,
        samplingStatus: CsatSamplingStatus.PENDING,
        deliveryStatus: CsatDeliveryStatus.NOT_APPLICABLE,
        surveyToken: 'token123',
        requestorEmail: '<EMAIL>',
        deliveryChannel: CsatDeliveryChannel.EMAIL,
        deliveryDetails: {},
        responseMetadata: {},
        hasResponse: false,
        hasComment: false,
        attemptNumber: 0,
        followUpCount: 0,
        previousAttempts: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(mockSurvey);

      const result = await controller.getSurveyPage(mockEncodedDetails);

      // Should still show the form
      expect(result.isForm).toBe(true);
    });
  });
});
