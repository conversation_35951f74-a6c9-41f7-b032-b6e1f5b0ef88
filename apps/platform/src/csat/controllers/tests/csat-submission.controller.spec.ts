import { Test, TestingModule } from '@nestjs/testing';
import { CsatDeliveryChannel, CsatDeliveryStatus, CsatFeedbackType, CsatMappingStatus, CsatResponseStatus, CsatSamplingStatus, Organization, Team } from '@repo/thena-platform-entities';
import { CsatSurveyService } from '../../services/csat-survey/csat-survey.service';
import { EncodingDecodingService } from '../../services/csat-template/encoding-decoding-service';
import { CsatSubmissionController } from '../csat-submission.controller';

describe('CsatSubmissionController', () => {
  let controller: CsatSubmissionController;
  let encodingDecodingService: jest.Mocked<EncodingDecodingService>;
  let csatSurveyService: jest.Mocked<CsatSurveyService>;

  beforeEach(async () => {
    const mockEncodingDecodingService = {
      decrypt: jest.fn(),
    };

    const mockCsatSurveyService = {
      getSurveyByTicketId: jest.fn(),
      submitSurvey: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [CsatSubmissionController],
      providers: [
        {
          provide: EncodingDecodingService,
          useValue: mockEncodingDecodingService,
        },
        {
          provide: CsatSurveyService,
          useValue: mockCsatSurveyService,
        },
      ],
    }).compile();

    controller = module.get<CsatSubmissionController>(CsatSubmissionController);
    encodingDecodingService = module.get(EncodingDecodingService);
    csatSurveyService = module.get(CsatSurveyService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getSubmissions', () => {
    const mockEncodedDetails = 'encoded-details';
    const mockDecodedDetails = { ticketId: 'ticket123' };

    // Function to create a fresh mock survey for each test
    const createMockSurvey = () => ({
      id: 'survey123',
      responseStatus: CsatResponseStatus.NO_RESPONSE,
      feedbackType: null,
      surveyConfig: null,
      commentText: null,
      ratingValue: null,
      ruleConfig: null,
      team: {} as Team,
      teamId: BigInt(1),
      organization: {} as Organization,
      organizationId: BigInt(1),
      ticketId: 'ticket123',
      ticketTitle: 'Test Ticket',
      ticketClosedAt: new Date(),
      ticketMetadata: {},
      mappingStatus: CsatMappingStatus.CREATED,
      samplingStatus: CsatSamplingStatus.PENDING,
      deliveryStatus: CsatDeliveryStatus.NOT_APPLICABLE,
      surveyToken: 'token123',
      requestorEmail: '<EMAIL>',
      deliveryChannel: CsatDeliveryChannel.EMAIL,
      deliveryDetails: {},
      responseMetadata: {},
      hasResponse: false,
      hasComment: false,
      attemptNumber: 0,
      followUpCount: 0,
      previousAttempts: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    it('should handle star rating submission', async () => {
      const submission = {
        rating: 5,
        ratingType: 'star' as const,
        comment: 'Great service!',
      };

      const mockSurvey = createMockSurvey();
      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(mockSurvey);
      csatSurveyService.submitSurvey.mockResolvedValue(undefined);

      const result = await controller.getSubmissions(mockEncodedDetails, submission);

      expect(encodingDecodingService.decrypt).toHaveBeenCalledWith(mockEncodedDetails);
      expect(csatSurveyService.getSurveyByTicketId).toHaveBeenCalledWith(mockDecodedDetails.ticketId);
      expect(csatSurveyService.submitSurvey).toHaveBeenCalledWith({
        ...mockSurvey,
        feedbackType: CsatFeedbackType.STAR,
        surveyConfig: { ratingScale: 5 },
        commentText: 'Great service!',
        ratingValue: 5,
        responseStatus: CsatResponseStatus.COMPLETED,
      });
      expect(result).toEqual({
        success: true,
        message: `Submission received successfully for ticket ${mockDecodedDetails.ticketId}`,
        decodedDetails: mockDecodedDetails,
        submission,
      });
    });

    it('should handle thumbs up submission', async () => {
      const submission = {
        rating: 'up' as const,
        ratingType: 'thumbs' as const,
        comment: 'Good experience',
      };

      const mockSurvey = createMockSurvey();
      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(mockSurvey);
      csatSurveyService.submitSurvey.mockResolvedValue(undefined);

      const result = await controller.getSubmissions(mockEncodedDetails, submission);

      expect(csatSurveyService.submitSurvey).toHaveBeenCalledWith({
        ...mockSurvey,
        feedbackType: CsatFeedbackType.THUMBS,
        surveyConfig: { ratingScale: 1 },
        commentText: 'Good experience',
        ratingValue: 1,
        responseStatus: CsatResponseStatus.COMPLETED,
      });
      expect(result.success).toBe(true);
    });

    it('should handle already completed survey', async () => {
      const submission = {
        rating: 5,
        ratingType: 'star' as const,
      };

      const mockSurvey = createMockSurvey();
      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockResolvedValue({
        ...mockSurvey,
        responseStatus: CsatResponseStatus.COMPLETED,
      });

      const result = await controller.getSubmissions(mockEncodedDetails, submission);

      expect(csatSurveyService.submitSurvey).not.toHaveBeenCalled();
      expect(result).toEqual({
        success: false,
        message: `Survey for ticket ${mockDecodedDetails.ticketId} has already been submitted`,
        decodedDetails: mockDecodedDetails,
      });
    });

    it('should handle thumbs down submission', async () => {
      const submission = {
        rating: 'down' as const,
        ratingType: 'thumbs' as const,
      };

      const mockSurvey = createMockSurvey();
      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(mockSurvey);
      csatSurveyService.submitSurvey.mockResolvedValue(undefined);

      const result = await controller.getSubmissions(mockEncodedDetails, submission);

      expect(csatSurveyService.submitSurvey).toHaveBeenCalledWith({
        ...mockSurvey,
        feedbackType: CsatFeedbackType.THUMBS,
        surveyConfig: { ratingScale: 0 },
        commentText: undefined,
        ratingValue: 0,
        responseStatus: CsatResponseStatus.COMPLETED,
      });
      expect(result.success).toBe(true);
    });

    it('should handle submission without comment', async () => {
      const submission = {
        rating: 4,
        ratingType: 'star' as const,
      };

      const mockSurvey = createMockSurvey();
      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(mockSurvey);
      csatSurveyService.submitSurvey.mockResolvedValue(undefined);

      const result = await controller.getSubmissions(mockEncodedDetails, submission);

      expect(csatSurveyService.submitSurvey).toHaveBeenCalledWith({
        ...mockSurvey,
        feedbackType: CsatFeedbackType.STAR,
        surveyConfig: { ratingScale: 4 },
        commentText: undefined,
        ratingValue: 4,
        responseStatus: CsatResponseStatus.COMPLETED,
      });
      expect(result.success).toBe(true);
    });

    it('should handle decryption errors', async () => {
      const submission = {
        rating: 5,
        ratingType: 'star' as const,
      };

      encodingDecodingService.decrypt.mockImplementation(() => {
        throw new Error('Decryption failed');
      });

      await expect(controller.getSubmissions(mockEncodedDetails, submission))
        .rejects.toThrow('Decryption failed');
    });

    it('should handle database errors when getting survey', async () => {
      const submission = {
        rating: 5,
        ratingType: 'star' as const,
      };

      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockRejectedValue(new Error('Database error'));

      await expect(controller.getSubmissions(mockEncodedDetails, submission))
        .rejects.toThrow('Database error');
    });

    it('should handle submission errors', async () => {
      const submission = {
        rating: 5,
        ratingType: 'star' as const,
      };

      const mockSurvey = createMockSurvey();
      encodingDecodingService.decrypt.mockReturnValue(mockDecodedDetails);
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(mockSurvey);
      csatSurveyService.submitSurvey.mockRejectedValue(new Error('Submission failed'));

      await expect(controller.getSubmissions(mockEncodedDetails, submission))
        .rejects.toThrow('Submission failed');
    });
  });

  describe('Edge cases and additional scenarios', () => {
    const mockEncodedDetails = 'encoded-details';
    const _mockDecodedDetails = { ticketId: 'ticket123' };

    const createMockSurvey = () => ({
      id: 'survey123',
      responseStatus: CsatResponseStatus.NO_RESPONSE,
      feedbackType: null,
      surveyConfig: null,
      commentText: null,
      ratingValue: null,
      ruleConfig: null,
      team: {} as Team,
      teamId: BigInt(1),
      organization: {} as Organization,
      organizationId: BigInt(1),
      ticketId: 'ticket123',
      ticketTitle: 'Test Ticket',
      ticketClosedAt: new Date(),
      ticketMetadata: {},
      mappingStatus: CsatMappingStatus.CREATED,
      samplingStatus: CsatSamplingStatus.PENDING,
      deliveryStatus: CsatDeliveryStatus.NOT_APPLICABLE,
      surveyToken: 'token123',
      requestorEmail: '<EMAIL>',
      deliveryChannel: CsatDeliveryChannel.EMAIL,
      deliveryDetails: {},
      responseMetadata: {},
      hasResponse: false,
      hasComment: false,
      attemptNumber: 0,
      followUpCount: 0,
      previousAttempts: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    it('should handle missing survey gracefully', async () => {
      const submission = {
        rating: 5,
        ratingType: 'star' as const,
      };

      encodingDecodingService.decrypt.mockReturnValue({ ticketId: 'ticket123' });
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(null);

      await expect(controller.getSubmissions(mockEncodedDetails, submission))
        .rejects.toThrow();
    });

    it('should handle survey with partial data', async () => {
      const submission = {
        rating: 5,
        ratingType: 'star' as const,
      };

      const partialSurvey = {
        ...createMockSurvey(),
        feedbackType: undefined,
        surveyConfig: undefined,
      };

      encodingDecodingService.decrypt.mockReturnValue({ ticketId: 'ticket123' });
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(partialSurvey);
      csatSurveyService.submitSurvey.mockResolvedValue(undefined);

      const result = await controller.getSubmissions(mockEncodedDetails, submission);

      expect(result.success).toBe(true);
      expect(csatSurveyService.submitSurvey).toHaveBeenCalledWith(
        expect.objectContaining({
          feedbackType: CsatFeedbackType.STAR,
          surveyConfig: { ratingScale: 5 },
        })
      );
    });

    it('should handle invalid rating values for star type', async () => {
      const submission = {
        rating: 0, // Invalid rating for star (should be 1-5)
        ratingType: 'star' as const,
      };

      const mockSurvey = createMockSurvey();
      encodingDecodingService.decrypt.mockReturnValue({ ticketId: 'ticket123' });
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(mockSurvey);
      csatSurveyService.submitSurvey.mockResolvedValue(undefined);

      const result = await controller.getSubmissions(mockEncodedDetails, submission);

      // Should still process but with the invalid value
      expect(result.success).toBe(true);
      expect(csatSurveyService.submitSurvey).toHaveBeenCalledWith(
        expect.objectContaining({
          ratingValue: 0, // The controller doesn't validate, just passes through
        })
      );
    });

    it('should handle very long comment text', async () => {
      const veryLongComment = 'x'.repeat(10000); // 10k characters
      const submission = {
        rating: 5,
        ratingType: 'star' as const,
        comment: veryLongComment,
      };

      const mockSurvey = createMockSurvey();
      encodingDecodingService.decrypt.mockReturnValue({ ticketId: 'ticket123' });
      csatSurveyService.getSurveyByTicketId.mockResolvedValue(mockSurvey);
      csatSurveyService.submitSurvey.mockResolvedValue(undefined);

      const result = await controller.getSubmissions(mockEncodedDetails, submission);

      expect(result.success).toBe(true);
      expect(csatSurveyService.submitSurvey).toHaveBeenCalledWith(
        expect.objectContaining({
          commentText: veryLongComment,
        })
      );
    });

    it('should handle concurrent submissions for the same survey', async () => {
      const submission = {
        rating: 5,
        ratingType: 'star' as const,
      };

      const mockSurvey = createMockSurvey();
      encodingDecodingService.decrypt.mockReturnValue({ ticketId: 'ticket123' });

      // Simulate concurrent access - first call returns NO_RESPONSE, second returns COMPLETED
      csatSurveyService.getSurveyByTicketId
        .mockResolvedValueOnce(mockSurvey)
        .mockResolvedValueOnce({ ...mockSurvey, responseStatus: CsatResponseStatus.COMPLETED });

      csatSurveyService.submitSurvey.mockResolvedValue(undefined);

      // First submission should succeed
      const result1 = await controller.getSubmissions(mockEncodedDetails, submission);
      expect(result1.success).toBe(true);

      // Second submission should fail (already completed)
      const result2 = await controller.getSubmissions(mockEncodedDetails, submission);
      expect(result2.success).toBe(false);
      expect(result2.message).toContain('already been submitted');
    });
  });
});
