import { <PERSON>, Get, Param, <PERSON>s } from '@nestjs/common';
import { CsatResponseStatus } from '@repo/thena-platform-entities';
import { FastifyReply } from 'fastify';
import { Public } from '../../auth/decorators/auth.decorator';
import { SkipAllThrottler } from '../../common/decorators/throttler.decorator';
import { SharedService } from '../../shared/shared.service';
import { CsatSurveyService } from '../services/csat-survey/csat-survey.service';
import { EncodingDecodingService } from '../services/csat-template/encoding-decoding-service';

interface CsatViewData {
  isError: boolean;
  isThankYou: boolean;
  isForm: boolean;
  title: string;
  message: string;
  feedbackType?: string;
  includeComment?: boolean;
  commentLabel?: string;
  commentPlaceholder?: string;
  brandingColor?: string;
  encodedDetails?: string;
  apiUrl?: string;
  ticketDetails?: {
    ticketId: string;
    ticketTitle: string;
    createdAt: string;
    closedAt: string;
  };
}

@Controller("/csat-feedback")
@SkipAllThrottler()
export class CsatWebController {
  constructor(
    private readonly encodingDecodingService: EncodingDecodingService,
    private readonly csatSurveyService: CsatSurveyService,
    private readonly sharedService: SharedService
  ) { }

  /**
  * Formats a date for display
  * @param date The date to format
  * @returns Formatted date string
  */
  private formatDate(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  }
  /**
   * Web page handler for CSAT surveys
   * Shows either the feedback form or thank you page based on survey status
   */
  @Public()
  @Get(':encodedDetails')
  // Remove @Render decorator and use Fastify's reply.view() instead
  async getSurveyPage(
    @Param('encodedDetails') encodedDetails: string,
    @Res() reply: FastifyReply
  ): Promise<void> {
    try {
      const decodedDetails = this.encodingDecodingService.decrypt(encodedDetails);
      const { ticketId } = decodedDetails;

      const survey = await this.csatSurveyService.getSurveyByTicketId(ticketId);
      const ticket = await this.sharedService.getTicketByPrimaryKey(ticketId);

      let viewData: CsatViewData;

      if (!survey || !ticket) {
        viewData = {
          isError: true,
          isThankYou: false,
          isForm: false,
          title: 'Survey Not Found',
          message: 'The survey you are looking for does not exist or has expired.',
        };
      } else if (survey.responseStatus === CsatResponseStatus.COMPLETED) {
        const config = survey.ruleConfig?.feedbackConfig || {};
        viewData = {
          isError: false,
          isThankYou: true,
          isForm: false,
          title: 'Thank You!',
          message: config.customThankYouMessage ||
            'We appreciate your feedback. Thank you for helping us improve!',
        };
      } else {
        const ticketUid = ticket.team.identifier + "-" + ticket.id;
        const ticketTitle = ticket.title;
        const createdAt = ticket.createdAt;
        const closedAt = survey.ticketClosedAt;

        const ticketDetails = {
          ticketId: ticketUid,
          ticketTitle: ticketTitle,
          createdAt: this.formatDate(createdAt),
          closedAt: this.formatDate(closedAt),
        };
        const config = survey.ruleConfig?.feedbackConfig || {};
        viewData = {
          isError: false,
          isThankYou: false,
          isForm: true,
          title: config.customTitle || 'How was your experience?',
          message: config.customMessage || 'Please let us know how we did with your recent support request.',
          feedbackType: config.feedbackType || 'star',
          includeComment: config.includeCommentField || false,
          commentLabel: config.commentFieldLabel || 'Any additional comments?',
          commentPlaceholder: config.commentFieldPlaceholder || 'Tell us more about your experience...',
          brandingColor: config.brandingColor || '#0284c7',
          encodedDetails,
          apiUrl: `/csat/submissions?details=${encodedDetails}`,
          ticketDetails,
        };
      }

      // Use Fastify's view rendering directly
      return reply.view('csat-feedback', viewData);
    } catch (error) {
      console.error('Error processing CSAT survey page:', error);

      const errorData = {
        isError: true,
        isThankYou: false,
        isForm: false,
        title: 'Error',
        message: 'An error occurred while processing your feedback request. Please try again later.',
      };

      return reply.view('csat-feedback', errorData);
    }
  }
}
