import { InjectQueue } from '@nestjs/bullmq';
import { Body, Controller, Post, Query } from '@nestjs/common';
import { CsatFeedbackType, CsatResponseStatus } from '@repo/thena-platform-entities';
import { TicketEvents } from '@repo/thena-shared-interfaces';
import { Queue } from 'bullmq';
import { Public } from '../../auth/decorators/auth.decorator';
import { SkipAllThrottler } from '../../common/decorators/throttler.decorator';
import { QueueNames } from '../../constants/queue.constants';
import { SharedService } from '../../shared/shared.service';
import { CsatSurveyService } from '../services/csat-survey/csat-survey.service';
import { EncodingDecodingService } from '../services/csat-template/encoding-decoding-service';
interface CsatSubmissionDto {
  rating: number | 'up' | 'down';
  ratingType: 'star' | 'thumbs';
  comment?: string;
}

@Controller('csat/submissions')
@SkipAllThrottler()
export class CsatSubmissionController {

  constructor(
    private readonly encodingDecodingService: EncodingDecodingService,
    private readonly csatSubmissionService: CsatSurveyService,
    private readonly sharedService: SharedService,
    @InjectQueue(QueueNames.TICKET_SNS_PUBLISHER)
    private readonly ticketSnsPublishQueue: Queue,
  ) { }

  @Public()
  @Post()
  async getSubmissions(
    @Query('details') details: string,
    @Body() submission: CsatSubmissionDto
  ) {
    const decodedDetails: { ticketId: string } = this.encodingDecodingService.decrypt(details);

    const survey = await this.csatSubmissionService.getSurveyByTicketId(decodedDetails.ticketId);

    if (survey.responseStatus === CsatResponseStatus.COMPLETED) {
      return {
        success: false,
        message: `Survey for ticket ${decodedDetails.ticketId} has already been submitted`,
        decodedDetails,
      }
    }

    // Map submission to survey
    survey.feedbackType = submission.ratingType === 'star' ? CsatFeedbackType.STAR : CsatFeedbackType.THUMBS;
    survey.surveyConfig = {
      ratingScale: typeof submission.rating === 'number' ? submission.rating : submission.rating === 'up' ? 1 : 0,
    };
    survey.commentText = submission.comment;
    const submissionType = submission.ratingType
    survey.ratingValue = submissionType === 'star' ? submission.rating as number : submissionType === 'thumbs' ? submission.rating === 'up' ? 1 : 0 : null;
    survey.responseStatus = CsatResponseStatus.COMPLETED;

    await this.csatSubmissionService.submitSurvey(survey);

    const user = await this.sharedService.getBotUserFromOrganization(survey.organizationId as unknown as string);

    this.ticketSnsPublishQueue.add(QueueNames.TICKET_SNS_PUBLISHER, {
      ticketId: decodedDetails.ticketId,
      eventType: TicketEvents.CSAT_RECEIVED,
      user: user,
      team: survey.team,
      csat: {
        id: survey.id,
        feedbackType: survey.feedbackType,
        surveyConfig: survey.surveyConfig,
        deliveryChannel: survey.deliveryChannel,
        survey: survey,
      },
    },
      {
        attempts: 3,
        backoff: {
          type: "exponential",
          delay: 1000,
        },
      },
    );

    console.log('csat received event published', {
      ticketId: decodedDetails.ticketId,
      eventType: TicketEvents.CSAT_RECEIVED,
      user: user,
      team: survey.team,
      csat: survey,
    });

    return {
      success: true,
      message: `Submission received successfully for ticket ${decodedDetails.ticketId}`,
      decodedDetails,
      submission,
    };
  }
}
