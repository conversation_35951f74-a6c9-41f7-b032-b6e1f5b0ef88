import { BullModule } from '@nestjs/bullmq';
import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Controllers
import { CsatSubmissionController } from './controllers/csat-submission.controller';
import { CsatWebController } from './controllers/csat-web.controller';
import { CsatController } from './controllers/csat.controller';

import { CsatAuditLog, CsatAuditLogRepository, CsatFeedbackConfig, CsatFeedbackConfigRepository, CsatRule, CsatRuleFilter, CsatRuleFilterRepository, CsatRuleRepository, CsatSettings, CsatSettingsRepository, CsatSurvey, CsatSurveyRepository, CsatTriggerConfig, CsatTriggerConfigRepository, EmailConfig, EmailConfigRepository, TransactionService } from '@repo/thena-platform-entities';
import { CommonModule } from '../common/common.module';
import { ConfigModule } from '../config/config.module';
import { ConfigKeys, ConfigService } from '../config/config.service';
import { QueueNames } from '../constants/queue.constants';
import { SharedModule } from '../shared/shared.module';
import { TicketsModule } from '../tickets/tickets.module';
import { CsatCronController } from './controllers/csat-cron.controller';
import { CsatAuditLogService } from './services/csat-audit-logs.service';
import { CsatCronService } from './services/csat-cron/cron.service';
import { CsatFeedbackConfigService } from './services/csat-feedback-config.service';
import { CsatMapperService } from './services/csat-mapper.service';
import { CsatRuleFilterService } from './services/csat-rule-filter.service';
import { CsatRuleService } from './services/csat-rule.service';
import { CsatSettingsService } from './services/csat-settings.service';
import { CsatSurveyService } from './services/csat-survey/csat-survey.service';
import { AmpEmailService } from './services/csat-template/email-template.service';
import { EncodingDecodingService } from './services/csat-template/encoding-decoding-service';
import { CsatTemplateService } from './services/csat-template/ticket-source-template.service';
import { CsatTriggerConfigService } from './services/csat-trigger-config.service';

@Module({
  imports: [
    SharedModule,
    CommonModule,
    forwardRef(() => TicketsModule),
    BullModule.registerQueueAsync({
      name: QueueNames.TICKET_SNS_PUBLISHER,
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get(ConfigKeys.REDIS_HOST),
          port: Number(configService.get(ConfigKeys.REDIS_PORT)),
          password: configService.get(ConfigKeys.REDIS_PASSWORD),
          username: configService.get(ConfigKeys.REDIS_USERNAME),
        },
        defaultJobOptions: {
          removeOnComplete: {
            age: 24 * 3600, // keep completed jobs for 24 hours
            count: 1000, // keep last 1000 completed jobs
          },
          removeOnFail: {
            age: 24 * 3600, // keep up to 24 hours
          },
        },
      }),
    }),
    TypeOrmModule.forFeature([
      CsatSettings,
      CsatRule,
      CsatRuleFilter,
      CsatTriggerConfig,
      CsatFeedbackConfig,
      CsatAuditLog,
      CsatSurvey,
      // Repositories
      CsatSettingsRepository,
      CsatRuleRepository,
      CsatRuleFilterRepository,
      CsatTriggerConfigRepository,
      CsatFeedbackConfigRepository,
      CsatAuditLogRepository,
      CsatSurveyRepository,
      EmailConfigRepository,
      EmailConfig,
    ]),
  ],
  controllers: [
    CsatController,
    CsatSubmissionController,
    CsatWebController,
    CsatCronController,
  ],
  providers: [
    // Services
    CsatSettingsService,
    CsatRuleService,
    CsatRuleFilterService,
    CsatTriggerConfigService,
    CsatFeedbackConfigService,
    CsatAuditLogService,
    TransactionService,
    CsatSurveyService,
    CsatMapperService,
    EncodingDecodingService,
    CsatTemplateService,
    CsatCronService,
    // Repositories
    CsatSettingsRepository,
    CsatRuleRepository,
    CsatRuleFilterRepository,
    CsatTriggerConfigRepository,
    CsatFeedbackConfigRepository,
    CsatAuditLogRepository,
    AmpEmailService,
    CsatSurveyRepository,
    EmailConfigRepository,
  ],
  exports: [
    CsatSettingsService,
    CsatRuleService,
    CsatAuditLogService,
    CsatSurveyService,
    AmpEmailService,
  ],
})
export class CsatModule { }
