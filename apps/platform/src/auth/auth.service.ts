import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { UserType } from "@repo/thena-platform-entities";
import * as _ from "lodash";
import { CurrentUser } from "../common/decorators";
import { IdGeneratorUtils } from "../common/utils/id-generator.utils";
import { ConfigKeys, ConfigService } from "../config/config.service";
import { OrganizationService } from "../organization/services/organization.service";
import { UsersService } from "../users/services/users.service";
import { SignInDto, SignUpDto } from "./dto/auth.dto";

@Injectable()
export class AuthService {
  constructor(
    private jwtService: JwtService,
    private usersService: UsersService,
    private organizationsService: OrganizationService,
    private configService: ConfigService,
  ) { }

  /**
   * Signs in a user with the provided email and password.
   * @param email The email address of the user.
   * @param password The password of the user.
   * @returns A promise that resolves to the user object if the sign-in is successful
   */
  async signIn(signInDto: SignInDto) {
    const { email } = signInDto;

    // Check if the email and password are provided
    if (!email) {
      throw new UnauthorizedException("Invalid email or password");
    }

    // Find this user by email
    const user = await this.usersService.findOneByEmail(email);
    if (!user) {
      throw new UnauthorizedException("Invalid email or password");
    }

    const payload: CurrentUser = {
      sub: user.id,
      uid: user.uid,
      email: user.email,
      timezone: user.timezone,
      userType: user.userType,
      orgId: user.organizationId,
      orgUid: user.organization.uid,
      orgTier: user.organization.tier,
    };

    // TODO: Remove this secret from everywhere
    const accessToken = await this.jwtService.signAsync(payload, {
      expiresIn: "7d",
      secret: this.configService.get(ConfigKeys.JWT_SECRET),
    });

    return { accessToken };
  }

  private generateUserIdentifier(userType: UserType): string {
    switch (userType) {
      case UserType.USER: {
        return IdGeneratorUtils.generate("U");
      }

      case UserType.CUSTOMER_USER: {
        return IdGeneratorUtils.generate("C");
      }

      case UserType.APP_USER: {
        return IdGeneratorUtils.generate("A");
      }

      case UserType.BOT_USER: {
        return IdGeneratorUtils.generate("O");
      }

      default: {
        throw new Error("Invalid user type");
      }
    }
  }

  /**
   * Signs up a new user.
   * @param signUpDto The data transfer object containing user information.
   * @returns A promise that resolves to the created user object.
   */
  async signUp(signUpDto: SignUpDto) {
    const { email, password, name, organizationId, userType } = signUpDto;

    // Check if the organization id is provided
    if (!organizationId) {
      throw new BadRequestException("Organization id is required");
    }

    // Find the org
    const organization = await this.organizationsService.findOneByIdentifier(
      organizationId,
    );

    // Check if the org exists
    if (_.isEmpty(organization)) {
      throw new NotFoundException("Organization not found");
    }

    const validUserTypes = [
      UserType.USER,
      UserType.ORG_ADMIN,
      UserType.CUSTOMER_USER,
    ];
    if (!validUserTypes.includes(userType)) {
      throw new BadRequestException("Invalid user type");
    }

    const user = await this.usersService.createUserWithoutAuth({
      email,
      password,
      name,
      organizationUid: organization.uid,
      userType: userType,
    });

    return user;
  }
}
