import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
  SQSConfig,
  SQSConsumerService,
  SQSModule,
} from "@repo/nestjs-commons/aws-utils/sqs";
import { SentryService } from "@repo/nestjs-commons/filters";
import {
  AUTH_GRPC_SERVICE_URL_TOKEN,
  AuthenticationGrpcClient,
} from "@repo/nestjs-commons/guards";
import { ILogger } from "@repo/nestjs-commons/logger";
import { SNSPublisherService } from "@repo/thena-eventbridge";
import {
  App,
  AppEventsAuditRepository,
  AppInstallation,
  AppInstallationRepository,
  AppRepository,
  AppWebhookDeliveryLog,
  AppWebhookDeliveryRepository,
  AppWebhookEventAuditLog,
  CachedAppInstallationsRepository,
  TransactionService,
} from "@repo/thena-platform-entities";
import { BullBoardModule } from "../bull-queue/bull-board.module";
import { EventDeliveryProcessor } from "../bull-queue/processors/event-delivery.processor";
import { CommonModule } from "../common/common.module";
import { ConfigService } from "../config/app.config.service";
import { IncomingWebhookActionController } from "./controller/incoming-webhook.action.controller";
import { IncomingWebhookService } from "./Service/incoming-webhook.service";
import { SvixWebhookService } from "./Service/svix.webhook.service";
import { WebhookService } from "./Service/webhook.service";

@Module({
  imports: [
    ConfigModule,
    CommonModule,
    TypeOrmModule.forFeature([
      App,
      AppInstallation,
      AppWebhookEventAuditLog,
      AppWebhookDeliveryLog,
    ]),
    SQSModule,
    BullBoardModule,
  ],
  providers: [
    {
      provide: "SQS_CONFIG",
      useFactory: (configService: ConfigService) => ({
        ...configService.sqsConfig,
      }),
      inject: [ConfigService],
    },
    {
      provide: "SQS_CONSUMER",
      useFactory: (config: SQSConfig, sentry: any, logger: ILogger) => {
        return new SQSConsumerService(config, sentry, logger);
      },
      inject: ["SQS_CONFIG", "Sentry", "CustomLogger"],
    },
    WebhookService,
    SvixWebhookService,
    AppInstallationRepository,
    AppEventsAuditRepository,
    AppWebhookDeliveryRepository,
    CachedAppInstallationsRepository,
    AppRepository,
    IncomingWebhookService,
    AuthenticationGrpcClient,
    TransactionService,
    {
      provide: AUTH_GRPC_SERVICE_URL_TOKEN,
      useFactory: (configService: ConfigService) =>
        configService.authGrpcServiceUrl,
      inject: [ConfigService],
    },

    // SNS Publisher used form inbound webhook messages to publish events to the platform
    {
      provide: "WEBHOOK_SNS_PUBLISHER",
      useFactory: (
        configService: ConfigService,
        sentryService: SentryService,
        loggerService: ILogger,
      ) => {
        return new SNSPublisherService(
          {
            topicArn: configService.get("AWS_SNS_WEBHOOK_TOPIC_ARN"),
            region: configService.get("AWS_REGION"),
            credentials: {
              accessKeyId: configService.get("AWS_ACCESS_KEY"),
              secretAccessKey: configService.get("AWS_SECRET_KEY"),
            },
          },
          sentryService,
          loggerService,
        );
      },
      inject: [ConfigService, "Sentry", "CustomLogger"],
    },
    EventDeliveryProcessor,
  ],
  exports: [WebhookService, SvixWebhookService],
  controllers: [IncomingWebhookActionController],
})
export class WebhookModule {}
