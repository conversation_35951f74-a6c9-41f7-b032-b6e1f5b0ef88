import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  OnModuleDestroy,
  OnModuleInit,
} from "@nestjs/common";
import {
  SQSConsumerService,
  SQSMessage,
} from "@repo/nestjs-commons/aws-utils/sqs";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  AppEventsAuditRepository,
  AppInstallationRepository,
  InstallationStatus,
  WebhookDeliveryStatus,
} from "@repo/thena-platform-entities";
import { EventDeliveryToSvixService } from "../../bull-queue/services/event-delivery-to-svix.service";
import { ConfigService } from "../../config/app.config.service";
import { SQSMessageAttributes } from "../Interfaces/incoming-webhook.interface";

/**
 * Service responsible for handling webhook delivery to installed apps
 *
 * Main responsibilities:
 * - Consumes messages from SQS
 * - Orchestrates webhook processing workflow
 * - Coordinates with specialized services for specific tasks
 *
 * @class WebhookService
 */

@Injectable()
export class WebhookService implements OnModuleInit, OnModuleDestroy {
  constructor(
    // SQS Consumer
    @Inject("SQS_CONSUMER")
    private readonly platformSqsConsumer: SQSConsumerService,
    // Logger
    @Inject("CustomLogger") private readonly logger: ILogger,
    // Repositories
    private readonly appEventsAuditRepository: AppEventsAuditRepository,
    private readonly appInstallationRepository: AppInstallationRepository,
    // Services
    private readonly configService: ConfigService,
    private readonly eventDeliveryToSvixService: EventDeliveryToSvixService, // BullMQ job service
  ) {}

  onModuleInit() {
    this.startConsumers();
  }

  onModuleDestroy() {
    this.stopConsumers();
  }

  private startConsumers(): void {
    this.platformSqsConsumer.startConsumer(async (message) => {
      /**
       * Check if the event payload is a valid json object
       */
      if (typeof message === "string") {
        this.logger.error(
          "[WebhookService] Invalid event payload. Expected a JSON object. Check the SNS settings [ENABLE_RAW_MESSAGE_DELIVERY SHOULD BE TRUE].",
        );
        throw new HttpException(
          {
            status: HttpStatus.BAD_REQUEST, // SNS will Move this meesage to Dead Letter Queue. [400 status code]
            error: "Invalid event payload. Expected a JSON object.",
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      await this.handleMessage(message);
    });

    this.logger.log(
      "[WebhookService] platform SQS consumer started successfully",
    );
  }

  private stopConsumers(): void {
    this.platformSqsConsumer.stopConsumer();
    this.logger.log(
      "[WebhookService] platform SQS consumer stopped successfully",
    );
  }

  /**
   * Creates an audit log entry for a failed webhook delivery
   *
   * @param eventId The event ID
   * @param appId The app ID
   * @param appName The app name
   * @param errorMessage The error message
   * @param isAppNotFound Whether the app was not found in SVIX
   * @returns Promise<AppWebhookDeliveryLog> The created delivery log
   */
  /**
   * Filters installations based on team access control rules
   * @param installations - Array of app installations
   * @param eventTeamId - Team ID from the event (can be null)
   * @returns Filtered installations that have access to the team
   */
  private filterInstallationsByTeamAccess(
    installations: Array<{
      appId: string;
      status: InstallationStatus;
      allowedTeamIds: string[];
      app: any;
    }>,
    eventTeamId: string | null,
  ): Array<{
    appId: string;
    status: InstallationStatus;
    allowedTeamIds: string[];
    app: any;
  }> {
    // Debug: Log all installations before filtering
    this.logger.log(
      `[WebhookService] Raw installations before filtering: ${JSON.stringify(
        installations.map((i) => ({
          appId: i.appId,
          allowedTeamIds: i.allowedTeamIds,
          appName: i.app?.name,
        })),
      )}`,
    );

    // If event has no team ID, skip team filtering - allow all installations
    if (!eventTeamId) {
      this.logger.log(
        `[WebhookService] Event has no teamId - skipping team access checks, allowing all installations`,
      );
      return installations;
    }

    // Event has team ID - filter installations that have this team in allowedTeamIds
    const filtered = installations.filter((installation) => {
      const { allowedTeamIds } = installation;
      // If installation has no team restrictions (empty array), deny access when event has team
      if (!allowedTeamIds || allowedTeamIds.length === 0) {
        this.logger.log(
          `[WebhookService] App ${installation.appId} has no team restrictions but event has teamId ${eventTeamId} - denying access`,
        );
        return false;
      }

      // Check if event team ID is in the allowed teams list
      const hasAccess = allowedTeamIds.includes(eventTeamId);

      if (hasAccess) {
        this.logger.log(
          `[WebhookService] ✅ App ${installation.appId} has access to team ${eventTeamId}`,
        );
      } else {
        this.logger.log(
          `[WebhookService] ❌ App ${
            installation.appId
          } does NOT have access to team ${eventTeamId}. Allowed teams: [${allowedTeamIds.join(
            ", ",
          )}]`,
        );
      }

      return hasAccess;
    });

    return filtered;
  }

  private async handleMessage(event: SQSMessage): Promise<void> {
    try {
      const { messageAttributes } = event;
      const { message } = event;
      const {
        event_id,
        event_name,
        context_organization_id,
        context_user_id,
        context_user_type,
      } = messageAttributes as SQSMessageAttributes;

      /**
       * Store the message in the audit log
       * This will be used to track the message delivery status
       * TODO: Make this find and update. Handle Idempotency
       */
      await this.appEventsAuditRepository.save({
        eventId: event_id,
        eventName: event_name,
        organizationId: context_organization_id,
        executingUserId: context_user_id,
        executingUserType: context_user_type,
        payload: message as Record<string, any>,
        status: WebhookDeliveryStatus.PENDING,
        targetAppIds: [],
        successfulAppIds: [],
        failedAppIds: [],
      });
      await this.processSQSMessage(event);
    } catch (error) {
      this.logger.error(`[WebhookService] Error processing webhook:`, error);
      throw error;
    }
  }

  /**
   * Processes a webhook message from SQS by queuing delivery jobs for each active app installation
   *
   * Flow:
   * 1. Find active apps with installations for the organization
   * 2. Queue delivery jobs for each app via EventDeliveryToSvixService
   * 3. Jobs are processed in FIFO order by EventDeliveryProcessor
   * 4. Each job handles SVIX delivery, audit logging, and error handling
   * 5. Update main audit log with queued app count
   *
   * @param event - The SQS message containing webhook data
   * @returns Promise<void>
   */
  private async processSQSMessage(event: SQSMessage): Promise<void> {
    const startTime = Date.now();
    const { message, messageAttributes } = event;
    const { event_id, event_name, context_organization_id } =
      messageAttributes as SQSMessageAttributes;

    // Extract team ID from the message payload - check both locations
    const eventTeamId = (message as any)?.teamId || null;

    this.logger.log(
      `[WebhookService] Processing event_id: ${event_id} | event_name: ${event_name} | org: ${context_organization_id} | teamId: ${eventTeamId}`,
    );

    // Get installations with allowedTeamIds for team-based filtering
    const installations = await this.appInstallationRepository.findAll({
      where: {
        status: InstallationStatus.ACTIVE,
        reinstallRequired: false,
        organizationId: context_organization_id,
      },
      select: ["appId", "status", "allowedTeamIds"],
      relations: {
        app: true,
      },
    });

    // Filter installations based on team access control
    const allowedInstallations = this.filterInstallationsByTeamAccess(
      installations,
      eventTeamId,
    );

    this.logger.log(
      `[WebhookService] Found ${allowedInstallations.length} installations with team access for teamId: ${eventTeamId}`,
    );

    if (allowedInstallations.length === 0) {
      this.logger.log(
        "[WebhookService] No active installations found to process webhook.",
      );
      return;
    }

    // Queue jobs for each app
    const queuedJobs: string[] = [];
    const failedToQueue: string[] = [];
    const failedJobs: { appId: string; error: string }[] = [];

    for (const data of allowedInstallations) {
      try {
        const jobResult =
          await this.eventDeliveryToSvixService.addEventDeliveryJob({
            app_id: data?.app?.uid,
            app_name: data?.app?.name,
            event_id: event_id,
            event_name: event_name,
            message: message as Record<string, any>,
            organization_id: context_organization_id,
            environment: this.configService.get("NODE_ENV"),
          });

        queuedJobs.push(data?.app?.uid);
        this.logger.log(
          `[WebhookService] Successfully queued job ${jobResult.jobId} for app: ${data?.app?.uid}`,
        );
      } catch (error) {
        failedJobs.push({
          appId: data?.app?.uid,
          error: error instanceof Error ? error.message : String(error),
        });

        this.logger.error(
          `[EventDeliveryToSvixService] Failed to add event delivery job for app: ${data?.app?.uid}, event: ${event_name}`,
          error instanceof Error ? error.stack : String(error),
        );
      }
    }

    // Update audit log with queued apps
    await this.appEventsAuditRepository.update(
      { eventId: event_id },
      {
        targetAppIds: queuedJobs,
        status: WebhookDeliveryStatus.PENDING,
        processingTimeMs: Date.now() - startTime,
      },
    );

    this.logger.log(
      `[WebhookService] Final count - Queued: ${queuedJobs.length} jobs for event_id: ${event_id}`,
    );

    this.logger.log(
      `[WebhookService] Webhook processing complete | Event ID: ${event_id} | Event Name: ${event_name} | Queued: ${
        queuedJobs.length
      } | Failed to queue: ${failedToQueue.length} | Processing Time: ${
        Date.now() - startTime
      }ms`,
    );
  }
}
