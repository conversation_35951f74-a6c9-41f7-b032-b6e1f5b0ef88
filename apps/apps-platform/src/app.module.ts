import { Module } from "@nestjs/common";
import { APP_GUARD } from "@nestjs/core";
import { RedisCacheModule } from "@repo/nestjs-commons/cache";
import {
  AUTH_GRPC_SERVICE_URL_TOKEN,
  ApiKeyAuthStrategy,
  AuthGuard,
  AuthenticationGrpcClient,
  BearerTokenHttpStrategy,
} from "@repo/nestjs-commons/guards";
import { NewRelicModule } from "@repo/nestjs-newrelic";
import { AppInstallModule } from "./app-install/app-install.module";
import { AppManifestModule } from "./app-manifest/app-manifest.module";
import { BullQueueModule } from "./bull-queue/bull.queue.module";
import { CommonModule } from "./common/common.module";
import { ConfigModule } from "./config/app.config.module";
import { ConfigService } from "./config/app.config.service";
import { DatabaseModule } from "./database/database.module";
import { GrpcModule } from "./grpc/grpc.module";
import { HealthModule } from "./health/health.module";
import { WebhookModule } from "./webhook/webhook.module";
@Module({
  imports: [
    CommonModule,
    NewRelicModule,
    ConfigModule.register({
      envFilePath: process.env.NODE_ENV === "test" ? ".env.test" : ".env",
    }),
    HealthModule,
    GrpcModule,
    DatabaseModule,
    RedisCacheModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        host: configService.get("REDIS_HOST"),
        port: Number(configService.get("REDIS_PORT")),
        password: configService.get("REDIS_PASSWORD"),
        username: configService.get("REDIS_USERNAME"),
        prefix: "apps-platform",
      }),
      inject: [ConfigService],
    }),
    AppManifestModule,
    AppInstallModule,
    WebhookModule,
    BullQueueModule,
  ],
  controllers: [],
  providers: [
    ApiKeyAuthStrategy,
    BearerTokenHttpStrategy,
    {
      provide: AUTH_GRPC_SERVICE_URL_TOKEN,
      useFactory: (configService: ConfigService) =>
        configService.authGrpcServiceUrl,
      inject: [ConfigService],
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    AuthenticationGrpcClient,
  ],
})
export class AppModule {}
