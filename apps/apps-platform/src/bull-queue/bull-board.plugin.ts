import { createBullBoard } from "@bull-board/api";
import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { FastifyAdapter } from "@bull-board/fastify";
import { Queue } from "bullmq";
import dotenv from "dotenv";
import { FastifyPluginCallback } from "fastify";
import fp from "fastify-plugin";
dotenv.config({ path: ".env" });

interface BullBoardPluginOptions {
  queues: Queue[];
  basePath?: string;
}

const plugin = fp<BullBoardPluginOptions>(function (
  fastify,
  opts: BullBoardPluginOptions,
  done,
) {
  const serverAdapter = new FastifyAdapter();
  serverAdapter.setBasePath(opts.basePath || "/admin/queues");

  createBullBoard({
    queues: opts.queues.map((queue) => new BullMQAdapter(queue)),
    serverAdapter,
    options: {
      uiConfig: {
        boardTitle: "App Studio",
      },
    },
  });

  fastify.register(serverAdapter.registerPlugin(), {
    prefix: opts.basePath || "/admin/queues",
    basePath: opts.basePath || "/admin/queues",
    logLevel: "silent",
  });

  // Add basic auth hook
  fastify.addHook("onRequest", async (request, reply) => {
    if (request.url.startsWith(opts.basePath || "/admin/queues")) {
      const authHeader = request.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Basic ")) {
        reply.header("WWW-Authenticate", 'Basic realm="Bull Board"');
        await reply.status(401).send();
        return;
      }

      const [username, password] = Buffer.from(
        authHeader.split(" ")[1],
        "base64",
      )
        .toString()
        .split(":");

      const adminUser = process.env.BULL_BOARD_USERNAME;
      const adminPassword = process.env.BULL_BOARD_PASSWORD;

      if (username !== adminUser || password !== adminPassword) {
        reply.header("WWW-Authenticate", 'Basic realm="Bull Board"');
        await reply.status(401).send();
        return;
      }
    }
  });

  done();
}) as unknown as FastifyPluginCallback;

export default plugin;
