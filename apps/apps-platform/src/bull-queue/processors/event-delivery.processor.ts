import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Inject, Injectable } from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import {
  AppEventsAuditRepository,
  AppRepository,
  AppWebhookDeliveryRepository,
  TransactionService,
  WebhookDeliveryStatus,
} from "@repo/thena-platform-entities";
import { Job } from "bullmq";
import { ALLOWED_FILTER_TYPES } from "../../webhook/Interfaces/webhook.svix.interface";
import { SvixWebhookService } from "../../webhook/Service/svix.webhook.service";
import { QUEUE_CONFIGS } from "../constants/queue.constant";
import { EventDeliveryPayload } from "../interfaces/event-delivery-payload.interface";

@Processor(QUEUE_CONFIGS.EVENT_DELIVERY_TO_SVIX_CONSUMER_APPS.name)
@Injectable()
export class EventDeliveryProcessor extends WorkerHost {
  constructor(
    @Inject("CustomLogger") private readonly logger: ILogger,
    private readonly svixWebhookService: SvixWebhookService,
    private readonly appRepository: AppRepository,
    private readonly appWebhookDeliveryRepository: AppWebhookDeliveryRepository,
    private readonly appEventsAuditRepository: AppEventsAuditRepository,
    private readonly transactionService: TransactionService,
  ) {
    super();
  }

  async process(job: Job<EventDeliveryPayload>): Promise<void> {
    const startTime = Date.now();
    const { app_id, app_name, event_id, event_name, message, environment } =
      job.data;

    this.logger.log(
      `[EventDeliveryProcessor] Processing job ${job.id} for app: ${app_id} | Event: ${event_name}`,
    );

    try {
      const response = await this.svixWebhookService.sendMessage(app_id, {
        eventType: ALLOWED_FILTER_TYPES.EVENTS,
        payload: {
          message,
          xWebhookEvent: event_name,
        },
        channels: [environment],
      });

      // Create delivery log for successful delivery
      await this.createDeliveryLog(
        event_id,
        app_id,
        app_name,
        WebhookDeliveryStatus.SUCCESS,
        startTime,
        { svixMessageId: response?.id },
      );

      // Update audit log with successful app ID
      await this.updateAuditLogSuccess(event_id, app_id);

      this.logger.log(
        `[EventDeliveryProcessor] Successfully delivered job ${job.id} to app: ${app_id} | Name: ${app_name}`,
      );
    } catch (error) {
      const isAppNotFound = error.code === 404;
      const isRateLimit = error.code === 429;

      // Create delivery log for failed delivery
      await this.createDeliveryLog(
        event_id,
        app_id,
        app_name,
        WebhookDeliveryStatus.FAILED,
        startTime,
        { errorMessage: error.message, isAppNotFound },
      );

      // Update audit log with failed app ID
      await this.updateAuditLogFailure(event_id, app_id);

      // Handle specific error cases
      if (isAppNotFound) {
        await this.markAppAsInactive(app_id, app_name);
        this.logger.log(
          `[EventDeliveryProcessor] App not found: ${app_id} | Name: ${app_name}`,
        );
      } else if (isRateLimit) {
        this.logger.error(
          `[EventDeliveryProcessor] Rate limit exceeded for app ${app_id} | Name: ${app_name} | Reason: ${error.message}`,
        );
        // TODO: set an alert on zenduty
      } else {
        this.logger.error(
          `[EventDeliveryProcessor] Failed to send to app ${app_id} | Name: ${app_name} | Reason: ${error.message}`,
        );
      }

      /**
       * Only retry if it's not a 404 (app not found)
       * Don't retry for 404 errors
       */
      if (isAppNotFound) {
        return;
      }

      throw error; // This will trigger retry based on queue config
    }
  }

  /**
   * Creates delivery log entry
   */
  private async createDeliveryLog(
    eventId: string,
    appId: string,
    appName: string,
    status: WebhookDeliveryStatus,
    startTime: number,
    options?: {
      errorMessage?: string;
      isAppNotFound?: boolean;
      svixMessageId?: string;
    },
  ): Promise<void> {
    const endTime = Date.now();
    const processingTimeMs = endTime - startTime;
    const { errorMessage, isAppNotFound, svixMessageId } = options || {};

    // Log the same way as original
    if (status === WebhookDeliveryStatus.SUCCESS) {
      this.logger.log(
        `[EventDeliveryProcessor] Successfully sent message ${eventId} to app ${appId} | Name: ${appName}`,
      );
    } else if (status === WebhookDeliveryStatus.FAILED) {
      if (isAppNotFound) {
        this.logger.warn(
          `[EventDeliveryProcessor] App not found for event ${eventId} to app ${appId} | Name: ${appName}`,
        );
      } else {
        this.logger.warn(
          `[EventDeliveryProcessor] Failed to send message for event ${eventId} to app ${appId} | Name: ${appName} | Error: ${errorMessage}`,
        );
      }
    }

    // Create delivery log
    await this.appWebhookDeliveryRepository.save({
      eventId,
      appId,
      status,
      attemptCount: 1,
      errorMessage:
        status === WebhookDeliveryStatus.FAILED
          ? `${appId}: ${errorMessage}`
          : null,
      processingTimeMs,
      svixMessageId: svixMessageId || null,
    });
  }

  /**
   * Updates audit log for successful delivery
   */
  private async updateAuditLogSuccess(
    eventId: string,
    appId: string,
  ): Promise<void> {
    const auditLog = await this.appEventsAuditRepository.findByCondition({
      where: { eventId },
    });

    if (auditLog) {
      const successfulAppIds = [...(auditLog.successfulAppIds || [])];
      if (!successfulAppIds.includes(appId)) {
        successfulAppIds.push(appId);
      }

      await this.appEventsAuditRepository.update(
        { eventId },
        { successfulAppIds },
      );
    }
  }

  /**
   * Updates audit log for failed delivery
   */
  private async updateAuditLogFailure(
    eventId: string,
    appId: string,
  ): Promise<void> {
    const auditLog = await this.appEventsAuditRepository.findByCondition({
      where: { eventId },
    });

    if (auditLog) {
      const failedAppIds = [...(auditLog.failedAppIds || [])];
      if (!failedAppIds.includes(appId)) {
        failedAppIds.push(appId);
      }

      await this.appEventsAuditRepository.update({ eventId }, { failedAppIds });
    }
  }

  /**
   * Marks app as inactive
   */
  private async markAppAsInactive(
    appId: string,
    appName: string,
  ): Promise<void> {
    await this.transactionService.runInTransaction(async (txnContext) => {
      this.logger.log(
        `[EventDeliveryProcessor] Marking app as inactive: ${appId} | Name: ${appName}`,
      );

      await this.appRepository.updateWithTxn(
        txnContext,
        { uid: appId },
        {
          isActive: false,
          deletedAt: new Date(),
          updatedAt: new Date(),
        },
      );
    });
  }
}
