import { InjectQueue } from "@nestjs/bullmq";
import { Inject, Injectable } from "@nestjs/common";
import { ILogger } from "@repo/nestjs-commons/logger";
import { Job, Queue } from "bullmq";
import { QUEUE_CONFIGS } from "../constants/queue.constant";
import { EventDeliveryPayload } from "../interfaces/event-delivery-payload.interface";

export interface JobResult {
  jobId: string;
  queueName: string;
  timestamp: Date;
}

@Injectable()
export class EventDeliveryToSvixService {
  private readonly queueConfig =
    QUEUE_CONFIGS.EVENT_DELIVERY_TO_SVIX_CONSUMER_APPS;

  constructor(
    @InjectQueue(QUEUE_CONFIGS.EVENT_DELIVERY_TO_SVIX_CONSUMER_APPS.name)
    private readonly eventDeliveryQueue: Queue,
    @Inject("CustomLogger") private readonly logger: ILogger,
  ) {}

  /**
   * Adds an event delivery job to the Svix consumer apps queue
   * @param payload - The event payload to be delivered
   * @param options - Optional job configuration
   * @returns Promise with job details
   */
  async addEventDeliveryJob(
    payload: EventDeliveryPayload,
    options?: {
      priority?: number;
      delay?: number;
      attempts?: number;
      removeOnComplete?: boolean | number;
      removeOnFail?: boolean | number;
      backoff?: { type: string; delay: number };
      fifoGroup?: string;
    },
  ): Promise<JobResult> {
    try {
      const jobName = this.generateJobName(payload);
      const uniqueJobId = this.generateUniqueJobId(payload);

      // Check if job already exists
      const existingJob = await this.eventDeliveryQueue.getJob(uniqueJobId);
      if (existingJob) {
        this.logger.warn(
          `[EventDeliveryToSvixService] Job already exists for app: ${payload.app_id}, event: ${payload.event_id} | JobId: ${uniqueJobId}`,
        );
        return {
          jobId: existingJob.id as string,
          queueName: this.eventDeliveryQueue.name,
          timestamp: new Date(),
        };
      }

      const jobOptions = this.buildJobOptions(options, uniqueJobId);

      this.logger.log(
        `[EventDeliveryToSvixService] Adding FIFO event delivery job for app: ${payload.app_id}, event: ${payload.event_name} | JobId: ${uniqueJobId}`,
      );

      const job: Job = await this.eventDeliveryQueue.add(
        jobName,
        payload,
        jobOptions,
      );

      const result: JobResult = {
        jobId: job.id as string,
        queueName: this.eventDeliveryQueue.name,
        timestamp: new Date(),
      };

      this.logger.log(
        `[EventDeliveryToSvixService] Successfully queued job ${result.jobId} for app: ${payload.app_id}`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `[EventDeliveryToSvixService] Failed to add event delivery job for app: ${
          payload.app_id
        }, event: ${payload.event_name}. Payload: ${JSON.stringify(payload)}`,
        error instanceof Error ? error.stack : String(error),
      );
      throw error;
    }
  }

  /**
   * Generates a descriptive job name
   */
  private generateJobName(payload: EventDeliveryPayload): string {
    return `svix-delivery-${payload.event_name}-${payload.app_id}`;
  }

  /**
   * Generates a unique job ID based on event_id and app_id to prevent duplicates
   */
  private generateUniqueJobId(payload: EventDeliveryPayload): string {
    return `${payload.event_id}-${payload.app_id}`;
  }

  /**
   * Builds job options using queue-specific defaults unless provided
   */
  private buildJobOptions(
    options?: {
      priority?: number;
      delay?: number;
      attempts?: number;
      removeOnComplete?: boolean | number;
      removeOnFail?: boolean | number;
      backoff?: { type: string; delay: number };
      fifoGroup?: string;
    },
    jobId?: string,
  ) {
    return {
      // Use queue-specific configuration as defaults
      attempts: options?.attempts ?? this.queueConfig.attempts,
      removeOnComplete:
        options?.removeOnComplete ?? this.queueConfig.removeOnComplete,
      removeOnFail: options?.removeOnFail ?? this.queueConfig.removeOnFail,
      backoff: options?.backoff ?? this.queueConfig.backoff,

      // Additional options that don't have defaults in queue config
      priority: options?.priority ?? 0,
      delay: options?.delay ?? 0,

      // FIFO Configuration
      parent: options?.fifoGroup
        ? {
            id: options.fifoGroup,
            queue: this.eventDeliveryQueue.name,
          }
        : undefined,

      // Use deterministic job ID for deduplication
      jobId: jobId,
    };
  }
}
