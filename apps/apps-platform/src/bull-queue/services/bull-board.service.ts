import { createBullBoard } from "@bull-board/api";
import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { FastifyAdapter } from "@bull-board/fastify";
import { InjectQueue } from "@nestjs/bullmq";
import { Injectable } from "@nestjs/common";
import { Queue } from "bullmq";
import type { FastifyPluginCallback } from "fastify";
import { QUEUE_CONFIGS } from "../constants/queue.constant";

@Injectable()
export class BullBoardService {
  private serverAdapter: FastifyAdapter;

  constructor(
    @InjectQueue(QUEUE_CONFIGS.EVENT_DELIVERY_TO_SVIX_CONSUMER_APPS.name)
    public readonly eventDeliveryToSvixConsumerAppsQueue: Queue,
  ) {
    this.setupBullBoard();
  }

  private setupBullBoard() {
    this.serverAdapter = new FastifyAdapter();
    this.serverAdapter.setBasePath("/admin/queues");

    createBullBoard({
      queues: [new BullMQAdapter(this.eventDeliveryToSvixConsumerAppsQueue)],
      serverAdapter: this.serverAdapter,
      options: {
        uiConfig: {
          boardTitle: "Apps Studio",
        },
      },
    });

    return this.serverAdapter;
  }

  getRouter(): FastifyPluginCallback {
    return this.serverAdapter.registerPlugin() as unknown as FastifyPluginCallback;
  }
}
