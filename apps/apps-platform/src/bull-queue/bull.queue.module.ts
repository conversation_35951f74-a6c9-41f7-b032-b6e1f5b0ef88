import { BullModule } from "@nestjs/bullmq";
import { Modu<PERSON> } from "@nestjs/common";

import { CommonModule } from "../common/common.module";
import { ConfigModule } from "../config/app.config.module";
import { ConfigService } from "../config/app.config.service";
import { BullBoardModule } from "./bull-board.module";
import { BULL_BOARD_CONFIG } from "./constants/bull-board.constants";
import { QUEUE_CONFIGS } from "./constants/queue.constant";
import { EventDeliveryToSvixService } from "./services/event-delivery-to-svix.service";

@Module({
  imports: [
    ConfigModule,
    CommonModule,
    BullBoardModule,
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        connection: {
          host: configService.get("REDIS_HOST"),
          port: Number(configService.get("REDIS_PORT")),
          password: configService.get("REDIS_PASSWORD"),
          username: configService.get("REDIS_USERNAME"),
        },
        defaultJobOptions: {
          removeOnComplete: false,
          removeOnFail: false,
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue({
      name: QUEUE_CONFIGS.EVENT_DELIVERY_TO_SVIX_CONSUMER_APPS.name,
      defaultJobOptions: BULL_BOARD_CONFIG.defaultJobOptions,
    }),
  ],
  providers: [EventDeliveryToSvixService],
  exports: [BullBoardModule, EventDeliveryToSvixService],
})
export class BullQueueModule {}
