import { BullModule } from "@nestjs/bullmq";
import { Mo<PERSON><PERSON> } from "@nestjs/common";
import { CommonModule } from "../common/common.module";
import { BULL_BOARD_CONFIG } from "./constants/bull-board.constants";
import { QUEUE_CONFIGS } from "./constants/queue.constant";
import { BullBoardService } from "./services/bull-board.service";
import { EventDeliveryToSvixService } from "./services/event-delivery-to-svix.service";

@Module({
  imports: [
    CommonModule,
    BullModule.registerQueue({
      name: QUEUE_CONFIGS.EVENT_DELIVERY_TO_SVIX_CONSUMER_APPS.name,
      defaultJobOptions: BULL_BOARD_CONFIG.defaultJobOptions,
    }),
  ],
  providers: [BullBoardService, EventDeliveryToSvixService],
  exports: [BullBoardService, EventDeliveryToSvixService],
})
export class BullBoardModule {}
