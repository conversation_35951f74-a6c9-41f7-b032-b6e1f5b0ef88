import { z } from "zod";

export const databaseConfigSchema = z.object({
  THENA_APPS_PLATFORM_DB_HOST: z.string().min(1),
  THENA_APPS_PLATFORM_DB_PORT: z
    .union([z.string().transform((val) => parseInt(val, 10)), z.number()])
    .default(6543),
  THENA_APPS_PLATFORM_DB_NAME: z.string().min(1),
  THENA_APPS_PLATFORM_DB_USER: z.string().min(1),
  THENA_APPS_PLATFORM_DB_PASSWORD: z.string().min(1),
});

export const configSchema = z.object({
  // App Configuration
  APP_TAG: z.string().min(1),
  NODE_ENV: z.enum(["development", "production", "staging"]),
  PORT: z.coerce.number().default(3000),
  SERVICE_TAG: z.string().min(1),

  // aws
  AWS_SQS_WEBHOOK_QUEUE_URL: z.string().min(1),
  AWS_SNS_WEBHOOK_TOPIC_ARN: z.string().min(1),

  // Database Configuration
  ...databaseConfigSchema.shape,

  // Redis Configuration
  REDIS_HOST: z.string().min(1).default("localhost"),
  REDIS_PORT: z.coerce.number().default(6379),
  REDIS_USERNAME: z.string().min(1).default("default"),
  REDIS_PASSWORD: z.string().min(1).default(""),

  // AWS Configuration
  AWS_ACCESS_KEY: z.string().min(1),
  AWS_SECRET_KEY: z.string().min(1),
  AWS_REGION: z.string().min(1),
  // Swagger Configuration
  SWAGGER_ENABLED: z.string().default("true"),
  SWAGGER_TITLE: z.string().default("APPs Studio API"),
  SWAGGER_DESCRIPTION: z.string().min(1),
  // Monitoring
  SENTRY_DSN: z.string().url(),

  // Logger Configuration
  LOG_LEVEL: z
    .enum(["error", "warn", "log", "debug", "verbose"])
    .default("log"),
  LOG_REDACT: z
    .array(z.string())
    .default(["password", "authorization", "cookie"]),
  JWT_SECRET: z.string().min(1),
  SVIX_AUTH_TOKEN: z.string().min(1),

  // GRPC Common Config
  PLATFORM_GRPC_HOST: z.coerce.string().default("0.0.0.0:50051"),
  AUTH_GRPC_SERVICE_URL: z.string().default("0.0.0.0:50052"),
  WORKFLOW_GRPC_SERVICE_URL: z.string().default("0.0.0.0:50053"),

  // Vault Configuration
  VAULT_URL: z.string().default("http://localhost:8200"),
  VAULT_TOKEN: z.string().default(""),
  CERT_PATH: z.string().default(""),

  // New Relic
  NEW_RELIC_LICENSE_KEY: z.string().min(1),
  NEW_RELIC_APP_NAME: z.string().min(1).default("Thena platform Apps Studio"),

  // Swagger
  GENERATE_SWAGGER: z.string().default("false"),

  // Bull Board Configuration
  BULL_BOARD_USERNAME: z.string().min(1),
  BULL_BOARD_PASSWORD: z.string().min(1),
});

export type Config = z.infer<typeof configSchema>;
