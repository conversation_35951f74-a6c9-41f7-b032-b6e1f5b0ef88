import { vi } from 'vitest';

class SentryServiceMock {
  captureException = vi.fn();
  captureMessage = vi.fn();
  setContext = vi.fn();
  addBreadcrumb = vi.fn();
  setUser = vi.fn();
  setExtra = vi.fn();
  setTag = vi.fn();
  startTransaction = vi.fn().mockReturnValue({
    finish: vi.fn(),
    setTag: vi.fn(),
    setData: vi.fn(),
  });
  configureScope = vi.fn().mockImplementation((callback) => {
    callback({
      setTag: vi.fn(),
      setUser: vi.fn(),
      setExtra: vi.fn(),
    });
  });
  sentryDsn = 'mock-dsn';
  appEnv = 'test';
  appTag = 'test';
  serviceTag = 'test';
}

export const mockSentryService = new SentryServiceMock();

export const SENTRY_TOKEN = 'MOCKED_SENTRY_TOKEN';
