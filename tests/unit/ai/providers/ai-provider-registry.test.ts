import { Logger } from '@nestjs/common';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { PromptType } from '../../../../src/ai/constants/prompt-types.enum';
import {
  IAiCompletionParams,
  IAiCustomFieldsResponse,
  IAiDescriptionGenerationResponse,
  IAiModelConfig,
  IAiPromptRequest,
  IAiProvider,
  IAiProviderConfig,
  IAiSentimentAnalysisResponse,
  IAiTeamRoutingResponse,
  IAiTicketDetectionResponse,
  IAiTitleGenerationResponse,
  IAiUrgencyDetectionResponse,
} from '../../../../src/ai/interfaces/ai-provider.interface';
import { TeamPrompts } from '../../../../src/ai/interfaces/team-prompts.interface';
import { AiProviderRegistry } from '../../../../src/ai/providers/ai-provider-registry';

// Create a mock provider for testing
class MockAiProvider implements IAiProvider {
  private activeModel = 'default-model';
  private models: Map<string, IAiModelConfig> = new Map();
  private prompts: Map<PromptType, string> = new Map();
  private teamPrompts: Map<string, TeamPrompts> = new Map();

  async initialize(config: IAiProviderConfig): Promise<void> {
    if (config.defaultModel) {
      this.activeModel = config.defaultModel;
    }
    return Promise.resolve();
  }

  async detectTicket(
    _conversation: string,
    _teamId?: string,
  ): Promise<{
    success: boolean;
    data?: IAiTicketDetectionResponse;
    error?: string;
  }> {
    return {
      success: true,
      data: { requiresSupportTicket: true },
    };
  }

  async analyzeSentiment(
    _conversation: string,
    _teamId?: string,
  ): Promise<{
    success: boolean;
    data?: IAiSentimentAnalysisResponse;
    error?: string;
  }> {
    return {
      success: true,
      data: {
        sentiment: 'positive',
        confidence: 0.8,
        urgency: 'low',
        frustrationLevel: 'low',
        satisfactionLevel: 'high',
      },
    };
  }

  async detectUrgency(
    _conversation: string,
    _teamId?: string,
    _urgencyLevels?: string[],
  ): Promise<{
    success: boolean;
    data?: IAiUrgencyDetectionResponse;
    error?: string;
  }> {
    return {
      success: true,
      data: {
        urgency: 'medium',
        confidence: 0.7,
        businessImpact: 'medium',
        timeSensitivity: 'medium',
        affectedUsersCount: 5,
        severityLevel: 'medium',
      },
    };
  }

  async routeToTeam(
    _conversation: string,
    _teamId?: string,
  ): Promise<{
    success: boolean;
    data?: IAiTeamRoutingResponse;
    error?: string;
  }> {
    return {
      success: true,
      data: {
        recommendedTeam: 'engineering',
        confidence: 0.9,
        technicalComplexity: 'medium',
        domainExpertise: ['software'],
        products: ['api'],
      },
    };
  }

  async extractCustomFields(
    _conversation: string,
    _fields?: string[],
    _teamId?: string,
  ): Promise<{
    success: boolean;
    data?: IAiCustomFieldsResponse;
    error?: string;
  }> {
    return {
      success: true,
      data: {
        fields: { field1: 'value1', field2: 'value2' },
        confidence: { field1: 0.9, field2: 0.8 },
      },
    };
  }

  async executePrompt<T>(
    _request: IAiPromptRequest,
  ): Promise<{ success: boolean; data?: T; error?: string }> {
    return {
      success: true,
      data: { result: 'custom prompt result' } as unknown as T,
    };
  }

  setPrompt(type: PromptType, prompt: string): void {
    this.prompts.set(type, prompt);
  }

  getAvailableModels(): string[] {
    return ['model1', 'model2', 'model3'];
  }

  setActiveModel(modelId: string): void {
    this.activeModel = modelId;
  }

  getActiveModel(): string {
    return this.activeModel;
  }

  addModel(modelId: string, config: IAiModelConfig): void {
    this.models.set(modelId, config);
  }

  setTeamPrompts(teamId: string, prompts: TeamPrompts): void {
    this.teamPrompts.set(teamId, prompts);
  }

  getPrompt(promptType: PromptType, teamId?: string): string {
    if (teamId && this.teamPrompts.has(teamId)) {
      const teamPrompt = this.teamPrompts.get(teamId);
      if (teamPrompt?.[promptType]) {
        return teamPrompt[promptType];
      }
    }
    return this.prompts.get(promptType) || '';
  }

  async complete(_params: IAiCompletionParams): Promise<any> {
    return {
      text: 'Completed text response',
      usage: { total_tokens: 50 },
    };
  }

  async generateTitle(
    _conversation: string,
    _teamId?: string,
  ): Promise<{
    success: boolean;
    data?: IAiTitleGenerationResponse;
    error?: string;
  }> {
    return {
      success: true,
      data: { title: 'Generated Title' },
    };
  }

  async generateDescription(
    _conversation: string,
    _teamId?: string,
  ): Promise<{
    success: boolean;
    data?: IAiDescriptionGenerationResponse;
    error?: string;
  }> {
    return {
      success: true,
      data: { description: 'Generated description for the conversation' },
    };
  }
}

describe('AiProviderRegistry', () => {
  let registry: AiProviderRegistry;
  let mockProvider1: IAiProvider;
  let mockProvider2: IAiProvider;

  beforeEach(() => {
    // Mock the logger to avoid console output during tests
    vi.spyOn(Logger.prototype, 'log').mockImplementation(() => undefined);
    vi.spyOn(Logger.prototype, 'error').mockImplementation(() => undefined);
    vi.spyOn(Logger.prototype, 'warn').mockImplementation(() => undefined);
    vi.spyOn(Logger.prototype, 'debug').mockImplementation(() => undefined);
    vi.spyOn(Logger.prototype, 'verbose').mockImplementation(() => undefined);

    // Create the registry
    registry = new AiProviderRegistry();

    // Create mock providers
    mockProvider1 = new MockAiProvider();
    mockProvider2 = new MockAiProvider();

    // Setup spies on the mock providers
    vi.spyOn(mockProvider1, 'initialize');
    vi.spyOn(mockProvider1, 'detectTicket');
    vi.spyOn(mockProvider1, 'analyzeSentiment');
    vi.spyOn(mockProvider1, 'detectUrgency');
    vi.spyOn(mockProvider1, 'routeToTeam');
    vi.spyOn(mockProvider1, 'extractCustomFields');
    vi.spyOn(mockProvider1, 'executePrompt');
    vi.spyOn(mockProvider1, 'setPrompt');
    vi.spyOn(mockProvider1, 'getAvailableModels');
    vi.spyOn(mockProvider1, 'setActiveModel');
    vi.spyOn(mockProvider1, 'getActiveModel');
    vi.spyOn(mockProvider1, 'addModel');
    vi.spyOn(mockProvider1, 'setTeamPrompts');
    vi.spyOn(mockProvider1, 'getPrompt');
    vi.spyOn(mockProvider1, 'complete');
    vi.spyOn(mockProvider1, 'generateTitle');
    vi.spyOn(mockProvider1, 'generateDescription');

    vi.spyOn(mockProvider2, 'setTeamPrompts');
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Provider Registration', () => {
    it('should register a provider', () => {
      // Act
      registry.registerProvider('provider1', mockProvider1);

      // Assert
      expect(registry.getRegisteredProviders()).toContain('provider1');
      expect(registry.getActiveProvider()).toBe('provider1');
    });

    it('should set the first registered provider as active', () => {
      // Act
      registry.registerProvider('provider1', mockProvider1);

      // Assert
      expect(registry.getActiveProvider()).toBe('provider1');
    });

    it('should register multiple providers', () => {
      // Act
      registry.registerProvider('provider1', mockProvider1);
      registry.registerProvider('provider2', mockProvider2);

      // Assert
      expect(registry.getRegisteredProviders()).toContain('provider1');
      expect(registry.getRegisteredProviders()).toContain('provider2');
      expect(registry.getRegisteredProviders().length).toBe(2);
    });

    it('should throw error when getting active provider with none registered', () => {
      // Assert
      expect(() => registry.getActiveProvider()).toThrow(
        'No active AI provider',
      );
    });
  });

  describe('Provider Activation', () => {
    beforeEach(() => {
      registry.registerProvider('provider1', mockProvider1);
      registry.registerProvider('provider2', mockProvider2);
    });

    it('should change active provider', () => {
      // Act
      registry.setActiveProvider('provider2');

      // Assert
      expect(registry.getActiveProvider()).toBe('provider2');
    });

    it('should throw error when changing to non-existent provider', () => {
      // Assert
      expect(() => registry.setActiveProvider('nonExistentProvider')).toThrow(
        'Provider nonExistentProvider not registered',
      );
    });
  });

  describe('Provider API Delegation', () => {
    beforeEach(() => {
      registry.registerProvider('provider1', mockProvider1);
    });

    it('should delegate initialize to active provider', async () => {
      // Arrange
      const config: IAiProviderConfig = {
        apiKey: 'test-key',
        defaultModel: 'test-model',
      };

      // Act
      await registry.initialize(config);

      // Assert
      expect(mockProvider1.initialize).toHaveBeenCalledWith(config);
    });

    it('should delegate detectTicket to active provider', async () => {
      // Arrange
      const conversation = 'test conversation';
      const teamId = 'team-123';

      // Act
      const result = await registry.detectTicket(conversation, teamId);

      // Assert
      expect(mockProvider1.detectTicket).toHaveBeenCalledWith(
        conversation,
        teamId,
      );
      expect(result.success).toBe(true);
      expect(result.data?.requiresSupportTicket).toBe(true);
    });

    it('should delegate analyzeSentiment to active provider', async () => {
      // Arrange
      const conversation = 'test conversation';
      const teamId = 'team-123';

      // Act
      const result = await registry.analyzeSentiment(conversation, teamId);

      // Assert
      expect(mockProvider1.analyzeSentiment).toHaveBeenCalledWith(
        conversation,
        teamId,
      );
      expect(result.success).toBe(true);
      expect(result.data?.sentiment).toBe('positive');
    });

    it('should delegate detectUrgency to active provider', async () => {
      // Arrange
      const conversation = 'test conversation';
      const teamId = 'team-123';
      const urgencyLevels = ['low', 'medium', 'high'];

      // Act
      const result = await registry.detectUrgency(
        conversation,
        teamId,
        urgencyLevels,
      );

      // Assert
      expect(mockProvider1.detectUrgency).toHaveBeenCalledWith(
        conversation,
        teamId,
        urgencyLevels,
      );
      expect(result.success).toBe(true);
      expect(result.data?.urgency).toBe('medium');
    });

    it('should delegate routeToTeam to active provider', async () => {
      // Arrange
      const conversation = 'test conversation';
      const teamId = 'team-123';

      // Act
      const result = await registry.routeToTeam(conversation, teamId);

      // Assert
      expect(mockProvider1.routeToTeam).toHaveBeenCalledWith(
        conversation,
        teamId,
      );
      expect(result.success).toBe(true);
      expect(result.data?.recommendedTeam).toBe('engineering');
    });

    it('should delegate extractCustomFields to active provider', async () => {
      // Arrange
      const conversation = 'test conversation';
      const fields = ['field1', 'field2'];
      const teamId = 'team-123';

      // Act
      const result = await registry.extractCustomFields(
        conversation,
        fields,
        teamId,
      );

      // Assert
      expect(mockProvider1.extractCustomFields).toHaveBeenCalledWith(
        conversation,
        fields,
        teamId,
      );
      expect(result.success).toBe(true);
      expect(result.data?.fields).toHaveProperty('field1', 'value1');
    });

    it('should delegate executePrompt to active provider', async () => {
      // Arrange
      const request: IAiPromptRequest = {
        prompt: 'test prompt',
        conversation: 'test conversation',
      };

      // Act
      const result = await registry.executePrompt(request);

      // Assert
      expect(mockProvider1.executePrompt).toHaveBeenCalledWith(request);
      expect(result.success).toBe(true);
    });

    it('should delegate setPrompt to active provider', () => {
      // Arrange
      const promptType = PromptType.TICKET_DETECTION;
      const prompt = 'test prompt';

      // Act
      registry.setPrompt(promptType, prompt);

      // Assert
      expect(mockProvider1.setPrompt).toHaveBeenCalledWith(promptType, prompt);
    });

    it('should delegate getAvailableModels to active provider', () => {
      // Act
      const result = registry.getAvailableModels();

      // Assert
      expect(mockProvider1.getAvailableModels).toHaveBeenCalled();
      expect(result).toEqual(['model1', 'model2', 'model3']);
    });

    it('should delegate setActiveModel to active provider', () => {
      // Arrange
      const modelId = 'model2';

      // Act
      registry.setActiveModel(modelId);

      // Assert
      expect(mockProvider1.setActiveModel).toHaveBeenCalledWith(modelId);
    });

    it('should delegate getActiveModel to active provider', () => {
      // Act
      const result = registry.getActiveModel();

      // Assert
      expect(mockProvider1.getActiveModel).toHaveBeenCalled();
      expect(result).toBe('default-model');
    });

    it('should delegate addModel to active provider', () => {
      // Arrange
      const modelId = 'new-model';
      const config: IAiModelConfig = {
        modelId: 'new-model',
        temperature: 0.7,
      };

      // Act
      registry.addModel(modelId, config);

      // Assert
      expect(mockProvider1.addModel).toHaveBeenCalledWith(modelId, config);
    });

    it('should delegate getPrompt to active provider', () => {
      // Arrange
      const promptType = PromptType.TICKET_DETECTION;
      const teamId = 'team-123';

      // Act
      registry.getPrompt(promptType, teamId);

      // Assert
      expect(mockProvider1.getPrompt).toHaveBeenCalledWith(promptType, teamId);
    });

    it('should delegate complete to active provider', async () => {
      // Arrange
      const params: IAiCompletionParams = {
        prompt: 'test prompt',
        temperature: 0.7,
      };

      // Act
      const result = await registry.complete(params);

      // Assert
      expect(mockProvider1.complete).toHaveBeenCalledWith(params);
      expect(result).toHaveProperty('text', 'Completed text response');
    });

    it('should delegate generateTitle to active provider', async () => {
      // Arrange
      const conversation = 'test conversation';
      const teamId = 'team-123';

      // Act
      const result = await registry.generateTitle(conversation, teamId);

      // Assert
      expect(mockProvider1.generateTitle).toHaveBeenCalledWith(
        conversation,
        teamId,
      );
      expect(result.success).toBe(true);
      expect(result.data?.title).toBe('Generated Title');
    });

    it('should delegate generateDescription to active provider', async () => {
      // Arrange
      const conversation = 'test conversation';
      const teamId = 'team-123';

      // Act
      const result = await registry.generateDescription(conversation, teamId);

      // Assert
      expect(mockProvider1.generateDescription).toHaveBeenCalledWith(
        conversation,
        teamId,
      );
      expect(result.success).toBe(true);
      expect(result.data?.description).toBe(
        'Generated description for the conversation',
      );
    });
  });

  describe('Error handling with no active provider', () => {
    // No providers registered scenarios

    it('should return error response for detectTicket with no provider', async () => {
      // Act
      const result = await registry.detectTicket('test');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('No active AI provider');
    });

    it('should return error response for analyzeSentiment with no provider', async () => {
      // Act
      const result = await registry.analyzeSentiment('test');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('No active AI provider');
    });

    it('should return error response for detectUrgency with no provider', async () => {
      // Act
      const result = await registry.detectUrgency('test');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('No active AI provider');
    });

    it('should return error response for routeToTeam with no provider', async () => {
      // Act
      const result = await registry.routeToTeam('test');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('No active AI provider');
    });

    it('should return error response for extractCustomFields with no provider', async () => {
      // Act
      const result = await registry.extractCustomFields('test');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('No active AI provider');
    });

    it('should return error response for executePrompt with no provider', async () => {
      // Act
      const result = await registry.executePrompt({ prompt: 'test' });

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('No active AI provider');
    });

    it('should throw error for setPrompt with no provider', () => {
      // Assert
      expect(() =>
        registry.setPrompt(PromptType.TICKET_DETECTION, 'test'),
      ).toThrow('No active AI provider');
    });

    it('should return empty array for getAvailableModels with no provider', () => {
      // Act
      const result = registry.getAvailableModels();

      // Assert
      expect(result).toEqual([]);
    });

    it('should throw error for setActiveModel with no provider', () => {
      // Assert
      expect(() => registry.setActiveModel('model1')).toThrow(
        'No active AI provider',
      );
    });

    it('should throw error for getActiveModel with no provider', () => {
      // Assert
      expect(() => registry.getActiveModel()).toThrow('No active AI provider');
    });

    it('should throw error for addModel with no provider', () => {
      // Assert
      expect(() => registry.addModel('model1', { modelId: 'model1' })).toThrow(
        'No active AI provider',
      );
    });

    it('should throw error for complete with no provider', async () => {
      // Assert
      await expect(registry.complete({ prompt: 'test' })).rejects.toThrow(
        'No active AI provider',
      );
    });

    it('should return error response for generateTitle with no provider', async () => {
      // Act
      const result = await registry.generateTitle('test');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('No active AI provider');
    });

    it('should return error response for generateDescription with no provider', async () => {
      // Act
      const result = await registry.generateDescription('test');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('No active AI provider');
    });
  });

  describe('Team Prompts', () => {
    beforeEach(() => {
      registry.registerProvider('provider1', mockProvider1);
      registry.registerProvider('provider2', mockProvider2);
    });

    it('should set team prompts for all registered providers', () => {
      // Arrange
      const teamId = 'team-123';
      const prompts: TeamPrompts = {
        [PromptType.TICKET_DETECTION]: 'custom ticket detection prompt',
      };

      // Act
      registry.setTeamPrompts(teamId, prompts);

      // Assert
      expect(mockProvider1.setTeamPrompts).toHaveBeenCalledWith(
        teamId,
        prompts,
      );
      expect(mockProvider2.setTeamPrompts).toHaveBeenCalledWith(
        teamId,
        prompts,
      );
    });
  });
});
