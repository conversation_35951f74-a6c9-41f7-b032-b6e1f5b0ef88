import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { TransactionContext } from '../../../../../../src/database/common';
import { SlackMessagesRepository } from '../../../../../../src/database/entities/slack-messages/repositories/slack-messages.repository';
import { SlackMessages } from '../../../../../../src/database/entities/slack-messages/slack-messages.entity';

describe('SlackMessagesRepository', () => {
  let repository: SlackMessagesRepository;
  let mockTypeOrmRepository: Repository<SlackMessages>;

  beforeEach(async () => {
    // Create mock implementation for TypeORM repository methods
    mockTypeOrmRepository = {
      save: vi.fn(),
      create: vi.fn(),
      findOne: vi.fn(),
      findOneBy: vi.fn(),
      find: vi.fn(),
      update: vi.fn(),
      softDelete: vi.fn(),
      count: vi.fn(),
      exists: vi.fn(),
    } as unknown as Repository<SlackMessages>;

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SlackMessagesRepository,
        {
          provide: getRepositoryToken(SlackMessages),
          useValue: mockTypeOrmRepository,
        },
      ],
    }).compile();

    repository = module.get<SlackMessagesRepository>(SlackMessagesRepository);
  });

  describe('Inherited CRUD operations', () => {
    it('should create a new slack message entity', () => {
      // Arrange
      const slackMessage = {
        channelId: 'C12345',
        ts: '**********.123456',
        text: 'Test message',
      };

      (mockTypeOrmRepository.create as Mock).mockReturnValue({
        id: 'message-id',
        ...slackMessage,
      });

      // Act
      const result = repository.create(slackMessage);

      // Assert
      expect(result).toEqual({
        id: 'message-id',
        channelId: 'C12345',
        ts: '**********.123456',
        text: 'Test message',
      });
      expect(mockTypeOrmRepository.create).toHaveBeenCalledWith(slackMessage);
    });

    it('should save a slack message', async () => {
      // Arrange
      const slackMessage = {
        channelId: 'C12345',
        ts: '**********.123456',
        text: 'Test message',
      };

      (mockTypeOrmRepository.save as Mock).mockResolvedValue({
        id: 'message-id',
        ...slackMessage,
      });

      // Act
      const result = await repository.save(slackMessage);

      // Assert
      expect(result).toEqual({
        id: 'message-id',
        channelId: 'C12345',
        ts: '**********.123456',
        text: 'Test message',
      });
      expect(mockTypeOrmRepository.save).toHaveBeenCalledWith(slackMessage);
    });

    it('should find one slack message by id', async () => {
      // Arrange
      const slackMessage = {
        id: 'message-id',
        channelId: 'C12345',
        ts: '**********.123456',
        text: 'Test message',
      };

      (mockTypeOrmRepository.findOneBy as Mock).mockResolvedValue(slackMessage);

      // Act
      const result = await repository.findOneById('message-id');

      // Assert
      expect(result).toEqual(slackMessage);
      expect(mockTypeOrmRepository.findOneBy).toHaveBeenCalledWith({
        id: 'message-id',
      });
    });

    it('should find slack messages by condition', async () => {
      // Arrange
      const slackMessage = {
        id: 'message-id',
        channelId: 'C12345',
        ts: '**********.123456',
        text: 'Test message',
      };

      (mockTypeOrmRepository.findOne as Mock).mockResolvedValue(slackMessage);

      // Act
      const result = await repository.findByCondition({
        where: { channelId: 'C12345' },
      });

      // Assert
      expect(result).toEqual(slackMessage);
      expect(mockTypeOrmRepository.findOne).toHaveBeenCalledWith({
        where: { channelId: 'C12345' },
      });
    });

    it('should find all slack messages', async () => {
      // Arrange
      const slackMessages = [
        {
          id: 'message-id-1',
          channelId: 'C12345',
          ts: '**********.123456',
          text: 'Test message 1',
        },
        {
          id: 'message-id-2',
          channelId: 'C12345',
          ts: '**********.654321',
          text: 'Test message 2',
        },
      ];

      (mockTypeOrmRepository.find as Mock).mockResolvedValue(slackMessages);

      // Act
      const result = await repository.findAll();

      // Assert
      expect(result).toEqual(slackMessages);
      expect(mockTypeOrmRepository.find).toHaveBeenCalled();
    });

    it('should find slack messages with options', async () => {
      // Arrange
      const slackMessages = [
        {
          id: 'message-id-1',
          channelId: 'C12345',
          ts: '**********.123456',
          text: 'Test message 1',
        },
      ];
      const options = {
        where: { channelId: 'C12345' },
        take: 1,
        order: { createdAt: 'DESC' },
      };

      (mockTypeOrmRepository.find as Mock).mockResolvedValue(slackMessages);

      // Act
      const result = await repository.findAll(options);

      // Assert
      expect(result).toEqual(slackMessages);
      expect(mockTypeOrmRepository.find).toHaveBeenCalledWith(options);
    });

    it('should update a slack message', async () => {
      // Arrange
      const updateResult = { affected: 1 };
      (mockTypeOrmRepository.update as Mock).mockResolvedValue(updateResult);

      // Act
      const result = await repository.update('message-id', {
        text: 'Updated message',
      });

      // Assert
      expect(result).toEqual(updateResult);
      expect(mockTypeOrmRepository.update).toHaveBeenCalledWith('message-id', {
        text: 'Updated message',
      });
    });

    it('should update a slack message with transaction', async () => {
      // Arrange
      const updateResult = { affected: 1 };
      const mockTransaction = {
        manager: {
          update: vi.fn().mockResolvedValue(updateResult),
        },
      } as unknown as TransactionContext;

      // Act
      const result = await repository.updateWithTxn(
        mockTransaction,
        { id: 'message-id' },
        { text: 'Updated with transaction' },
      );

      // Assert
      expect(result).toEqual(updateResult);
      expect(mockTransaction.manager.update).toHaveBeenCalledWith(
        undefined,
        { id: 'message-id' },
        { text: 'Updated with transaction' },
      );
    });

    it('should soft delete a slack message', async () => {
      // Arrange
      const deleteResult = { affected: 1 };
      (mockTypeOrmRepository.softDelete as Mock).mockResolvedValue(
        deleteResult,
      );

      // Act
      const result = await repository.softDelete({ id: 'message-id' });

      // Assert
      expect(result).toEqual(deleteResult);
      expect(mockTypeOrmRepository.softDelete).toHaveBeenCalledWith({
        id: 'message-id',
      });
    });

    it('should count slack messages', async () => {
      // Arrange
      (mockTypeOrmRepository.count as Mock).mockResolvedValue(5);

      // Act
      const result = await repository.count();

      // Assert
      expect(result).toEqual(5);
      expect(mockTypeOrmRepository.count).toHaveBeenCalled();
    });

    it('should check if slack message exists', async () => {
      // Arrange
      (mockTypeOrmRepository.exists as Mock).mockResolvedValue(true);

      // Act
      const result = await repository.exists({
        where: { channelId: 'C12345' },
      });

      // Assert
      expect(result).toBe(true);
      expect(mockTypeOrmRepository.exists).toHaveBeenCalledWith({
        where: { channelId: 'C12345' },
      });
    });
  });

  describe('Error handling', () => {
    it('should handle errors when finding by id', async () => {
      // Arrange
      const mockError = new Error('Database error');
      (mockTypeOrmRepository.findOneBy as Mock).mockRejectedValue(mockError);

      // Act & Assert
      await expect(repository.findOneById('message-id')).rejects.toThrow(
        'Database error',
      );
    });

    it('should handle errors when saving', async () => {
      // Arrange
      const mockError = new Error('Save error');
      (mockTypeOrmRepository.save as Mock).mockRejectedValue(mockError);

      // Act & Assert
      await expect(repository.save({ text: 'Test' })).rejects.toThrow(
        'Save error',
      );
    });

    it('should handle errors when updating', async () => {
      // Arrange
      const mockError = new Error('Update error');
      (mockTypeOrmRepository.update as Mock).mockRejectedValue(mockError);

      // Act & Assert
      await expect(
        repository.update('message-id', { text: 'Updated' }),
      ).rejects.toThrow('Update error');
    });
  });

  describe('Additional repository methods', () => {
    it('should save many entities', async () => {
      // Arrange
      const slackMessages = [
        {
          channelId: 'C12345',
          ts: '**********.123456',
          text: 'Message 1',
        },
        {
          channelId: 'C12345',
          ts: '**********.654321',
          text: 'Message 2',
        },
      ];

      const savedMessages = slackMessages.map((msg, index) => ({
        id: `message-id-${index + 1}`,
        ...msg,
      }));

      (mockTypeOrmRepository.save as Mock).mockResolvedValue(savedMessages);

      // Act
      const result = await repository.saveMany(slackMessages);

      // Assert
      expect(result).toEqual(savedMessages);
      expect(mockTypeOrmRepository.save).toHaveBeenCalledWith(slackMessages);
    });

    it('should save many entities with transaction', async () => {
      // Arrange
      const slackMessages = [
        {
          channelId: 'C12345',
          ts: '**********.123456',
          text: 'Message 1',
        },
        {
          channelId: 'C12345',
          ts: '**********.654321',
          text: 'Message 2',
        },
      ];

      const savedMessages = slackMessages.map((msg, index) => ({
        id: `message-id-${index + 1}`,
        ...msg,
      }));

      const mockTransaction = {
        manager: {
          save: vi.fn().mockResolvedValue(savedMessages),
        },
      } as unknown as TransactionContext;

      // Act
      const result = await repository.saveManyWithTxn(
        mockTransaction,
        slackMessages as any,
      );

      // Assert
      expect(result).toEqual(savedMessages);
      expect(mockTransaction.manager.save).toHaveBeenCalledWith(
        undefined,
        slackMessages,
      );
    });
  });
});
