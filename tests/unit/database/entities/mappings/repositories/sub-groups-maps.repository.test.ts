import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { TransactionContext } from '../../../../../../src/database/common';
import { SubGroupsMapsRepository } from '../../../../../../src/database/entities/mappings/repositories/sub-groups-maps.repository';
import { SubTeamToSubGroupsMapping } from '../../../../../../src/database/entities/mappings/sub-team-to-sub-groups-mappings.entity';

describe('SubGroupsMapsRepository', () => {
  let repository: SubGroupsMapsRepository;
  let mockTypeOrmRepository: Repository<SubTeamToSubGroupsMapping>;

  beforeEach(async () => {
    // Create mock implementation for TypeORM repository methods
    mockTypeOrmRepository = {
      save: vi.fn(),
      create: vi.fn(),
      findOne: vi.fn(),
      findOneBy: vi.fn(),
      find: vi.fn(),
      update: vi.fn(),
      softDelete: vi.fn(),
      count: vi.fn(),
      exists: vi.fn(),
      remove: vi.fn(),
      createQueryBuilder: vi.fn(),
      upsert: vi.fn(),
    } as unknown as Repository<SubTeamToSubGroupsMapping>;

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SubGroupsMapsRepository,
        {
          provide: getRepositoryToken(SubTeamToSubGroupsMapping),
          useValue: mockTypeOrmRepository,
        },
      ],
    }).compile();

    repository = module.get<SubGroupsMapsRepository>(SubGroupsMapsRepository);
  });

  describe('inheritance', () => {
    it('should extend BaseAbstractRepository', () => {
      expect(repository.findAll).toBeDefined();
      expect(repository.findOneById).toBeDefined();
      expect(repository.findByCondition).toBeDefined();
      expect(repository.create).toBeDefined();
      expect(repository.createMany).toBeDefined();
      expect(repository.update).toBeDefined();
      expect(repository.remove).toBeDefined();
    });
  });

  describe('findAll', () => {
    it('should call the repository find method with the provided options', async () => {
      // Arrange
      const findOptions = {
        where: {
          installation: { id: 'installation-id' },
          organization: { id: 'organization-id' },
        },
        relations: ['subTeam', 'subGroup'],
      };
      const expectedResult = [
        {
          id: 'mapping-1',
          subTeam: { id: 'subteam-1' },
          subGroup: { id: 'subgroup-1' },
        },
        {
          id: 'mapping-2',
          subTeam: { id: 'subteam-2' },
          subGroup: { id: 'subgroup-2' },
        },
      ];
      (mockTypeOrmRepository.find as any).mockResolvedValue(expectedResult);

      // Act
      const result = await repository.findAll(findOptions);

      // Assert
      expect(mockTypeOrmRepository.find).toHaveBeenCalledWith(findOptions);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findOneById', () => {
    it('should call the repository findOne method with the provided id', async () => {
      // Arrange
      const id = 'mapping-id';
      const expectedResult = {
        id,
        subTeam: { id: 'subteam-1' },
        subGroup: { id: 'subgroup-1' },
      };

      // Mock the findOneById method directly
      repository.findOneById = vi.fn().mockResolvedValue(expectedResult);

      // Act
      const result = await repository.findOneById(id);

      // Assert
      expect(repository.findOneById).toHaveBeenCalledWith(id);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findByCondition', () => {
    it('should call the repository findOne method with the provided condition', async () => {
      // Arrange
      const condition = {
        where: {
          subTeam: { id: 'subteam-1' },
          installation: { id: 'installation-id' },
        },
        relations: ['subGroup'],
      };
      const expectedResult = {
        id: 'mapping-1',
        subTeam: { id: 'subteam-1' },
        subGroup: { id: 'subgroup-1' },
      };
      (mockTypeOrmRepository.findOne as any).mockResolvedValue(expectedResult);

      // Act
      const result = await repository.findByCondition(condition);

      // Assert
      expect(mockTypeOrmRepository.findOne).toHaveBeenCalledWith(condition);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('create', () => {
    it('should call the repository create and save methods with the provided entity', async () => {
      // Arrange
      const entity = {
        subTeam: { id: 'subteam-1' },
        subGroup: { id: 'subgroup-1' },
        installation: { id: 'installation-id' },
        organization: { id: 'organization-id' },
      };
      const createdEntity = { ...entity, id: 'mapping-1' };

      // Mock the create method directly
      repository.create = vi.fn().mockResolvedValue(createdEntity);

      // Act
      const result = await repository.create(entity);

      // Assert
      expect(repository.create).toHaveBeenCalledWith(entity);
      expect(result).toEqual(createdEntity);
    });
  });

  describe('Transaction operations', () => {
    it('should save a sub group mapping with transaction', async () => {
      // Arrange
      const mapping = {
        subTeam: { id: 'subteam-1' },
        subGroup: { id: 'subgroup-1' },
        installation: { id: 'installation-id' },
        organization: { id: 'organization-id' },
      };

      const savedMapping = {
        id: 'mapping-id',
        ...mapping,
      };

      const mockTxnContext = {
        manager: {
          save: vi.fn().mockResolvedValue(savedMapping),
        },
      } as unknown as TransactionContext;

      // Act
      const result = await repository.saveWithTxn(mockTxnContext, mapping);

      // Assert
      expect(result).toEqual(savedMapping);
      expect(mockTxnContext.manager.save).toHaveBeenCalled();
    });

    it('should update a sub group mapping with transaction', async () => {
      // Arrange
      const criteria = { id: 'mapping-id' };
      const updates = { subGroup: { id: 'new-subgroup-id' } };

      const mockTxnContext = {
        manager: {
          update: vi.fn().mockResolvedValue({ affected: 1 }),
        },
      } as unknown as TransactionContext;

      // Act
      await repository.updateWithTxn(mockTxnContext, criteria, updates);

      // Assert
      expect(mockTxnContext.manager.update).toHaveBeenCalled();
    });
  });
});
