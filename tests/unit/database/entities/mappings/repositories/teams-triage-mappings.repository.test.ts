import { DataSource, Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { BotCtx } from '../../../../../../src/auth/interfaces/context.interface';
import { TeamTriageRuleMappingRepository } from '../../../../../../src/database/entities/mappings/repositories/teams-triage-mappings.repository';

describe('TeamTriageRuleMappingRepository', () => {
  let repository: TeamTriageRuleMappingRepository;
  let mockDataSource: DataSource;
  let mockEntityManager: any;

  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();

    // Create mock implementations
    mockEntityManager = {
      find: vi.fn(),
      findOne: vi.fn(),
      save: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      softDelete: vi.fn(),
    };

    mockDataSource = {
      createEntityManager: vi.fn().mockReturnValue(mockEntityManager),
    } as unknown as DataSource;

    // Create repository instance
    repository = new TeamTriageRuleMappingRepository(mockDataSource);

    // Mock the repository methods
    repository.find = vi.fn();
  });

  describe('findTeamMappings', () => {
    it('should find team mappings for a platform team', async () => {
      // Arrange
      const platformTeamId = 'team-id';
      const botCtx: BotCtx = {
        installation: { id: 'installation-id' } as any,
        organization: { id: 'organization-id' } as any,
        installations: [],
      };

      const mappings = [
        {
          id: 'mapping-id-1',
          platformTeam: { id: platformTeamId },
          installation: { id: 'installation-id' },
          organization: { id: 'organization-id' },
          triageChannels: [{ id: 'channel-id-1' }],
        },
        {
          id: 'mapping-id-2',
          platformTeam: { id: platformTeamId },
          installation: { id: 'installation-id' },
          organization: { id: 'organization-id' },
          triageChannels: [{ id: 'channel-id-2' }],
        },
      ];

      (repository.find as Mock).mockResolvedValue(mappings);

      // Act
      const result = await repository.findTeamMappings(platformTeamId, botCtx);

      // Assert
      expect(result).toEqual(mappings);
      expect(repository.find).toHaveBeenCalledWith({
        where: {
          platformTeam: { id: platformTeamId },
          installation: { id: 'installation-id' },
          organization: { id: 'organization-id' },
        },
        relations: ['triageChannels'],
      });
    });

    it('should return an empty array when no mappings are found', async () => {
      // Arrange
      const platformTeamId = 'team-id';
      const botCtx: BotCtx = {
        installation: { id: 'installation-id' } as any,
        organization: { id: 'organization-id' } as any,
        installations: [],
      };

      (repository.find as Mock).mockResolvedValue([]);

      // Act
      const result = await repository.findTeamMappings(platformTeamId, botCtx);

      // Assert
      expect(result).toEqual([]);
      expect(repository.find).toHaveBeenCalled();
    });
  });

  describe('findActiveRulesForTeam', () => {
    it('should find active triage rules for a platform team', async () => {
      // Arrange
      const platformTeamId = 'team-id';
      const botCtx: BotCtx = {
        installation: { id: 'installation-id' } as any,
        organization: { id: 'organization-id' } as any,
        installations: [],
      };

      const mappings = [
        {
          id: 'mapping-id-1',
          platformTeam: { id: platformTeamId },
          installation: { id: 'installation-id' },
          organization: { id: 'organization-id' },
          isEnabled: true,
          triageChannels: [{ id: 'channel-id-1' }],
        },
      ];

      (repository.find as Mock).mockResolvedValue(mappings);

      // Act
      const result = await repository.findActiveRulesForTeam(
        platformTeamId,
        botCtx,
      );

      // Assert
      expect(result).toEqual(mappings);
      expect(repository.find).toHaveBeenCalledWith({
        where: {
          platformTeam: { id: platformTeamId },
          installation: { id: 'installation-id' },
          organization: { id: 'organization-id' },
          isEnabled: true,
        },
        relations: ['triageChannels'],
      });
    });

    it('should return an empty array when no active rules are found', async () => {
      // Arrange
      const platformTeamId = 'team-id';
      const botCtx: BotCtx = {
        installation: { id: 'installation-id' } as any,
        organization: { id: 'organization-id' } as any,
        installations: [],
      };

      (repository.find as Mock).mockResolvedValue([]);

      // Act
      const result = await repository.findActiveRulesForTeam(
        platformTeamId,
        botCtx,
      );

      // Assert
      expect(result).toEqual([]);
      expect(repository.find).toHaveBeenCalled();
    });
  });

  describe('setDefaultMapping', () => {
    it('should set a default mapping for a platform team', async () => {
      // Arrange
      const platformTeamId = 'team-id';
      const botCtx: BotCtx = {
        installation: { id: 'installation-id' } as any,
        organization: { id: 'organization-id' } as any,
        installations: [],
      };
      const channel = { id: 'channel-id', name: 'channel-name' };

      const mockQueryBuilder = {
        update: vi.fn().mockReturnThis(),
        set: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        andWhere: vi.fn().mockReturnThis(),
        execute: vi.fn().mockResolvedValue({ affected: 1 }),
      };

      const mockTransaction = vi.fn().mockImplementation(async (callback) => {
        return callback({
          createQueryBuilder: vi.fn().mockReturnValue(mockQueryBuilder),
          create: vi.fn().mockReturnValue({
            id: 'new-mapping-id',
            platformTeam: { id: platformTeamId },
            installation: { id: botCtx.installation.id },
            organization: { id: botCtx.organization.id },
            triageChannels: [{ id: channel.id }],
            isDefault: true,
            isEnabled: true,
          }),
          save: vi.fn().mockResolvedValue({
            id: 'new-mapping-id',
            platformTeam: { id: platformTeamId },
            installation: { id: botCtx.installation.id },
            organization: { id: botCtx.organization.id },
            triageChannels: [{ id: channel.id }],
            isDefault: true,
            isEnabled: true,
          }),
        });
      });

      mockDataSource.transaction = mockTransaction;

      // Act
      const result = await repository.setDefaultMapping(
        platformTeamId,
        botCtx,
        channel as any,
      );

      // Assert
      expect(result).toEqual({
        id: 'new-mapping-id',
        platformTeam: { id: platformTeamId },
        installation: { id: botCtx.installation.id },
        organization: { id: botCtx.organization.id },
        triageChannels: [{ id: channel.id }],
        isDefault: true,
        isEnabled: true,
      });
      expect(mockTransaction).toHaveBeenCalled();
    });
  });

  describe('updateRuleChannels', () => {
    it('should update channels for a rule mapping', async () => {
      // Arrange
      const ruleId = 'rule-id';
      const channels = [
        { id: 'channel-id-1', name: 'channel-1' },
        { id: 'channel-id-2', name: 'channel-2' },
      ];
      const botCtx: BotCtx = {
        installation: { id: 'installation-id' } as any,
        organization: { id: 'organization-id' } as any,
        installations: [],
      };

      const existingMapping = {
        id: ruleId,
        platformTeam: { id: 'team-id' },
        installation: { id: botCtx.installation.id },
        organization: { id: botCtx.organization.id },
        triageChannels: [{ id: 'old-channel-id' }],
        isDefault: false,
        isEnabled: true,
      };

      const updatedMapping = {
        ...existingMapping,
        triageChannels: channels,
      };

      const mockTransaction = vi.fn().mockImplementation(async (callback) => {
        return callback({
          findOne: vi.fn().mockResolvedValue(existingMapping),
          save: vi.fn().mockResolvedValue(updatedMapping),
        });
      });

      mockDataSource.transaction = mockTransaction;

      // Act
      const result = await repository.updateRuleChannels(
        ruleId,
        channels as any,
        botCtx,
      );

      // Assert
      expect(result).toEqual(updatedMapping);
      expect(mockTransaction).toHaveBeenCalled();
    });

    it('should throw an error if rule mapping is not found', async () => {
      // Arrange
      const ruleId = 'non-existent-rule-id';
      const channels = [{ id: 'channel-id-1', name: 'channel-1' }];
      const botCtx: BotCtx = {
        installation: { id: 'installation-id' } as any,
        organization: { id: 'organization-id' } as any,
        installations: [],
      };

      const mockTransaction = vi.fn().mockImplementation(async (callback) => {
        return callback({
          findOne: vi.fn().mockResolvedValue(null),
        });
      });

      mockDataSource.transaction = mockTransaction;

      // Act & Assert
      await expect(
        repository.updateRuleChannels(ruleId, channels as any, botCtx),
      ).rejects.toThrow('Rule mapping not found');
      expect(mockTransaction).toHaveBeenCalled();
    });
  });

  describe('Inherited Repository methods', () => {
    it('should inherit methods from TypeORM Repository', () => {
      // Assert
      expect(repository).toBeInstanceOf(Repository);
      expect(repository.find).toBeDefined();
      expect(repository.findOne).toBeDefined();
      expect(repository.save).toBeDefined();
      expect(repository.update).toBeDefined();
      expect(repository.delete).toBeDefined();
      expect(repository.softDelete).toBeDefined();
    });
  });
});
