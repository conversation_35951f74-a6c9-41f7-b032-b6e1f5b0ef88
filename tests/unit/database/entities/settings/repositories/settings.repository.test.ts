import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { TransactionContext } from '../../../../../../src/database/common';
import { SettingsRepository } from '../../../../../../src/database/entities/settings/repositories/settings.repository';
import { Settings } from '../../../../../../src/database/entities/settings/settings.entity';

describe('SettingsRepository', () => {
  let repository: SettingsRepository;
  let mockTypeOrmRepository: Repository<Settings>;

  beforeEach(async () => {
    // Create mock implementation for TypeORM repository methods
    mockTypeOrmRepository = {
      save: vi.fn(),
      create: vi.fn(),
      findOne: vi.fn(),
      findOneBy: vi.fn(),
      find: vi.fn(),
      update: vi.fn(),
      softDelete: vi.fn(),
      count: vi.fn(),
      exists: vi.fn(),
    } as unknown as Repository<Settings>;

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SettingsRepository,
        {
          provide: getRepositoryToken(Settings),
          useValue: mockTypeOrmRepository,
        },
      ],
    }).compile();

    repository = module.get<SettingsRepository>(SettingsRepository);
  });

  describe('Inherited CRUD operations', () => {
    it('should create a new settings entity', () => {
      // Arrange
      const settings = {
        key: 'test_key',
        value: JSON.stringify({ setting: 'value' }),
        scope: 'organization',
      };

      (mockTypeOrmRepository.create as Mock).mockReturnValue({
        id: 'settings-id',
        ...settings,
      });

      // Act
      const result = repository.create(settings);

      // Assert
      expect(result).toEqual({
        id: 'settings-id',
        key: 'test_key',
        value: JSON.stringify({ setting: 'value' }),
        scope: 'organization',
      });
      expect(mockTypeOrmRepository.create).toHaveBeenCalledWith(settings);
    });

    it('should save settings', async () => {
      // Arrange
      const settings = {
        key: 'test_key',
        value: JSON.stringify({ setting: 'value' }),
        scope: 'organization',
      };

      (mockTypeOrmRepository.save as Mock).mockResolvedValue({
        id: 'settings-id',
        ...settings,
      });

      // Act
      const result = await repository.save(settings);

      // Assert
      expect(result).toEqual({
        id: 'settings-id',
        key: 'test_key',
        value: JSON.stringify({ setting: 'value' }),
        scope: 'organization',
      });
      expect(mockTypeOrmRepository.save).toHaveBeenCalledWith(settings);
    });

    it('should find one settings by id', async () => {
      // Arrange
      const settings = {
        id: 'settings-id',
        key: 'test_key',
        value: JSON.stringify({ setting: 'value' }),
        scope: 'organization',
      };

      (mockTypeOrmRepository.findOneBy as Mock).mockResolvedValue(settings);

      // Act
      const result = await repository.findOneById('settings-id');

      // Assert
      expect(result).toEqual(settings);
      expect(mockTypeOrmRepository.findOneBy).toHaveBeenCalledWith({
        id: 'settings-id',
      });
    });

    it('should find settings by condition', async () => {
      // Arrange
      const settings = {
        id: 'settings-id',
        key: 'test_key',
        value: JSON.stringify({ setting: 'value' }),
        scope: 'organization',
      };

      (mockTypeOrmRepository.findOne as Mock).mockResolvedValue(settings);

      // Act
      const result = await repository.findByCondition({
        where: { key: 'test_key' },
      });

      // Assert
      expect(result).toEqual(settings);
      expect(mockTypeOrmRepository.findOne).toHaveBeenCalledWith({
        where: { key: 'test_key' },
      });
    });

    it('should find all settings', async () => {
      // Arrange
      const settingsArray = [
        {
          id: 'settings-id-1',
          key: 'test_key_1',
          value: JSON.stringify({ setting: 'value1' }),
          scope: 'organization',
        },
        {
          id: 'settings-id-2',
          key: 'test_key_2',
          value: JSON.stringify({ setting: 'value2' }),
          scope: 'installation',
        },
      ];

      (mockTypeOrmRepository.find as Mock).mockResolvedValue(settingsArray);

      // Act
      const result = await repository.findAll();

      // Assert
      expect(result).toEqual(settingsArray);
      expect(mockTypeOrmRepository.find).toHaveBeenCalled();
    });

    it('should find settings with options', async () => {
      // Arrange
      const settingsArray = [
        {
          id: 'settings-id-1',
          key: 'test_key_1',
          value: JSON.stringify({ setting: 'value1' }),
          scope: 'organization',
        },
      ];
      const options = {
        where: { scope: 'organization' },
        take: 1,
        skip: 0,
      };

      (mockTypeOrmRepository.find as Mock).mockResolvedValue(settingsArray);

      // Act
      const result = await repository.findAll(options);

      // Assert
      expect(result).toEqual(settingsArray);
      expect(mockTypeOrmRepository.find).toHaveBeenCalledWith(options);
    });

    it('should update settings', async () => {
      // Arrange
      const updateResult = { affected: 1 };
      (mockTypeOrmRepository.update as Mock).mockResolvedValue(updateResult);

      // Act
      const result = await repository.update('settings-id', {
        value: JSON.stringify({ setting: 'updated_value' }),
      });

      // Assert
      expect(result).toEqual(updateResult);
      expect(mockTypeOrmRepository.update).toHaveBeenCalledWith('settings-id', {
        value: JSON.stringify({ setting: 'updated_value' }),
      });
    });

    it('should update settings with transaction', async () => {
      // Arrange
      const criteria = { id: 'settings-id' };
      const updates = { value: JSON.stringify({ setting: 'updated_value' }) };
      const updateResult = { affected: 1 };

      const mockTxnContext = {
        manager: {
          update: vi.fn().mockResolvedValue(updateResult),
        },
      } as unknown as TransactionContext;

      // Act
      const result = await repository.updateWithTxn(
        mockTxnContext,
        criteria,
        updates,
      );

      // Assert
      expect(result).toEqual(updateResult);
      expect(mockTxnContext.manager.update).toHaveBeenCalledWith(
        undefined,
        criteria,
        updates,
      );
    });

    it('should soft delete settings', async () => {
      // Arrange
      const deleteResult = { affected: 1 };
      (mockTypeOrmRepository.softDelete as Mock).mockResolvedValue(
        deleteResult,
      );

      // Act
      const result = await repository.softDelete({ id: 'settings-id' });

      // Assert
      expect(result).toEqual(deleteResult);
      expect(mockTypeOrmRepository.softDelete).toHaveBeenCalledWith({
        id: 'settings-id',
      });
    });

    it('should count settings', async () => {
      // Arrange
      (mockTypeOrmRepository.count as Mock).mockResolvedValue(5);

      // Act
      const result = await repository.count();

      // Assert
      expect(result).toEqual(5);
      expect(mockTypeOrmRepository.count).toHaveBeenCalled();
    });

    it('should check if settings exists', async () => {
      // Arrange
      (mockTypeOrmRepository.exists as Mock).mockResolvedValue(true);

      // Act
      const result = await repository.exists({
        where: { key: 'test_key' },
      });

      // Assert
      expect(result).toBe(true);
      expect(mockTypeOrmRepository.exists).toHaveBeenCalledWith({
        where: { key: 'test_key' },
      });
    });
  });
});
