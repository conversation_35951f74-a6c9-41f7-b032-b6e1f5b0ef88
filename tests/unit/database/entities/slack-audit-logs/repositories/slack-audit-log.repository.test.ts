import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { TransactionContext } from '../../../../../../src/database/common';
import { AuditLogOp, AuditLogVisibility } from '../../../../../../src/database/constants';
import { SlackAuditLog } from '../../../../../../src/database/entities/slack-audit-logs/slack-audit-logs.entity';
import { SlackAuditLogRepository } from '../../../../../../src/database/entities/slack-audit-logs/repositories/slack-audit-log.repository';

describe('SlackAuditLogRepository', () => {
  let repository: SlackAuditLogRepository;
  let mockTypeOrmRepository: Repository<SlackAuditLog>;

  beforeEach(async () => {
    // Create mock implementation for TypeORM repository methods
    mockTypeOrmRepository = {
      save: vi.fn(),
      create: vi.fn(),
      findOne: vi.fn(),
      findOneBy: vi.fn(),
      find: vi.fn(),
      update: vi.fn(),
      softDelete: vi.fn(),
      count: vi.fn(),
      exists: vi.fn(),
    } as unknown as Repository<SlackAuditLog>;

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SlackAuditLogRepository,
        {
          provide: getRepositoryToken(SlackAuditLog),
          useValue: mockTypeOrmRepository,
        },
      ],
    }).compile();

    repository = module.get<SlackAuditLogRepository>(SlackAuditLogRepository);
  });

  describe('Inherited CRUD operations', () => {
    it('should create a new audit log entity', () => {
      // Arrange
      const auditLog = {
        eventTs: '**********.123456',
        activityPerformedBy: 'U12345',
        activity: 'Test activity',
        description: 'Test description',
        op: AuditLogOp.CREATE,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation: { id: 'installation-id' },
        organization: { id: 'organization-id' },
      };

      (mockTypeOrmRepository.create as Mock).mockReturnValue({
        id: 'audit-log-id',
        ...auditLog,
      });

      // Act
      const result = repository.create(auditLog);

      // Assert
      expect(result).toEqual({
        id: 'audit-log-id',
        eventTs: '**********.123456',
        activityPerformedBy: 'U12345',
        activity: 'Test activity',
        description: 'Test description',
        op: AuditLogOp.CREATE,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation: { id: 'installation-id' },
        organization: { id: 'organization-id' },
      });
      expect(mockTypeOrmRepository.create).toHaveBeenCalledWith(auditLog);
    });

    it('should save an audit log', async () => {
      // Arrange
      const auditLog = {
        eventTs: '**********.123456',
        activityPerformedBy: 'U12345',
        activity: 'Test activity',
        description: 'Test description',
        op: AuditLogOp.CREATE,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation: { id: 'installation-id' },
        organization: { id: 'organization-id' },
      };

      (mockTypeOrmRepository.save as Mock).mockResolvedValue({
        id: 'audit-log-id',
        ...auditLog,
      });

      // Act
      const result = await repository.save(auditLog);

      // Assert
      expect(result).toEqual({
        id: 'audit-log-id',
        eventTs: '**********.123456',
        activityPerformedBy: 'U12345',
        activity: 'Test activity',
        description: 'Test description',
        op: AuditLogOp.CREATE,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation: { id: 'installation-id' },
        organization: { id: 'organization-id' },
      });
      expect(mockTypeOrmRepository.save).toHaveBeenCalledWith(auditLog);
    });

    it('should find one audit log by id', async () => {
      // Arrange
      const auditLog = {
        id: 'audit-log-id',
        eventTs: '**********.123456',
        activityPerformedBy: 'U12345',
        activity: 'Test activity',
        description: 'Test description',
        op: AuditLogOp.CREATE,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation: { id: 'installation-id' },
        organization: { id: 'organization-id' },
      };

      (mockTypeOrmRepository.findOneBy as Mock).mockResolvedValue(auditLog);

      // Act
      const result = await repository.findOneById('audit-log-id');

      // Assert
      expect(result).toEqual(auditLog);
      expect(mockTypeOrmRepository.findOneBy).toHaveBeenCalledWith({
        id: 'audit-log-id',
      });
    });

    it('should find all audit logs', async () => {
      // Arrange
      const auditLogs = [
        {
          id: 'audit-log-id-1',
          eventTs: '**********.123456',
          activityPerformedBy: 'U12345',
          activity: 'Test activity 1',
          description: 'Test description 1',
          op: AuditLogOp.CREATE,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation: { id: 'installation-id' },
          organization: { id: 'organization-id' },
        },
        {
          id: 'audit-log-id-2',
          eventTs: '**********.654321',
          activityPerformedBy: 'U67890',
          activity: 'Test activity 2',
          description: 'Test description 2',
          op: AuditLogOp.UPDATE,
          visibility: AuditLogVisibility.ORGANIZATION,
          installation: { id: 'installation-id' },
          organization: { id: 'organization-id' },
        },
      ];

      (mockTypeOrmRepository.find as Mock).mockResolvedValue(auditLogs);

      // Act
      const result = await repository.findAll();

      // Assert
      expect(result).toEqual(auditLogs);
      expect(mockTypeOrmRepository.find).toHaveBeenCalled();
    });
  });

  describe('recordAuditLog', () => {
    it('should save audit log without transaction context', async () => {
      // Arrange
      const auditLog = {
        eventTs: '**********.123456',
        activityPerformedBy: 'U12345',
        activity: 'Test activity',
        description: 'Test description',
        op: AuditLogOp.CREATE,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation: { id: 'installation-id' },
        organization: { id: 'organization-id' },
      };

      (mockTypeOrmRepository.save as Mock).mockResolvedValue({
        id: 'audit-log-id',
        ...auditLog,
      });

      // Act
      await repository.recordAuditLog(auditLog);

      // Assert
      expect(mockTypeOrmRepository.save).toHaveBeenCalledWith({
        eventTs: '**********.123456',
        activityPerformedBy: 'U12345',
        activity: 'Test activity',
        description: 'Test description',
        op: AuditLogOp.CREATE,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation: { id: 'installation-id' },
        organization: { id: 'organization-id' },
      });
    });

    it('should save audit log with transaction context', async () => {
      // Arrange
      const auditLog = {
        eventTs: '**********.123456',
        activityPerformedBy: 'U12345',
        activity: 'Test activity',
        description: 'Test description',
        op: AuditLogOp.CREATE,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation: { id: 'installation-id' },
        organization: { id: 'organization-id' },
      };

      const txnContext = { id: 'txn-1' } as TransactionContext;

      // Mock the saveWithTxn method
      repository.saveWithTxn = vi.fn().mockResolvedValue({
        id: 'audit-log-id',
        ...auditLog,
      });

      // Act
      await repository.recordAuditLog(auditLog, txnContext);

      // Assert
      expect(repository.saveWithTxn).toHaveBeenCalledWith(txnContext, {
        eventTs: '**********.123456',
        activityPerformedBy: 'U12345',
        activity: 'Test activity',
        description: 'Test description',
        op: AuditLogOp.CREATE,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation: { id: 'installation-id' },
        organization: { id: 'organization-id' },
      });
    });

    it('should handle errors when saving audit log', async () => {
      // Arrange
      const auditLog = {
        eventTs: '**********.123456',
        activityPerformedBy: 'U12345',
        activity: 'Test activity',
        description: 'Test description',
        op: AuditLogOp.CREATE,
        visibility: AuditLogVisibility.ORGANIZATION,
        installation: { id: 'installation-id' },
        organization: { id: 'organization-id' },
      };

      const error = new Error('Test error');
      (mockTypeOrmRepository.save as Mock).mockRejectedValue(error);

      // Mock console.error
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act
      await repository.recordAuditLog(auditLog);

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith(
        '[SlackAuditLogRepository] Failed to save audit log',
        error,
      );

      // Restore console.error
      consoleSpy.mockRestore();
    });
  });
});
