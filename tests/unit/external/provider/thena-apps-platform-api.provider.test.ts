import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { Config<PERSON>eys, ConfigService } from '../../../../src/config/config.service';
import { Organizations } from '../../../../src/database/entities/organizations/organizations.entity';
import { ThenaAppsPlatformApiProvider } from '../../../../src/external/provider/thena-apps-platform-api.provider';
import { EmittableSlackEvents, ReactionAddedEvent } from '../../../../src/external/provider/constants/platform-events.constants';
import { ILogger } from '../../../../src/utils';
import { SentryService } from '../../../../src/utils/filters/sentry-alerts.filter';

// Mock global fetch
global.fetch = vi.fn();

describe('ThenaAppsPlatformApiProvider', () => {
  let provider: ThenaAppsPlatformApiProvider;
  let mockLogger: ILogger;
  let mockConfigService: ConfigService;
  let mockSentryService: SentryService;
  let mockOrganization: Organizations;

  beforeEach(() => {
    vi.resetAllMocks();

    // Mock the logger
    mockLogger = {
      error: vi.fn(),
      debug: vi.fn(),
    } as unknown as ILogger;

    // Mock the config service
    mockConfigService = {
      get: vi.fn(),
    } as unknown as ConfigService;

    // Mock the sentry service
    mockSentryService = {
      captureException: vi.fn(),
    } as unknown as SentryService;

    // Mock the organization
    mockOrganization = {
      id: 'org-1',
      uid: 'org-uid-1',
      apiKey: 'api-key-1',
      name: 'Test Organization',
      externalPk: 'ext-1',
      installingUserId: 'user-1',
      metadata: {
        applicationId: 'app-1',
        installationId: 'install-1',
        createdBy: 'user-1',
      },
    } as Organizations;

    // Configure the config service mock
    (mockConfigService.get as Mock).mockImplementation((key: string) => {
      if (key === ConfigKeys.APPS_PLATFORM_API_URL) {
        return 'https://api.apps-platform.test';
      }
      return '';
    });

    // Create the provider
    provider = new ThenaAppsPlatformApiProvider(mockLogger, mockSentryService, mockConfigService);
  });

  describe('proxy', () => {
    it('should throw an error if the organization has no API key', async () => {
      const orgWithoutApiKey = { ...mockOrganization, apiKey: null } as unknown as Organizations;

      try {
        await provider.proxy(orgWithoutApiKey, 'GET', '/test');
        expect(true).toBe(false); // This line should not be reached
      } catch (error) {
        expect(error.message).toBe('Organization has no API key');
      }
    });

    it('should make a GET request with the correct headers', async () => {
      const mockResponse = new Response(JSON.stringify({ data: 'test' }), {
        status: 200,
      });
      (fetch as Mock).mockResolvedValue(mockResponse);

      await provider.proxy(mockOrganization, 'GET', '/test');

      expect(fetch).toHaveBeenCalledWith(
        'https://api.apps-platform.test/test',
        expect.objectContaining({
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'x-org-id': mockOrganization.uid,
            'x-bot-key': mockOrganization.apiKey,
          },
        })
      );
    });

    it('should make a POST request with the correct body', async () => {
      const mockResponse = new Response(JSON.stringify({ data: 'test' }), {
        status: 200,
      });
      (fetch as Mock).mockResolvedValue(mockResponse);
      const body = { test: 'data' };

      await provider.proxy(mockOrganization, 'POST', '/test', body);

      expect(fetch).toHaveBeenCalledWith(
        'https://api.apps-platform.test/test',
        expect.objectContaining({
          method: 'POST',
          headers: expect.any(Object),
          body: JSON.stringify(body),
        })
      );
    });

    it('should handle empty body for non-GET requests', async () => {
      const mockResponse = new Response(JSON.stringify({ data: 'test' }), {
        status: 200,
      });
      (fetch as Mock).mockResolvedValue(mockResponse);

      await provider.proxy(mockOrganization, 'POST', '/test');

      expect(fetch).toHaveBeenCalledWith(
        'https://api.apps-platform.test/test',
        expect.objectContaining({
          method: 'POST',
          headers: expect.any(Object),
          body: '{}', // Empty JSON object
        })
      );
    });

    it('should handle URL path normalization', async () => {
      const mockResponse = new Response(JSON.stringify({ data: 'test' }), {
        status: 200,
      });
      (fetch as Mock).mockResolvedValue(mockResponse);

      await provider.proxy(mockOrganization, 'GET', '/test/path');

      expect(fetch).toHaveBeenCalledWith(
        'https://api.apps-platform.test/test/path',
        expect.any(Object)
      );

      (fetch as Mock).mockReset();
      (fetch as Mock).mockResolvedValue(mockResponse);

      (mockConfigService.get as Mock).mockReturnValue('https://api.apps-platform.test/');
      await provider.proxy(mockOrganization, 'GET', 'test/path');

      expect(fetch).toHaveBeenCalledWith(
        'https://api.apps-platform.test/test/path',
        expect.any(Object)
      );
    });
  });

  describe('postEventsToPlatform', () => {
    it('should throw an error if the organization has no API key', async () => {
      const orgWithoutApiKey = { ...mockOrganization, apiKey: null } as unknown as Organizations;
      const mockEvent: ReactionAddedEvent = {
        type: EmittableSlackEvents.REACTION_ADDED,
        user: 'U12345',
        reaction: 'thumbsup',
        item_user: 'U54321',
        item: {
          type: 'message',
          channel: 'C12345',
          ts: '**********.123456',
        },
        event_ts: '**********.123456',
      };

      const result = await provider.postEventsToPlatform(orgWithoutApiKey, mockEvent);

      expect(result).toBeUndefined();
      expect(mockSentryService.captureException).toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to post event to apps platform!')
      );
    });

    it('should post event successfully when organization has API key', async () => {
      const mockEvent: ReactionAddedEvent = {
        type: EmittableSlackEvents.REACTION_ADDED,
        user: 'U12345',
        reaction: 'thumbsup',
        item_user: 'U54321',
        item: {
          type: 'message',
          channel: 'C12345',
          ts: '**********.123456',
        },
        event_ts: '**********.123456',
      };
      const mockResponse = new Response(JSON.stringify({ success: true }), {
        status: 200,
      });
      (fetch as Mock).mockResolvedValue(mockResponse);

      const result = await provider.postEventsToPlatform(mockOrganization, mockEvent);

      expect(fetch).toHaveBeenCalledWith(
        'https://api.apps-platform.test/incoming-webhook/events',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-org-id': mockOrganization.uid,
            'x-bot-key': mockOrganization.apiKey,
          },
          body: JSON.stringify({
            event_name: EmittableSlackEvents.REACTION_ADDED,
            payload: mockEvent,
          }),
        })
      );
      expect(result).toEqual({ success: true });
    });
  });
});
