import { HttpStatus } from '@nestjs/common';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { RedisService } from '../../../../src/common/redis/redis.service';
import {
  ConfigKeys,
  ConfigService,
} from '../../../../src/config/config.service';
import { Installations } from '../../../../src/database/entities';
import { Organizations } from '../../../../src/database/entities/organizations/organizations.entity';
import { ThenaPlatformApiProvider } from '../../../../src/external/provider/thena-platform-api.provider';
import { UserPlatformLookupService } from '../../../../src/shared/user-platform-lookup/user-platform-lookup.service';
import { ILogger } from '../../../../src/utils';

// Mock global fetch
global.fetch = vi.fn();
global.AbortSignal = {
  timeout: vi.fn().mockReturnValue({ aborted: false }),
} as any;

describe('ThenaPlatformApiProvider', () => {
  let provider: ThenaPlatformApiProvider;
  let mockLogger: ILogger;
  let mockConfigService: ConfigService;
  let mockUserPlatformLookupService: UserPlatformLookupService;
  let mockRedisService: any;
  let mockInstallation: Installations;
  let mockOrganization: Organizations;

  beforeEach(() => {
    vi.resetAllMocks();

    // Mock the logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    // Mock the config service
    mockConfigService = {
      get: vi.fn(),
    } as unknown as ConfigService;

    // Mock the user platform lookup service
    mockUserPlatformLookupService = {
      getPlatformUserId: vi.fn(),
      getPlatformUserInfo: vi.fn(),
      hasValidPlatformUserId: vi.fn(),
      refreshUserMapping: vi.fn(),
      clearCache: vi.fn(),
    } as unknown as UserPlatformLookupService;

    // Mock the Redis service
    mockRedisService = {
      get: vi.fn(),
      set: vi.fn(),
      del: vi.fn(),
      setNX: vi.fn(),
      getClient: vi.fn(),
      getSubscriberClient: vi.fn(),
      getPublisherClient: vi.fn(),
    } as unknown as RedisService;

    // Mock the organization
    mockOrganization = {
      id: 'org-1',
      uid: 'org-uid-1',
      apiKey: 'api-key-1',
      name: 'Test Organization',
      externalPk: 'ext-1',
      installingUserId: 'user-1',
      metadata: {
        applicationId: '',
        installationId: '',
        createdBy: '',
      },
      bots: [],
      users: [],
      channels: [],
      installations: [],
      teams: [],
      subgroups: [],
    };

    // Mock the installation
    mockInstallation = {
      id: 'inst-1',
      teamId: 'team-1',
      teamName: 'Team 1',
      botToken: 'xoxb-token-1',
      organization: mockOrganization,
    } as unknown as Installations;

    // Configure the config service mock
    (mockConfigService.get as Mock).mockImplementation((key: string) => {
      if (key === ConfigKeys.PLATFORM_API_URL) {
        return 'https://api.platform.test';
      }
      if (key === ConfigKeys.ANNOTATOR_API_URL) {
        return 'https://api.annotator.test';
      }
      return '';
    });

    // Create the provider
    provider = new ThenaPlatformApiProvider(
      mockLogger,
      mockConfigService,
      mockUserPlatformLookupService,
      mockRedisService,
    );
  });

  describe('proxy', () => {
    it('should throw an error if the organization has no API key', async () => {
      // Setup
      const orgWithoutApiKey = { ...mockOrganization, apiKey: null };

      // Execute and verify
      try {
        await provider.proxy(
          orgWithoutApiKey as unknown as Organizations,
          'GET',
          '/test',
        );
        // If we reach here, the test should fail because no error was thrown
        expect(true).toBe(false); // This line should not be reached
      } catch (error) {
        // Verify the error message
        expect(error.message).toBe('Organization has no API key');
      }
    });

    it('should make a GET request with the correct headers', async () => {
      // Setup
      const mockResponse = new Response(JSON.stringify({ data: 'test' }), {
        status: 200,
      });
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      await provider.proxy(mockOrganization, 'GET', '/test');

      // Verify
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/test',
        expect.objectContaining({
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'x-org-id': mockOrganization.uid,
            'x-api-key': mockOrganization.apiKey,
            'x-request-source': 'slack',
          },
        }),
      );
    });

    it('should make a POST request with the correct body', async () => {
      // Setup
      const mockResponse = new Response(JSON.stringify({ data: 'test' }), {
        status: 200,
      });
      (fetch as Mock).mockResolvedValue(mockResponse);
      const body = { test: 'data' };

      // Execute
      await provider.proxy(mockOrganization, 'POST', '/test', body);

      // Verify
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/test',
        expect.objectContaining({
          method: 'POST',
          headers: expect.any(Object),
          body: JSON.stringify(body),
        }),
      );
    });

    it('should handle network errors', async () => {
      // Setup
      (fetch as Mock).mockRejectedValue(new Error('Network error'));

      // Execute and verify
      await expect(
        provider.proxy(mockOrganization, 'GET', '/test'),
      ).rejects.toThrow('Network error');
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('createNewTicket', () => {
    it('should create a new ticket successfully', async () => {
      // Setup
      const mockTicketData = {
        requestorEmail: '<EMAIL>',
        title: 'Test Ticket',
        description: 'Test Description',
        teamId: 'team-1',
        urgency: 'high',
        metadata: {
          slack: {
            channel: 'C12345',
            ts: '**********.123456',
            user: 'U12345',
          },
        },
      };

      const mockResponse = new Response(
        JSON.stringify({
          data: {
            id: 'ticket-1',
            title: mockTicketData.title,
          },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.createNewTicket(mockInstallation, {
        ...mockTicketData,
        text: mockTicketData.description,
      });

      // Verify
      expect(result).toEqual({
        id: 'ticket-1',
        title: mockTicketData.title,
      });
      expect(fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining(mockTicketData.title),
        }),
      );
    });

    it('should throw an error if the ticket creation fails', async () => {
      // Setup
      const mockTicketData = {
        requestorEmail: '<EMAIL>',
        title: 'Test Ticket',
        description: 'Test Description',
        teamId: 'team-1',
        urgency: 'high',
        metadata: {
          slack: {
            channel: 'C12345',
            ts: '**********.123456',
            user: 'U12345',
          },
        },
      };

      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to create ticket',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.createNewTicket(mockInstallation, {
          ...mockTicketData,
          text: mockTicketData.description,
        }),
      ).rejects.toThrow('Failed to create ticket');
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('getTeams', () => {
    it('should fetch teams successfully', async () => {
      // Setup
      const mockTeams = [
        { id: 'team-1', name: 'Team 1' },
        { id: 'team-2', name: 'Team 2' },
      ];

      const mockResponse = new Response(
        JSON.stringify({
          data: mockTeams,
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.getTeams(mockInstallation);

      // Verify
      expect(result).toEqual(mockTeams);
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/teams',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'x-org-id': mockOrganization.uid,
            'x-api-key': mockOrganization.apiKey,
          }),
        }),
      );
    });

    it('should handle API errors when fetching teams', async () => {
      // Setup
      const mockResponse = new Response('Error fetching teams', {
        status: HttpStatus.INTERNAL_SERVER_ERROR,
      });
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(provider.getTeams(mockInstallation)).rejects.toThrow(
        'Teams API returned status 500',
      );
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should handle unexpected response format', async () => {
      // Setup
      const mockResponse = new Response(
        JSON.stringify({
          // Missing data property
          status: 'success',
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(provider.getTeams(mockInstallation)).rejects.toThrow(
        'Teams API returned unexpected data format',
      );
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('updateTicket', () => {
    it('should update a ticket successfully', async () => {
      // Setup
      const ticketId = 'ticket-1';
      const updateData = {
        title: 'Updated Title',
        statusId: 'status-1',
      };

      const mockResponse = new Response(
        JSON.stringify({
          data: {
            id: ticketId,
            title: updateData.title,
          },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.updateTicket(
        mockInstallation,
        ticketId,
        updateData,
      );

      // Verify
      expect(result).toEqual({
        data: {
          id: ticketId,
          title: updateData.title,
        },
      });
      expect(fetch).toHaveBeenCalledWith(
        `https://api.platform.test/v1/tickets/${ticketId}`,
        expect.objectContaining({
          method: 'PATCH',
          body: JSON.stringify(updateData),
        }),
      );
    });

    it('should throw an error if the ticket update fails', async () => {
      // Setup
      const ticketId = 'ticket-1';
      const updateData = {
        title: 'Updated Title',
      };

      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to update ticket',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.updateTicket(mockInstallation, ticketId, {
          statusId: 'status-1',
        }),
      ).rejects.toThrow('Failed to update ticket');
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('createNewComment', () => {
    it('should create a comment with X-User-ID header when platform user ID is available', async () => {
      // Setup
      const slackUserId = 'U12345';
      const platformUserId = 'platform-user-123';
      const commentData = {
        content: 'Test comment',
        ticketId: 'ticket-1',
        channelId: 'C12345',
        files: [],
        metadata: {
          ts: '**********.123456',
          ignoreSelf: true,
          slackUserId,
        },
      };

      // Mock Redis to return cache miss (null)
      (mockRedisService.get as Mock).mockResolvedValue(null);
      (mockRedisService.set as Mock).mockResolvedValue('OK');

      // Mock the user platform lookup service
      (
        mockUserPlatformLookupService.getPlatformUserId as Mock
      ).mockResolvedValue(platformUserId);

      const mockResponse = new Response(
        JSON.stringify({
          data: { id: 'comment-1' },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      await provider.createNewComment(mockInstallation, commentData);

      // Verify that the X-User-ID header was included
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/tickets/ticket-1/comment',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'X-User-ID': platformUserId,
          }),
        }),
      );

      // Verify that the user platform lookup service was called
      expect(
        mockUserPlatformLookupService.getPlatformUserId,
      ).toHaveBeenCalledWith(slackUserId, mockInstallation);

      // Verify that Redis was used for caching
      expect(mockRedisService.get).toHaveBeenCalledWith(
        `platform-user-lookup:${mockInstallation.id}:${slackUserId}`,
      );
      expect(mockRedisService.set).toHaveBeenCalledWith(
        `platform-user-lookup:${mockInstallation.id}:${slackUserId}`,
        JSON.stringify({ platformUserId }),
        'EX',
        300, // 5 minutes
      );
    });

    it('should create a comment without X-User-ID header when platform user ID is not available', async () => {
      // Setup
      const slackUserId = 'U12345';
      const commentData = {
        content: 'Test comment',
        ticketId: 'ticket-1',
        channelId: 'C12345',
        files: [],
        metadata: {
          ts: '**********.123456',
          ignoreSelf: true,
          slackUserId,
        },
      };

      // Mock Redis to return cache miss (null)
      (mockRedisService.get as Mock).mockResolvedValue(null);
      (mockRedisService.set as Mock).mockResolvedValue('OK');

      // Mock the user platform lookup service to return null
      (
        mockUserPlatformLookupService.getPlatformUserId as Mock
      ).mockResolvedValue(null);

      const mockResponse = new Response(
        JSON.stringify({
          data: { id: 'comment-1' },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      await provider.createNewComment(mockInstallation, commentData);

      // Verify that the X-User-ID header was NOT included
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/tickets/ticket-1/comment',
        expect.objectContaining({
          method: 'POST',
          headers: expect.not.objectContaining({
            'X-User-ID': expect.anything(),
          }),
        }),
      );

      // Verify that Redis cached the null result
      expect(mockRedisService.set).toHaveBeenCalledWith(
        `platform-user-lookup:${mockInstallation.id}:${slackUserId}`,
        JSON.stringify({ platformUserId: null }),
        'EX',
        300, // 5 minutes
      );
    });

    it('should create a comment without X-User-ID header when slackUserId is not provided', async () => {
      // Setup
      const commentData = {
        content: 'Test comment',
        ticketId: 'ticket-1',
        channelId: 'C12345',
        files: [],
        metadata: {
          ts: '**********.123456',
          ignoreSelf: true,
        },
        // No slackUserId provided
      };

      const mockResponse = new Response(
        JSON.stringify({
          data: { id: 'comment-1' },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      await provider.createNewComment(mockInstallation, commentData);

      // Verify that the user platform lookup service was NOT called
      expect(
        mockUserPlatformLookupService.getPlatformUserId,
      ).not.toHaveBeenCalled();

      // Verify that the X-User-ID header was NOT included
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/tickets/ticket-1/comment',
        expect.objectContaining({
          method: 'POST',
          headers: expect.not.objectContaining({
            'X-User-ID': expect.anything(),
          }),
        }),
      );
    });

    it('should use cached platform user ID when available', async () => {
      // Setup
      const slackUserId = 'U12345';
      const platformUserId = 'platform-user-123';
      const commentData = {
        content: 'Test comment',
        ticketId: 'ticket-1',
        channelId: 'C12345',
        files: [],
        metadata: {
          ts: '**********.123456',
          ignoreSelf: true,
          slackUserId,
        },
      };

      // Mock Redis to return cached value
      (mockRedisService.get as Mock).mockResolvedValue(
        JSON.stringify({ platformUserId }),
      );

      const mockResponse = new Response(
        JSON.stringify({
          data: { id: 'comment-1' },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      await provider.createNewComment(mockInstallation, commentData);

      // Verify that the X-User-ID header was included
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/tickets/ticket-1/comment',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'X-User-ID': platformUserId,
          }),
        }),
      );

      // Verify that the user platform lookup service was NOT called (cache hit)
      expect(
        mockUserPlatformLookupService.getPlatformUserId,
      ).not.toHaveBeenCalled();

      // Verify that Redis get was called but set was not (cache hit)
      expect(mockRedisService.get).toHaveBeenCalledWith(
        `platform-user-lookup:${mockInstallation.id}:${slackUserId}`,
      );
      expect(mockRedisService.set).not.toHaveBeenCalled();
    });

    it.todo('should handle Redis errors gracefully', async () => {
      // Setup
      const slackUserId = 'U12345';
      const platformUserId = 'platform-user-123';
      const commentData = {
        content: 'Test comment',
        ticketId: 'ticket-1',
        channelId: 'C12345',
        files: [],
        metadata: {
          ts: '**********.123456',
          ignoreSelf: true,
          slackUserId,
        },
      };

      // Mock Redis to throw an error
      (mockRedisService.get as Mock).mockRejectedValue(
        new Error('Redis error'),
      );
      (mockRedisService.set as Mock).mockResolvedValue('OK');

      // Mock the user platform lookup service
      (
        mockUserPlatformLookupService.getPlatformUserId as Mock
      ).mockResolvedValue(platformUserId);

      const mockResponse = new Response(
        JSON.stringify({
          data: { id: 'comment-1' },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      await provider.createNewComment(mockInstallation, commentData);

      // Verify that the comment was still created despite Redis error
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/tickets/ticket-1/comment',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'X-User-ID': platformUserId,
          }),
        }),
      );

      // Verify that the user platform lookup service was called (fallback)
      expect(
        mockUserPlatformLookupService.getPlatformUserId,
      ).toHaveBeenCalledWith(slackUserId, mockInstallation);
    });

    it('should handle comment creation errors', async () => {
      // Setup
      const commentData = {
        content: 'Test comment',
        ticketId: 'ticket-1',
        channelId: 'C12345',
        files: [],
        metadata: {
          ts: '**********.123456',
          ignoreSelf: true,
          slackUserId: 'U12345',
        },
      };

      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to create comment',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.createNewComment(mockInstallation, commentData),
      ).rejects.toThrow('Failed to create comment');
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('addReaction', () => {
    it('should add reaction successfully', async () => {
      // Setup
      const commentId = 'comment-1';
      const emoji = 'thumbsup';
      const userName = 'John Doe';
      const userEmail = '<EMAIL>';
      const userAvatar = 'https://example.com/avatar.jpg';

      const mockResponse = new Response(
        JSON.stringify({
          data: { id: 'reaction-1' },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.addReaction(
        mockInstallation,
        commentId,
        emoji,
        userName,
        userEmail,
        userAvatar,
      );

      // Verify
      expect(result).toEqual({ id: 'reaction-1' });
      expect(fetch).toHaveBeenCalledWith(
        `https://api.platform.test/v1/reactions/${commentId}`,
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            name: emoji,
            impersonatedUserName: userName,
            impersonatedUserEmail: userEmail,
            impersonatedUserAvatar: userAvatar,
            metadata: {
              ignoreSelf: true,
            },
          }),
        }),
      );
    });

    it('should handle reaction creation errors', async () => {
      // Setup
      const commentId = 'comment-1';
      const emoji = 'thumbsup';
      const userName = 'John Doe';
      const userEmail = '<EMAIL>';
      const userAvatar = 'https://example.com/avatar.jpg';

      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to add reaction',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.addReaction(
          mockInstallation,
          commentId,
          emoji,
          userName,
          userEmail,
          userAvatar,
        ),
      ).rejects.toThrow('Failed to add reaction');
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('getCommentThreads', () => {
    it('should get comment threads successfully', async () => {
      // Setup
      const commentId = 'comment-1';
      const mockThreads = [
        { id: 'thread-1', content: 'Thread comment 1' },
        { id: 'thread-2', content: 'Thread comment 2' },
      ];

      const mockResponse = new Response(
        JSON.stringify({
          data: mockThreads,
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.getCommentThreads(
        mockInstallation,
        commentId,
      );

      // Verify
      expect(result).toEqual(mockThreads);
      expect(fetch).toHaveBeenCalledWith(
        `https://api.platform.test/v1/comments/${commentId}/threads`,
        expect.objectContaining({
          method: 'GET',
        }),
      );
    });

    it('should handle get comment threads errors', async () => {
      // Setup
      const commentId = 'comment-1';

      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to get comment threads',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.getCommentThreads(mockInstallation, commentId),
      ).rejects.toThrow('Failed to get comment threads');
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    it('should handle timeout errors', async () => {
      // Setup
      const timeoutError = new Error('Request timeout');
      timeoutError.name = 'AbortError';
      (fetch as Mock).mockRejectedValue(timeoutError);

      // Execute and verify
      await expect(
        provider.proxy(mockOrganization, 'GET', '/test'),
      ).rejects.toThrow('Platform API request timeout');
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Request timeout'),
      );
    });

    it.todo('should handle non-JSON response errors', async () => {
      // Setup
      const mockResponse = new Response('Internal Server Error', {
        status: 500,
        headers: { 'Content-Type': 'text/plain' },
      });
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.proxy(mockOrganization, 'GET', '/test'),
      ).rejects.toThrow('Platform API returned status 500');
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it.todo('should handle malformed JSON responses', async () => {
      // Setup
      const mockResponse = new Response('{ invalid json', {
        status: 200,
      });
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.proxy(mockOrganization, 'GET', '/test'),
      ).rejects.toThrow();
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('isApiAvailable', () => {
    it('should return true when API is available', async () => {
      // Setup
      const mockResponse = new Response('OK', { status: 200 });
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.isApiAvailable();

      // Verify
      expect(result).toBe(true);
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/health',
        expect.objectContaining({
          method: 'GET',
        }),
      );
    });

    it('should return false when API is not available', async () => {
      // Setup
      const mockResponse = new Response('Not Found', { status: 404 });
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.isApiAvailable();

      // Verify
      expect(result).toBe(false);
      // The method only logs a warning when there's an exception, not when response.ok is false
    });

    it('should return false when fetch throws an error', async () => {
      // Setup
      (fetch as Mock).mockRejectedValue(new Error('Network error'));

      // Execute
      const result = await provider.isApiAvailable();

      // Verify
      expect(result).toBe(false);
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('Platform API health check failed'),
      );
    });
  });

  describe('linkUsersToPlatform', () => {
    it('should link users to platform successfully', async () => {
      // Setup
      const payload = {
        externalType: 'slack' as const,
        details: [
          {
            email: '<EMAIL>',
            slackSinkDetails: {
              id: 'U12345',
              teamId: 'T12345',
            },
          },
        ],
      };

      const mockResponse = new Response(
        JSON.stringify({
          data: { linkedUsers: 1 },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.linkUsersToPlatform(
        mockInstallation,
        payload,
      );

      // Verify
      expect(result).toEqual({ linkedUsers: 1 });
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/users/ingest/link-external',
        expect.objectContaining({
          method: 'PATCH',
          body: JSON.stringify(payload),
        }),
      );
    });

    it('should handle link users errors', async () => {
      // Setup
      const payload = {
        externalType: 'slack' as const,
        details: [],
      };

      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to link users',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.linkUsersToPlatform(mockInstallation, payload),
      ).rejects.toThrow('Failed to link users');
    });
  });

  describe('getEntityDetails', () => {
    it('should get entity details successfully', async () => {
      // Setup
      const identifier = 'ticketId';
      const entityId = 'ticket-123';
      const entityType = 'ticket';

      const mockResponse = new Response(
        JSON.stringify({
          data: { id: entityId, type: entityType },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.getEntityDetails(
        mockInstallation,
        identifier,
        entityId,
        entityType,
      );

      // Verify
      expect(result).toEqual({ id: entityId, type: entityType });
      expect(fetch).toHaveBeenCalledWith(
        'https://api.annotator.test/v1/annotator/data',
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining(entityId),
        }),
      );
    });

    it('should handle entity details errors', async () => {
      // Setup
      const mockResponse = new Response(
        JSON.stringify({
          message: 'Entity not found',
        }),
        { status: 404 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.getEntityDetails(
          mockInstallation,
          'ticketId',
          'ticket-123',
          'ticket',
        ),
      ).rejects.toThrow('Entity not found');
    });
  });

  describe('setSlackAuth', () => {
    it('should set slack auth successfully', async () => {
      // Setup
      const userEmail = '<EMAIL>';
      const mockResponse = new Response(
        JSON.stringify({
          data: { success: true },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.setSlackAuth(mockInstallation, userEmail);

      // Verify
      expect(result).toEqual({ data: { success: true } });
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining(
          `/v1/users/metadata/slack-auth?userEmail=${userEmail}&teamId=${mockInstallation.teamId}`,
        ),
        expect.objectContaining({
          method: 'PATCH',
        }),
      );
    });

    it('should unset slack auth when unset flag is true', async () => {
      // Setup
      const userEmail = '<EMAIL>';
      const mockResponse = new Response(
        JSON.stringify({
          data: { success: true },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      await provider.setSlackAuth(mockInstallation, userEmail, true);

      // Verify
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('&unset=true'),
        expect.objectContaining({
          method: 'PATCH',
        }),
      );
    });

    it('should handle set slack auth errors', async () => {
      // Setup
      const userEmail = '<EMAIL>';
      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to set slack auth',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.setSlackAuth(mockInstallation, userEmail),
      ).rejects.toThrow('Failed to set slack auth');
    });
  });

  describe('fetchFormsForTeam', () => {
    it('should fetch forms for team successfully', async () => {
      // Setup
      const teamId = 'team-1';
      const mockForms = [
        { id: 'form-1', name: 'Form 1' },
        { id: 'form-2', name: 'Form 2' },
      ];

      const mockResponse = new Response(
        JSON.stringify({
          data: mockForms,
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.fetchFormsForTeam(mockInstallation, teamId);

      // Verify
      expect(result).toEqual(mockForms);
      expect(fetch).toHaveBeenCalledWith(
        `https://api.platform.test/v1/forms?onlyTeamForms=true&teamId=${teamId}`,
        expect.objectContaining({
          method: 'GET',
        }),
      );
    });

    it('should handle fetch forms errors', async () => {
      // Setup
      const teamId = 'team-1';
      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to fetch forms',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.fetchFormsForTeam(mockInstallation, teamId),
      ).rejects.toThrow('Failed to fetch forms');
    });
  });

  describe('updateComment', () => {
    it('should update comment successfully', async () => {
      // Setup
      const commentData = {
        commentId: 'comment-1',
        content: 'Updated content',
        htmlContent: '<p>Updated content</p>',
        commentAs: '<EMAIL>',
      };

      const mockResponse = new Response(
        JSON.stringify({
          data: { id: 'comment-1' },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.updateComment(
        mockInstallation,
        commentData,
      );

      // Verify
      expect(result).toEqual({ data: { id: 'comment-1' } });
      expect(fetch).toHaveBeenCalledWith(
        `https://api.platform.test/v1/comments/${commentData.commentId}`,
        expect.objectContaining({
          method: 'PATCH',
          body: expect.stringContaining(commentData.content),
        }),
      );
    });

    it('should handle update comment errors', async () => {
      // Setup
      const commentData = {
        commentId: 'comment-1',
        content: 'Updated content',
        htmlContent: '<p>Updated content</p>',
        commentAs: '<EMAIL>',
      };

      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to update comment',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.updateComment(mockInstallation, commentData),
      ).rejects.toThrow('Failed to update comment');
    });
  });

  describe('updateCommentWithMetadata', () => {
    it('should update comment with metadata successfully', async () => {
      // Setup
      const commentId = 'comment-1';
      const metadata = {
        external_sinks: {
          slack: {
            ignoreSelf: true,
            threadTs: '**********.123456',
            ts: '**********.123456',
            slackThreadLink:
              'slack://channel?team=T123&id=C123&message=**********.123456',
          },
        },
      };

      const mockResponse = new Response(
        JSON.stringify({
          data: { id: 'comment-1' },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.updateCommentWithMetadata(
        mockInstallation,
        commentId,
        metadata,
      );

      // Verify
      expect(result).toEqual({ data: { id: 'comment-1' } });
      expect(fetch).toHaveBeenCalledWith(
        `https://api.platform.test/v1/comments/${commentId}`,
        expect.objectContaining({
          method: 'PATCH',
          body: expect.stringContaining('external_sinks'),
        }),
      );
    });

    it('should handle update comment metadata errors', async () => {
      // Setup
      const commentId = 'comment-1';
      const metadata = {
        external_sinks: {
          slack: {
            ignoreSelf: true,
            threadTs: '**********.123456',
            ts: '**********.123456',
            slackThreadLink:
              'slack://channel?team=T123&id=C123&message=**********.123456',
          },
        },
      };

      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to update comment metadata',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.updateCommentWithMetadata(
          mockInstallation,
          commentId,
          metadata,
        ),
      ).rejects.toThrow('Failed to update comment metadata');
    });
  });

  describe('deleteComment', () => {
    it('should delete comment successfully', async () => {
      // Setup
      const commentId = 'comment-1';
      const mockResponse = new Response('', { status: 200 });
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.deleteComment(mockInstallation, commentId);

      // Verify
      expect(result).toEqual({ ok: true });
      expect(fetch).toHaveBeenCalledWith(
        `https://api.platform.test/v1/comments/${commentId}`,
        expect.objectContaining({
          method: 'DELETE',
        }),
      );
    });

    it('should handle delete comment errors', async () => {
      // Setup
      const commentId = 'comment-1';
      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to delete comment',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.deleteComment(mockInstallation, commentId),
      ).rejects.toThrow('Failed to delete comment');
    });
  });

  describe('getStatusesForTeam', () => {
    it('should get statuses for team successfully', async () => {
      // Setup
      const teamId = 'team-1';
      const mockStatuses = [
        { id: 'status-1', name: 'Open' },
        { id: 'status-2', name: 'Closed' },
      ];

      const mockResponse = new Response(
        JSON.stringify({
          data: mockStatuses,
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.getStatusesForTeam(
        mockInstallation,
        teamId,
      );

      // Verify
      expect(result).toEqual(mockStatuses);
      expect(fetch).toHaveBeenCalledWith(
        `https://api.platform.test/v1/tickets/status?teamId=${teamId}`,
        expect.objectContaining({
          method: 'GET',
        }),
      );
    });

    it('should return empty array when get statuses fails', async () => {
      // Setup
      const teamId = 'team-1';
      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to get statuses',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.getStatusesForTeam(
        mockInstallation,
        teamId,
      );

      // Verify
      expect(result).toEqual([]);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to get statuses for team'),
      );
    });

    it('should return empty array when exception occurs', async () => {
      // Setup
      const teamId = 'team-1';
      (fetch as Mock).mockRejectedValue(new Error('Network error'));

      // Execute
      const result = await provider.getStatusesForTeam(
        mockInstallation,
        teamId,
      );

      // Verify
      expect(result).toEqual([]);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error getting statuses for team'),
      );
    });
  });

  describe('getPrioritiesForTeam', () => {
    it('should get priorities for team successfully', async () => {
      // Setup
      const teamId = 'team-1';
      const mockPriorities = [
        { id: 'priority-1', name: 'High', level: 1 },
        { id: 'priority-2', name: 'Medium', level: 2 },
        { id: 'priority-3', name: 'Low', level: 3 },
      ];

      const mockResponse = new Response(
        JSON.stringify({
          data: mockPriorities,
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.getPrioritiesForTeam(
        mockInstallation,
        teamId,
      );

      // Verify
      expect(result).toEqual(mockPriorities);
      expect(fetch).toHaveBeenCalledWith(
        `https://api.platform.test/v1/tickets/priority?teamId=${teamId}`,
        expect.objectContaining({
          method: 'GET',
        }),
      );
    });

    it('should return empty array when get priorities fails', async () => {
      // Setup
      const teamId = 'team-1';
      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to get priorities',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.getPrioritiesForTeam(
        mockInstallation,
        teamId,
      );

      // Verify
      expect(result).toEqual([]);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to get priorities for team'),
      );
    });

    it('should return empty array when exception occurs', async () => {
      // Setup
      const teamId = 'team-1';
      (fetch as Mock).mockRejectedValue(new Error('Network error'));

      // Execute
      const result = await provider.getPrioritiesForTeam(
        mockInstallation,
        teamId,
      );

      // Verify
      expect(result).toEqual([]);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error getting priorities for team'),
      );
    });
  });

  describe('getTicket', () => {
    it('should get ticket successfully', async () => {
      // Setup
      const ticketId = 'ticket-1';
      const mockTicket = {
        id: 'ticket-1',
        title: 'Test Ticket',
        description: 'Test Description',
        status: 'open',
      };

      const mockResponse = new Response(
        JSON.stringify({
          data: mockTicket,
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.getTicket(mockInstallation, ticketId);

      // Verify
      expect(result).toEqual(mockTicket);
      expect(fetch).toHaveBeenCalledWith(
        `https://api.platform.test/v1/tickets/${ticketId}`,
        expect.objectContaining({
          method: 'GET',
        }),
      );
    });

    it('should handle get ticket errors', async () => {
      // Setup
      const ticketId = 'ticket-1';
      const mockResponse = new Response(
        JSON.stringify({
          message: 'Ticket not found',
        }),
        { status: 404 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.getTicket(mockInstallation, ticketId),
      ).rejects.toThrow('Ticket not found');
    });
  });

  describe('getTeamMembers', () => {
    it('should get team members successfully', async () => {
      // Setup
      const teamId = 'team-1';
      const mockMembers = [
        { id: 'user-1', name: 'John Doe', email: '<EMAIL>' },
        { id: 'user-2', name: 'Jane Smith', email: '<EMAIL>' },
      ];

      const mockResponse = new Response(
        JSON.stringify({
          data: mockMembers,
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.getTeamMembers(mockInstallation, teamId);

      // Verify
      expect(result).toEqual(mockMembers);
      expect(fetch).toHaveBeenCalledWith(
        `https://api.platform.test/v1/teams/${teamId}/members`,
        expect.objectContaining({
          method: 'GET',
        }),
      );
    });

    it('should handle get team members errors', async () => {
      // Setup
      const teamId = 'team-1';
      const mockResponse = new Response(
        JSON.stringify({
          message: 'Team not found',
        }),
        { status: 404 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.getTeamMembers(mockInstallation, teamId),
      ).rejects.toThrow('Team not found');
    });
  });

  describe('closeTicket', () => {
    it('should close ticket successfully', async () => {
      // Setup
      const ticketId = 'ticket-1';
      const ticketTeamId = 'team-1';

      // Mock getStatusesForTeam to return statuses with a closed status
      const mockStatuses = [
        { id: 'status-1', name: 'Open', parentStatusId: null },
        { id: 'status-2', name: 'Closed', parentStatusId: null },
      ];
      vi.spyOn(provider, 'getStatusesForTeam').mockResolvedValue(mockStatuses);

      // Mock updateTicket
      vi.spyOn(provider, 'updateTicket').mockResolvedValue({
        data: { id: 'ticket-1' },
      });

      // Execute
      await provider.closeTicket(mockInstallation, ticketId, ticketTeamId);

      // Verify
      expect(provider.getStatusesForTeam).toHaveBeenCalledWith(
        mockInstallation,
        ticketTeamId,
      );
      expect(provider.updateTicket).toHaveBeenCalledWith(
        mockInstallation,
        ticketId,
        { statusId: 'status-2' },
      );
    });

    it('should handle close ticket when no closed status found', async () => {
      // Setup
      const ticketId = 'ticket-1';
      const ticketTeamId = 'team-1';

      // Mock getStatusesForTeam to return statuses without a closed status
      const mockStatuses = [
        { id: 'status-1', name: 'Open', parentStatusId: null },
        { id: 'status-2', name: 'In Progress', parentStatusId: null },
      ];
      vi.spyOn(provider, 'getStatusesForTeam').mockResolvedValue(mockStatuses);

      // Execute and verify
      await expect(
        provider.closeTicket(mockInstallation, ticketId, ticketTeamId),
      ).rejects.toThrow('No closed status found for team');

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('No closed status found for team'),
      );
    });

    it('should handle close ticket errors', async () => {
      // Setup
      const ticketId = 'ticket-1';
      const ticketTeamId = 'team-1';

      // Mock getStatusesForTeam to throw an error
      vi.spyOn(provider, 'getStatusesForTeam').mockRejectedValue(
        new Error('Failed to get statuses'),
      );

      // Execute and verify
      await expect(
        provider.closeTicket(mockInstallation, ticketId, ticketTeamId),
      ).rejects.toThrow('Failed to get statuses');

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to close ticket'),
      );
    });
  });

  describe('archiveTicket', () => {
    it('should archive ticket successfully', async () => {
      // Setup
      const ticketId = 'ticket-1';
      const mockResponse = new Response(
        JSON.stringify({
          data: { id: 'ticket-1', archived: true },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.archiveTicket(mockInstallation, ticketId);

      // Verify
      expect(result).toEqual({ data: { id: 'ticket-1', archived: true } });
      expect(fetch).toHaveBeenCalledWith(
        `https://api.platform.test/v1/tickets/${ticketId}/archive`,
        expect.objectContaining({
          method: 'PATCH',
        }),
      );
    });

    it('should handle archive ticket errors', async () => {
      // Setup
      const ticketId = 'ticket-1';
      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to archive ticket',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.archiveTicket(mockInstallation, ticketId),
      ).rejects.toThrow('Failed to archive ticket');
    });
  });

  describe('createCustomField', () => {
    it.todo('should create custom field successfully', async () => {
      // Setup
      const customFieldData = {
        name: 'Custom Field',
        source: 'slack',
        fieldType: 'text',
        description: 'A custom field for testing',
        teamId: 'team-1',
      };

      const mockResponse = new Response(
        JSON.stringify({
          data: { id: 'field-1', name: 'Custom Field' },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.createCustomField(
        mockInstallation,
        customFieldData,
      );

      // Verify
      expect(result).toEqual({ data: { id: 'field-1', name: 'Custom Field' } });
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/custom-fields',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(customFieldData),
        }),
      );
    });

    it('should handle create custom field errors', async () => {
      // Setup
      const customFieldData = {
        name: 'Custom Field',
        source: 'slack',
        fieldType: 'text',
        description: 'A custom field for testing',
        teamId: 'team-1',
      };

      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to create custom field',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.createCustomField(mockInstallation, customFieldData),
      ).rejects.toThrow('Failed to create custom field');
    });
  });

  describe('searchCustomField', () => {
    it.todo('should search custom field successfully', async () => {
      // Setup
      const searchParams = {
        teamId: 'team-1',
        name: 'Custom Field',
      };

      const mockFields = [
        { id: 'field-1', name: 'Custom Field', type: 'text' },
      ];

      const mockResponse = new Response(
        JSON.stringify({
          data: mockFields,
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.searchCustomField(
        mockInstallation,
        searchParams,
      );

      // Verify
      expect(result).toEqual(mockFields);
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/custom-fields/search',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(searchParams),
        }),
      );
    });

    it('should handle search custom field errors', async () => {
      // Setup
      const searchParams = {
        teamId: 'team-1',
        name: 'Custom Field',
      };

      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to search custom fields',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.searchCustomField(mockInstallation, searchParams),
      ).rejects.toThrow('Failed to search custom fields');
    });
  });

  describe('getAccountsByDomains', () => {
    it.todo('should get accounts by domains successfully', async () => {
      // Setup
      const domains = ['example.com', 'test.com'];
      const mockAccounts = [
        { id: 'account-1', domain: 'example.com', name: 'Example Corp' },
        { id: 'account-2', domain: 'test.com', name: 'Test Inc' },
      ];

      const mockResponse = new Response(
        JSON.stringify({
          data: mockAccounts,
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.getAccountsByDomains(
        mockInstallation,
        domains,
      );

      // Verify
      expect(result).toEqual(mockAccounts);
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/accounts/by-domains',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({ domains }),
        }),
      );
    });

    it('should handle get accounts by domains errors', async () => {
      // Setup
      const domains = ['example.com'];
      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to get accounts',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.getAccountsByDomains(mockInstallation, domains),
      ).rejects.toThrow('Failed to get accounts');
    });
  });

  describe('getAccountsByIds', () => {
    it.todo('should get accounts by IDs successfully', async () => {
      // Setup
      const accountIds = ['account-1', 'account-2'];
      const mockAccounts = [
        { id: 'account-1', name: 'Example Corp' },
        { id: 'account-2', name: 'Test Inc' },
      ];

      const mockResponse = new Response(
        JSON.stringify({
          data: mockAccounts,
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.getAccountsByIds(
        mockInstallation,
        accountIds,
      );

      // Verify
      expect(result).toEqual(mockAccounts);
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/accounts/by-ids',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({ accountIds }),
        }),
      );
    });

    it('should handle get accounts by IDs errors', async () => {
      // Setup
      const accountIds = ['account-1'];
      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to get accounts',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.getAccountsByIds(mockInstallation, accountIds),
      ).rejects.toThrow('Failed to get accounts');
    });
  });

  describe('createAccount', () => {
    it.todo('should create account successfully', async () => {
      // Setup
      const accountData = {
        name: 'New Account',
        primaryDomain: 'newaccount.com',
        description: 'A new test account',
      };

      const mockResponse = new Response(
        JSON.stringify({
          data: { id: 'account-1', name: 'New Account' },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.createAccount(
        mockInstallation,
        accountData,
      );

      // Verify
      expect(result).toEqual({
        data: { id: 'account-1', name: 'New Account' },
      });
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/accounts',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(accountData),
        }),
      );
    });

    it('should handle create account errors', async () => {
      // Setup
      const accountData = {
        name: 'New Account',
        primaryDomain: 'newaccount.com',
        description: 'A new test account',
      };

      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to create account',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.createAccount(mockInstallation, accountData),
      ).rejects.toThrow('Failed to create account');
    });
  });

  describe('updateAccount', () => {
    it.todo('should update account successfully', async () => {
      // Setup
      const accountId = 'account-1';
      const updateData = {
        name: 'Updated Account',
        description: 'Updated description',
      };

      const mockResponse = new Response(
        JSON.stringify({
          data: { id: 'account-1', name: 'Updated Account' },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.updateAccount(
        mockInstallation,
        accountId,
        updateData,
      );

      // Verify
      expect(result).toEqual({
        data: { id: 'account-1', name: 'Updated Account' },
      });
      expect(fetch).toHaveBeenCalledWith(
        `https://api.platform.test/v1/accounts/${accountId}`,
        expect.objectContaining({
          method: 'PATCH',
          body: JSON.stringify(updateData),
        }),
      );
    });

    it('should handle update account errors', async () => {
      // Setup
      const accountId = 'account-1';
      const updateData = {
        name: 'Updated Account',
      };

      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to update account',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.updateAccount(mockInstallation, accountId, updateData),
      ).rejects.toThrow('Failed to update account');
    });
  });

  describe('createCustomerContact', () => {
    it.todo('should create customer contact successfully', async () => {
      // Setup
      const contactData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        accountIds: ['account-1'],
      };

      const mockResponse = new Response(
        JSON.stringify({
          data: { id: 'contact-1', name: 'John Doe' },
        }),
        { status: 200 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute
      const result = await provider.createCustomerContact(
        mockInstallation,
        contactData,
      );

      // Verify
      expect(result).toEqual({ data: { id: 'contact-1', name: 'John Doe' } });
      expect(fetch).toHaveBeenCalledWith(
        'https://api.platform.test/v1/customer-contacts',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(contactData),
        }),
      );
    });

    it('should handle create customer contact errors', async () => {
      // Setup
      const contactData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        accountIds: ['account-1'],
      };

      const mockResponse = new Response(
        JSON.stringify({
          message: 'Failed to create customer contact',
        }),
        { status: 400 },
      );
      (fetch as Mock).mockResolvedValue(mockResponse);

      // Execute and verify
      await expect(
        provider.createCustomerContact(mockInstallation, contactData),
      ).rejects.toThrow('Failed to create customer contact');
    });
  });
});
