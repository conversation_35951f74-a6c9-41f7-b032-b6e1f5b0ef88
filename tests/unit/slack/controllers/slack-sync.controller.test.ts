import { HttpException, InternalServerErrorException } from '@nestjs/common';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { BotCtx } from '../../../../src/auth/interfaces';
import {
  InstallationStatus,
  Installations,
} from '../../../../src/database/entities/installations/installations.entity';
import { Organizations } from '../../../../src/database/entities/organizations/organizations.entity';
import {
  SlackSyncController,
  SlackSyncType,
} from '../../../../src/slack/controllers/slack-sync.controller';
import {
  SlackExternalUsersSyncJob,
  SlackUsersSyncJob,
} from '../../../../src/slack/processors/jobs';
import { ILogger } from '../../../../src/utils';

describe('SlackSyncController', () => {
  let controller: SlackSyncController;
  let mockLogger: ILogger;
  let mockSlackExternalUsersSyncJob: SlackExternalUsersSyncJob;
  let mockSlackUsersSyncJob: SlackUsersSyncJob;
  let botCtx: BotCtx;

  const createMockInstallation = (
    id: string,
    teamId: string,
  ): Installations => ({
    id,
    name: `Installation ${id}`,
    teamId,
    status: InstallationStatus.SYNCED,
    botToken: `xoxb-token-${id}`,
    organization: null as any,
  } as Installations);

  const createMockOrganization = (id: string): Organizations => ({
    id,
    name: `Organization ${id}`,
    metadata: {
      applicationId: `app-${id}`,
      installationId: `inst-${id}`,
      createdBy: `user-${id}`,
    },
  } as Organizations);

  beforeEach(() => {
    vi.resetAllMocks();

    // Mock the installations and organization
    const mockInstallation = createMockInstallation('1', 'T123456');
    const mockOrganization = createMockOrganization('1');

    // Set up the bot context
    botCtx = {
      installations: [mockInstallation],
      installation: mockInstallation,
      organization: mockOrganization,
    };

    // Mock the logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
    } as unknown as ILogger;

    // Mock the external users sync job
    mockSlackExternalUsersSyncJob = {
      execute: vi.fn().mockResolvedValue(undefined),
    } as unknown as SlackExternalUsersSyncJob;

    // Mock the internal users sync job
    mockSlackUsersSyncJob = {
      execute: vi.fn().mockResolvedValue(undefined),
    } as unknown as SlackUsersSyncJob;

    // Create the controller
    controller = new SlackSyncController(
      mockLogger,
      mockSlackExternalUsersSyncJob,
      mockSlackUsersSyncJob,
    );
  });

  describe('sync', () => {
    it('should trigger external users sync job when syncType is EXTERNAL_USERS', async () => {
      const result = await controller.sync(
        SlackSyncType.EXTERNAL_USERS,
        botCtx,
      );

      expect(mockSlackExternalUsersSyncJob.execute).toHaveBeenCalledWith(
        botCtx.installation,
      );
      expect(result).toEqual({
        success: true,
        message: 'external-users sync completed successfully',
        syncType: 'external-users',
        installation: 'Installation 1',
      });
    });

    it('should trigger internal users sync job when syncType is INTERNAL_USERS', async () => {
      const result = await controller.sync(
        SlackSyncType.INTERNAL_USERS,
        botCtx,
      );

      expect(mockSlackUsersSyncJob.execute).toHaveBeenCalledWith(
        botCtx.installation,
      );
      expect(result).toEqual({
        success: true,
        message: 'internal-users sync completed successfully',
        syncType: 'internal-users',
        installation: 'Installation 1',
      });
    });

    it('should handle errors from the external users sync job', async () => {
      const error = new Error('Sync job error');
      (mockSlackExternalUsersSyncJob.execute as Mock).mockRejectedValue(error);

      await expect(
        controller.sync(SlackSyncType.EXTERNAL_USERS, botCtx),
      ).rejects.toThrow(InternalServerErrorException);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to sync external-users for installation Installation 1: Sync job error'),
        error.stack,
      );
    });

    it('should handle errors from the internal users sync job', async () => {
      const error = new Error('Sync job error');
      (mockSlackUsersSyncJob.execute as Mock).mockRejectedValue(error);

      await expect(
        controller.sync(SlackSyncType.INTERNAL_USERS, botCtx),
      ).rejects.toThrow(InternalServerErrorException);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to sync internal-users for installation Installation 1: Sync job error'),
        error.stack,
      );
    });

    it('should handle HttpException errors and rethrow them', async () => {
      const httpError = new HttpException('HTTP error', 400);

      vi.spyOn(
        controller as any,
        'slackExternalUsersSyncJob',
        'get',
      ).mockImplementation(() => {
        throw httpError;
      });

      await expect(
        controller.sync(SlackSyncType.EXTERNAL_USERS, botCtx),
      ).rejects.toThrow(HttpException);
    });

    it('should handle general errors and throw InternalServerErrorException', async () => {
      const error = new Error('General error');

      vi.spyOn(
        controller as any,
        'slackExternalUsersSyncJob',
        'get',
      ).mockImplementation(() => {
        throw error;
      });

      await expect(
        controller.sync(SlackSyncType.EXTERNAL_USERS, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should handle non-Error objects and log them', async () => {
      const nonErrorObject = { message: 'Not an Error instance' };

      vi.spyOn(
        controller as any,
        'slackExternalUsersSyncJob',
        'get',
      ).mockImplementation(() => {
        throw nonErrorObject;
      });

      await expect(
        controller.sync(SlackSyncType.EXTERNAL_USERS, botCtx),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });
});
