import { Response } from 'express';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { InstallationRepository } from '../../../../src/database/entities/installations/repositories';
import { ConditionalFormBuilderComposite } from '../../../../src/slack/blocks/components/composite/form-builder';
import { FormBuilderController } from '../../../../src/slack/controllers/form-builder.controller';
import { SlackWebAPIService } from '../../../../src/slack/providers/slack-apis/slack-apis.service';
import { FormBuilderService } from '../../../../src/slack/services/form-builder.service';
import { ILogger } from '../../../../src/utils/logger/logger.interface';

describe('FormBuilderController', () => {
  let controller: FormBuilderController;
  let mockLogger: ILogger;
  let mockFormBuilderService: FormBuilderService;
  let mockFormBuilder: ConditionalFormBuilderComposite;
  let mockSlackApiService: SlackWebAPIService;
  let mockInstallationRepository: InstallationRepository;
  let mockResponse: Response;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    } as unknown as ILogger;

    mockFormBuilderService = {
      getFormById: vi.fn().mockResolvedValue({
        fields: [{ id: 'field1', label: 'Field 1' }],
        conditions: [],
        conditionOrder: [],
      }),
    } as unknown as FormBuilderService;

    mockFormBuilder = {
      build: vi.fn(),
    } as unknown as ConditionalFormBuilderComposite;

    mockSlackApiService = {
      openView: vi.fn(),
      postMessage: vi.fn(),
      updateView: vi.fn(),
    } as unknown as SlackWebAPIService;

    mockInstallationRepository = {
      findByCondition: vi.fn(),
    } as unknown as InstallationRepository;

    mockResponse = {
      status: vi.fn().mockReturnThis(),
      send: vi.fn(),
    } as unknown as Response;

    controller = new FormBuilderController(
      mockLogger,
      mockFormBuilderService,
      mockFormBuilder,
      mockSlackApiService,
      mockInstallationRepository,
    );
  });

  describe('handleFormSelection', () => {
    it('should handle form selection successfully', async () => {
      const payloadData = {
        trigger_id: 'trigger-123',
        team: { id: 'T12345' },
        channel: { id: 'C12345' },
        actions: [
          {
            action_id: 'form_selector_block',
            selected_option: { value: 'form-123' },
          },
        ],
      };

      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      };

      (mockInstallationRepository.findByCondition as Mock).mockResolvedValue(
        mockInstallation,
      );

      // Skip mocking nested function calls - just test that we get a response
      await controller.handleFormSelection({ payload: payloadData }, mockResponse);

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.send).toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      const payloadData = {
        trigger_id: 'trigger-123',
        team: { id: 'T12345' },
        channel: { id: 'C12345' },
        actions: [
          {
            action_id: 'form_selector_block',
            selected_option: { value: 'form-123' },
          },
        ],
      };

      (mockInstallationRepository.findByCondition as Mock).mockRejectedValue(
        new Error('DB error'),
      );

      await controller.handleFormSelection({ payload: payloadData }, mockResponse);

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.send).toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error handling form selection: DB error',
        expect.any(String),
      );
    });
  });

  describe('handleFormSubmission', () => {
    it('should handle form submission successfully', async () => {
      const payloadData = {
        view: {
          private_metadata: JSON.stringify({
            formId: 'form-123',
            teamId: 'T12345',
            channelId: 'C12345',
          }),
          state: {
            values: {
              block1: {
                field_name: { value: 'Test Name' },
              },
              block2: {
                field_description: { value: 'Test Description' },
              },
            },
          },
        },
        user: {
          id: 'U12345',
        },
      };

      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      };

      (mockInstallationRepository.findByCondition as Mock).mockResolvedValue(
        mockInstallation,
      );
      (mockSlackApiService.postMessage as Mock).mockResolvedValue({ ok: true });

      await controller.handleFormSubmission({ payload: payloadData }, mockResponse);

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.send).toHaveBeenCalled();
      expect(mockInstallationRepository.findByCondition).toHaveBeenCalledWith({
        where: { teamId: 'T12345' },
      });
      expect(mockSlackApiService.postMessage).toHaveBeenCalledWith(
        'xoxb-token',
        {
          channel: 'U12345',
          text: expect.stringContaining(
            'Your form has been submitted successfully',
          ),
        },
      );
    });

    it('should handle errors gracefully', async () => {
      const payloadData = {
        view: {
          private_metadata: JSON.stringify({
            formId: 'form-123',
            teamId: 'T12345',
            channelId: 'C12345',
          }),
          state: {
            values: {},
          },
        },
        user: {
          id: 'U12345',
        },
      };

      (mockInstallationRepository.findByCondition as Mock).mockRejectedValue(
        new Error('DB error'),
      );

      await controller.handleFormSubmission({ payload: payloadData }, mockResponse);

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.send).toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error handling form submission: DB error',
        expect.any(String),
      );
    });
  });

  describe('handleInteraction', () => {
    it('should handle block actions in a modal view', async () => {
      const payloadData = {
        type: 'block_actions',
        actions: [
          {
            action_id: 'field_name',
            value: 'Test Name',
          },
        ],
        view: {
          type: 'modal',
          id: 'V12345',
          hash: 'hash123',
          private_metadata: JSON.stringify({
            formId: 'form-123',
            teamId: 'T12345',
          }),
          state: {
            values: {
              block1: {
                field_name: { value: 'Test Name' },
              },
            },
          },
        },
        team: {
          id: 'T12345',
        },
      };

      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      };

      const mockFormData = {
        fields: [{ id: 'name', label: 'Name' }],
        conditions: [],
        conditionOrder: [],
      };

      const mockUpdatedBlocks = {
        blocks: [{ type: 'input', block_id: 'block1' }],
      };

      (mockInstallationRepository.findByCondition as Mock).mockResolvedValue(
        mockInstallation,
      );
      (mockFormBuilderService.getFormById as Mock).mockResolvedValue(
        mockFormData,
      );
      (mockFormBuilder.build as Mock).mockReturnValue(mockUpdatedBlocks);
      (mockSlackApiService.updateView as Mock).mockResolvedValue({ ok: true });

      await controller.handleInteraction({ payload: payloadData }, mockResponse);

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.send).toHaveBeenCalled();
      expect(mockInstallationRepository.findByCondition).toHaveBeenCalledWith({
        where: { teamId: 'T12345' },
      });
      expect(mockFormBuilderService.getFormById).toHaveBeenCalledWith(
        mockInstallation,
        'form-123',
        'T12345',
      );
      expect(mockFormBuilder.build).toHaveBeenCalledWith({
        fields: mockFormData.fields,
        conditions: mockFormData.conditions,
        conditionOrder: mockFormData.conditionOrder,
        values: { name: 'Test Name' },
      });
      expect(mockSlackApiService.updateView).toHaveBeenCalledWith(
        'xoxb-token',
        {
          view_id: 'V12345',
          hash: 'hash123',
          view: expect.objectContaining({
            blocks: mockUpdatedBlocks.blocks,
          }),
        },
      );
    });

    it('should handle errors gracefully', async () => {
      const payloadData = {
        type: 'block_actions',
        actions: [
          {
            action_id: 'field_name',
            value: 'Test Name',
          },
        ],
        view: {
          type: 'modal',
          id: 'V12345',
          hash: 'hash123',
          private_metadata: JSON.stringify({
            formId: 'form-123',
            teamId: 'T12345',
          }),
        },
        team: {
          id: 'T12345',
        },
      };

      (mockInstallationRepository.findByCondition as Mock).mockRejectedValue(
        new Error('DB error'),
      );

      await controller.handleInteraction({ payload: payloadData }, mockResponse);

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.send).toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error handling interaction: DB error',
        expect.any(String),
      );
    });
  });
});
