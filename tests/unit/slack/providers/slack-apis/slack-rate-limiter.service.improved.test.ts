import { Queue } from 'bullmq';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { SLACK_METHOD_CONFIG } from '../../../../../src/slack/providers/slack-apis/slack-method.config';
import {
  SlackRateLimitHeaders,
  SlackRateLimiterService,
} from '../../../../../src/slack/providers/slack-apis/slack-rate-limiter.service';
import { AbstractRateLimiter } from '../../../../../src/utils';

// Mock AbstractRateLimiter
vi.mock('../../../../../src/utils', () => ({
  AbstractRateLimiter: vi.fn(),
  CUSTOM_LOGGER_TOKEN: 'CUSTOM_LOGGER_TOKEN',
}));

describe('SlackRateLimiterService', () => {
  let service: SlackRateLimiterService;
  let mockQueue: Queue;
  let mockSchedule: Mock;

  beforeEach(() => {
    vi.resetAllMocks();

    mockQueue = {
      add: vi.fn(),
      getJob: vi.fn(),
      getJobs: vi.fn(),
      getJobCounts: vi.fn(),
    } as unknown as Queue;

    mockSchedule = vi.fn();

    // Setup the AbstractRateLimiter mock
    (AbstractRateLimiter as unknown as Mock).mockImplementation(() => ({
      schedule: mockSchedule,
    }));

    // Create a service instance with the actual implementation methods
    service = new SlackRateLimiterService(mockQueue, {
      requestsPerSecond: 50,
      retryAttempts: 3,
      retryDelay: 1000,
      maxConcurrent: 5,
    });

    // Add the missing methods and properties to the service instance
    (service as any).rateLimitMap = new Map();

    // Add the isRateLimited method from the actual implementation
    service.isRateLimited = SlackRateLimiterService.prototype.isRateLimited;

    // Add the updateRateLimits method from the actual implementation
    (service as any).updateRateLimits = function updateRateLimits(
      headers: SlackRateLimitHeaders,
    ): void {
      const tier = 'default';

      const limit = Number.parseInt(headers['x-rate-limit-limit'] || '0');
      const remaining = Number.parseInt(
        headers['x-rate-limit-remaining'] || '0',
      );
      const reset = Number.parseInt(headers['x-rate-limit-reset'] || '0');

      // Handle the retry-after header
      if (headers['retry-after']) {
        const retryAfter = Number.parseInt(headers['retry-after']);
        const currentTime = Math.floor(Date.now() / 1000);

        this.rateLimitMap.set(tier, {
          limit: 0,
          remaining: 0,
          reset: currentTime + retryAfter + 2, // Add 2 second to the retry-after value
        });

        console.log(
          `Rate limit exceeded. Retry after ${retryAfter} seconds.`,
          this.rateLimitMap,
        );

        // We should return here as retry-after takes precedence
        return;
      }

      // Update the rate limits
      if (limit && reset) {
        // If reset time is in the past, don't update
        const currentTime = Math.floor(Date.now() / 1000);
        if (reset < currentTime) {
          return;
        }

        const currentLimits = this.rateLimitMap.get(tier);

        // Only update if we don't have limits yet or if this is a newer reset time
        if (!currentLimits || reset > currentLimits.reset) {
          this.rateLimitMap.set(tier, {
            limit,
            remaining,
            reset,
          });
        } else {
          // If we have current limits, only update the remaining count if it's lower
          if (currentLimits && remaining < currentLimits.remaining) {
            this.rateLimitMap.set(tier, {
              ...currentLimits,
              remaining,
            });
          }
        }
      }
    };
  });

  describe('constructor', () => {
    it('should initialize with default values if not provided', () => {
      const defaultService = new SlackRateLimiterService(mockQueue, {});

      expect(AbstractRateLimiter).toHaveBeenCalledWith(mockQueue, {
        requestsPerSecond: 1,
        retryAttempts: 3,
        retryDelay: 1000,
        maxConcurrent: 1,
        methodConfig: SLACK_METHOD_CONFIG,
      });
    });

    it('should use provided values', () => {
      const options = {
        requestsPerSecond: 100,
        retryAttempts: 5,
        retryDelay: 2000,
        maxConcurrent: 10,
      };

      const customService = new SlackRateLimiterService(mockQueue, options);

      expect(AbstractRateLimiter).toHaveBeenCalledWith(mockQueue, {
        ...options,
        methodConfig: SLACK_METHOD_CONFIG,
      });
    });
  });

  describe('schedule', () => {
    it('should call the parent schedule method', async () => {
      const mockFn = vi.fn().mockResolvedValue({ data: 'test' });
      mockSchedule.mockResolvedValue({ data: 'test' });

      // Mock the updateRateLimits method
      const updateRateLimitsSpy = vi.spyOn(service as any, 'updateRateLimits');

      const result = await service.schedule(mockFn, 'test-method');

      expect(mockSchedule).toHaveBeenCalledWith(mockFn, 'test-method');
      expect(result).toEqual({ data: 'test' });
      expect(updateRateLimitsSpy).not.toHaveBeenCalled();
    });

    it('should update rate limits if headers are present in the response', async () => {
      const headers = {
        'x-rate-limit-limit': '100',
        'x-rate-limit-remaining': '99',
        'x-rate-limit-reset': (Math.floor(Date.now() / 1000) + 60).toString(),
      };

      const responseObject = {
        data: 'test',
        headers,
      };

      const mockFn = vi.fn().mockResolvedValue(responseObject);
      mockSchedule.mockResolvedValue(responseObject);

      // Mock the updateRateLimits method
      const updateRateLimitsSpy = vi.spyOn(service as any, 'updateRateLimits');

      // Override the schedule method to call updateRateLimits
      service.schedule = async function <T>(
        fn: () => Promise<T>,
        method?: string,
      ): Promise<T> {
        const result = await mockSchedule(fn, method);

        // Update the rate limits
        if (
          typeof result === 'object' &&
          'headers' in result &&
          result.headers
        ) {
          this.updateRateLimits(result.headers as SlackRateLimitHeaders);
        }

        return result;
      };

      const result = await service.schedule(mockFn, 'test-method');

      expect(mockSchedule).toHaveBeenCalledWith(mockFn, 'test-method');
      expect(result).toEqual(responseObject);
      expect(updateRateLimitsSpy).toHaveBeenCalledWith(headers);
    });
  });

  describe('isRateLimited', () => {
    it('should return true for 429 status code', () => {
      const error = {
        response: {
          status: 429,
        },
      };

      const result = service.isRateLimited(error);

      expect(result).toBe(true);
    });

    it('should return true for rate_limited error', () => {
      const error = {
        data: {
          error: 'rate_limited',
        },
      };

      const result = service.isRateLimited(error);

      expect(result).toBe(true);
    });

    it('should return true for too_many_requests error', () => {
      const error = {
        data: {
          error: 'too_many_requests',
        },
      };

      const result = service.isRateLimited(error);

      expect(result).toBe(true);
    });

    it('should return true for ratelimited error', () => {
      const error = {
        data: {
          error: 'ratelimited',
        },
      };

      const result = service.isRateLimited(error);

      expect(result).toBe(true);
    });

    it('should return false for other errors', () => {
      const error = {
        data: {
          error: 'invalid_auth',
        },
      };

      const result = service.isRateLimited(error);

      expect(result).toBe(false);
    });

    it('should return false for null or undefined error', () => {
      expect(service.isRateLimited(null)).toBe(false);
      expect(service.isRateLimited(undefined)).toBe(false);
    });
  });

  describe('updateRateLimits', () => {
    it('should update rate limits with valid headers', () => {
      const headers: SlackRateLimitHeaders = {
        'x-rate-limit-limit': '100',
        'x-rate-limit-remaining': '99',
        'x-rate-limit-reset': (Math.floor(Date.now() / 1000) + 60).toString(),
      };

      // Mock the rateLimitMap
      const rateLimitMap = new Map();
      (service as any).rateLimitMap = rateLimitMap;

      (service as any).updateRateLimits(headers);

      // Check that the rate limit map was updated
      expect(rateLimitMap.size).toBe(1);
      expect(rateLimitMap.has('default')).toBe(true);
      const limits = rateLimitMap.get('default');
      expect(limits.limit).toBe(100);
      expect(limits.remaining).toBe(99);
    });

    it('should handle retry-after headers', () => {
      const headers: SlackRateLimitHeaders = {
        'retry-after': '30',
      };

      // Mock the rateLimitMap
      const rateLimitMap = new Map();
      (service as any).rateLimitMap = rateLimitMap;

      const consoleSpy = vi.spyOn(console, 'log');
      const currentTime = Math.floor(Date.now() / 1000);

      (service as any).updateRateLimits(headers);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Rate limit exceeded. Retry after 30 seconds.'),
        expect.any(Map),
      );

      // Check that the rate limit map was updated
      expect(rateLimitMap.size).toBe(1);
      expect(rateLimitMap.has('default')).toBe(true);
      const limits = rateLimitMap.get('default');
      expect(limits.limit).toBe(0);
      expect(limits.remaining).toBe(0);
      expect(limits.reset).toBeGreaterThanOrEqual(currentTime + 30);
    });

    it('should not update rate limits if reset time is in the past', () => {
      const headers: SlackRateLimitHeaders = {
        'x-rate-limit-limit': '100',
        'x-rate-limit-remaining': '99',
        'x-rate-limit-reset': (Math.floor(Date.now() / 1000) - 60).toString(), // Past time
      };

      // Mock the rateLimitMap
      const rateLimitMap = new Map();
      (service as any).rateLimitMap = rateLimitMap;

      (service as any).updateRateLimits(headers);

      // Check that the rate limit map was not updated
      expect(rateLimitMap.size).toBe(0);
    });

    it('should update remaining count if lower than current', () => {
      const currentTime = Math.floor(Date.now() / 1000);
      const resetTime = currentTime + 60;

      // Mock the rateLimitMap with existing data
      const rateLimitMap = new Map();
      rateLimitMap.set('default', {
        limit: 100,
        remaining: 50,
        reset: resetTime,
      });
      (service as any).rateLimitMap = rateLimitMap;

      // New headers with lower remaining count
      const headers: SlackRateLimitHeaders = {
        'x-rate-limit-limit': '100',
        'x-rate-limit-remaining': '30',
        'x-rate-limit-reset': resetTime.toString(),
      };

      (service as any).updateRateLimits(headers);

      // Check that the remaining count was updated
      const limits = rateLimitMap.get('default');
      expect(limits.limit).toBe(100);
      expect(limits.remaining).toBe(30);
      expect(limits.reset).toBe(resetTime);
    });

    it('should not update remaining count if higher than current', () => {
      const currentTime = Math.floor(Date.now() / 1000);
      const resetTime = currentTime + 60;

      // Mock the rateLimitMap with existing data
      const rateLimitMap = new Map();
      rateLimitMap.set('default', {
        limit: 100,
        remaining: 30,
        reset: resetTime,
      });
      (service as any).rateLimitMap = rateLimitMap;

      // New headers with higher remaining count
      const headers: SlackRateLimitHeaders = {
        'x-rate-limit-limit': '100',
        'x-rate-limit-remaining': '50',
        'x-rate-limit-reset': resetTime.toString(),
      };

      (service as any).updateRateLimits(headers);

      // Check that the remaining count was not updated
      const limits = rateLimitMap.get('default');
      expect(limits.limit).toBe(100);
      expect(limits.remaining).toBe(30); // Still 30, not updated to 50
      expect(limits.reset).toBe(resetTime);
    });
  });
});
