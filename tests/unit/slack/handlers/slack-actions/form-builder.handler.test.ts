import { WebClient } from '@slack/web-api';
import { <PERSON><PERSON>, beforeEach, describe, expect, it, vi } from 'vitest';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories';
import { TeamRelationshipType } from '../../../../../src/database/entities/mappings';
import { ConditionalFormBuilderComposite } from '../../../../../src/slack/blocks/components/composite/form-builder/conditional-form-builder.composite';
import { SettingsCore } from '../../../../../src/slack/core/management/settings.management';
import {
  DecoratedSlackActionMiddlewareArgs,
  DecoratedSlackViewMiddlewareArgs,
} from '../../../../../src/slack/event-handlers';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON>,
  Form<PERSON>ield<PERSON>ction<PERSON><PERSON><PERSON>,
  FormSubmissionHandler,
} from '../../../../../src/slack/handlers/slack-actions/form-builder.handler';
import { FormBuilderService } from '../../../../../src/slack/services/form-builder.service';
import { FormSubmissionService } from '../../../../../src/slack/services/form-submission.service';
import { ILogger } from '../../../../../src/utils';

describe('FormBuilderHandler', () => {
  let handler: FormBuilderHandler;
  let mockLogger: ILogger;
  let mockFormBuilderService: FormBuilderService;
  let mockFormBuilder: ConditionalFormBuilderComposite;
  let mockChannelsRepository: ChannelsRepository;
  let mockSettingsCore: SettingsCore;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    } as unknown as ILogger;

    mockFormBuilderService = {
      getFormById: vi.fn(),
    } as unknown as FormBuilderService;

    mockFormBuilder = {
      build: vi.fn().mockReturnValue({ blocks: [] }),
    } as unknown as ConditionalFormBuilderComposite;

    mockChannelsRepository = {
      findByCondition: vi.fn(),
    } as unknown as ChannelsRepository;

    mockSettingsCore = {
      getValue: vi.fn().mockResolvedValue([]),
    } as unknown as SettingsCore;

    handler = new FormBuilderHandler(
      mockLogger,
      mockFormBuilderService,
      mockFormBuilder,
      mockChannelsRepository,
      mockSettingsCore,
    );
  });

  describe('handle', () => {
    it('should open a modal with form when valid form is selected', async () => {
      const actionPayload = {
        action_id: 'form_continue',
        block_id: 'form_selector_block',
      };

      const args = {
        ack: vi.fn(),
        respond: vi.fn(),
        say: vi.fn(),
        payload: actionPayload,
        action: actionPayload,
        logger: mockLogger,
        client: vi.fn() as unknown as WebClient,
        next: vi.fn().mockResolvedValue(undefined),
        body: {
          type: 'block_actions',
          actions: [actionPayload],
          state: {
            values: {
              form_selector_block: {
                form_selector: {
                  selected_option: {
                    value: 'form-123',
                  },
                },
              },
            },
          },
          container: {
            channel_id: 'C12345',
          },
          trigger_id: 'trigger-123',
        },
        context: {
          installation: {
            id: 'installation-id',
          },
          organization: {
            id: 'org-id',
          },
          user: {
            id: 'user-id',
          },
          client: {
            views: {
              open: vi.fn(),
              update: vi.fn(),
            },
            chat: {
              postMessage: vi.fn(),
              postEphemeral: vi.fn(),
              update: vi.fn(),
            },
          },
        },
      };

      const mockChannel = {
        channelId: 'C12345',
        platformTeamsToChannelMappings: [
          {
            relationshipType: TeamRelationshipType.PRIMARY,
            platformTeam: {
              uid: 'team-123',
            },
          },
        ],
      };

      const mockFormData = {
        fields: [{ id: 'field1', name: 'Field 1', type: 'text' }],
        conditions: [],
        conditionOrder: [],
        name: 'Test Form',
      };

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(
        mockChannel,
      );
      (mockFormBuilderService.getFormById as Mock).mockResolvedValue(
        mockFormData,
      );
      (mockFormBuilder.build as Mock).mockReturnValue({
        blocks: [{ type: 'input' }],
      });

      await handler.handle(
        args as unknown as DecoratedSlackActionMiddlewareArgs,
      );

      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          channelId: 'C12345',
          installation: { id: 'installation-id' },
        },
        relations: [
          'platformTeamsToChannelMappings',
          'platformTeamsToChannelMappings.platformTeam',
        ],
      });
      expect(mockFormBuilderService.getFormById).toHaveBeenCalledWith(
        args.context.installation,
        'form-123',
        'team-123',
        [],
      );
      expect(mockFormBuilder.build).toHaveBeenCalledWith({
        fields: mockFormData.fields,
        conditions: mockFormData.conditions,
        conditionOrder: mockFormData.conditionOrder,
        values: {},
        name: mockFormData.name,
      });
      expect(args.context.client.views.open).toHaveBeenCalledWith({
        trigger_id: 'trigger-123',
        view: expect.objectContaining({
          type: 'modal',
          callback_id: 'form_submission_modal',
          private_metadata: expect.any(String),
          blocks: [{ type: 'input' }],
        }),
      });
    });

    it('should handle case when no form is selected', async () => {
      const actionPayload = {
        action_id: 'form_continue',
        block_id: 'form_selector_block',
      };

      const args = {
        ack: vi.fn(),
        respond: vi.fn(),
        say: vi.fn(),
        payload: actionPayload,
        action: actionPayload,
        logger: mockLogger,
        client: vi.fn() as unknown as WebClient,
        next: vi.fn().mockResolvedValue(undefined),
        body: {
          type: 'block_actions',
          actions: [actionPayload],
          state: {
            values: {
              form_selector_block: {
                form_selector: {
                  selected_option: null, // No form selected
                },
              },
            },
          },
          trigger_id: 'trigger-123',
        },
        context: {
          installation: {
            id: 'installation-id',
          },
          organization: {
            id: 'org-id',
          },
          user: {
            id: 'user-id',
          },
          client: {
            views: {
              open: vi.fn(),
              update: vi.fn(),
            },
            chat: {
              postMessage: vi.fn(),
              postEphemeral: vi.fn(),
              update: vi.fn(),
            },
          },
        },
      };

      await handler.handle(
        args as unknown as DecoratedSlackActionMiddlewareArgs,
      );

      expect(mockLogger.error).toHaveBeenCalled();
      expect(args.context.client.views.open).not.toHaveBeenCalled();
    });

    it('should handle case when channel is not found', async () => {
      const actionPayload = {
        action_id: 'form_continue',
        block_id: 'form_selector_block',
      };

      const args = {
        ack: vi.fn(),
        respond: vi.fn(),
        say: vi.fn(),
        payload: actionPayload,
        action: actionPayload,
        logger: mockLogger,
        client: vi.fn() as unknown as WebClient,
        next: vi.fn().mockResolvedValue(undefined),
        body: {
          type: 'block_actions',
          actions: [actionPayload],
          state: {
            values: {
              form_selector_block: {
                form_selector: {
                  selected_option: {
                    value: 'form-123',
                  },
                },
              },
            },
          },
          container: {
            channel_id: 'C12345',
          },
          trigger_id: 'trigger-123',
        },
        context: {
          installation: {
            id: 'installation-id',
          },
          organization: {
            id: 'org-id',
          },
          user: {
            id: 'user-id',
          },
          client: {
            views: {
              open: vi.fn(),
              update: vi.fn(),
            },
            chat: {
              postMessage: vi.fn(),
              postEphemeral: vi.fn(),
              update: vi.fn(),
            },
          },
        },
      };

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(null);

      await handler.handle(
        args as unknown as DecoratedSlackActionMiddlewareArgs,
      );

      expect(mockLogger.error).toHaveBeenCalled();
      expect(args.context.client.views.open).not.toHaveBeenCalled();
    });

    it('should handle case when primary team is not found', async () => {
      const actionPayload = {
        action_id: 'form_continue',
        block_id: 'form_selector_block',
      };

      const args = {
        ack: vi.fn(),
        respond: vi.fn(),
        say: vi.fn(),
        payload: actionPayload,
        action: actionPayload,
        logger: mockLogger,
        client: vi.fn() as unknown as WebClient,
        next: vi.fn().mockResolvedValue(undefined),
        body: {
          type: 'block_actions',
          actions: [actionPayload],
          state: {
            values: {
              form_selector_block: {
                form_selector: {
                  selected_option: {
                    value: 'form-123',
                  },
                },
              },
            },
          },
          container: {
            channel_id: 'C12345',
          },
          trigger_id: 'trigger-123',
        },
        context: {
          installation: {
            id: 'installation-id',
          },
          organization: {
            id: 'org-id',
          },
          user: {
            id: 'user-id',
          },
          client: {
            views: {
              open: vi.fn(),
              update: vi.fn(),
            },
            chat: {
              postMessage: vi.fn(),
              postEphemeral: vi.fn(),
              update: vi.fn(),
            },
          },
        },
      };

      const mockChannel = {
        channelId: 'C12345',
        platformTeamsToChannelMappings: [
          {
            relationshipType: TeamRelationshipType.SECONDARY,
            platformTeam: {
              uid: 'team-123',
            },
          },
        ],
      };

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(
        mockChannel,
      );

      await handler.handle(
        args as unknown as DecoratedSlackActionMiddlewareArgs,
      );

      expect(mockLogger.error).toHaveBeenCalled();
      expect(args.context.client.views.open).not.toHaveBeenCalled();
    });

    it('should handle errors during form fetching', async () => {
      const actionPayload = {
        action_id: 'form_continue',
        block_id: 'form_selector_block',
      };

      const args = {
        ack: vi.fn(),
        respond: vi.fn(),
        say: vi.fn(),
        payload: actionPayload,
        action: actionPayload,
        logger: mockLogger,
        client: vi.fn() as unknown as WebClient,
        next: vi.fn().mockResolvedValue(undefined),
        body: {
          type: 'block_actions',
          actions: [actionPayload],
          state: {
            values: {
              form_selector_block: {
                form_selector: {
                  selected_option: {
                    value: 'form-123',
                  },
                },
              },
            },
          },
          container: {
            channel_id: 'C12345',
          },
          trigger_id: 'trigger-123',
        },
        context: {
          installation: {
            id: 'installation-id',
          },
          organization: {
            id: 'org-id',
          },
          user: {
            id: 'user-id',
          },
          client: {
            views: {
              open: vi.fn(),
              update: vi.fn(),
            },
            chat: {
              postMessage: vi.fn(),
              postEphemeral: vi.fn(),
              update: vi.fn(),
            },
          },
        },
      };

      const mockChannel = {
        channelId: 'C12345',
        platformTeamsToChannelMappings: [
          {
            relationshipType: TeamRelationshipType.PRIMARY,
            platformTeam: {
              uid: 'team-123',
            },
          },
        ],
      };

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(
        mockChannel,
      );
      (mockFormBuilderService.getFormById as Mock).mockRejectedValue(
        new Error('API error'),
      );

      await handler.handle(
        args as unknown as DecoratedSlackActionMiddlewareArgs,
      );

      expect(mockLogger.error).toHaveBeenCalled();
      expect(args.context.client.views.open).not.toHaveBeenCalled();
    });
  });
});

describe('FormFieldActionHandler', () => {
  let handler: FormFieldActionHandler;
  let mockFormBuilder: ConditionalFormBuilderComposite;
  let mockLogger: ILogger;
  let mockFormBuilderService: FormBuilderService;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    } as unknown as ILogger;

    mockFormBuilder = {
      build: vi.fn().mockReturnValue({ blocks: [] }),
    } as unknown as ConditionalFormBuilderComposite;

    mockFormBuilderService = {
      getFormById: vi.fn(),
    } as unknown as FormBuilderService;

    handler = new FormFieldActionHandler(mockLogger, mockFormBuilder, mockFormBuilderService);
  });

  describe('handle', () => {
    it('should update view with new blocks when field value changes', async () => {
      const actionPayload = {
        block_id: 'block_field1',
        action_id: 'action1',
      };

      const args = {
        ack: vi.fn(),
        respond: vi.fn(),
        say: vi.fn(),
        payload: actionPayload,
        action: actionPayload,
        logger: mockLogger,
        client: vi.fn() as unknown as WebClient,
        next: vi.fn().mockResolvedValue(undefined),
        body: {
          type: 'block_actions',
          actions: [actionPayload],
          view: {
            id: 'view-123',
            callback_id: 'form_submission_modal',
            private_metadata: JSON.stringify({
              fields: [
                { id: 'field1', name: 'Field 1', type: 'text' },
                { id: 'field2', name: 'Field 2', type: 'select' },
              ],
              conditions: [],
              conditionOrder: [],
            }),
            state: {
              values: {
                block_field1: {
                  action1: {
                    type: 'plain_text_input',
                    value: 'test value',
                  },
                },
                block_field2: {
                  action2: {
                    type: 'static_select',
                    selected_option: {
                      value: 'option1',
                    },
                  },
                },
              },
            },
          },
        },
        context: {
          installation: {
            id: 'installation-id',
          },
          organization: {
            id: 'org-id',
          },
          user: {
            id: 'user-id',
          },
          client: {
            views: {
              update: vi.fn(),
            },
          },
        },
      };

      (mockFormBuilder.build as Mock).mockReturnValue({
        blocks: [{ type: 'input', block_id: 'block_field1' }],
      });

      (mockFormBuilderService.getFormById as Mock).mockResolvedValue({
        fields: [
          { id: 'field1', name: 'Field 1', type: 'text' },
          { id: 'field2', name: 'Field 2', type: 'select' },
        ],
        conditions: [],
        conditionOrder: [],
        name: 'Test Form',
      });

      await handler.handle(
        args as unknown as DecoratedSlackActionMiddlewareArgs,
      );

      expect(mockFormBuilder.build).toHaveBeenCalledWith({
        fields: [
          { id: 'field1', name: 'Field 1', type: 'text' },
          { id: 'field2', name: 'Field 2', type: 'select' },
        ],
        conditions: [],
        conditionOrder: [],
        values: {
          field1: 'test value',
          field2: 'option1',
        },
        name: 'Test Form',
      });
      expect(args.context.client.views.update).toHaveBeenCalledWith({
        view_id: 'view-123',
        view: expect.objectContaining({
          type: 'modal',
          callback_id: 'form_submission_modal',
          blocks: [{ type: 'input', block_id: 'block_field1' }],
        }),
      });
    });

    it('should handle different field types correctly', async () => {
      const actionPayload = {
        block_id: 'block_field1',
        action_id: 'action1',
      };

      const args = {
        ack: vi.fn(),
        respond: vi.fn(),
        say: vi.fn(),
        payload: actionPayload,
        action: actionPayload,
        logger: mockLogger,
        client: vi.fn() as unknown as WebClient,
        next: vi.fn().mockResolvedValue(undefined),
        body: {
          type: 'block_actions',
          actions: [actionPayload],
          view: {
            id: 'view-123',
            callback_id: 'form_submission_modal',
            private_metadata: JSON.stringify({
              fields: [
                { id: 'field1', name: 'Text Field', type: 'text' },
                { id: 'field2', name: 'Number Field', type: 'number' },
                { id: 'field3', name: 'Select Field', type: 'select' },
                { id: 'field4', name: 'Multi Select Field', type: 'multiselect' },
                { id: 'field5', name: 'Date Field', type: 'date' },
                { id: 'field6', name: 'Checkbox Field', type: 'checkbox' },
              ],
              conditions: [],
              conditionOrder: [],
            }),
            state: {
              values: {
                block_field1: {
                  action1: {
                    type: 'plain_text_input',
                    value: 'text value',
                  },
                },
                block_field2: {
                  action2: {
                    type: 'number_input',
                    value: '42',
                  },
                },
                block_field3: {
                  action3: {
                    type: 'static_select',
                    selected_option: {
                      value: 'option1',
                    },
                  },
                },
                block_field4: {
                  action4: {
                    type: 'multi_static_select',
                    selected_options: [
                      { value: 'option1' },
                      { value: 'option2' },
                    ],
                  },
                },
                block_field5: {
                  action5: {
                    type: 'datepicker',
                    selected_date: '2025-04-22',
                  },
                },
                block_field6: {
                  action6: {
                    type: 'checkboxes',
                    selected_options: [{ value: 'checked' }],
                  },
                },
              },
            },
          },
        },
        context: {
          installation: {
            id: 'installation-id',
          },
          organization: {
            id: 'org-id',
          },
          user: {
            id: 'user-id',
          },
          client: {
            views: {
              update: vi.fn(),
            },
          },
        },
      };

      (mockFormBuilder.build as Mock).mockReturnValue({
        blocks: [{ type: 'input', block_id: 'block_field1' }],
      });

      (mockFormBuilderService.getFormById as Mock).mockResolvedValue({
        fields: [
          { id: 'field1', name: 'Text Field', type: 'text' },
          { id: 'field2', name: 'Number Field', type: 'number' },
          { id: 'field3', name: 'Select Field', type: 'select' },
          { id: 'field4', name: 'Multi Select Field', type: 'multiselect' },
          { id: 'field5', name: 'Date Field', type: 'date' },
          { id: 'field6', name: 'Checkbox Field', type: 'checkbox' },
        ],
        conditions: [],
        conditionOrder: [],
        name: 'Test Form',
      });

      await handler.handle(
        args as unknown as DecoratedSlackActionMiddlewareArgs,
      );

      expect(mockFormBuilder.build).toHaveBeenCalledWith({
        fields: [
          { id: 'field1', name: 'Text Field', type: 'text' },
          { id: 'field2', name: 'Number Field', type: 'number' },
          { id: 'field3', name: 'Select Field', type: 'select' },
          { id: 'field4', name: 'Multi Select Field', type: 'multiselect' },
          { id: 'field5', name: 'Date Field', type: 'date' },
          { id: 'field6', name: 'Checkbox Field', type: 'checkbox' },
        ],
        conditions: [],
        conditionOrder: [],
        values: {
          field1: 'text value',
          field2: 42,
          field3: 'option1',
          field4: ['option1', 'option2'],
          field5: '2025-04-22',
          field6: true,
        },
        name: 'Test Form',
      });
      expect(args.context.client.views.update).toHaveBeenCalled();
    });

    it('should ignore non-block actions', async () => {
      const actionPayload = {
        block_id: 'block_field1',
        action_id: 'action1',
      };

      const args = {
        ack: vi.fn(),
        respond: vi.fn(),
        say: vi.fn(),
        payload: actionPayload,
        action: actionPayload,
        logger: mockLogger,
        client: vi.fn() as unknown as WebClient,
        next: vi.fn().mockResolvedValue(undefined),
        body: {
          type: 'not_block_actions',
          actions: [actionPayload],
        },
        context: {
          installation: {
            id: 'installation-id',
          },
          organization: {
            id: 'org-id',
          },
          user: {
            id: 'user-id',
          },
          client: {
            views: {
              update: vi.fn(),
            },
          },
        },
      };

      await handler.handle(
        args as unknown as DecoratedSlackActionMiddlewareArgs,
      );

      expect(args.context.client.views.update).not.toHaveBeenCalled();
    });

    it('should ignore non-field blocks', async () => {
      const actionPayload = {
        block_id: 'not_a_field_block',
        action_id: 'action1',
      };

      const args = {
        ack: vi.fn(),
        respond: vi.fn(),
        say: vi.fn(),
        payload: actionPayload,
        action: actionPayload,
        logger: mockLogger,
        client: vi.fn() as unknown as WebClient,
        next: vi.fn().mockResolvedValue(undefined),
        body: {
          type: 'block_actions',
          actions: [actionPayload],
        },
        context: {
          installation: {
            id: 'installation-id',
          },
          organization: {
            id: 'org-id',
          },
          user: {
            id: 'user-id',
          },
          client: {
            views: {
              update: vi.fn(),
            },
          },
        },
      };

      await handler.handle(
        args as unknown as DecoratedSlackActionMiddlewareArgs,
      );

      expect(args.context.client.views.update).not.toHaveBeenCalled();
    });

    it('should handle errors during view update', async () => {
      const actionPayload = {
        block_id: 'block_field1',
        action_id: 'action1',
      };

      const args = {
        ack: vi.fn(),
        respond: vi.fn(),
        say: vi.fn(),
        payload: actionPayload,
        action: actionPayload,
        logger: mockLogger,
        client: vi.fn() as unknown as WebClient,
        next: vi.fn().mockResolvedValue(undefined),
        body: {
          type: 'block_actions',
          actions: [actionPayload],
          view: {
            id: 'view-123',
            callback_id: 'form_submission_modal',
            private_metadata: JSON.stringify({
              fields: [{ id: 'field1', name: 'Field 1', type: 'text' }],
              conditions: [],
              conditionOrder: [],
            }),
            state: {
              values: {
                block_field1: {
                  action1: {
                    type: 'plain_text_input',
                    value: 'test value',
                  },
                },
              },
            },
          },
        },
        context: {
          client: {
            views: {
              update: vi.fn().mockRejectedValue(new Error('API error')),
            },
          },
        },
      };

      (mockFormBuilderService.getFormById as Mock).mockResolvedValue({
        fields: [{ id: 'field1', name: 'Field 1', type: 'text' }],
        conditions: [],
        conditionOrder: [],
        name: 'Test Form',
      });

      await handler.handle(
        args as unknown as DecoratedSlackActionMiddlewareArgs,
      );

      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
});

describe('FormSubmissionHandler', () => {
  let handler: FormSubmissionHandler;
  let mockLogger: ILogger;
  let mockFormSubmissionService: FormSubmissionService;
  let mockFormBuilderService: FormBuilderService;
  let mockNotificationService: any;
  let mockTicketCreationHelper: any;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    } as unknown as ILogger;

    mockFormSubmissionService = {
      processSubmission: vi.fn(),
      validateSubmission: vi.fn().mockReturnValue([]),
      toKeyValueName: vi.fn(),
      toKeyValue: vi.fn(),
    } as unknown as FormSubmissionService;

    mockFormBuilderService = {
      getFormById: vi.fn(),
    } as unknown as FormBuilderService;

    mockNotificationService = {
      notifyUser: vi.fn(),
      sendTicketCreationConfirmation: vi.fn(),
    };

    mockTicketCreationHelper = {
      createTicketWithMetadata: vi.fn().mockResolvedValue({ id: 'ticket-123' }),
    };

    handler = new FormSubmissionHandler(
      mockLogger,
      mockFormSubmissionService,
      mockFormBuilderService,
      mockNotificationService,
      mockTicketCreationHelper,
    );
  });

  describe('handle', () => {
    it('should create a ticket when form submission is valid', async () => {
      const viewPayload = {
        callback_id: 'form_submission_modal',
      };

      const args = {
        ack: vi.fn(),
        respond: vi.fn(),
        say: vi.fn(),
        action: viewPayload,
        payload: viewPayload,
        body: {
          view: {
            private_metadata: JSON.stringify({
              formId: 'form-123',
              teamId: 'team-123',
              channelId: 'C12345',
              fields: [
                { id: 'title', name: 'Title', type: 'text' },
                { id: 'description', name: 'Description', type: 'text' },
                { id: 'priority', name: 'Priority', type: 'select' },
                { id: 'requestor', name: 'Requestor', type: 'text' },
                { id: 'custom_field1', name: 'Custom Field 1', type: 'text' },
              ],
            }),
            state: {
              values: {},
            },
          },
        },
        context: {
          installation: {
            id: 'installation-id',
          },
          organization: {
            id: 'org-123',
          },
          user: {
            id: 'user-id',
          },
          client: {
            views: {
              update: vi.fn(),
            },
          },
        },
      };

      const mockSubmissions = [
        { field: { id: 'title', name: 'Title', type: 'text' }, value: 'Test Ticket' },
        {
          field: { id: 'description', name: 'Description', type: 'text' },
          value: 'Test Description',
        },
        { field: { id: 'priority', name: 'Priority', type: 'select' }, value: 'high' },
        {
          field: { id: 'requestor', name: 'Requestor', type: 'text' },
          value: '<EMAIL>',
        },
        {
          field: { id: 'custom_field1', name: 'Custom Field 1', type: 'text' },
          value: 'Custom Value',
        },
      ];

      (mockFormSubmissionService.processSubmission as Mock).mockReturnValue(
        mockSubmissions,
      );
      (mockFormSubmissionService.validateSubmission as Mock).mockReturnValue(
        [],
      );
      (mockFormSubmissionService.toKeyValueName as Mock).mockReturnValue({
        title: 'Test Ticket',
        description: 'Test Description',
        priority: 'high',
        requestor: '<EMAIL>',
        custom_field1: 'Custom Value',
      });
      (mockFormBuilderService.getFormById as Mock).mockResolvedValue({
        fields: [
          { id: 'title', name: 'Title', type: 'text' },
          { id: 'description', name: 'Description', type: 'text' },
          { id: 'priority', name: 'Priority', type: 'select' },
          { id: 'requestor', name: 'Requestor', type: 'text' },
          { id: 'custom_field1', name: 'Custom Field 1', type: 'text' },
        ],
      });
      (mockTicketCreationHelper.createTicketWithMetadata as Mock).mockResolvedValue({
        id: 'ticket-123',
      });

      const result = await handler.handle(
        args as unknown as DecoratedSlackViewMiddlewareArgs,
      );

      expect(mockFormSubmissionService.processSubmission).toHaveBeenCalled();
      expect(mockFormSubmissionService.validateSubmission).toHaveBeenCalled();
      expect(mockTicketCreationHelper.createTicketWithMetadata).toHaveBeenCalled();
      expect(result).toEqual({
        response_action: 'clear',
      });
    });

    it('should return validation errors when form submission is invalid', async () => {
      const viewPayload = {
        callback_id: 'form_submission_modal',
      };

      const args = {
        ack: vi.fn(),
        respond: vi.fn(),
        say: vi.fn(),
        action: viewPayload,
        payload: viewPayload,
        body: {
          view: {
            private_metadata: JSON.stringify({
              formId: 'form-123',
              teamId: 'team-123',
              channelId: 'C12345',
              fields: [
                { id: 'title', name: 'Title', type: 'text' },
                { id: 'description', name: 'Description', type: 'text' },
              ],
            }),
            state: {
              values: {},
            },
          },
        },
        context: {
          installation: {
            id: 'installation-id',
          },
          organization: {
            id: 'org-123',
          },
          user: {
            id: 'user-id',
          },
          client: {
            views: {
              update: vi.fn(),
            },
          },
        },
      };

      const mockSubmissions = [
        { field: { id: 'title', name: 'Title', type: 'text' }, value: '' }, // Empty title
        {
          field: { id: 'description', name: 'Description', type: 'text' },
          value: 'Test Description',
        },
      ];

      const validationErrors = ['Title is required'];

      (mockFormSubmissionService.processSubmission as Mock).mockReturnValue(
        mockSubmissions,
      );
      (mockFormSubmissionService.validateSubmission as Mock).mockReturnValue(
        validationErrors,
      );

      (mockFormBuilderService.getFormById as Mock).mockResolvedValue({
        fields: [
          { id: 'title', name: 'Title', type: 'text' },
          { id: 'description', name: 'Description', type: 'text' },
        ],
      });

      const result = await handler.handle(
        args as unknown as DecoratedSlackViewMiddlewareArgs,
      );

      expect(mockFormSubmissionService.processSubmission).toHaveBeenCalled();
      expect(mockFormSubmissionService.validateSubmission).toHaveBeenCalled();
      expect(mockTicketCreationHelper.createTicketWithMetadata).not.toHaveBeenCalled();
      expect(result).toEqual({
        response_action: 'errors',
        errors: expect.any(Object),
      });
    });

    it('should handle errors during ticket creation', async () => {
      const viewPayload = {
        callback_id: 'form_submission_modal',
      };

      const args = {
        ack: vi.fn(),
        respond: vi.fn(),
        say: vi.fn(),
        action: viewPayload,
        payload: viewPayload,
        body: {
          view: {
            private_metadata: JSON.stringify({
              formId: 'form-123',
              teamId: 'team-123',
              channelId: 'C12345',
              fields: [
                { id: 'title', name: 'Title', type: 'text' },
                { id: 'description', name: 'Description', type: 'text' },
              ],
            }),
            state: {
              values: {},
            },
          },
        },
        context: {
          installation: {
            id: 'installation-id',
          },
          organization: {
            id: 'org-123',
          },
          user: {
            id: 'user-id',
          },
          client: {
            views: {
              update: vi.fn(),
            },
          },
        },
      };

      const mockSubmissions = [
        { field: { id: 'title', name: 'Title', type: 'text' }, value: 'Test Ticket' },
        {
          field: { id: 'description', name: 'Description', type: 'text' },
          value: 'Test Description',
        },
        {
          field: { id: 'requestor', name: 'Requestor', type: 'text' },
          value: '<EMAIL>',
        },
      ];

      (mockFormSubmissionService.processSubmission as Mock).mockReturnValue(
        mockSubmissions,
      );
      (mockFormSubmissionService.validateSubmission as Mock).mockReturnValue(
        [],
      );
      (mockFormSubmissionService.toKeyValueName as Mock).mockReturnValue({
        title: 'Test Ticket',
        description: 'Test Description',
        requestorEmail: '<EMAIL>',
      });
      (mockFormBuilderService.getFormById as Mock).mockResolvedValue({
        fields: [
          { id: 'title', name: 'Title', type: 'text' },
          { id: 'description', name: 'Description', type: 'text' },
          { id: 'priority', name: 'Priority', type: 'select' },
          { id: 'requestor', name: 'Requestor', type: 'text' },
          { id: 'custom_field1', name: 'Custom Field 1', type: 'text' },
        ],
      });
      (mockTicketCreationHelper.createTicketWithMetadata as Mock).mockRejectedValue(
        new Error('API error'),
      );

      const result = await handler.handle(
        args as unknown as DecoratedSlackViewMiddlewareArgs,
      );

      expect(mockFormSubmissionService.processSubmission).toHaveBeenCalled();
      expect(mockFormSubmissionService.validateSubmission).toHaveBeenCalled();
      expect(mockTicketCreationHelper.createTicketWithMetadata).toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalled();
      expect(result).toEqual({
        response_action: 'errors',
        errors: {
          block_0: 'API error',
        },
      });
    });

    it('should handle unexpected errors during form processing', async () => {
      const viewPayload = {
        callback_id: 'form_submission_modal',
      };

      const args = {
        ack: vi.fn(),
        respond: vi.fn(),
        say: vi.fn(),
        action: viewPayload,
        payload: viewPayload,
        body: {
          view: {
            private_metadata: JSON.stringify({
              formId: 'form-123',
              teamId: 'team-123',
              channelId: 'C12345',
              fields: [{ id: 'title', name: 'Title', type: 'text' }],
            }),
            state: {
              values: {},
            },
          },
        },
        context: {
          installation: {
            id: 'installation-id',
          },
          organization: {
            id: 'org-123',
          },
          user: {
            id: 'user-id',
          },
          client: {
            views: {
              update: vi.fn(),
            },
          },
        },
      };

      (mockFormSubmissionService.processSubmission as Mock).mockImplementation(
        () => {
          throw new Error('Unexpected error');
        },
      );

      (mockFormBuilderService.getFormById as Mock).mockResolvedValue({
        fields: [{ id: 'title', name: 'Title', type: 'text' }],
      });

      await expect(
        handler.handle(args as unknown as DecoratedSlackViewMiddlewareArgs),
      ).rejects.toThrow('Unexpected error');
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
});
