import { beforeEach, describe, expect, it, vi } from 'vitest';
import { AiService } from '../../../../src/ai/ai.service';
import { Installations } from '../../../../src/database/entities';
import { Organizations } from '../../../../src/database/entities/organizations/organizations.entity';
import { PlatformTeams } from '../../../../src/database/entities/teams/teams.entity';
import { AiTicketGeneratorService } from '../../../../src/slack/services/ai-ticket-generator.service';
import { ILogger } from '../../../../src/utils';

describe('AiTicketGeneratorService', () => {
  let service: AiTicketGeneratorService;
  let mockLogger: ILogger;
  let mockAiService: AiService;
  let mockInstallation: Installations;
  let mockPlatformTeam: PlatformTeams;
  let mockOrganization: Organizations;

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    } as unknown as ILogger;

    mockAiService = {
      setActiveProvider: vi.fn(),
      setActiveModel: vi.fn(),
      loadTeamPrompts: vi.fn(),
      generateTicketTitle: vi.fn(),
      generateTicketDescription: vi.fn(),
    } as unknown as AiService;

    mockOrganization = {
      id: 'org-1',
      uid: 'org-uid-1',
    } as Organizations;

    mockInstallation = {
      id: 'installation-1',
      organization: mockOrganization,
    } as Installations;

    mockPlatformTeam = {
      id: 'team-1',
      name: 'Test Team',
    } as PlatformTeams;

    service = new AiTicketGeneratorService(mockLogger, mockAiService);
  });

  describe('setAiProviderAndModel', () => {
    it('should set Claude provider for Claude models', () => {
      // Execute
      service.setAiProviderAndModel('claude-3-7-sonnet-20250219');

      // Verify
      expect(mockAiService.setActiveProvider).toHaveBeenCalledWith('claude');
      expect(mockAiService.setActiveModel).toHaveBeenCalledWith(
        'claude-3-7-sonnet-20250219',
      );
    });

    it('should set Claude provider for Haiku model', () => {
      // Execute
      service.setAiProviderAndModel('claude-3-5-haiku-20241022');

      // Verify
      expect(mockAiService.setActiveProvider).toHaveBeenCalledWith('claude');
      expect(mockAiService.setActiveModel).toHaveBeenCalledWith(
        'claude-3-5-haiku-20241022',
      );
    });

    it('should set OpenAI provider for GPT models', () => {
      // Execute
      service.setAiProviderAndModel('gpt-4o');

      // Verify
      expect(mockAiService.setActiveProvider).toHaveBeenCalledWith('openai');
      expect(mockAiService.setActiveModel).toHaveBeenCalledWith('gpt-4o');
    });

    it('should set OpenAI provider for o3-mini model', () => {
      // Execute
      service.setAiProviderAndModel('o3-mini-2025-01-31');

      // Verify
      expect(mockAiService.setActiveProvider).toHaveBeenCalledWith('openai');
      expect(mockAiService.setActiveModel).toHaveBeenCalledWith(
        'o3-mini-2025-01-31',
      );
    });

    it('should default to OpenAI o3-mini for unknown models', () => {
      // Execute
      service.setAiProviderAndModel('unknown-model');

      // Verify
      expect(mockAiService.setActiveProvider).toHaveBeenCalledWith('openai');
      expect(mockAiService.setActiveModel).toHaveBeenCalledWith(
        'o3-mini-2025-01-31',
      );
    });
  });

  describe('generateTicketContent', () => {
    const messageContent = 'User is having trouble with login functionality';
    const aiModel = 'gpt-4o';
    const fallbackTitle = 'Fallback Title';
    const fallbackDescription = 'Fallback Description';

    it('should generate ticket content successfully', async () => {
      // Setup
      const expectedTitle = 'Login Issue';
      const expectedDescription = 'User experiencing login problems';

      mockAiService.loadTeamPrompts = vi.fn().mockResolvedValue(undefined);
      mockAiService.generateTicketTitle = vi
        .fn()
        .mockResolvedValue(expectedTitle);
      mockAiService.generateTicketDescription = vi
        .fn()
        .mockResolvedValue(expectedDescription);

      // Execute
      const result = await service.generateTicketContent(
        messageContent,
        mockPlatformTeam,
        mockInstallation,
        aiModel,
        fallbackTitle,
        fallbackDescription,
      );

      // Verify
      expect(result).toEqual({
        title: expectedTitle,
        description: expectedDescription,
      });
      expect(mockLogger.debug).toHaveBeenCalledWith(
        `Using AI model ${aiModel} for ticket generation`,
      );
      expect(mockAiService.setActiveProvider).toHaveBeenCalledWith('openai');
      expect(mockAiService.setActiveModel).toHaveBeenCalledWith(aiModel);
      expect(mockAiService.loadTeamPrompts).toHaveBeenCalledWith(
        mockPlatformTeam.id,
        mockInstallation.id,
        mockOrganization.id,
      );
    });

    it('should use fallback title when AI title generation fails', async () => {
      // Setup
      const expectedDescription = 'AI generated description';

      mockAiService.loadTeamPrompts = vi.fn().mockResolvedValue(undefined);
      mockAiService.generateTicketTitle = vi
        .fn()
        .mockRejectedValue(new Error('Title generation failed'));
      mockAiService.generateTicketDescription = vi
        .fn()
        .mockResolvedValue(expectedDescription);

      // Execute
      const result = await service.generateTicketContent(
        messageContent,
        mockPlatformTeam,
        mockInstallation,
        aiModel,
        fallbackTitle,
        fallbackDescription,
      );

      // Verify
      expect(result).toEqual({
        title: fallbackTitle,
        description: expectedDescription,
      });
    });

    it('should use fallback description when AI description generation fails', async () => {
      // Setup
      const expectedTitle = 'AI generated title';

      mockAiService.loadTeamPrompts = vi.fn().mockResolvedValue(undefined);
      mockAiService.generateTicketTitle = vi
        .fn()
        .mockResolvedValue(expectedTitle);
      mockAiService.generateTicketDescription = vi
        .fn()
        .mockRejectedValue(new Error('Description generation failed'));

      // Execute
      const result = await service.generateTicketContent(
        messageContent,
        mockPlatformTeam,
        mockInstallation,
        aiModel,
        fallbackTitle,
        fallbackDescription,
      );

      // Verify
      expect(result).toEqual({
        title: expectedTitle,
        description: fallbackDescription,
      });
    });

    it('should use fallback values when both AI generations fail', async () => {
      // Setup
      mockAiService.loadTeamPrompts = vi.fn().mockResolvedValue(undefined);
      mockAiService.generateTicketTitle = vi
        .fn()
        .mockRejectedValue(new Error('Title generation failed'));
      mockAiService.generateTicketDescription = vi
        .fn()
        .mockRejectedValue(new Error('Description generation failed'));

      // Execute
      const result = await service.generateTicketContent(
        messageContent,
        mockPlatformTeam,
        mockInstallation,
        aiModel,
        fallbackTitle,
        fallbackDescription,
      );

      // Verify
      expect(result).toEqual({
        title: fallbackTitle,
        description: fallbackDescription,
      });
    });

    it('should handle errors and return fallback values', async () => {
      // Setup
      mockAiService.loadTeamPrompts = vi
        .fn()
        .mockRejectedValue(new Error('Load prompts failed'));

      // Execute
      const result = await service.generateTicketContent(
        messageContent,
        mockPlatformTeam,
        mockInstallation,
        aiModel,
        fallbackTitle,
        fallbackDescription,
      );

      // Verify
      expect(result).toEqual({
        title: fallbackTitle,
        description: fallbackDescription,
      });
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error generating ticket content with AI'),
      );
    });
  });
});
