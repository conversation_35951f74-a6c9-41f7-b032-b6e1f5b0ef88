import { Cache } from 'cache-manager';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { AnnotatorApiProvider } from '../../../../src/external/provider/annotator-api.provider';
import { ThenaPlatformApiProvider } from '../../../../src/external/provider/thena-platform-api.provider';
import { TriageCategory } from '../../../../src/slack/constants/triage-fields.constants';
import { TriageFieldsService } from '../../../../src/slack/services/triage-fields.service';
import { ILogger } from '../../../../src/utils/logger/logger.interface';
import { mockSentryService } from '../../../mocks/sentry.mock';

describe('TriageFieldsService', () => {
  let service: TriageFieldsService;
  let mockLogger: ILogger;
  let mockCacheManager: Cache;
  let mockAnnotatorApiProvider: AnnotatorApiProvider;
  let mockPlatformApiProvider: ThenaPlatformApiProvider;

  beforeEach(() => {
    vi.resetAllMocks();

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockCacheManager = {
      get: vi.fn(),
      set: vi.fn(),
      del: vi.fn(),
      reset: vi.fn(),
      wrap: vi.fn(),
    } as unknown as Cache;

    mockAnnotatorApiProvider = {
      getFieldTypeMappings: vi.fn(),
    } as unknown as AnnotatorApiProvider;

    mockPlatformApiProvider = {
      getMapping: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    service = new TriageFieldsService(
      mockLogger,
      mockCacheManager,
      mockAnnotatorApiProvider,
      mockSentryService,
    );
  });

  describe('getFieldMappings', () => {
    it('should fetch field mappings from API for ticket category', async () => {
      const category = TriageCategory.TICKET;
      const installation = { id: 'installation-id' } as any;
      const baseField = 'ticket';
      const expectedMappings = {
        title: 'string',
        description: 'string',
        priority: 'string',
      };

      (mockAnnotatorApiProvider.getFieldTypeMappings as Mock).mockResolvedValue(
        expectedMappings,
      );

      const result = await service.getFieldMappings(
        category,
        installation,
        baseField,
      );

      expect(result).toEqual(expectedMappings);
      expect(
        mockAnnotatorApiProvider.getFieldTypeMappings,
      ).toHaveBeenCalledWith(installation, 'Ticket', baseField);
    });

    it('should fetch field mappings from API for account category', async () => {
      const category = TriageCategory.ACCOUNT;
      const installation = { id: 'installation-id' } as any;
      const baseField = 'account';
      const expectedMappings = {
        name: 'string',
        tier: 'string',
        region: 'string',
      };

      (mockAnnotatorApiProvider.getFieldTypeMappings as Mock).mockResolvedValue(
        expectedMappings,
      );

      const result = await service.getFieldMappings(
        category,
        installation,
        baseField,
      );

      expect(result).toEqual(expectedMappings);
      expect(
        mockAnnotatorApiProvider.getFieldTypeMappings,
      ).toHaveBeenCalledWith(installation, 'Account', baseField);
    });

    it('should return fallback mappings for channel category', async () => {
      const category = TriageCategory.CHANNEL;
      const installation = { id: 'installation-id' } as any;
      const baseField = 'channel';

      const result = await service.getFieldMappings(
        category,
        installation,
        baseField,
      );

      expect(result).toEqual({
        name: 'string',
        purpose: 'string',
        topic: 'string',
        memberCount: 'number',
        isPrivate: 'boolean',
        isShared: 'boolean',
        tags: 'array',
      });
      expect(
        mockAnnotatorApiProvider.getFieldTypeMappings,
      ).not.toHaveBeenCalled();
    });

    it('should return fallback mappings when API call fails', async () => {
      const category = TriageCategory.TICKET;
      const installation = { id: 'installation-id' } as any;
      const baseField = 'ticket';
      const error = new Error('API error');

      (mockAnnotatorApiProvider.getFieldTypeMappings as Mock).mockRejectedValue(
        error,
      );

      const result = await service.getFieldMappings(
        category,
        installation,
        baseField,
      );

      expect(result).toEqual({
        title: 'string',
        description: 'string',
        priority: 'string',
        status: 'string',
        requestorEmail: 'string',
        assignee: 'string',
        reporter: 'string',
        labels: 'array',
        type: 'string',
      });
      expect(mockAnnotatorApiProvider.getFieldTypeMappings).toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should return empty object for unknown category', async () => {
      const category = 'UNKNOWN' as TriageCategory;
      const installation = { id: 'installation-id' } as any;
      const baseField = 'unknown';

      const result = await service.getFieldMappings(
        category,
        installation,
        baseField,
      );

      expect(result).toEqual({});
      expect(
        mockAnnotatorApiProvider.getFieldTypeMappings,
      ).not.toHaveBeenCalled();
    });
  });
});
