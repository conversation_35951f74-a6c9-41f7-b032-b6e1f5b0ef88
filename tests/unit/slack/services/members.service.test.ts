import { BadRequestException } from '@nestjs/common';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { BotCtx } from '../../../../src/auth/interfaces';
import { Users } from '../../../../src/database/entities';
import { Installations } from '../../../../src/database/entities/installations/installations.entity';
import { Organizations } from '../../../../src/database/entities/organizations/organizations.entity';
import { UsersRepository } from '../../../../src/database/entities/users/repositories/users.repository';
import { CommonQueryParamsToFetchEntityData } from '../../../../src/slack/query-params';
import { SlackMembersService } from '../../../../src/slack/services/members.service';
import { ILogger } from '../../../../src/utils';

describe('SlackMembersService', () => {
  let service: SlackMembersService;
  let mockLogger: ILogger;
  let mockUsersRepository: UsersRepository;
  let mockBotCtx: BotCtx;
  let mockOrganization: Organizations;
  let mockInstallations: Installations[];

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    } as unknown as ILogger;

    mockUsersRepository = {
      findAll: vi.fn(),
    } as unknown as UsersRepository;

    mockOrganization = {
      id: 'org-1',
      uid: 'org-uid-1',
    } as Organizations;

    mockInstallations = [
      {
        id: 'installation-1',
        organization: mockOrganization,
      } as Installations,
      {
        id: 'installation-2',
        organization: mockOrganization,
      } as Installations,
    ];

    mockBotCtx = {
      organization: mockOrganization,
      installations: mockInstallations,
    } as BotCtx;

    service = new SlackMembersService(mockLogger, mockUsersRepository);
  });

  describe('getAllMembers', () => {
    let mockUsers: Users[];

    beforeEach(() => {
      mockUsers = [
        {
          id: 'user-1',
          slackId: 'U12345',
          isBot: false,
          slackDeleted: false,
          organization: mockOrganization,
          installation: mockInstallations[0],
        } as Users,
        {
          id: 'user-2',
          slackId: 'U67890',
          isBot: false,
          slackDeleted: false,
          organization: mockOrganization,
          installation: mockInstallations[1],
        } as Users,
      ];
    });

    it('should return all members with default pagination', async () => {
      // Setup
      const query: CommonQueryParamsToFetchEntityData = {};
      mockUsersRepository.findAll = vi.fn().mockResolvedValue(mockUsers);

      // Execute
      const result = await service.getAllMembers(mockBotCtx, query);

      // Verify
      expect(result).toEqual({
        data: mockUsers,
        meta: {
          page: 1,
          limit: 10,
          total: 2,
          totalPages: 1,
        },
      });

      expect(mockUsersRepository.findAll).toHaveBeenCalledWith({
        where: {
          isBot: false,
          slackId: expect.objectContaining({
            _type: 'not',
            _value: 'USLACKBOT',
          }),
          slackDeleted: false,
          organization: { id: mockOrganization.id },
          installation: {
            id: expect.objectContaining({
              _type: 'in',
              _value: ['installation-1', 'installation-2'],
            }),
          },
        },
      });
    });

    it('should return members with custom pagination', async () => {
      // Setup
      const query: CommonQueryParamsToFetchEntityData = {
        page: 2,
        limit: 5,
      };
      mockUsersRepository.findAll = vi.fn().mockResolvedValue(mockUsers);

      // Execute
      const result = await service.getAllMembers(mockBotCtx, query);

      // Verify
      expect(result).toEqual({
        data: mockUsers,
        meta: {
          page: 2,
          limit: 5,
          total: 2,
          totalPages: 1,
        },
      });
    });

    it('should calculate correct total pages', async () => {
      // Setup
      const query: CommonQueryParamsToFetchEntityData = {
        page: 1,
        limit: 1,
      };
      mockUsersRepository.findAll = vi.fn().mockResolvedValue(mockUsers);

      // Execute
      const result = await service.getAllMembers(mockBotCtx, query);

      // Verify
      expect(result.meta.totalPages).toBe(2); // 2 users with limit 1 = 2 pages
    });

    it('should throw BadRequestException when limit exceeds 100', async () => {
      // Setup
      const query: CommonQueryParamsToFetchEntityData = {
        limit: 101,
      };

      // Execute & Verify
      await expect(service.getAllMembers(mockBotCtx, query)).rejects.toThrow(
        new BadRequestException('Limit cannot exceed 100'),
      );
    });

    it('should throw BadRequestException when page is less than 1', async () => {
      // Setup
      const query: CommonQueryParamsToFetchEntityData = {
        page: 0,
      };

      // Execute & Verify
      await expect(service.getAllMembers(mockBotCtx, query)).rejects.toThrow(
        new BadRequestException('Page must be greater than 0'),
      );
    });

    it('should handle empty user list', async () => {
      // Setup
      const query: CommonQueryParamsToFetchEntityData = {};
      mockUsersRepository.findAll = vi.fn().mockResolvedValue([]);

      // Execute
      const result = await service.getAllMembers(mockBotCtx, query);

      // Verify
      expect(result).toEqual({
        data: [],
        meta: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
        },
      });
    });

    it('should filter out bots and deleted users', async () => {
      // Setup
      const query: CommonQueryParamsToFetchEntityData = {};
      mockUsersRepository.findAll = vi.fn().mockResolvedValue(mockUsers);

      // Execute
      await service.getAllMembers(mockBotCtx, query);

      // Verify
      expect(mockUsersRepository.findAll).toHaveBeenCalledWith({
        where: expect.objectContaining({
          isBot: false,
          slackDeleted: false,
          slackId: expect.objectContaining({
            _type: 'not',
            _value: 'USLACKBOT',
          }),
        }),
      });
    });

    it('should filter by organization and installations', async () => {
      // Setup
      const query: CommonQueryParamsToFetchEntityData = {};
      mockUsersRepository.findAll = vi.fn().mockResolvedValue(mockUsers);

      // Execute
      await service.getAllMembers(mockBotCtx, query);

      // Verify
      expect(mockUsersRepository.findAll).toHaveBeenCalledWith({
        where: expect.objectContaining({
          organization: { id: mockOrganization.id },
          installation: {
            id: expect.objectContaining({
              _type: 'in',
              _value: ['installation-1', 'installation-2'],
            }),
          },
        }),
      });
    });

    it('should handle repository errors', async () => {
      // Setup
      const query: CommonQueryParamsToFetchEntityData = {};
      const error = new Error('Database connection failed');
      mockUsersRepository.findAll = vi.fn().mockRejectedValue(error);

      // Execute & Verify
      await expect(service.getAllMembers(mockBotCtx, query)).rejects.toThrow(
        error,
      );
    });
  });
});
