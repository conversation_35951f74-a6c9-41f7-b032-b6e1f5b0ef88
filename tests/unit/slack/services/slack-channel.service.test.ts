import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { BotCtx } from '../../../../src/auth/interfaces';
import { CustomerContacts, Users } from '../../../../src/database/entities';
import { ChannelsRepository } from '../../../../src/database/entities/channels/repositories';
import { TeamsRepository } from '../../../../src/database/entities/teams';
import { ThenaPlatformApiProvider } from '../../../../src/external/provider/thena-platform-api.provider';
import { SlackMessageCore } from '../../../../src/slack/core/messages/slack-message.core';
import { CoreTriageService } from '../../../../src/slack/core/messages/triage.core';
import { TriageChannelConfigHandler } from '../../../../src/slack/handlers/slack-views/triage-channel-config.handler';
import { SlackWebAPIService } from '../../../../src/slack/providers/slack-apis/slack-apis.service';
import {
  GetAllSlackChannelsQueryParams,
  SlackChannelAvailabilityTypes,
} from '../../../../src/slack/query-params';
import { SlackChannelService } from '../../../../src/slack/services/slack-channel.service';
import { ILogger } from '../../../../src/utils/logger/logger.interface';

describe('SlackChannelService', () => {
  let service: SlackChannelService;
  let mockLogger: ILogger;
  let mockTriageChannelConfigHandler: TriageChannelConfigHandler;
  let mockSlackMessageCore: SlackMessageCore;
  let mockCoreTriageService: CoreTriageService;
  let mockCustomerContactsRepository: Repository<CustomerContacts>;
  let mockUsersRepository: Repository<Users>;
  let mockChannelsRepository: ChannelsRepository;
  let mockSlackWebAPIService: SlackWebAPIService;
  let mockPlatformService: ThenaPlatformApiProvider;
  let mockPlatformTeamRepository: TeamsRepository;

  beforeEach(() => {
    vi.resetAllMocks();

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockTriageChannelConfigHandler = {
      handleViaApi: vi.fn(),
    } as unknown as TriageChannelConfigHandler;

    mockSlackMessageCore = {
      getSlackMessageByPlatformTicketId: vi.fn(),
    } as unknown as SlackMessageCore;

    mockCoreTriageService = {
      sendTriageMessageToChannel: vi.fn(),
    } as unknown as CoreTriageService;

    mockCustomerContactsRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<CustomerContacts>;

    mockUsersRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<Users>;

    mockChannelsRepository = {
      findByCondition: vi.fn(),
      findWithRelations: vi.fn(),
      findAll: vi.fn(),
      count: vi.fn(),
      createQueryBuilder: vi.fn(),
    } as unknown as ChannelsRepository;

    mockSlackWebAPIService = {
      getTeamInfo: vi.fn(),
    } as unknown as SlackWebAPIService;

    mockPlatformService = {
      getTicket: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    mockPlatformTeamRepository = {
      findAll: vi.fn(),
    } as unknown as TeamsRepository;

    service = new SlackChannelService(
      mockLogger,
      mockTriageChannelConfigHandler,
      mockSlackMessageCore,
      mockCoreTriageService,
      mockCustomerContactsRepository,
      mockUsersRepository,
      mockChannelsRepository,
      mockCustomerContactsRepository, // Duplicate in constructor
      mockSlackWebAPIService,
      mockPlatformService,
      mockPlatformTeamRepository,
    );
  });

  describe('configureTriageChannel', () => {
    it('should call triageChannelConfigHandler.handleViaApi with correct parameters', async () => {
      const data = {
        channelToTriage: 'C12345',
        triageChannel: 'C67890',
      };
      const botCtx = {
        installation: {
          id: 'installation-id',
          botToken: 'mock-bot-token',
          organization: {
            id: 'org-id',
          },
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      await service.configureTriageChannel(data, botCtx);

      expect(mockTriageChannelConfigHandler.handleViaApi).toHaveBeenCalledWith(
        data,
        botCtx,
      );
    });
  });

  describe('createTriageThread', () => {
    it('should create a triage thread successfully', async () => {
      const data = {
        platformTicketId: 'ticket-123',
        message: 'Test message',
        platformCommentId: 'comment-123',
        commentAsEmail: '<EMAIL>',
        commentAsName: 'Test User',
        commentAsAvatar: 'avatar-url',
      };
      const channelId = 'C12345';
      const botCtx = {
        installation: {
          id: 'installation-id',
          botToken: 'mock-bot-token',
          organization: {
            id: 'org-id',
          },
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'triage-channel',
      };

      const mockSlackMessage = {
        id: 'message-id',
        platformTicketId: 'ticket-123',
      };

      const mockTicketData = {
        id: 'ticket-123',
        title: 'Test Ticket',
        requestorEmail: '<EMAIL>',
      };

      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
      };

      const mockCustomerContact = {
        id: 'contact-id',
        slackId: 'U67890',
        slackProfileEmail: '<EMAIL>',
        userDump: {
          team_id: 'T67890',
        },
      };

      const mockTeamInfoResponse = {
        ok: true,
        team: {
          id: 'T67890',
          name: 'Customer Team',
          icon: {
            image_132: 'icon-url',
          },
        },
      };

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(
        mockChannel,
      );
      (
        mockSlackMessageCore.getSlackMessageByPlatformTicketId as Mock
      ).mockResolvedValue(mockSlackMessage);
      (mockPlatformService.getTicket as Mock).mockResolvedValue(mockTicketData);
      (mockUsersRepository.findOne as Mock).mockResolvedValue(mockUser);
      (mockCustomerContactsRepository.findOne as Mock).mockResolvedValue(
        mockCustomerContact,
      );
      (mockSlackWebAPIService.getTeamInfo as Mock).mockResolvedValue(
        mockTeamInfoResponse,
      );

      await service.createTriageThread(data, channelId, botCtx);

      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          channelId,
          installation: { id: 'installation-id' },
          organization: { id: 'org-id' },
        },
      });
      expect(
        mockSlackMessageCore.getSlackMessageByPlatformTicketId,
      ).toHaveBeenCalledWith(botCtx.installation, 'ticket-123', {
        createIndependentIfNotFound: true,
      });
      expect(mockPlatformService.getTicket).toHaveBeenCalledWith(
        botCtx.installation,
        'ticket-123',
      );
      expect(mockUsersRepository.findOne).toHaveBeenCalledWith({
        where: {
          slackProfileEmail: '<EMAIL>',
          installation: { id: 'installation-id' },
          organization: { id: 'org-id' },
        },
      });
      expect(mockCustomerContactsRepository.findOne).toHaveBeenCalledWith({
        where: {
          slackProfileEmail: '<EMAIL>',
          installation: { id: 'installation-id' },
          organization: { id: 'org-id' },
        },
      });
      expect(mockSlackWebAPIService.getTeamInfo).toHaveBeenCalledWith(
        botCtx.installation.botToken,
        { team: 'T67890' },
      );
      expect(
        mockCoreTriageService.sendTriageMessageToChannel,
      ).toHaveBeenCalledWith(
        botCtx.installation,
        {
          channel: mockChannel,
          ticket: mockTicketData,
          slackMessage: mockSlackMessage,
          returnExisting: false,
          message: 'Test message',
          slackTeamName: 'Customer Team',
          slackTeamIconUrl: 'icon-url',
          platformCommentId: 'comment-123',
          commentAs: mockUser,
          commentAsName: 'Test User',
          commentAsAvatar: 'avatar-url',
          commentAsEmail: '<EMAIL>',
        },
        true,
      );
    });

    it('should throw NotFoundException when triage channel is not found', async () => {
      const data = {
        platformTicketId: 'ticket-123',
        message: 'Test message',
        platformCommentId: 'comment-123',
      };
      const channelId = 'C12345';
      const botCtx = {
        installation: {
          id: 'installation-id',
          botToken: 'mock-bot-token',
          organization: {
            id: 'org-id',
          },
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(null);

      await expect(
        service.createTriageThread(data, channelId, botCtx),
      ).rejects.toThrow(NotFoundException);
      expect(mockChannelsRepository.findByCondition).toHaveBeenCalled();
      expect(
        mockSlackMessageCore.getSlackMessageByPlatformTicketId,
      ).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException when platform ticket is not found', async () => {
      const data = {
        platformTicketId: 'ticket-123',
        message: 'Test message',
        platformCommentId: 'comment-123',
      };
      const channelId = 'C12345';
      const botCtx = {
        installation: {
          id: 'installation-id',
          botToken: 'mock-bot-token',
          organization: {
            id: 'org-id',
          },
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'triage-channel',
      };

      const mockSlackMessage = {
        id: 'message-id',
        platformTicketId: 'ticket-123',
      };

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(
        mockChannel,
      );
      (
        mockSlackMessageCore.getSlackMessageByPlatformTicketId as Mock
      ).mockResolvedValue(mockSlackMessage);
      (mockPlatformService.getTicket as Mock).mockResolvedValue(null);

      await expect(
        service.createTriageThread(data, channelId, botCtx),
      ).rejects.toThrow(NotFoundException);
      expect(mockChannelsRepository.findByCondition).toHaveBeenCalled();
      expect(
        mockSlackMessageCore.getSlackMessageByPlatformTicketId,
      ).toHaveBeenCalled();
      expect(mockPlatformService.getTicket).toHaveBeenCalled();
    });

    it('should handle errors when fetching team info', async () => {
      const data = {
        platformTicketId: 'ticket-123',
        message: 'Test message',
        platformCommentId: 'comment-123',
      };
      const channelId = 'C12345';
      const botCtx = {
        installation: {
          id: 'installation-id',
          botToken: 'mock-bot-token',
          organization: {
            id: 'org-id',
          },
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'triage-channel',
      };

      const mockSlackMessage = {
        id: 'message-id',
        platformTicketId: 'ticket-123',
      };

      const mockTicketData = {
        id: 'ticket-123',
        title: 'Test Ticket',
        requestorEmail: '<EMAIL>',
      };

      const mockCustomerContact = {
        id: 'contact-id',
        slackId: 'U67890',
        slackProfileEmail: '<EMAIL>',
        userDump: {
          team_id: 'T67890',
        },
      };

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(
        mockChannel,
      );
      (
        mockSlackMessageCore.getSlackMessageByPlatformTicketId as Mock
      ).mockResolvedValue(mockSlackMessage);
      (mockPlatformService.getTicket as Mock).mockResolvedValue(mockTicketData);
      (mockCustomerContactsRepository.findOne as Mock).mockResolvedValue(
        mockCustomerContact,
      );
      (mockSlackWebAPIService.getTeamInfo as Mock).mockRejectedValue(
        new Error('API error'),
      );

      await service.createTriageThread(data, channelId, botCtx);

      expect(mockLogger.error).toHaveBeenCalled();
      expect(
        mockCoreTriageService.sendTriageMessageToChannel,
      ).toHaveBeenCalledWith(
        botCtx.installation,
        expect.objectContaining({
          channel: mockChannel,
          ticket: mockTicketData,
          slackMessage: mockSlackMessage,
          slackTeamName: null,
          slackTeamIconUrl: null,
        }),
        true,
      );
    });
  });

  describe('getAllSlackChannelsByTeamId', () => {
    it.todo(
      'should return channels for a team with correct pagination',
      async () => {
        const botCtx = {
          organization: {
            id: 'org-id',
          },
        } as unknown as BotCtx;

        const teamId = 'team-123';

        const query: GetAllSlackChannelsQueryParams = {
          page: 1,
          limit: 10,
          availabilityType: SlackChannelAvailabilityTypes.configured,
          searchQuery: 'test',
          showPrivateChannels: false,
          showExternalChannels: false,
        };

        const mockPlatformTeams = [
          {
            uid: 'team-123',
            installation: {
              id: 'installation-id',
            },
          },
        ];

        const mockQueryBuilder = {
          leftJoinAndSelect: vi.fn().mockReturnThis(),
          where: vi.fn().mockReturnThis(),
          orderBy: vi.fn().mockReturnThis(),
          skip: vi.fn().mockReturnThis(),
          take: vi.fn().mockReturnThis(),
          getMany: vi.fn().mockResolvedValue([
            {
              id: 'channel-id',
              channelId: 'C12345',
              name: 'test-channel',
              installation: {
                id: 'installation-id',
                teamId: 'T12345',
                teamName: 'Team Name',
              },
              platformTeamsToChannelMappings: [
                {
                  relationshipType: 'PRIMARY',
                  platformTeam: {
                    id: 'platform-team-id',
                    uid: 'team-123',
                    installedBy: 'U12345',
                    installation: {
                      id: 'installation-id',
                      teamId: 'T12345',
                      teamName: 'Team Name',
                    },
                  },
                },
              ],
            },
          ]),
        };

        (mockPlatformTeamRepository.findAll as Mock).mockResolvedValue(
          mockPlatformTeams,
        );
        (mockChannelsRepository.createQueryBuilder as Mock).mockReturnValue(
          mockQueryBuilder,
        );
        (mockChannelsRepository.count as Mock).mockResolvedValue(1);

        const result = await service.getAllSlackChannelsByTeamId(
          botCtx,
          teamId,
          query,
        );

        expect(mockPlatformTeamRepository.findAll).toHaveBeenCalledWith({
          where: { uid: teamId, organization: { id: 'org-id' } },
          relations: { installation: true },
        });
        expect(mockChannelsRepository.createQueryBuilder).toHaveBeenCalled();
        expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledTimes(4);
        expect(mockQueryBuilder.where).toHaveBeenCalled();
        expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
          'channel.createdAt',
          'DESC',
        );
        expect(mockQueryBuilder.skip).toHaveBeenCalledWith(0);
        expect(mockQueryBuilder.take).toHaveBeenCalledWith(10);
        expect(result).toEqual({
          data: [
            expect.objectContaining({
              id: 'channel-id',
              channelId: 'C12345',
              name: 'test-channel',
              teamId: 'T12345',
              teams: [
                expect.objectContaining({
                  uid: 'team-123',
                  relationshipType: 'PRIMARY',
                }),
              ],
            }),
          ],
          meta: {
            page: 1,
            limit: 10,
            total: 1,
            totalPages: 1,
          },
        });
      },
    );

    it.todo(
      'should throw BadRequestException when limit exceeds 100',
      async () => {
        const botCtx = {
          organization: {
            id: 'org-id',
          },
        } as unknown as BotCtx;

        const teamId = 'team-123';

        const query: GetAllSlackChannelsQueryParams = {
          page: 1,
          limit: 101,
        };

        // Mock the platform team repository to return data before validation happens
        const mockPlatformTeams = [
          {
            uid: 'team-123',
            installation: {
              id: 'installation-id',
            },
          },
        ];
        (mockPlatformTeamRepository.findAll as Mock).mockResolvedValue(
          mockPlatformTeams,
        );

        // Create a mock query builder that can be properly chained
        const mockQueryBuilder = {
          leftJoinAndSelect: vi.fn().mockReturnThis(),
          where: vi.fn().mockReturnThis(),
          orderBy: vi.fn().mockReturnThis(),
          skip: vi.fn().mockReturnThis(),
          take: vi.fn().mockReturnThis(),
          getMany: vi.fn().mockResolvedValue([]),
        };

        (mockChannelsRepository.createQueryBuilder as Mock).mockReturnValue(
          mockQueryBuilder,
        );

        await expect(
          service.getAllSlackChannelsByTeamId(botCtx, teamId, query),
        ).rejects.toThrow(BadRequestException);
      },
    );

    it.todo(
      'should throw BadRequestException when page is less than 1',
      async () => {
        const botCtx = {
          organization: {
            id: 'org-id',
          },
        } as unknown as BotCtx;

        const teamId = 'team-123';

        const query: GetAllSlackChannelsQueryParams = {
          page: 0,
          limit: 10,
        };

        // Mock the platform team repository to return data before validation happens
        const mockPlatformTeams = [
          {
            uid: 'team-123',
            installation: {
              id: 'installation-id',
            },
          },
        ];
        (mockPlatformTeamRepository.findAll as Mock).mockResolvedValue(
          mockPlatformTeams,
        );

        // Create a mock query builder that can be properly chained
        const mockQueryBuilder = {
          leftJoinAndSelect: vi.fn().mockReturnThis(),
          where: vi.fn().mockReturnThis(),
          orderBy: vi.fn().mockReturnThis(),
          skip: vi.fn().mockReturnThis(),
          take: vi.fn().mockReturnThis(),
          getMany: vi.fn().mockResolvedValue([]),
        };

        (mockChannelsRepository.createQueryBuilder as Mock).mockReturnValue(
          mockQueryBuilder,
        );

        await expect(
          service.getAllSlackChannelsByTeamId(botCtx, teamId, query),
        ).rejects.toThrow(BadRequestException);
      },
    );
  });

  describe('getAllSlackChannels', () => {
    it.todo('should return channels with correct pagination', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const query: GetAllSlackChannelsQueryParams = {
        page: 1,
        limit: 10,
        forOrganization: true,
        availabilityType: SlackChannelAvailabilityTypes.available,
        searchQuery: 'test',
        showPrivateChannels: false,
        showExternalChannels: false,
        showBotAddedChannels: true,
      };

      const mockQueryBuilder = {
        leftJoinAndSelect: vi.fn().mockReturnThis(),
        where: vi.fn().mockReturnThis(),
        orderBy: vi.fn().mockReturnThis(),
        skip: vi.fn().mockReturnThis(),
        take: vi.fn().mockReturnThis(),
        getMany: vi.fn().mockResolvedValue([
          {
            id: 'channel-id',
            channelId: 'C12345',
            name: 'test-channel',
            installation: {
              id: 'installation-id',
              teamId: 'T12345',
              teamName: 'Team Name',
            },
            platformTeamsToChannelMappings: [
              {
                relationshipType: 'PRIMARY',
                platformTeam: {
                  id: 'platform-team-id',
                  uid: 'team-123',
                  installedBy: 'U12345',
                  installation: {
                    id: 'installation-id',
                    teamId: 'T12345',
                    teamName: 'Team Name',
                  },
                },
              },
            ],
          },
        ]),
      };

      (mockChannelsRepository.createQueryBuilder as Mock).mockReturnValue(
        mockQueryBuilder,
      );
      (mockChannelsRepository.count as Mock).mockResolvedValue(1);

      const result = await service.getAllSlackChannels(botCtx, query);

      expect(mockChannelsRepository.createQueryBuilder).toHaveBeenCalled();
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledTimes(4);
      expect(mockQueryBuilder.where).toHaveBeenCalled();
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        'channel.createdAt',
        'DESC',
      );
      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(0);
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(10);
      expect(result).toEqual({
        data: [
          expect.objectContaining({
            id: 'channel-id',
            channelId: 'C12345',
            name: 'test-channel',
            teamId: 'T12345',
            teams: [
              expect.objectContaining({
                uid: 'team-123',
                relationshipType: 'PRIMARY',
              }),
            ],
          }),
        ],
        meta: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1,
        },
      });
    });

    it('should throw BadRequestException when limit exceeds 100', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const query: GetAllSlackChannelsQueryParams = {
        page: 1,
        limit: 101,
      };

      await expect(service.getAllSlackChannels(botCtx, query)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw BadRequestException when page is less than 1', async () => {
      const botCtx = {
        installation: {
          id: 'installation-id',
        },
        organization: {
          id: 'org-id',
        },
      } as unknown as BotCtx;

      const query: GetAllSlackChannelsQueryParams = {
        page: 0,
        limit: 10,
      };

      await expect(service.getAllSlackChannels(botCtx, query)).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('getAllCustomersFromChannel', () => {
    it('should return all customers from a channel', async () => {
      const installation = {
        id: 'installation-id',
      } as any;

      const channel = {
        id: 'channel-id',
      } as any;

      const mockChannelWithCustomers = {
        id: 'channel-id',
        customerContacts: [
          {
            id: 'contact-1',
            slackId: 'U12345',
          },
          {
            id: 'contact-2',
            slackId: 'U67890',
          },
        ],
      };

      (mockChannelsRepository.findWithRelations as Mock).mockResolvedValue([
        mockChannelWithCustomers,
      ]);

      const result = await service.getAllCustomersFromChannel(
        installation,
        channel,
      );

      expect(mockChannelsRepository.findWithRelations).toHaveBeenCalledWith({
        where: { id: 'channel-id', installation: { id: 'installation-id' } },
        relations: ['customerContacts'],
      });
      expect(result).toEqual(mockChannelWithCustomers.customerContacts);
    });

    it('should return empty array when no customers are found', async () => {
      const installation = {
        id: 'installation-id',
      } as any;

      const channel = {
        id: 'channel-id',
      } as any;

      (mockChannelsRepository.findWithRelations as Mock).mockResolvedValue([]);

      const result = await service.getAllCustomersFromChannel(
        installation,
        channel,
      );

      expect(mockChannelsRepository.findWithRelations).toHaveBeenCalled();
      expect(result).toEqual([]);
    });
  });
});
