import { NotFoundException } from '@nestjs/common';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { BotCtx } from '../../../../src/auth/interfaces';
import { TransactionService } from '../../../../src/database/common';
import { SlackSubgroups } from '../../../../src/database/entities';
import { Installations } from '../../../../src/database/entities/installations/installations.entity';
import { SubGroupsMapsRepository } from '../../../../src/database/entities/mappings/repositories/sub-groups-maps.repository';
import { SubGroupsMaps } from '../../../../src/database/entities/mappings/sub-groups-maps.entity';
import { Organizations } from '../../../../src/database/entities/organizations/organizations.entity';
import { SlackSubgroupsRepository } from '../../../../src/database/entities/subgroups/repositories/subgroups.repository';
import { TeamsRepository } from '../../../../src/database/entities/teams';
import { PlatformTeams } from '../../../../src/database/entities/teams/teams.entity';
import {
  MapSubGroupToSubTeamDTO,
  UpdateSubGroupMappingDTO,
} from '../../../../src/slack/dtos/sub-groups.dto';
import { SearchQueryParams } from '../../../../src/slack/query-params';
import { SlackSubGroupsService } from '../../../../src/slack/services/slack-sub-groups.service';
import { ILogger } from '../../../../src/utils';

describe('SlackSubGroupsService', () => {
  let service: SlackSubGroupsService;
  let mockLogger: ILogger;
  let mockTransactionService: TransactionService;
  let mockTeamsRepository: TeamsRepository;
  let mockSubGroupsRepository: SlackSubgroupsRepository;
  let mockSubGroupMappingsRepository: SubGroupsMapsRepository;
  let mockBotCtx: BotCtx;
  let mockOrganization: Organizations;
  let mockInstallation: Installations;

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    } as unknown as ILogger;

    mockTransactionService = {
      runInTransaction: vi
        .fn()
        .mockImplementation((callback) => callback('txn')),
    } as unknown as TransactionService;

    mockTeamsRepository = {
      findByCondition: vi.fn(),
    } as unknown as TeamsRepository;

    mockSubGroupsRepository = {
      findAll: vi.fn(),
      findByCondition: vi.fn(),
    } as unknown as SlackSubgroupsRepository;

    mockSubGroupMappingsRepository = {
      findAll: vi.fn(),
      findByCondition: vi.fn(),
      saveWithTxn: vi.fn(),
      updateWithTxn: vi.fn(),
      removeWithTxn: vi.fn(),
    } as unknown as SubGroupsMapsRepository;

    mockOrganization = {
      id: 'org-1',
      uid: 'org-uid-1',
    } as Organizations;

    mockInstallation = {
      id: 'installation-1',
      organization: mockOrganization,
    } as Installations;

    mockBotCtx = {
      organization: mockOrganization,
      installation: mockInstallation,
      installations: [mockInstallation],
    } as BotCtx;

    service = new SlackSubGroupsService(
      mockLogger,
      mockTransactionService,
      mockTeamsRepository,
      mockSubGroupsRepository,
      mockSubGroupMappingsRepository,
    );
  });

  describe('getAllSubGroupsForWorkspace', () => {
    const mockSubGroups: SlackSubgroups[] = [
      {
        id: 'subgroup-1',
        slackGroupId: 'S12345',
        slackHandle: 'developers',
        description: 'Development team',
        usersCount: 5,
        installation: mockInstallation,
        organization: mockOrganization,
      } as SlackSubgroups,
    ];

    it('should return all sub groups without search query', async () => {
      // Setup
      const query: SearchQueryParams = {};
      mockSubGroupsRepository.findAll = vi
        .fn()
        .mockResolvedValue(mockSubGroups);

      // Execute
      const result = await service.getAllSubGroupsForWorkspace(
        query,
        mockBotCtx,
      );

      // Verify
      expect(result).toEqual(mockSubGroups);
      expect(mockSubGroupsRepository.findAll).toHaveBeenCalledWith({
        where: {
          installation: { id: mockInstallation.id },
          organization: { id: mockOrganization.id },
        },
      });
    });

    it('should return filtered sub groups with search query', async () => {
      // Setup
      const query: SearchQueryParams = { searchQuery: 'dev' };
      mockSubGroupsRepository.findAll = vi
        .fn()
        .mockResolvedValue(mockSubGroups);

      // Execute
      const result = await service.getAllSubGroupsForWorkspace(
        query,
        mockBotCtx,
      );

      // Verify
      expect(result).toEqual(mockSubGroups);
      expect(mockSubGroupsRepository.findAll).toHaveBeenCalledWith({
        where: {
          installation: { id: mockInstallation.id },
          organization: { id: mockOrganization.id },
          slackHandle: expect.objectContaining({
            _type: 'ilike',
            _value: '%dev%',
          }),
        },
      });
    });

    it('should handle empty results', async () => {
      // Setup
      const query: SearchQueryParams = {};
      mockSubGroupsRepository.findAll = vi.fn().mockResolvedValue([]);

      // Execute
      const result = await service.getAllSubGroupsForWorkspace(
        query,
        mockBotCtx,
      );

      // Verify
      expect(result).toEqual([]);
    });
  });

  describe('getAllSubGroupMappings', () => {
    const mockMappings: SubGroupsMaps[] = [
      {
        id: 'mapping-1',
        platformSubTeam: 'platform-subteam-1',
        installation: mockInstallation,
        organization: mockOrganization,
      } as SubGroupsMaps,
    ];

    it('should return all sub group mappings', async () => {
      // Setup
      mockSubGroupMappingsRepository.findAll = vi
        .fn()
        .mockResolvedValue(mockMappings);

      // Execute
      const result = await service.getAllSubGroupMappings(mockBotCtx);

      // Verify
      expect(result).toEqual(mockMappings);
      expect(mockSubGroupMappingsRepository.findAll).toHaveBeenCalledWith({
        where: {
          installation: { id: mockInstallation.id },
          organization: { id: mockOrganization.id },
        },
      });
    });
  });

  describe('createMapping', () => {
    const mockSlackSubGroup: SlackSubgroups = {
      id: 'subgroup-1',
      slackGroupId: 'S12345',
      slackHandle: 'developers',
      installation: mockInstallation,
      organization: mockOrganization,
    } as SlackSubgroups;

    const mockPlatformTeam: PlatformTeams = {
      id: 'team-1',
      uid: 'team-uid-1',
      installation: mockInstallation,
      organization: mockOrganization,
    } as PlatformTeams;

    const mockData: MapSubGroupToSubTeamDTO = {
      slackSubGroupId: 'S12345',
      platformSubGroupId: 'platform-subteam-1',
      platformTeamId: 'team-uid-1',
    };

    it('should create mapping successfully', async () => {
      // Setup
      mockSubGroupsRepository.findByCondition = vi
        .fn()
        .mockResolvedValue(mockSlackSubGroup);
      mockTeamsRepository.findByCondition = vi
        .fn()
        .mockResolvedValue(mockPlatformTeam);
      mockSubGroupMappingsRepository.saveWithTxn = vi
        .fn()
        .mockResolvedValue(undefined);

      // Execute
      const result = await service.createMapping(mockData, mockBotCtx);

      // Verify
      expect(result).toEqual(mockSlackSubGroup);
      expect(mockSubGroupsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          slackGroupId: mockData.slackSubGroupId,
          installation: { id: mockInstallation.id },
          organization: { id: mockOrganization.id },
        },
      });
      expect(mockTeamsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          uid: mockData.platformTeamId,
          installation: { id: mockInstallation.id },
          organization: { id: mockOrganization.id },
        },
      });
      expect(mockTransactionService.runInTransaction).toHaveBeenCalled();
    });

    it('should throw NotFoundException when slack sub group not found', async () => {
      // Setup
      mockSubGroupsRepository.findByCondition = vi.fn().mockResolvedValue(null);

      // Execute & Verify
      await expect(service.createMapping(mockData, mockBotCtx)).rejects.toThrow(
        new NotFoundException('Slack sub group not found'),
      );
    });

    it('should throw NotFoundException when platform team not found', async () => {
      // Setup
      mockSubGroupsRepository.findByCondition = vi
        .fn()
        .mockResolvedValue(mockSlackSubGroup);
      mockTeamsRepository.findByCondition = vi.fn().mockResolvedValue(null);

      // Execute & Verify
      await expect(service.createMapping(mockData, mockBotCtx)).rejects.toThrow(
        new NotFoundException('Platform team not found'),
      );
    });
  });

  describe('updateMapping', () => {
    const mockMapping: SubGroupsMaps = {
      id: 'mapping-1',
      platformSubTeam: 'platform-subteam-1',
      installation: mockInstallation,
      organization: mockOrganization,
    } as SubGroupsMaps;

    const mockData: UpdateSubGroupMappingDTO = {
      slackSubGroupId: 'subgroup-2',
      platformSubGroupId: 'platform-subteam-2',
    };

    it('should update mapping successfully', async () => {
      // Setup
      mockSubGroupMappingsRepository.findByCondition = vi
        .fn()
        .mockResolvedValueOnce(mockMapping)
        .mockResolvedValueOnce({ ...mockMapping, ...mockData });
      mockSubGroupMappingsRepository.updateWithTxn = vi
        .fn()
        .mockResolvedValue(undefined);

      // Execute
      const result = await service.updateMapping(
        'mapping-1',
        mockData,
        mockBotCtx,
      );

      // Verify
      expect(result).toEqual({ ...mockMapping, ...mockData });
      expect(
        mockSubGroupMappingsRepository.findByCondition,
      ).toHaveBeenCalledWith({
        where: {
          id: 'mapping-1',
          installation: { id: mockInstallation.id },
          organization: { id: mockOrganization.id },
        },
      });
      expect(mockTransactionService.runInTransaction).toHaveBeenCalled();
    });

    it('should throw NotFoundException when mapping not found', async () => {
      // Setup
      mockSubGroupMappingsRepository.findByCondition = vi
        .fn()
        .mockResolvedValue(null);

      // Execute & Verify
      await expect(
        service.updateMapping('mapping-1', mockData, mockBotCtx),
      ).rejects.toThrow(new NotFoundException('Mapping not found'));
    });
  });

  describe('getAllMappedSubGroupsAndTeams', () => {
    const mockMappings = [
      {
        id: 'mapping-1',
        platformSubTeam: 'platform-subteam-1',
        subGroup: {
          id: 'subgroup-1',
          slackGroupId: 'S12345',
          slackHandle: 'developers',
          description: 'Development team',
          usersCount: 5,
        },
        platformTeam: {
          id: 'team-1',
          uid: 'team-uid-1',
        },
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2023-01-02'),
        installation: mockInstallation,
        organization: mockOrganization,
      },
    ];

    it('should return all mapped sub groups and teams', async () => {
      // Setup
      mockSubGroupMappingsRepository.findAll = vi
        .fn()
        .mockResolvedValue(mockMappings);

      // Execute
      const result = await service.getAllMappedSubGroupsAndTeams(mockBotCtx);

      // Verify
      expect(result).toEqual([
        {
          id: 'mapping-1',
          slackSubGroup: {
            id: 'subgroup-1',
            slackGroupId: 'S12345',
            slackHandle: 'developers',
            description: 'Development team',
            usersCount: 5,
          },
          platformTeam: {
            id: 'team-1',
            uid: 'team-uid-1',
          },
          platformSubTeamId: 'platform-subteam-1',
          createdAt: new Date('2023-01-01'),
          updatedAt: new Date('2023-01-02'),
        },
      ]);

      expect(mockSubGroupMappingsRepository.findAll).toHaveBeenCalledWith({
        where: {
          installation: { id: mockInstallation.id },
          organization: { id: mockOrganization.id },
        },
        relations: {
          subGroup: true,
          platformTeam: true,
        },
      });
    });

    it('should handle empty mappings', async () => {
      // Setup
      mockSubGroupMappingsRepository.findAll = vi.fn().mockResolvedValue([]);

      // Execute
      const result = await service.getAllMappedSubGroupsAndTeams(mockBotCtx);

      // Verify
      expect(result).toEqual([]);
    });
  });

  describe('deleteMapping', () => {
    const mockMapping: SubGroupsMaps = {
      id: 'mapping-1',
      platformSubTeam: 'platform-subteam-1',
      installation: mockInstallation,
      organization: mockOrganization,
    } as SubGroupsMaps;

    it('should delete mapping successfully', async () => {
      // Setup
      mockSubGroupMappingsRepository.findByCondition = vi
        .fn()
        .mockResolvedValue(mockMapping);
      mockSubGroupMappingsRepository.removeWithTxn = vi
        .fn()
        .mockResolvedValue(undefined);

      // Execute
      const result = await service.deleteMapping('mapping-1', mockBotCtx);

      // Verify
      expect(result).toEqual({ id: 'mapping-1', deleted: true });
      expect(
        mockSubGroupMappingsRepository.findByCondition,
      ).toHaveBeenCalledWith({
        where: {
          id: 'mapping-1',
          installation: { id: mockInstallation.id },
          organization: { id: mockOrganization.id },
        },
      });
      expect(mockTransactionService.runInTransaction).toHaveBeenCalled();
      expect(mockSubGroupMappingsRepository.removeWithTxn).toHaveBeenCalledWith(
        'txn',
        mockMapping,
      );
    });

    it('should throw NotFoundException when mapping not found', async () => {
      // Setup
      mockSubGroupMappingsRepository.findByCondition = vi
        .fn()
        .mockResolvedValue(null);

      // Execute & Verify
      await expect(
        service.deleteMapping('mapping-1', mockBotCtx),
      ).rejects.toThrow(new NotFoundException('Mapping not found'));
    });

    it('should handle transaction errors', async () => {
      // Setup
      const error = new Error('Transaction failed');
      mockSubGroupMappingsRepository.findByCondition = vi
        .fn()
        .mockResolvedValue(mockMapping);
      mockTransactionService.runInTransaction = vi
        .fn()
        .mockRejectedValue(error);

      // Execute & Verify
      await expect(
        service.deleteMapping('mapping-1', mockBotCtx),
      ).rejects.toThrow(error);
    });
  });
});
