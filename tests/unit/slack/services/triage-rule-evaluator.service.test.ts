import 'reflect-metadata';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { AnnotatorApiProvider } from '../../../../src/external/provider/annotator-api.provider';
import { TriageCategory } from '../../../../src/slack/constants/triage-fields.constants';
import { TriageOperator } from '../../../../src/slack/dtos/triage-rule.dto';
import { TriageFieldsService } from '../../../../src/slack/services/triage-fields.service';
import { TriageRuleEvaluatorService } from '../../../../src/slack/services/triage-rule-evaluator.service';
import { ILogger } from '../../../../src/utils/logger/logger.interface';
import { mockSentryService } from '../../../mocks/sentry.mock';

describe('TriageRuleEvaluatorService', () => {
  let service: TriageRuleEvaluatorService;
  let mockLogger: ILogger;
  let mockTriageFieldsService: TriageFieldsService;
  let mockAnnotatorApiProvider: AnnotatorApiProvider;

  beforeEach(() => {
    vi.resetAllMocks();

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockTriageFieldsService = {
      getFieldMappings: vi.fn().mockResolvedValue({
        title: 'string',
        priority: 'object',
        status: 'object',
        customer: 'object',
        tags: 'array',
      }),
    } as unknown as TriageFieldsService;

    mockAnnotatorApiProvider = {
      getEntityMetadata: vi.fn().mockResolvedValue({
        fields: {
          priority: {
            type: 'lookup',
            fields: {
              name: { type: 'string' },
              id: { type: 'string' },
            },
          },
          status: {
            type: 'lookup',
            fields: {
              name: { type: 'string' },
              id: { type: 'string' },
            },
          },
          customer: {
            type: 'lookup',
            fields: {
              email: { type: 'string' },
              name: { type: 'string' },
            },
          },
        },
      }),
    } as unknown as AnnotatorApiProvider;

    service = new TriageRuleEvaluatorService(
      mockLogger,
      mockTriageFieldsService,
      mockAnnotatorApiProvider,
      mockSentryService,
    );
  });

  describe('evaluateRules', () => {
    it('should return true when all AND conditions match', async () => {
      const rules = {
        AND: [
          {
            category: TriageCategory.TICKET,
            field: 'title',
            operator: TriageOperator.CONTAINS,
            value: 'Test',
          },
          {
            category: TriageCategory.TICKET,
            field: 'priority.name',
            operator: TriageOperator.EQUALS,
            value: 'High',
          },
        ],
      };

      const context = {
        ticket: {
          title: 'Test Ticket',
          priority: { name: 'High' },
        },
      };

      const result = await service.evaluateRules(rules, context);

      expect(result).toBe(true);
    });

    it('should return false when any AND condition does not match', async () => {
      const rules = {
        AND: [
          {
            category: TriageCategory.TICKET,
            field: 'title',
            operator: TriageOperator.CONTAINS,
            value: 'Test',
          },
          {
            category: TriageCategory.TICKET,
            field: 'priority.name',
            operator: TriageOperator.EQUALS,
            value: 'Low',
          },
        ],
      };

      const context = {
        ticket: {
          title: 'Test Ticket',
          priority: { name: 'High' },
        },
      };

      const result = await service.evaluateRules(rules, context);

      expect(result).toBe(false);
    });

    it('should return true when at least one OR condition matches', async () => {
      const rules = {
        OR: [
          {
            category: TriageCategory.TICKET,
            field: 'title',
            operator: TriageOperator.CONTAINS,
            value: 'Nonexistent',
          },
          {
            category: TriageCategory.TICKET,
            field: 'priority.name',
            operator: TriageOperator.EQUALS,
            value: 'High',
          },
        ],
      };

      const context = {
        ticket: {
          title: 'Test Ticket',
          priority: { name: 'High' },
        },
      };

      const result = await service.evaluateRules(rules, context);

      expect(result).toBe(true);
    });

    it('should return false when no OR conditions match', async () => {
      const rules = {
        OR: [
          {
            category: TriageCategory.TICKET,
            field: 'title',
            operator: TriageOperator.CONTAINS,
            value: 'Nonexistent',
          },
          {
            category: TriageCategory.TICKET,
            field: 'priority.name',
            operator: TriageOperator.EQUALS,
            value: 'Low',
          },
        ],
      };

      const context = {
        ticket: {
          title: 'Test Ticket',
          priority: { name: 'High' },
        },
      };

      const result = await service.evaluateRules(rules, context);

      expect(result).toBe(false);
    });

    it('should handle nested fields correctly', async () => {
      const rules = {
        AND: [
          {
            category: TriageCategory.TICKET,
            field: 'customer.email',
            operator: TriageOperator.CONTAINS,
            value: 'example.com',
          },
        ],
      };

      const context = {
        ticket: {
          customer: {
            email: '<EMAIL>',
          },
        },
      };

      const result = await service.evaluateRules(rules, context);

      expect(result).toBe(true);
    });

    it('should handle array fields correctly', async () => {
      const rules = {
        AND: [
          {
            category: TriageCategory.TICKET,
            field: 'tags',
            operator: TriageOperator.CONTAINS,
            value: 'urgent',
          },
        ],
      };

      const context = {
        ticket: {
          tags: ['bug', 'urgent', 'frontend'],
        },
      };

      const result = await service.evaluateRules(rules, context);

      expect(result).toBe(true);
    });

    it('should handle different operators correctly', async () => {
      let rules = {
        AND: [
          {
            category: TriageCategory.TICKET,
            field: 'status.name',
            operator: TriageOperator.EQUALS,
            value: 'Open',
          },
        ],
      };

      let context = {
        ticket: {
          status: { name: 'Open' },
        },
      };

      let result = await service.evaluateRules(rules, context);
      expect(result).toBe(true);

      rules = {
        AND: [
          {
            category: TriageCategory.TICKET,
            field: 'status.name',
            operator: TriageOperator.NOT_EQUALS,
            value: 'Closed',
          },
        ],
      };

      result = await service.evaluateRules(rules, context);
      expect(result).toBe(true);

      rules = {
        AND: [
          {
            category: TriageCategory.TICKET,
            field: 'title',
            operator: TriageOperator.CONTAINS,
            value: 'Bug',
          },
        ],
      };

      context = {
        ticket: {
          status: { name: 'Open' },
          title: 'Bug in login form',
        } as any, // Use type assertion for the entire ticket object
      };

      result = await service.evaluateRules(rules, context);
      expect(result).toBe(true);

      rules = {
        AND: [
          {
            category: TriageCategory.TICKET,
            field: 'title',
            operator: TriageOperator.NOT_CONTAINS,
            value: 'Feature',
          },
        ],
      };

      result = await service.evaluateRules(rules, context);
      expect(result).toBe(true);
    });

    it('should handle missing fields gracefully', async () => {
      const rules = {
        AND: [
          {
            category: TriageCategory.TICKET,
            field: 'nonexistent.field',
            operator: TriageOperator.EQUALS,
            value: 'something',
          },
        ],
      };

      const context = {
        ticket: {
          title: 'Test Ticket',
        },
      };

      // Create a specific spy for the error method
      const errorSpy = vi.spyOn(mockLogger, 'error');

      // Mock the method to throw an error for nonexistent.field
      vi.spyOn(service as any, 'evaluateCondition').mockImplementationOnce(
        () => {
          throw new Error('Cannot read property of undefined');
        },
      );

      const result = await service.evaluateRules(rules, context);

      expect(result).toBe(false);
      // Check that logger.error has been called with a message containing information about the missing field
      expect(errorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error evaluating triage rules'),
        expect.any(String),
      );
    });

    it('should handle null or undefined rules', async () => {
      const context = {
        ticket: {
          title: 'Test Ticket',
        },
      };

      const emptyRules = { AND: [] } as any;
      let result = await service.evaluateRules(emptyRules, context);
      expect(result).toBe(false);

      const minimalRules = { AND: [], OR: [] } as any;
      result = await service.evaluateRules(minimalRules, context);
      expect(result).toBe(false);
    });
  });

  describe('validateField', () => {
    it('should return true for valid fields', async () => {
      const installation = { id: 'installation-123' } as any;

      const result = await service.validateField(
        TriageCategory.TICKET,
        'title',
        installation,
      );

      expect(result).toBe(true);
      expect(mockTriageFieldsService.getFieldMappings).toHaveBeenCalledWith(
        TriageCategory.TICKET,
        installation,
        'title',
      );
    });

    it('should return false for invalid fields', async () => {
      const installation = { id: 'installation-123' } as any;

      (mockTriageFieldsService.getFieldMappings as Mock).mockResolvedValueOnce(
        {},
      );

      const result = await service.validateField(
        TriageCategory.TICKET,
        'nonexistent',
        installation,
      );

      expect(result).toBe(false);
    });

    it('should validate nested fields correctly', async () => {
      const installation = { id: 'installation-123' } as any;

      const result = await service.validateField(
        TriageCategory.TICKET,
        'priority.name',
        installation,
      );

      expect(result).toBe(true);
      expect(mockAnnotatorApiProvider.getEntityMetadata).toHaveBeenCalledWith(
        installation,
        'Ticket',
        ['priority'],
      );
    });
  });

  describe('validateFieldValue', () => {
    it('should validate string values correctly', async () => {
      const installation = { id: 'installation-123' } as any;

      const result = await service.validateFieldValue(
        TriageCategory.TICKET,
        'title',
        'Test Ticket',
        installation,
      );

      expect(result).toBe(true);
    });

    it('should validate number values correctly', async () => {
      const installation = { id: 'installation-123' } as any;

      (mockTriageFieldsService.getFieldMappings as Mock).mockResolvedValueOnce({
        count: 'number',
      });

      const result = await service.validateFieldValue(
        TriageCategory.TICKET,
        'count',
        42,
        installation,
      );

      expect(result).toBe(true);
    });

    it('should validate boolean values correctly', async () => {
      const installation = { id: 'installation-123' } as any;

      (mockTriageFieldsService.getFieldMappings as Mock).mockResolvedValueOnce({
        isActive: 'boolean',
      });

      const result = await service.validateFieldValue(
        TriageCategory.TICKET,
        'isActive',
        true,
        installation,
      );

      expect(result).toBe(true);
    });

    it('should validate array values correctly', async () => {
      const installation = { id: 'installation-123' } as any;

      // Create a more focused mock for this test
      mockTriageFieldsService.getFieldMappings = vi.fn().mockResolvedValue({
        tags: 'array',
      });

      // Pass the value as a string since the method handles string or array for array fields
      const result = await service.validateFieldValue(
        TriageCategory.TICKET,
        'tags',
        'bug,urgent', // Use a string instead of an array
        installation,
      );

      // The method expects true for either an array or string when fieldType is 'array'
      expect(result).toBe(true);
    });

    it('should return false for invalid field types', async () => {
      const installation = { id: 'installation-123' } as any;

      (mockTriageFieldsService.getFieldMappings as Mock).mockResolvedValueOnce({
        title: 'unknown_type',
      });

      const result = await service.validateFieldValue(
        TriageCategory.TICKET,
        'title',
        'Test Ticket',
        installation,
      );

      expect(result).toBe(false);
    });
  });
});
