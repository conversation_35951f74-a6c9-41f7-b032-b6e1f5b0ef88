import { Repository } from 'typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { EventDeduplicationService } from '../../../../src/common/redis/event-deduplication.service';
import { ConfigService } from '../../../../src/config/config.service';
import { TransactionService } from '../../../../src/database/common/transactions.service';
import { Users } from '../../../../src/database/entities';
import { ChannelsRepository } from '../../../../src/database/entities/channels/repositories';
import { CommentThreadMapsRepository } from '../../../../src/database/entities/mappings/repositories/comment-thread-maps.repository';
import { SlackMessagesRepository } from '../../../../src/database/entities/slack-messages/repositories/slack-messages.repository';
import { ThenaPlatformApiProvider } from '../../../../src/external/provider/thena-platform-api.provider';
import { Ticket } from '../../../../src/platform/interfaces';
import { SlackAppManagementService } from '../../../../src/slack/core';
import { SlackWebAPIService } from '../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CommentThreadCacheService } from '../../../../src/slack/services/comment-thread-cache.service';
import {
  ThreadProcessingOptions,
  TicketCreationHelper,
  TicketCreationPayload,
} from '../../../../src/slack/services/ticket-creation-helper.service';
import { ILogger } from '../../../../src/utils';
import { BaseSlackBlocksToHtml } from '../../../../src/utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';

describe('TicketCreationHelper', () => {
  let service: TicketCreationHelper;
  let mockLogger: ILogger;
  let mockConfigService: ConfigService;
  let mockThenaPlatformApiProvider: ThenaPlatformApiProvider;
  let mockSlackWebAPIService: SlackWebAPIService;
  let mockSlackAppManagementService: SlackAppManagementService;
  let mockTransactionService: TransactionService;
  let mockEventDeduplicationService: EventDeduplicationService;
  let mockChannelsRepository: ChannelsRepository;
  let mockSlackMessagesRepository: SlackMessagesRepository;
  let mockCommentThreadMapsRepository: CommentThreadMapsRepository;
  let mockBaseSlackBlocksToHtml: BaseSlackBlocksToHtml;
  let mockCommentThreadCacheService: CommentThreadCacheService;
  let mockUsersRepository: Repository<Users>;

  const mockInstallation = {
    id: 'installation-1',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-1' },
  };

  const mockTicket: Ticket = {
    id: 'ticket-123',
    ticketId: 456,
    teamId: 'team-1',
    subTeamIdentifier: 'subteam-1',
    status: 'open',
    statusId: 'status-1',
    priority: 'high',
    priorityId: 'priority-1',
  } as Ticket;

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      log: vi.fn(),
    } as unknown as ILogger;

    mockConfigService = {
      get: vi.fn().mockReturnValue('https://platform.test'),
    } as unknown as ConfigService;

    mockThenaPlatformApiProvider = {
      createNewTicket: vi.fn(),
      createNewComment: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    mockSlackWebAPIService = {
      getConversationHistory: vi.fn(),
      getConversationReplies: vi.fn(),
      getPermalink: vi.fn(),
      sendMessage: vi.fn(),
      getUserInfo: vi.fn(),
    } as unknown as SlackWebAPIService;

    mockSlackAppManagementService = {
      upsertPersonWithIdentification: vi.fn(),
    } as unknown as SlackAppManagementService;

    mockTransactionService = {
      runInTransaction: vi
        .fn()
        .mockImplementation((callback) => callback('txn')),
    } as unknown as TransactionService;

    mockEventDeduplicationService = {
      processIdempotently: vi
        .fn()
        .mockImplementation((eventId, eventType, callback) => callback()),
    } as unknown as EventDeduplicationService;

    mockChannelsRepository = {
      findByCondition: vi.fn(),
    } as unknown as ChannelsRepository;

    mockSlackMessagesRepository = {
      saveWithTxn: vi.fn(),
      updateWithTxn: vi.fn(),
      findByCondition: vi.fn(),
    } as unknown as SlackMessagesRepository;

    mockCommentThreadMapsRepository = {
      saveWithTxn: vi.fn(),
    } as unknown as CommentThreadMapsRepository;

    mockBaseSlackBlocksToHtml = {
      initialize: vi.fn(),
      convert: vi.fn().mockReturnValue('<p>Converted HTML</p>'),
    } as unknown as BaseSlackBlocksToHtml;

    mockCommentThreadCacheService = {
      clearCache: vi.fn(),
      getCacheStats: vi.fn().mockReturnValue({ hits: 0, misses: 0 }),
    } as unknown as CommentThreadCacheService;

    mockUsersRepository = {
      find: vi.fn(),
    } as unknown as Repository<Users>;

    service = new TicketCreationHelper(
      mockLogger,
      mockConfigService,
      mockThenaPlatformApiProvider,
      mockSlackWebAPIService,
      mockSlackAppManagementService,
      mockTransactionService,
      mockEventDeduplicationService,
      mockChannelsRepository,
      mockSlackMessagesRepository,
      mockCommentThreadMapsRepository,
      mockBaseSlackBlocksToHtml,
      mockCommentThreadCacheService,
      mockUsersRepository,
    );
  });

  describe('createTicketWithMetadata', () => {
    const mockPayload: TicketCreationPayload = {
      title: 'Test Ticket',
      requestorEmail: '<EMAIL>',
      teamId: 'team-1',
      text: 'Test description',
      description: 'Detailed description',
      priorityId: 'high',
      urgency: 'urgent',
      subTeamId: 'subteam-1',
      performRouting: true,
      formId: 'form-1',
      customFieldValues: [{ field: 'value' }],
      metadata: {
        slack: {
          channel: 'C12345',
          ts: '**********.123456',
          user: 'U12345',
        },
        slackTeamId: 'T12345',
      },
      additionalField: 'additional value',
    };

    it('should create ticket with proper payload structure', async () => {
      // Setup
      mockThenaPlatformApiProvider.createNewTicket = vi
        .fn()
        .mockResolvedValue(mockTicket);

      // Execute
      const result = await service.createTicketWithMetadata(
        mockInstallation,
        mockPayload,
      );

      // Verify
      expect(result).toEqual(mockTicket);
      expect(mockThenaPlatformApiProvider.createNewTicket).toHaveBeenCalledWith(
        mockInstallation,
        expect.objectContaining({
          additionalField: 'additional value',
          requestorEmail: '<EMAIL>',
          title: 'Test Ticket',
          text: 'Test description',
          description: 'Detailed description',
          teamId: 'team-1',
          subTeamId: 'subteam-1',
          performRouting: true,
          priorityId: 'high',
          formId: 'form-1',
          customFieldValues: [{ field: 'value' }],
          metadata: {
            slackTeamId: 'T12345',
            slack: {
              channel: 'C12345',
              ts: '**********.123456',
              user: 'U12345',
            },
          },
        }),
      );
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Creating ticket with payload'),
        expect.any(String),
      );
    });

    it('should handle urgency fallback for priorityId', async () => {
      // Setup
      const { priorityId, ...payloadWithoutPriorityId } = mockPayload;
      mockThenaPlatformApiProvider.createNewTicket = vi
        .fn()
        .mockResolvedValue(mockTicket);

      // Execute
      await service.createTicketWithMetadata(
        mockInstallation,
        payloadWithoutPriorityId,
      );

      // Verify
      expect(mockThenaPlatformApiProvider.createNewTicket).toHaveBeenCalledWith(
        mockInstallation,
        expect.objectContaining({
          priorityId: 'urgent', // Should use urgency as fallback
        }),
      );
    });

    it('should use installation teamId when slackTeamId is not provided', async () => {
      // Setup
      const payloadWithoutSlackTeamId = {
        ...mockPayload,
        metadata: {
          slack: mockPayload.metadata.slack,
        },
      };
      mockThenaPlatformApiProvider.createNewTicket = vi
        .fn()
        .mockResolvedValue(mockTicket);

      // Execute
      await service.createTicketWithMetadata(
        mockInstallation,
        payloadWithoutSlackTeamId,
      );

      // Verify
      expect(mockThenaPlatformApiProvider.createNewTicket).toHaveBeenCalledWith(
        mockInstallation,
        expect.objectContaining({
          metadata: expect.objectContaining({
            slackTeamId: 'T12345', // Should use installation teamId
          }),
        }),
      );
    });

    it('should handle API errors', async () => {
      // Setup
      const error = new Error('API Error');
      mockThenaPlatformApiProvider.createNewTicket = vi
        .fn()
        .mockRejectedValue(error);

      // Execute & Verify
      await expect(
        service.createTicketWithMetadata(mockInstallation, mockPayload),
      ).rejects.toThrow(error);
    });
  });

  describe('sendTicketCreationConfirmation', () => {
    it('should send confirmation message with correct format', async () => {
      // Setup
      mockSlackWebAPIService.sendMessage = vi
        .fn()
        .mockResolvedValue({ ok: true });

      // Execute
      await service.sendTicketCreationConfirmation(
        mockInstallation,
        mockTicket,
        'C12345',
        '**********.123456',
      );

      // Verify
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalledWith(
        'xoxb-token',
        {
          channel: 'C12345',
          text: '<https://platform.test/dashboard/team-1?ticketId=ticket-123|subteam-1#456>: Ticket created successfully',
          thread_ts: '**********.123456',
          unfurl_links: true,
          unfurl_media: true,
        },
      );
    });

    it('should handle errors gracefully', async () => {
      // Setup
      const error = new Error('Send message failed');
      mockSlackWebAPIService.sendMessage = vi.fn().mockRejectedValue(error);

      // Execute
      await service.sendTicketCreationConfirmation(
        mockInstallation,
        mockTicket,
        'C12345',
        '**********.123456',
      );

      // Verify
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error sending ticket creation confirmation'),
      );
    });
  });

  describe('postConversationThreadToPlatform', () => {
    const mockOptions: ThreadProcessingOptions = {
      channelId: 'C12345',
      messageTs: '**********.123456',
      userId: 'U12345',
      shouldProcessReactions: true,
      shouldSendConfirmation: true,
    };

    const mockChannel = {
      id: 'channel-1',
      channelId: 'C12345',
    };

    const mockSlackMessage = {
      ts: '**********.123456',
      user: 'U12345',
      text: 'Test message',
      thread_ts: undefined,
    };

    beforeEach(() => {
      mockChannelsRepository.findByCondition = vi
        .fn()
        .mockResolvedValue(mockChannel);
      mockSlackWebAPIService.getConversationHistory = vi
        .fn()
        .mockResolvedValue({
          ok: true,
          messages: [mockSlackMessage],
        });
      mockSlackWebAPIService.getPermalink = vi.fn().mockResolvedValue({
        ok: true,
        permalink: 'https://slack.com/permalink',
      });
    });

    it('should process conversation thread successfully', async () => {
      // Setup
      const processMessageAndRepliesSpy = vi
        .spyOn(service as any, 'processMessageAndReplies')
        .mockResolvedValue(undefined);
      const processAllReactionsForCommentSpy = vi
        .spyOn(service as any, 'processAllReactionsForComment')
        .mockResolvedValue(undefined);
      const sendTicketCreationConfirmationSpy = vi
        .spyOn(service, 'sendTicketCreationConfirmation')
        .mockResolvedValue(undefined);

      // Execute
      await service.postConversationThreadToPlatform(
        mockInstallation,
        mockTicket,
        mockOptions,
      );

      // Verify
      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          channelId: 'C12345',
          installation: { id: 'installation-1' },
        },
      });
      expect(
        mockSlackWebAPIService.getConversationHistory,
      ).toHaveBeenCalledWith('xoxb-token', {
        channel: 'C12345',
        latest: '**********.123456',
        limit: 1,
        inclusive: true,
      });
      expect(mockSlackWebAPIService.getPermalink).toHaveBeenCalledWith(
        'xoxb-token',
        { channel: 'C12345', message_ts: '**********.123456' },
      );
      expect(processMessageAndRepliesSpy).toHaveBeenCalled();
      expect(processAllReactionsForCommentSpy).toHaveBeenCalled();
      expect(sendTicketCreationConfirmationSpy).toHaveBeenCalled();
      expect(mockCommentThreadCacheService.clearCache).toHaveBeenCalled();
    });

    it('should skip processing when channel not found', async () => {
      // Setup
      mockChannelsRepository.findByCondition = vi.fn().mockResolvedValue(null);

      // Execute
      await service.postConversationThreadToPlatform(
        mockInstallation,
        mockTicket,
        mockOptions,
      );

      // Verify
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Channel not found for team'),
      );
      expect(
        mockSlackWebAPIService.getConversationHistory,
      ).not.toHaveBeenCalled();
    });

    it('should skip processing when message history fails', async () => {
      // Setup
      mockSlackWebAPIService.getConversationHistory = vi
        .fn()
        .mockResolvedValue({
          ok: false,
          error: 'channel_not_found',
        });

      // Execute
      await service.postConversationThreadToPlatform(
        mockInstallation,
        mockTicket,
        mockOptions,
      );

      // Verify
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error getting slack message history'),
        'channel_not_found',
      );
    });

    it('should skip processing when no message found', async () => {
      // Setup
      mockSlackWebAPIService.getConversationHistory = vi
        .fn()
        .mockResolvedValue({
          ok: true,
          messages: [],
        });

      // Execute
      await service.postConversationThreadToPlatform(
        mockInstallation,
        mockTicket,
        mockOptions,
      );

      // Verify
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('No message found with timestamp'),
      );
    });

    it('should skip processing when permalink fails', async () => {
      // Setup
      mockSlackWebAPIService.getPermalink = vi.fn().mockResolvedValue({
        ok: false,
        error: 'message_not_found',
      });

      // Execute
      await service.postConversationThreadToPlatform(
        mockInstallation,
        mockTicket,
        mockOptions,
      );

      // Verify
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error getting slack message permalink'),
        'message_not_found',
      );
    });

    it('should skip reactions when shouldProcessReactions is false', async () => {
      // Setup
      const optionsWithoutReactions = {
        ...mockOptions,
        shouldProcessReactions: false,
      };
      const processAllReactionsForCommentSpy = vi
        .spyOn(service as any, 'processAllReactionsForComment')
        .mockResolvedValue(undefined);
      vi.spyOn(service as any, 'processMessageAndReplies').mockResolvedValue(
        undefined,
      );
      vi.spyOn(service, 'sendTicketCreationConfirmation').mockResolvedValue(
        undefined,
      );

      // Execute
      await service.postConversationThreadToPlatform(
        mockInstallation,
        mockTicket,
        optionsWithoutReactions,
      );

      // Verify
      expect(processAllReactionsForCommentSpy).not.toHaveBeenCalled();
    });

    it('should skip confirmation when shouldSendConfirmation is false', async () => {
      // Setup
      const optionsWithoutConfirmation = {
        ...mockOptions,
        shouldSendConfirmation: false,
      };
      const sendTicketCreationConfirmationSpy = vi
        .spyOn(service, 'sendTicketCreationConfirmation')
        .mockResolvedValue(undefined);
      vi.spyOn(service as any, 'processMessageAndReplies').mockResolvedValue(
        undefined,
      );
      vi.spyOn(
        service as any,
        'processAllReactionsForComment',
      ).mockResolvedValue(undefined);

      // Execute
      await service.postConversationThreadToPlatform(
        mockInstallation,
        mockTicket,
        optionsWithoutConfirmation,
      );

      // Verify
      expect(sendTicketCreationConfirmationSpy).not.toHaveBeenCalled();
    });

    it('should handle errors and clear cache', async () => {
      // Setup
      const error = new Error('Processing failed');
      vi.spyOn(service as any, 'processMessageAndReplies').mockRejectedValue(
        error,
      );

      // Execute
      await service.postConversationThreadToPlatform(
        mockInstallation,
        mockTicket,
        mockOptions,
      );

      // Verify
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Error posting conversation thread to platform',
        ),
      );
      expect(mockCommentThreadCacheService.clearCache).toHaveBeenCalled();
    });
  });
});
