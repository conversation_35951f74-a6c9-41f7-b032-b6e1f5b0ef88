import { WebClient } from '@slack/web-api';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { ConfigService } from '../../../../src/config/config.service';
import { NotificationService } from '../../../../src/slack/services/notification.service';
import { ILogger } from '../../../../src/utils';

describe('NotificationService', () => {
  let service: NotificationService;
  let mockLogger: ILogger;
  let mockConfigService: ConfigService;
  let mockWebClient: WebClient;

  const mockTicketData = {
    id: 'ticket-123',
    title: 'Test Ticket',
    teamIdentifier: 'TEAM1',
    ticketId: 456,
  };

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      log: vi.fn(),
    } as unknown as ILogger;

    mockConfigService = {
      get: vi.fn().mockReturnValue('https://platform.test'),
    } as unknown as ConfigService;

    mockWebClient = {
      chat: {
        postEphemeral: vi.fn(),
        postMessage: vi.fn(),
      },
    } as unknown as WebClient;

    service = new NotificationService(mockLogger, mockConfigService);
  });

  describe('buildTicketUrl', () => {
    it('should build ticket URL with teamId', () => {
      // Execute
      const result = (service as any).buildTicketUrl('ticket-123', 'TEAM1');

      // Verify
      expect(result).toBe(
        'https://platform.test/dashboard/TEAM1?ticketId=ticket-123',
      );
      expect(mockConfigService.get).toHaveBeenCalled();
    });

    it('should build ticket URL with teamIdentifier from ticketData', () => {
      // Execute
      const result = (service as any).buildTicketUrl('ticket-123', undefined, {
        teamIdentifier: 'TEAM2',
      });

      // Verify
      expect(result).toBe(
        'https://platform.test/dashboard/TEAM2?ticketId=ticket-123',
      );
    });

    it('should handle missing team information', () => {
      // Execute
      const result = (service as any).buildTicketUrl('ticket-123');

      // Verify
      expect(result).toBe(
        'https://platform.test/dashboard/?ticketId=ticket-123',
      );
    });

    it('should encode special characters in team ID', () => {
      // Execute
      const result = (service as any).buildTicketUrl('ticket-123', 'TEAM@#$');

      // Verify
      expect(result).toBe(
        'https://platform.test/dashboard/TEAM%40%23%24?ticketId=ticket-123',
      );
    });
  });

  describe('formatTicketIdentifier', () => {
    it('should format ticket identifier with teamIdentifier and ticketId', () => {
      // Execute
      const result = (service as any).formatTicketIdentifier(mockTicketData);

      // Verify
      expect(result).toBe('TEAM1#456');
    });

    it('should use teamId parameter when teamIdentifier is missing', () => {
      // Setup
      const ticketDataWithoutTeam = {
        ...mockTicketData,
        teamIdentifier: undefined,
      };

      // Execute
      const result = (service as any).formatTicketIdentifier(
        ticketDataWithoutTeam,
        'TEAM2',
      );

      // Verify
      expect(result).toBe('TEAM2#456');
    });

    it('should extract ticket number from id when ticketId is missing', () => {
      // Setup
      const ticketDataWithoutTicketId = {
        id: '789-abc-def',
        title: 'Test',
        teamIdentifier: 'TEAM1',
      };

      // Execute
      const result = (service as any).formatTicketIdentifier(
        ticketDataWithoutTicketId,
      );

      // Verify
      expect(result).toBe('TEAM1#789');
    });

    it('should use full id when no dash separator exists', () => {
      // Setup
      const ticketDataWithSimpleId = {
        id: 'simple123',
        title: 'Test',
        teamIdentifier: 'TEAM1',
      };

      // Execute
      const result = (service as any).formatTicketIdentifier(
        ticketDataWithSimpleId,
      );

      // Verify
      expect(result).toBe('TEAM1#simple123');
    });

    it('should handle missing team information', () => {
      // Setup
      const ticketDataWithoutTeam = {
        id: 'ticket-123',
        title: 'Test',
        ticketId: 456,
      };

      // Execute
      const result = (service as any).formatTicketIdentifier(
        ticketDataWithoutTeam,
      );

      // Verify
      expect(result).toBe('#456');
    });
  });

  describe('createMessagePayload', () => {
    it('should create message payload with correct structure', () => {
      // Execute
      const result = (service as any).createMessagePayload(
        'C12345',
        'https://platform.test/ticket/123',
        'TEAM1#456',
        'Test message',
      );

      // Verify
      expect(result).toEqual({
        channel: 'C12345',
        text: 'Test message',
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: '<https://platform.test/ticket/123|TEAM1#456>: Test message',
            },
          },
        ],
      });
    });

    it('should throw error when channel ID is missing', () => {
      // Execute & Verify
      expect(() => {
        (service as any).createMessagePayload(
          '',
          'url',
          'identifier',
          'message',
        );
      }).toThrow('Channel ID is required for creating message payload');
    });
  });

  describe('sendEphemeralMessage', () => {
    const mockPayload = {
      channel: 'C12345',
      text: 'Test message',
      blocks: [],
    };

    it('should send ephemeral message successfully', async () => {
      // Setup
      mockWebClient.chat.postEphemeral = vi
        .fn()
        .mockResolvedValue({ ok: true });

      // Execute
      await (service as any).sendEphemeralMessage(
        mockWebClient,
        mockPayload,
        'U12345',
        'TEST_SPAN',
      );

      // Verify
      expect(mockWebClient.chat.postEphemeral).toHaveBeenCalledWith({
        ...mockPayload,
        user: 'U12345',
      });
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'TEST_SPAN Sending as ephemeral message',
      );
      expect(mockLogger.log).toHaveBeenCalledWith(
        'TEST_SPAN Sending ephemeral message to user U12345 in channel C12345',
      );
    });

    it('should throw error when userId is missing', async () => {
      // Execute & Verify
      await expect(
        (service as any).sendEphemeralMessage(
          mockWebClient,
          mockPayload,
          '',
          'TEST_SPAN',
        ),
      ).rejects.toThrow('User ID is required for ephemeral messages');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'TEST_SPAN User ID is required for ephemeral messages',
      );
    });
  });

  describe('sendThreadMessage', () => {
    const mockPayload = {
      channel: 'C12345',
      text: 'Test message',
      blocks: [],
    };

    it('should send thread message successfully', async () => {
      // Setup
      mockWebClient.chat.postMessage = vi.fn().mockResolvedValue({ ok: true });

      // Execute
      await (service as any).sendThreadMessage(
        mockWebClient,
        mockPayload,
        '1234567890.123456',
        'TEST_SPAN',
      );

      // Verify
      expect(mockWebClient.chat.postMessage).toHaveBeenCalledWith({
        ...mockPayload,
        thread_ts: '1234567890.123456',
      });
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'TEST_SPAN Sending in thread: 1234567890.123456',
      );
    });

    it('should throw error when threadTs is missing', async () => {
      // Execute & Verify
      await expect(
        (service as any).sendThreadMessage(
          mockWebClient,
          mockPayload,
          '',
          'TEST_SPAN',
        ),
      ).rejects.toThrow('Thread timestamp is required for thread messages');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'TEST_SPAN Thread timestamp is required for thread messages',
      );
    });
  });

  describe('sendTicketCreationConfirmation', () => {
    it('should send ticket creation confirmation', async () => {
      // Setup
      const sendTicketNotificationSpy = vi
        .spyOn(service as any, 'sendTicketNotification')
        .mockResolvedValue(undefined);

      // Execute
      await service.sendTicketCreationConfirmation(
        mockWebClient,
        'C12345',
        'U12345',
        mockTicketData,
        '1234567890.123456',
        'TEAM1',
      );

      // Verify
      expect(sendTicketNotificationSpy).toHaveBeenCalledWith(
        mockWebClient,
        'C12345',
        'U12345',
        mockTicketData,
        'Ticket created successfully',
        '1234567890.123456',
        'TEAM1',
        'NotificationService.sendTicketCreationConfirmation',
      );
    });
  });

  describe('sendTicketAlreadyExistsNotification', () => {
    it('should send ticket already exists notification', async () => {
      // Setup
      const sendTicketNotificationSpy = vi
        .spyOn(service as any, 'sendTicketNotification')
        .mockResolvedValue(undefined);

      // Execute
      await service.sendTicketAlreadyExistsNotification(
        mockWebClient,
        'C12345',
        'U12345',
        mockTicketData,
        '1234567890.123456',
        'TEAM1',
      );

      // Verify
      expect(sendTicketNotificationSpy).toHaveBeenCalledWith(
        mockWebClient,
        'C12345',
        'U12345',
        mockTicketData,
        'ℹ️ Ticket already exists',
        '1234567890.123456',
        'TEAM1',
        'NotificationService.sendTicketAlreadyExistsNotification',
      );
    });
  });

  describe('sendTicketNotification', () => {
    it('should send ephemeral notification when userId is provided', async () => {
      // Setup
      const sendEphemeralMessageSpy = vi
        .spyOn(service as any, 'sendEphemeralMessage')
        .mockResolvedValue(undefined);
      const buildTicketUrlSpy = vi
        .spyOn(service as any, 'buildTicketUrl')
        .mockReturnValue('https://test.com/ticket');
      const formatTicketIdentifierSpy = vi
        .spyOn(service as any, 'formatTicketIdentifier')
        .mockReturnValue('TEAM1#123');
      const createMessagePayloadSpy = vi
        .spyOn(service as any, 'createMessagePayload')
        .mockReturnValue({ channel: 'C12345', text: 'test', blocks: [] });

      // Execute
      await (service as any).sendTicketNotification(
        mockWebClient,
        'C12345',
        'U12345',
        mockTicketData,
        'Test message',
      );

      // Verify
      expect(buildTicketUrlSpy).toHaveBeenCalledWith(
        mockTicketData.id,
        undefined,
        mockTicketData,
      );
      expect(formatTicketIdentifierSpy).toHaveBeenCalledWith(
        mockTicketData,
        undefined,
      );
      expect(createMessagePayloadSpy).toHaveBeenCalledWith(
        'C12345',
        'https://test.com/ticket',
        'TEAM1#123',
        'Test message',
      );
      expect(sendEphemeralMessageSpy).toHaveBeenCalled();
    });

    it('should send thread notification when threadTs is provided', async () => {
      // Setup
      const sendThreadMessageSpy = vi
        .spyOn(service as any, 'sendThreadMessage')
        .mockResolvedValue(undefined);
      vi.spyOn(service as any, 'buildTicketUrl').mockReturnValue(
        'https://test.com/ticket',
      );
      vi.spyOn(service as any, 'formatTicketIdentifier').mockReturnValue(
        'TEAM1#123',
      );
      vi.spyOn(service as any, 'createMessagePayload').mockReturnValue({
        channel: 'C12345',
        text: 'test',
        blocks: [],
      });

      // Execute
      await (service as any).sendTicketNotification(
        mockWebClient,
        'C12345',
        'U12345',
        mockTicketData,
        'Test message',
        '1234567890.123456',
      );

      // Verify
      expect(sendThreadMessageSpy).toHaveBeenCalled();
    });

    it('should throw error when channelId is missing', async () => {
      // Execute & Verify
      await expect(
        (service as any).sendTicketNotification(
          mockWebClient,
          '',
          'U12345',
          mockTicketData,
          'Test message',
        ),
      ).rejects.toThrow('Channel ID is required');
    });

    it('should throw error when ticketData is invalid', async () => {
      // Execute & Verify
      await expect(
        (service as any).sendTicketNotification(
          mockWebClient,
          'C12345',
          'U12345',
          null,
          'Test message',
        ),
      ).rejects.toThrow('Valid ticket data with ID is required');
    });

    it('should throw error when neither userId nor threadTs is provided', async () => {
      // Execute & Verify
      await expect(
        (service as any).sendTicketNotification(
          mockWebClient,
          'C12345',
          '',
          mockTicketData,
          'Test message',
        ),
      ).rejects.toThrow('Either userId or threadTs must be provided');
    });

    it('should handle and log errors', async () => {
      // Setup
      const error = new Error('Test error');
      vi.spyOn(service as any, 'buildTicketUrl').mockImplementation(() => {
        throw error;
      });

      // Execute & Verify
      await expect(
        (service as any).sendTicketNotification(
          mockWebClient,
          'C12345',
          'U12345',
          mockTicketData,
          'Test message',
        ),
      ).rejects.toThrow(error);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Error sending notification: Error: Test error',
        ),
      );
    });
  });
});
