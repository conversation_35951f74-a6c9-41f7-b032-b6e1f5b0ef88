import { beforeEach, describe, expect, it, vi } from 'vitest';
import { ConfigService } from '../../../../../../../src/config/config.service';
import { Users } from '../../../../../../../src/database/entities';
import { TriageMessageBlock } from '../../../../../../../src/slack/blocks/components/composite/triage/ticket-triage-blocks.composite';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../../../src/utils';

describe('TriageMessageBlock', () => {
  let triageMessageBlock: TriageMessageBlock;
  let mockConfigService: any;
  let mockLogger: any;
  let baseTicket: any;

  beforeEach(() => {
    // Mock ConfigService
    mockConfigService = {
      get: vi.fn().mockImplementation((key) => {
        if (key === 'THENA_WEB_URL') return 'https://app.thena.io';
        return null;
      }),
    };

    // Mock Logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
    };

    triageMessageBlock = new TriageMessageBlock(mockConfigService, mockLogger);

    // Base ticket for testing
    baseTicket = {
      id: 'ticket-123',
      ticketId: 'TKT-123',
      ticketTeamId: 'team-456',
      priority: 'High',
      priorityId: 'priority-1',
      status: 'Open',
      statusId: 'status-1',
      customer: {
        name: 'John Doe',
        email: '<EMAIL>',
        company: 'Example Corp',
      },
      subject: 'Test Ticket',
      content: 'This is a test ticket content',
      timestamp: '2023-01-01T12:00:00Z',
      teamIdentifier: 'TEAM',
    };
  });

  describe('build', () => {
    it('should handle archived tickets', () => {
      // Arrange
      const ticket = {
        ...baseTicket,
        archivedAt: '2023-01-02T12:00:00Z', // Archived ticket
      };

      // Act
      const result = triageMessageBlock.build({
        ticket,
      });

      // Assert
      expect(result).toBeDefined();
      expect(result.blocks).toBeInstanceOf(Array);

      // Check that no actions block is present for archived tickets
      const actionsBlock = result.blocks.find(
        (block: any) => block.type === 'actions',
      );
      expect(actionsBlock).toBeUndefined();

      // Check that logger was called
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          `Ticket ${ticket.id} is archived, skipping interactivity`,
        ),
      );

      // Check the head text for archived tickets
      const contextBlock = result.blocks[0] as any;
      expect(contextBlock.type).toBe('context');
      expect(contextBlock.elements[0].text).toContain('is archived');
    });

    it('should build blocks for regular tickets', () => {
      // Act
      const result = triageMessageBlock.build({
        ticket: baseTicket,
      });

      // Assert
      expect(result).toBeDefined();
      expect(result.blocks).toBeInstanceOf(Array);
      expect(result.blocks.length).toBeGreaterThan(1);

      // Check context block
      const contextBlock = result.blocks[0] as any;
      expect(contextBlock.type).toBe('context');
      expect(contextBlock.elements[0].text).toContain('is flagged as a ticket');
      expect(contextBlock.elements[0].text).toContain(
        `${baseTicket.ticketId}`,
      );

      // Check action blocks
      const actionBlocks = result.blocks.filter(
        (block: any) => block.type === 'actions',
      );
      expect(actionBlocks.length).toBe(2);

      // Check status and priority selects
      const statusPriorityBlock = actionBlocks[0] as any;
      expect(statusPriorityBlock.elements.length).toBe(2);
      expect(statusPriorityBlock.elements[0].action_id).toBe(
        TriageMessageBlock.ACTION_IDS.STATUS,
      );
      expect(statusPriorityBlock.elements[1].action_id).toBe(
        TriageMessageBlock.ACTION_IDS.PRIORITY,
      );

      // Check action buttons
      const buttonsBlock = actionBlocks[1] as any;
      expect(buttonsBlock.elements.length).toBe(4); // Assign, Close, Archive, View in Thena

      // Check specific buttons
      const assignButton = buttonsBlock.elements.find(
        (el: any) => el.action_id === TriageMessageBlock.ACTION_IDS.ASSIGN,
      );
      expect(assignButton).toBeDefined();

      const closeButton = buttonsBlock.elements.find(
        (el: any) => el.action_id === TriageMessageBlock.ACTION_IDS.CLOSE,
      );
      expect(closeButton).toBeDefined();
      expect(closeButton?.style).toBe('primary');

      const archiveButton = buttonsBlock.elements.find(
        (el: any) => el.action_id === TriageMessageBlock.ACTION_IDS.ARCHIVE,
      );
      expect(archiveButton).toBeDefined();
      expect(archiveButton?.style).toBe('danger');

      // Check View in Thena button
      const viewButton = buttonsBlock.elements.find(
        (el: any) => el.text?.text === 'View in Thena',
      );
      expect(viewButton).toBeDefined();
      expect(viewButton?.url).toBe(
        `https://app.thena.io/dashboard/${baseTicket.ticketTeamId}?ticketId=${baseTicket.id}`,
      );
    });

    it('should build blocks for tickets with assigned agents', () => {
      // Arrange
      const ticketWithAssignedAgent = {
        ...baseTicket,
        assignedAgent: {
          id: 'agent-123',
          name: 'Jane Smith',
          email: '<EMAIL>',
        },
      };

      const slackUser = {
        id: 'user-123',
        slackId: 'U12345',
        name: 'jane_smith',
        email: '<EMAIL>',
      } as unknown as Users;

      // Act
      const result = triageMessageBlock.build({
        ticket: ticketWithAssignedAgent,
        slackUserAssociated: slackUser,
      });

      // Assert
      expect(result).toBeDefined();

      // Check context block text for assigned agent with slack user
      const contextBlock = result.blocks[0] as any;
      expect(contextBlock.elements[0].text).toContain('<@U12345> is looking into it');
    });

    it('should build blocks for tickets with associated Slack users', () => {
      // Arrange
      const slackUser = {
        id: 'user-123',
        slackId: 'U12345',
        name: 'slack_user',
        email: '<EMAIL>',
      } as unknown as Users;

      // Act
      const result = triageMessageBlock.build({
        ticket: baseTicket,
        slackUserAssociated: slackUser,
      });

      // Assert
      expect(result).toBeDefined();

      // Check context block text for Slack user
      const contextBlock = result.blocks[0] as any;
      expect(contextBlock.elements[0].text).toContain(
        `<@${slackUser.slackId}> is looking into it`,
      );
    });

    it('should build blocks for internal threads', () => {
      // Arrange
      const openedBy = 'Jane Smith';

      // Act
      const result = triageMessageBlock.build({
        ticket: baseTicket,
        isInternalThread: true,
        openedBy,
      });

      // Assert
      expect(result).toBeDefined();

      // Check context block text for internal thread
      const contextBlock = result.blocks[0] as any;
      expect(contextBlock.elements[0].text).toContain(
        'Internal thread opened by',
      );
      expect(contextBlock.elements[0].text).toContain(openedBy);

      // Check that no action blocks are present for internal threads
      const actionBlocks = result.blocks.filter(
        (block: any) => block.type === 'actions',
      );
      expect(actionBlocks.length).toBe(0);
    });

    it('should handle tickets with permalink', () => {
      // Arrange
      const ticketWithPermalink = {
        ...baseTicket,
        permalink: 'https://slack.com/archives/C12345/p123456789',
      };

      // Act
      const result = triageMessageBlock.build({
        ticket: ticketWithPermalink,
      });

      // Assert
      expect(result).toBeDefined();

      // Check context block text for permalink
      const contextBlock = result.blocks[0] as any;
      expect(contextBlock.elements[0].text).toContain(
        `<${ticketWithPermalink.permalink}|This message>`,
      );
    });

    it('should handle internal thread without openedBy', () => {
      // Act
      const result = triageMessageBlock.build({
        ticket: baseTicket,
        isInternalThread: true,
      });

      // Assert
      expect(result).toBeDefined();

      // Check context block text for internal thread without openedBy
      const contextBlock = result.blocks[0] as any;
      expect(contextBlock.elements[0].text).toContain(
        'Internal thread opened by a user',
      );
    });

    it('should have correct static properties', () => {
      // Assert
      expect(TriageMessageBlock.BLOCK_ID).toBe('support_triage_block');
      expect(TriageMessageBlock.ACTION_IDS.STATUS).toBe('update_status');
      expect(TriageMessageBlock.ACTION_IDS.PRIORITY).toBe('set_priority');
      expect(TriageMessageBlock.ACTION_IDS.ASSIGN).toBe('assign_ticket');
      expect(TriageMessageBlock.ACTION_IDS.LOOKING).toBe('looking_into_ticket');
      expect(TriageMessageBlock.ACTION_IDS.CLOSE).toBe('close_ticket');
      expect(TriageMessageBlock.ACTION_IDS.ARCHIVE).toBe('archive_ticket');
    });

    it('should handle closed tickets', () => {
      // Arrange
      const closedTicket = {
        ...baseTicket,
        status: 'Closed',
        updatedAt: '2023-01-02T12:00:00Z',
      };

      // Act
      const result = triageMessageBlock.build({
        ticket: closedTicket,
      });

      // Assert
      expect(result).toBeDefined();
      expect(result.blocks).toBeInstanceOf(Array);

      // Check context block for closed ticket
      const contextBlock = result.blocks[0] as any;
      expect(contextBlock.type).toBe('context');
      expect(contextBlock.elements[0].text).toContain('has been closed');

      // Check that only status select is present for closed tickets
      const actionBlocks = result.blocks.filter(
        (block: any) => block.type === 'actions',
      );
      expect(actionBlocks.length).toBe(1);
      expect(actionBlocks[0].elements.length).toBe(1);
      expect((actionBlocks[0].elements[0] as any).action_id).toBe(
        TriageMessageBlock.ACTION_IDS.STATUS,
      );
    });

    it('should handle triage messages with sentiment and priority', () => {
      // Arrange
      const triageTicket = {
        ...baseTicket,
        sentiment: 'positive',
        priority: 'High',
        teamIdentifier: 'TEAM',
      };

      // Act
      const result = triageMessageBlock.build({
        ticket: triageTicket,
        isTriageMessage: true,
      });

      // Assert
      expect(result).toBeDefined();

      // Check context block for triage message
      const contextBlock = result.blocks[0] as any;
      expect(contextBlock.elements[0].text).toContain('_positive_ sentiment');
      expect(contextBlock.elements[0].text).toContain('_High_ priority');
      expect(contextBlock.elements[0].text).toContain('#TEAM-TKT-123');
    });

    it('should handle triage messages with assigned slack user', () => {
      // Arrange
      const slackUser = {
        id: 'user-123',
        slackId: 'U12345',
        name: 'slack_user',
        email: '<EMAIL>',
      } as unknown as Users;

      const triageTicket = {
        ...baseTicket,
        sentiment: 'neutral',
        priority: 'Medium',
        teamIdentifier: 'TEAM',
        assignedAgent: {
          name: 'Jane Smith',
          email: '<EMAIL>',
        },
      };

      // Act
      const result = triageMessageBlock.build({
        ticket: triageTicket,
        slackUserAssociated: slackUser,
        isTriageMessage: true,
      });

      // Assert
      expect(result).toBeDefined();

      // Check context block for triage message with assigned user
      const contextBlock = result.blocks[0] as any;
      expect(contextBlock.elements[0].text).toContain('is assigned to the ticket');
      expect(contextBlock.elements[0].text).toContain(`<@${slackUser.slackId}>`);
    });
  });

  describe('getTicketPermalink', () => {
    it('should generate correct permalink URL', () => {
      // Act
      const result = triageMessageBlock.build({
        ticket: baseTicket,
      });

      // Assert
      // Check the View in Thena button URL which uses getTicketPermalink internally
      const actionBlocks = result.blocks.filter(
        (block: any) => block.type === 'actions',
      );
      const buttonsBlock = actionBlocks[1] as any;
      const viewButton = buttonsBlock.elements.find(
        (el: any) => el.text?.text === 'View in Thena',
      );

      expect(viewButton).toBeDefined();
      expect(viewButton?.url).toBe(
        `https://app.thena.io/dashboard/${baseTicket.ticketTeamId}?ticketId=${baseTicket.id}`,
      );

      // Verify ConfigService was called with the correct key
      expect(mockConfigService.get).toHaveBeenCalledWith('THENA_WEB_URL');
    });
  });
});
