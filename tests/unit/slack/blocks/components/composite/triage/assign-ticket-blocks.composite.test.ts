import { describe, it, expect, beforeEach } from 'vitest';
import { SelectAssigneeComposite } from '../../../../../../../src/slack/blocks/components/composite/triage/assign-ticket-blocks.composite';

describe('SelectAssigneeComposite', () => {
  let selectAssigneeComposite: SelectAssigneeComposite;

  beforeEach(() => {
    selectAssigneeComposite = new SelectAssigneeComposite();
  });

  describe('build', () => {
    it('should build blocks for selecting an assignee', () => {
      // Act
      const result = selectAssigneeComposite.build();

      // Assert
      expect(result).toBeDefined();
      expect(result.blocks).toBeInstanceOf(Array);
      expect(result.blocks.length).toBe(1);

      // Check assignee select input
      const inputBlock = result.blocks[0] as any;
      expect(inputBlock.type).toBe('input');
      expect(inputBlock.block_id).toBe(SelectAssigneeComposite.BLOCK_ID);
      expect(inputBlock.element.type).toBe('external_select');
      expect(inputBlock.element.action_id).toBe(SelectAssigneeComposite.ACTION_IDS.ASSIGNEE_SELECT);
      expect(inputBlock.element.min_query_length).toBe(0);
      expect(inputBlock.element.placeholder.text).toBe('Search for an assignee');
      expect(inputBlock.label.text).toBe('Assignee');
    });

    it('should have correct static properties', () => {
      // Assert
      expect(SelectAssigneeComposite.BLOCK_ID).toBe('assign_ticket_modal');
      expect(SelectAssigneeComposite.ACTION_IDS.ASSIGNEE_SELECT).toBe('assignee_select');
    });
  });
});
