import { beforeEach, describe, expect, it } from 'vitest';
import {
  ConditionalFormBuilderComposite,
  Field,
} from '../../../../../../../src/slack/blocks/components/composite/form-builder/conditional-form-builder.composite';

describe('ConditionalFormBuilderComposite - Field Renderers', () => {
  let formBuilder: ConditionalFormBuilderComposite;

  beforeEach(() => {
    const mockLogger = {
      log: () => {},
      error: () => {},
      warn: () => {},
      debug: () => {},
      verbose: () => {},
    };
    formBuilder = new ConditionalFormBuilderComposite(mockLogger);
  });

  describe('renderField', () => {
    it('should render a text input field', () => {
      const field: Field = {
        id: 'title',
        name: 'Title',
        type: 'text',
        mandatoryOnCreation: true,
        visibleToCustomer: true,
        editableByCustomer: true,
        accessibleInTicketCreationForm: true,
      };

      const result = (formBuilder as any).renderField(field, {});

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('input');
      expect(result[0].block_id).toBe('block_title');
      expect(result[0].element.type).toBe('plain_text_input');
      expect(result[0].element.action_id).toBe('field_title');
      expect(result[0].label.text).toBe('Title');
      expect(result[0].optional).toBe(false);
    });

    it('should render a text input field with initial value', () => {
      const field: Field = {
        id: 'title',
        name: 'Title',
        type: 'text',
        mandatoryOnCreation: true,
        visibleToCustomer: true,
        editableByCustomer: true,
        accessibleInTicketCreationForm: true,
      };

      const values = { title: 'Initial Title Value' };

      const result = (formBuilder as any).renderField(field, values);

      expect(result[0].element.initial_value).toBe('Initial Title Value');
    });

    it('should render a textarea input field', () => {
      const field: Field = {
        id: 'description',
        name: 'Description',
        type: 'textarea',
        mandatoryOnCreation: false,
        visibleToCustomer: true,
        editableByCustomer: true,
        accessibleInTicketCreationForm: true,
      };

      const result = (formBuilder as any).renderField(field, {});

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('input');
      expect(result[0].element.type).toBe('plain_text_input');
      expect(result[0].element.multiline).toBe(true);
      expect(result[0].optional).toBe(true);
    });

    it('should render a number input field', () => {
      const field: Field = {
        id: 'amount',
        name: 'Amount',
        type: 'number',
        mandatoryOnCreation: true,
        visibleToCustomer: true,
        editableByCustomer: true,
        accessibleInTicketCreationForm: true,
      };

      const result = (formBuilder as any).renderField(field, {});

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('input');
      expect(result[0].element.type).toBe('number_input');
    });

    it('should render a select input field with static options', () => {
      const field: Field = {
        id: 'priority',
        name: 'Priority',
        type: 'select',
        mandatoryOnCreation: true,
        visibleToCustomer: true,
        editableByCustomer: true,
        accessibleInTicketCreationForm: true,
        options: [
          { label: 'High', value: 'high' },
          { label: 'Medium', value: 'medium' },
          { label: 'Low', value: 'low' },
        ],
      };

      const result = (formBuilder as any).renderField(field, {});

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('input');
      expect(result[0].element.type).toBe('static_select');
      expect(result[0].element.options).toHaveLength(3);
      expect(result[0].element.options[0].value).toBe('high');
      expect(result[0].element.options[0].text.text).toBe('High');
    });

    it('should render a select input field with initial selected option', () => {
      const field: Field = {
        id: 'priority',
        name: 'Priority',
        type: 'select',
        mandatoryOnCreation: true,
        visibleToCustomer: true,
        editableByCustomer: true,
        accessibleInTicketCreationForm: true,
        options: [
          { label: 'High', value: 'high' },
          { label: 'Medium', value: 'medium' },
          { label: 'Low', value: 'low' },
        ],
      };

      const values = { priority: 'medium' };

      const result = (formBuilder as any).renderField(field, values);

      expect(result[0].element.initial_option.value).toBe('medium');
      expect(result[0].element.initial_option.text.text).toBe('Medium');
    });

    it('should render a select input field with external data source', () => {
      const field: Field = {
        id: 'assignee',
        name: 'Assignee',
        type: 'select',
        mandatoryOnCreation: false,
        visibleToCustomer: true,
        editableByCustomer: true,
        accessibleInTicketCreationForm: true,
        apiForOptions: 'https://api.example.com/users',
      };

      const result = (formBuilder as any).renderField(field, {});

      expect(result).toHaveLength(1);
      expect(result[0].element.type).toBe('external_select');
      expect(result[0].element.min_query_length).toBe(0);
    });

    it('should render a checkbox input field', () => {
      const field: Field = {
        id: 'urgent',
        name: 'Urgent',
        type: 'checkbox',
        mandatoryOnCreation: false,
        visibleToCustomer: true,
        editableByCustomer: true,
        accessibleInTicketCreationForm: true,
      };

      const result = (formBuilder as any).renderField(field, {});

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('input');
      expect(result[0].element.type).toBe('checkboxes');
      expect(result[0].element.options).toHaveLength(1);
      expect(result[0].element.options[0].value).toBe('true');
      expect(result[0].element.initial_options).toBeUndefined();
    });

    it('should render a checkbox input field with initial checked state', () => {
      const field: Field = {
        id: 'urgent',
        name: 'Urgent',
        type: 'checkbox',
        mandatoryOnCreation: false,
        visibleToCustomer: true,
        editableByCustomer: true,
        accessibleInTicketCreationForm: true,
      };

      const values = { urgent: true };

      const result = (formBuilder as any).renderField(field, values);

      expect(result[0].element.initial_options).toHaveLength(1);
      expect(result[0].element.initial_options[0].value).toBe('true');
    });

    it('should render a date input field', () => {
      const field: Field = {
        id: 'dueDate',
        name: 'Due Date',
        type: 'date',
        mandatoryOnCreation: false,
        visibleToCustomer: true,
        editableByCustomer: true,
        accessibleInTicketCreationForm: true,
      };

      const result = (formBuilder as any).renderField(field, {});

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('input');
      expect(result[0].element.type).toBe('datepicker');
    });

    it('should render a date input field with initial value', () => {
      const field: Field = {
        id: 'dueDate',
        name: 'Due Date',
        type: 'date',
        mandatoryOnCreation: false,
        visibleToCustomer: true,
        editableByCustomer: true,
        accessibleInTicketCreationForm: true,
      };

      const values = { dueDate: '2023-12-31' };

      const result = (formBuilder as any).renderField(field, values);

      expect(result[0].element.initial_date).toBe('2023-12-31');
    });

    it('should handle unknown field types gracefully', () => {
      const field: Field = {
        id: 'unknown',
        name: 'Unknown Field',
        type: 'unknown_type',
        mandatoryOnCreation: false,
        visibleToCustomer: true,
        editableByCustomer: true,
        accessibleInTicketCreationForm: true,
      };

      const result = (formBuilder as any).renderField(field, {});

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('input');
      expect(result[0].element.type).toBe('plain_text_input');
    });
  });
});
