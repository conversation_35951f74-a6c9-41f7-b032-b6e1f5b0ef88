import { describe, it, expect, beforeEach } from 'vitest';
import { ChannelSetupBlocks } from '../../../../../../../src/slack/blocks/components/composite/channels/channel-setup-blocks.composite';
import { ChannelType } from '../../../../../../../src/database/entities/channels/channels.entity';

interface Team {
  id: string;
  name: string;
}

describe('ChannelSetupBlocks', () => {
  let channelSetupBlocks: ChannelSetupBlocks;

  beforeEach(() => {
    channelSetupBlocks = new ChannelSetupBlocks();
  });

  describe('build', () => {
    it('should build blocks with empty teams array', () => {
      // Act
      const result = channelSetupBlocks.build();

      // Assert
      expect(result).toBeDefined();
      expect(result.blocks).toBeInstanceOf(Array);
      expect(result.blocks.length).toBeGreaterThan(0);

      // Check header
      expect(result.blocks[0].type).toBe('header');
      expect((result.blocks[0] as any).text.text).toBe('Channel setup required 🎯');

      // Check team select
      const teamSelectBlock = result.blocks.find(
        (block: any) => block.block_id === `${ChannelSetupBlocks.BLOCK_ID}_team`
      );
      expect(teamSelectBlock).toBeDefined();
      expect((teamSelectBlock as any).element.type).toBe('static_select');
      expect((teamSelectBlock as any).element.action_id).toBe(ChannelSetupBlocks.ACTION_IDS.TEAM_SELECT);
      expect((teamSelectBlock as any).element.options).toEqual([]);

      // Check channel type select
      const channelTypeBlock = result.blocks.find(
        (block: any) => block.block_id === `${ChannelSetupBlocks.BLOCK_ID}_type`
      );
      expect(channelTypeBlock).toBeDefined();
      expect((channelTypeBlock as any).element.type).toBe('static_select');
      expect((channelTypeBlock as any).element.action_id).toBe(ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT);
      
      // Check channel type options
      expect((channelTypeBlock as any).element.options).toHaveLength(3);
      expect((channelTypeBlock as any).element.options[0].value).toBe(ChannelType.CUSTOMER_CHANNEL);
      expect((channelTypeBlock as any).element.options[1].value).toBe(ChannelType.INTERNAL_HELPDESK);
      expect((channelTypeBlock as any).element.options[2].value).toBe(ChannelType.TRIAGE_CHANNEL);
    });

    it('should build blocks with provided teams array', () => {
      // Arrange
      const teams: Team[] = [
        { id: 'team1', name: 'Engineering' },
        { id: 'team2', name: 'Marketing' },
        { id: 'team3', name: 'Sales' },
      ];

      // Act
      const result = channelSetupBlocks.build(teams as any);

      // Assert
      expect(result).toBeDefined();
      
      // Check team select options
      const teamSelectBlock = result.blocks.find(
        (block: any) => block.block_id === `${ChannelSetupBlocks.BLOCK_ID}_team`
      );
      expect((teamSelectBlock as any).element.options).toHaveLength(3);
      
      // Check first team option
      expect((teamSelectBlock as any).element.options[0].text.text).toBe('Engineering');
      expect((teamSelectBlock as any).element.options[0].value).toBe('team1');
      
      // Check second team option
      expect((teamSelectBlock as any).element.options[1].text.text).toBe('Marketing');
      expect((teamSelectBlock as any).element.options[1].value).toBe('team2');
      
      // Check third team option
      expect((teamSelectBlock as any).element.options[2].text.text).toBe('Sales');
      expect((teamSelectBlock as any).element.options[2].value).toBe('team3');
    });

    it('should include descriptions for all channel types', () => {
      // Act
      const result = channelSetupBlocks.build();

      // Assert
      const blocks = result.blocks;
      
      // Find description blocks based on actual implementation text
      const customerChannelBlock = blocks.find(
        (block: any) => block.type === 'section' && (block.text?.text?.includes('Customer channel') || block.text?.text?.includes('👥'))
      );
      const internalHelpdeskBlock = blocks.find(
        (block: any) => block.type === 'section' && (block.text?.text?.includes('Internal helpdesk') || block.text?.text?.includes('⚙️'))
      );
      const triageChannelBlock = blocks.find(
        (block: any) => block.type === 'section' && (block.text?.text?.includes('Triage channel') || block.text?.text?.includes('🔔'))
      );
      
      // Check descriptions based on actual implementation
      expect(customerChannelBlock).toBeDefined();
      expect((customerChannelBlock as any).text.text).toContain('Convert messages from external domains into tickets');
      
      expect(internalHelpdeskBlock).toBeDefined();
      expect((internalHelpdeskBlock as any).text.text).toContain('Convert messages into tickets in Thena');
      
      expect(triageChannelBlock).toBeDefined();
      expect((triageChannelBlock as any).text.text).toContain('Used to triage tickets and enable collaboration');
    });

    it('should have correct static properties', () => {
      // Assert
      expect(ChannelSetupBlocks.BLOCK_ID).toBe('channel_setup_form');
      expect(ChannelSetupBlocks.ACTION_IDS.TEAM_SELECT).toBe('team_select');
      expect(ChannelSetupBlocks.ACTION_IDS.CHANNEL_TYPE_SELECT).toBe('channel_type_select');
    });
  });
});
