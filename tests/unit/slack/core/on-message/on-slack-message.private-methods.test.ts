import { KnownEventFromType } from '@slack/bolt';
import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  CommentThreadMappings,
  Installations,
  SlackMessages,
} from '../../../../../src/database/entities';
import { Channels } from '../../../../../src/database/entities/channels/channels.entity';
import { TeamRelationshipType } from '../../../../../src/database/entities/mappings/platform-teams-to-channel-mappings.entity';
import { Person } from '../../../../../src/database/interfaces/person.interface';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { SettingsCore } from '../../../../../src/slack/core/management';
import { CoreSlackMessage } from '../../../../../src/slack/core/on-message/core-slack-message';
import { OnMessageHandler } from '../../../../../src/slack/core/on-message/on-slack-message';
import { OnThreadMessageHandler } from '../../../../../src/slack/core/on-message/on-thread-message';
import { ILogger } from '../../../../../src/utils';
import * as CommonUtils from '../../../../../src/utils/common';
import { BaseSlackBlocksToHtml } from '../../../../../src/utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';

describe('OnMessageHandler - Private Methods', () => {
  let handler: OnMessageHandler;
  let mockLogger: ILogger;
  let mockSlackMessagesRepository: Repository<SlackMessages>;
  let mockCommentThreadMappingsRepository: Repository<CommentThreadMappings>;
  let mockPlatformApiProvider: ThenaPlatformApiProvider;
  let mockCoreSlackMessage: CoreSlackMessage;
  let mockBaseSlackBlocksToHtml: BaseSlackBlocksToHtml;
  let mockOnThreadMessageHandler: OnThreadMessageHandler;
  let mockSettingsCore: SettingsCore;

  beforeEach(() => {
    // Create mocks with minimal implementations to avoid memory issues
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    } as unknown as ILogger;

    mockSlackMessagesRepository = {
      save: vi.fn(),
      findOne: vi.fn(),
      findByCondition: vi.fn(),
      update: vi.fn().mockResolvedValue({}),
    } as unknown as Repository<SlackMessages>;

    mockCommentThreadMappingsRepository = {
      save: vi.fn(),
      findOne: vi.fn(),
    } as unknown as Repository<CommentThreadMappings>;

    mockPlatformApiProvider = {
      createNewTicket: vi.fn(),
      createNewComment: vi.fn(),
      getTicket: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    mockCoreSlackMessage = {
      checkAndGetSlackMessageDetails: vi.fn(),
      aiCheckIsValidTicket: vi.fn(),
      ticketFromSourceSlack: vi.fn(),
      createTicketForTeam: vi.fn(),
      getChannel: vi.fn(),
      getUser: vi.fn(),
      shouldGroupWithExistingConversation: vi.fn(),
    } as unknown as CoreSlackMessage;

    mockBaseSlackBlocksToHtml = {
      initialize: vi.fn(),
      convert: vi.fn(),
    } as unknown as BaseSlackBlocksToHtml;

    mockOnThreadMessageHandler = {
      handle: vi.fn(),
    } as unknown as OnThreadMessageHandler;

    mockSettingsCore = {
      getValue: vi.fn(),
    } as unknown as SettingsCore;

    // Mock CommonUtils.getSlackMentions function
    vi.spyOn(CommonUtils, 'getSlackMentions').mockImplementation(() => ({
      mentionedUsers: [],
      mentionedUserGroups: [],
      botMentioned: false,
    }));

    // Create handler instance
    handler = new OnMessageHandler(
      mockLogger,
      mockSlackMessagesRepository,
      mockCommentThreadMappingsRepository,
      mockPlatformApiProvider,
      mockCoreSlackMessage,
      mockBaseSlackBlocksToHtml,
      mockOnThreadMessageHandler,
      mockSettingsCore,
    );
  });

  describe('handleNewMessage', () => {
    it('should skip processing for direct message channels', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      } as unknown as Installations;

      const event = {
        type: 'message',
        user: 'U12345',
        channel: 'D12345', // Direct message channel starts with D
        team: 'T12345',
        ts: '**********.123456',
        text: 'This is a direct message',
      } as unknown as KnownEventFromType<'message'>;

      // Act
      await (handler as any).handleNewMessage(mockInstallation, event);

      // Assert
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Message received in a direct message channel'),
      );
      expect(
        mockCoreSlackMessage.checkAndGetSlackMessageDetails,
      ).not.toHaveBeenCalled();
    });

    it('should skip processing when channel is not found', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      } as unknown as Installations;

      const event = {
        type: 'message',
        user: 'U12345',
        channel: 'C12345',
        team: 'T12345',
        ts: '**********.123456',
        text: 'This is a message',
      } as unknown as KnownEventFromType<'message'>;

      // Mock channel not found
      (
        mockCoreSlackMessage.checkAndGetSlackMessageDetails as Mock
      ).mockResolvedValue({
        channel: null,
        user: {} as Person,
        isGrouped: false,
        isCustomer: true,
      });

      // Act
      await (handler as any).handleNewMessage(mockInstallation, event);

      // Assert
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Channel C12345 not found in the database'),
      );
    });
  });

  describe('ticketFromSourceSlack', () => {
    it('should create a ticket and comment from a Slack message', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
        organization: { id: 'org-id' },
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'general',
        platformTeamsToChannelMappings: [
          {
            relationshipType: TeamRelationshipType.PRIMARY,
            platformTeam: { id: 'team-id', uid: 'team-uid' },
          },
        ],
      } as unknown as Channels;

      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
        displayName: 'Test User',
        slackProfileEmail: '<EMAIL>',
        getUserAvatar: () => 'https://example.com/avatar.jpg',
        userHasEmail: () => true,
      } as unknown as Person;

      const event = {
        type: 'message',
        user: 'U12345',
        channel: 'C12345',
        team: 'T12345',
        ts: '**********.123456',
        text: 'This is a message that should create a ticket',
        blocks: [
          {
            type: 'rich_text',
            elements: [
              {
                type: 'rich_text_section',
                elements: [
                  {
                    type: 'text',
                    text: 'This is a message that should create a ticket',
                  },
                ],
              },
            ],
          },
        ],
      } as unknown as KnownEventFromType<'message'>;

      // Set up spy for createTicketAndComment
      const createTicketAndCommentSpy = vi.spyOn(
        handler as any,
        'createTicketAndComment',
      );
      createTicketAndCommentSpy.mockResolvedValue({
        ticket: { id: 'ticket-id', title: 'Test Ticket' },
        comment: { id: 'comment-id' },
      });

      // Set up spy for createCommentAndMapping
      const createCommentAndMappingSpy = vi.spyOn(
        handler as any,
        'createCommentAndMapping',
      );
      createCommentAndMappingSpy.mockResolvedValue({
        data: {
          id: 'comment-id',
          content: 'This is a message that should create a ticket',
        },
      });

      // Act
      const result = await (handler as any).ticketFromSourceSlack(
        mockInstallation,
        mockChannel,
        mockUser,
        event,
      );

      // Assert
      expect(createTicketAndCommentSpy).toHaveBeenCalledWith(
        mockInstallation,
        mockChannel,
        mockUser,
        event,
      );
      expect(createCommentAndMappingSpy).not.toHaveBeenCalled(); // This method doesn't exist in ticketFromSourceSlack
      expect(result).toEqual({
        ticket: { id: 'ticket-id', title: 'Test Ticket' },
        comment: { id: 'comment-id' },
      });
    });
  });
});
