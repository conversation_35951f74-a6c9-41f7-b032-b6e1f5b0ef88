import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { AiService } from '../../../../../src/ai/ai.service';
import {
  Channels,
  CustomerContacts,
  Installations,
  PlatformTeams,
  Users,
} from '../../../../../src/database/entities';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories';
import { TeamRelationshipType } from '../../../../../src/database/entities/mappings/platform-teams-to-channel-mappings.entity';
import { TeamsRepository } from '../../../../../src/database/entities/teams/repositories/teams.repository';
import { UsersRepository } from '../../../../../src/database/entities/users/repositories/users.repository';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { SettingsCore } from '../../../../../src/slack/core/management';
import { CoreSlackMessage } from '../../../../../src/slack/core/on-message/core-slack-message';
import { ILogger } from '../../../../../src/utils';
import * as CommonUtils from '../../../../../src/utils/common';

describe('CoreSlackMessage', () => {
  let service: CoreSlackMessage;
  let mockLogger: ILogger;
  let mockChannelsRepository: ChannelsRepository;
  let mockUsersRepository: Repository<Users>;
  let mockCustomerContactsRepository: Repository<CustomerContacts>;
  let mockTeamsRepository: TeamsRepository;
  let mockPlatformApiProvider: ThenaPlatformApiProvider;
  let mockAiService: AiService;
  let mockSettingsCore: SettingsCore;

  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();

    // Create mock implementations
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockUsersRepository = {
      findOne: vi.fn(),
      find: vi.fn(),
    } as unknown as Repository<Users>;

    mockCustomerContactsRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<CustomerContacts>;

    mockChannelsRepository = {
      findOne: vi.fn(),
      findByCondition: vi.fn(),
      find: vi.fn(),
      save: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      count: vi.fn(),
      exists: vi.fn(),
      remove: vi.fn(),
      softDelete: vi.fn(),
      findOneById: vi.fn(),
      findWithRelations: vi.fn(),
      findAll: vi.fn(),
    } as unknown as ChannelsRepository;

    const mockGroupedSlackMessagesRepository = {
      save: vi.fn(),
      findOne: vi.fn(),
    } as unknown as any;

    const mockSubGroupsMapsRepository = {
      findByCondition: vi.fn(),
    } as unknown as any;

    mockTeamsRepository = {
      findOne: vi.fn(),
      findAll: vi.fn(),
    } as unknown as TeamsRepository;

    const mockConversationGroupingService = {
      shouldGroupWithExistingConversation: vi.fn().mockResolvedValue({
        isGrouped: false,
      }),
    } as unknown as any;

    mockSettingsCore = {
      getValue: vi.fn(),
      getValueOrDefault: vi.fn(),
      setValue: vi.fn(),
      deleteValue: vi.fn(),
    } as unknown as SettingsCore;

    const mockSlackAppManagementService = {
      upsertPersonWithIdentification: vi.fn(),
    } as unknown as any;

    mockPlatformApiProvider = {
      createNewTicket: vi.fn(),
      getTeam: vi.fn(),
      getPrioritiesForTeam: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    const mockSlackWebAPIService = {
      getPermalink: vi.fn(),
    } as unknown as any;

    mockAiService = {
      isValidTicket: vi.fn(),
      detectUrgency: vi.fn(),
      generateTicketTitle: vi.fn(),
      generateTicketDescription: vi.fn(),
      loadTeamPrompts: vi.fn(),
      setActiveModel: vi.fn(),
      setActiveProvider: vi.fn(),
    } as unknown as AiService;

    // Create the service instance
    service = new CoreSlackMessage(
      mockLogger,
      mockUsersRepository,
      mockCustomerContactsRepository,
      mockChannelsRepository,
      mockGroupedSlackMessagesRepository,
      mockSubGroupsMapsRepository,
      mockTeamsRepository,
      mockConversationGroupingService,
      mockSettingsCore,
      mockSlackAppManagementService,
      mockPlatformApiProvider,
      mockSlackWebAPIService,
      mockAiService,
    );
  });

  describe('checkAndGetSlackMessageDetails', () => {
    it('should return channel, user, and flags for a valid message', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: {
          id: 'org-123',
        },
      } as unknown as Installations;

      const event = {
        type: 'message',
        user: 'U12345',
        channel: 'C12345',
        team: 'T12345',
        ts: '**********.123456',
        text: 'This is a test message',
      };

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        channelType: ChannelType.CUSTOMER_CHANNEL,
        isBotActive: true,
        platformTeamsToChannelMappings: [],
      } as unknown as Channels;

      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
      } as unknown as Users;

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(
        mockChannel,
      );
      (mockUsersRepository.findOne as Mock).mockResolvedValue(null);
      (mockCustomerContactsRepository.findOne as Mock).mockResolvedValue(
        mockUser,
      );

      // Add mock for the shouldGroupWithExistingConversation method
      const mockConversationGroupingService = {
        shouldGroupWithExistingConversation: vi.fn().mockResolvedValue({
          isGrouped: false,
        }),
      };

      // Use property accessor to avoid readonly property error
      Object.defineProperty(service, 'conversationGroupingService', {
        value: mockConversationGroupingService,
      });

      // Act
      const result = await service.checkAndGetSlackMessageDetails(
        mockInstallation,
        event as any,
      );

      // Assert
      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          channelId: 'C12345',
          installation: { id: 'installation-id' },
        },
        relations: {
          platformTeamsToChannelMappings: {
            platformTeam: true,
          },
        },
      });
      expect(mockUsersRepository.findOne).toHaveBeenCalledWith({
        where: {
          slackId: 'U12345',
          installation: {
            id: 'installation-id',
          },
          organization: {
            id: 'org-123',
          },
        },
      });
      expect(mockCustomerContactsRepository.findOne).toHaveBeenCalledWith({
        where: {
          slackId: 'U12345',
          installation: { id: 'installation-id' },
        },
      });
      expect(result).toEqual({
        channel: mockChannel,
        user: mockUser,
        isCustomer: true,
        isGrouped: false,
      });
    });

    it('should throw an error if message is not a top level message', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: {
          id: 'org-123',
        },
      } as unknown as Installations;

      const event = {
        type: 'message',
        // Missing text property
        user: 'U12345',
        channel: 'C12345',
      };

      // Act & Assert
      await expect(
        service.checkAndGetSlackMessageDetails(mockInstallation, event as any),
      ).rejects.toThrow('Message is not a top level message');
    });

    it('should throw an error if message has no team', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: {
          id: 'org-123',
        },
      } as unknown as Installations;

      const event = {
        type: 'message',
        text: 'This is a test message',
        user: 'U12345',
        channel: 'C12345',
        // Missing team property
      };

      // Act & Assert
      await expect(
        service.checkAndGetSlackMessageDetails(mockInstallation, event as any),
      ).rejects.toThrow('Slack workspace not attached to the event message!');
    });

    it('should throw an error if channel is not found', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: {
          id: 'org-123',
        },
      } as unknown as Installations;

      const event = {
        type: 'message',
        text: 'This is a test message',
        user: 'U12345',
        channel: 'C12345',
        team: 'T12345',
        ts: '**********.123456',
      };

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.checkAndGetSlackMessageDetails(mockInstallation, event as any),
      ).rejects.toThrow('Channel not found!');
    });

    it('should skip ticket creation for non-customer in non-helpdesk channel', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: {
          id: 'org-123',
        },
      } as unknown as Installations;

      const event = {
        type: 'message',
        text: 'This is a test message',
        user: 'U12345',
        channel: 'C12345',
        team: 'T12345',
        ts: '**********.123456',
      };

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        channelType: ChannelType.CUSTOMER_CHANNEL,
        isBotActive: true,
        platformTeamsToChannelMappings: [],
      } as unknown as Channels;

      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
      } as unknown as Users;

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(
        mockChannel,
      );
      (mockUsersRepository.findOne as Mock).mockResolvedValue(mockUser);
      (mockCustomerContactsRepository.findOne as Mock).mockResolvedValue(null);

      // Add mock for the shouldGroupWithExistingConversation method
      const mockConversationGroupingService = {
        shouldGroupWithExistingConversation: vi.fn().mockResolvedValue({
          isGrouped: false,
        }),
      };

      // Use property accessor to avoid readonly property error
      Object.defineProperty(service, 'conversationGroupingService', {
        value: mockConversationGroupingService,
      });

      // Act
      const result = await service.checkAndGetSlackMessageDetails(
        mockInstallation,
        event as any,
      );

      // Assert
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining(
          'User U12345 is not a customer! Skipping ticket creation.',
        ),
      );
      expect(result).toEqual({
        channel: mockChannel,
        user: mockUser,
        isCustomer: false,
        isGrouped: false,
      });
    });
  });

  describe('aiCheckIsValidTicket', () => {
    it('should return true when AI detects a valid ticket', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: {
          id: 'org-123',
        },
      } as unknown as Installations;

      const mockPlatformTeam = {
        id: 'team-id',
        uid: 'team-uid',
      } as unknown as PlatformTeams;

      const text = 'This is a valid ticket request';

      // Mock settings and AI service
      (mockSettingsCore.getValue as Mock).mockImplementation((key) => {
        if (key === 'ai_model') return Promise.resolve('gpt-4');
        return Promise.resolve(null);
      });
      (mockAiService.setActiveProvider as Mock).mockReturnValue(mockAiService);
      (mockAiService.setActiveModel as Mock).mockReturnValue(mockAiService);
      (mockAiService.isValidTicket as Mock).mockResolvedValue(true);

      // Act
      const result = await service.aiCheckIsValidTicket(
        text,
        mockPlatformTeam,
        mockInstallation,
      );

      // Assert
      expect(mockSettingsCore.getValue).toHaveBeenCalledWith('ai_model', {
        platformTeam: mockPlatformTeam,
        workspace: mockInstallation,
      });
      expect(mockAiService.setActiveProvider).toHaveBeenCalledWith('openai');
      expect(mockAiService.setActiveModel).toHaveBeenCalledWith(
        'o3-mini-2025-01-31',
      );
      expect(mockAiService.isValidTicket).toHaveBeenCalledWith(
        text,
        mockPlatformTeam.id,
      );
      expect(result).toBe(true);
    });

    it('should return false when AI detects an invalid ticket', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: {
          id: 'org-123',
        },
      } as unknown as Installations;

      const mockPlatformTeam = {
        id: 'team-id',
        uid: 'team-uid',
      } as unknown as PlatformTeams;

      const text = 'This is not a valid ticket request';

      // Mock settings and AI service
      (mockSettingsCore.getValue as Mock).mockImplementation((key) => {
        if (key === 'ai_model') return Promise.resolve('gpt-4');
        return Promise.resolve(null);
      });
      (mockAiService.setActiveProvider as Mock).mockReturnValue(mockAiService);
      (mockAiService.setActiveModel as Mock).mockReturnValue(mockAiService);
      (mockAiService.isValidTicket as Mock).mockResolvedValue(false);

      // Act
      const result = await service.aiCheckIsValidTicket(
        text,
        mockPlatformTeam,
        mockInstallation,
      );

      // Assert
      expect(mockSettingsCore.getValue).toHaveBeenCalledWith('ai_model', {
        platformTeam: mockPlatformTeam,
        workspace: mockInstallation,
      });
      expect(mockAiService.setActiveProvider).toHaveBeenCalledWith('openai');
      expect(mockAiService.setActiveModel).toHaveBeenCalledWith(
        'o3-mini-2025-01-31',
      );
      expect(mockAiService.isValidTicket).toHaveBeenCalledWith(
        text,
        mockPlatformTeam.id,
      );
      expect(result).toBe(false);
    });
  });

  describe('aiGetTicketDetails', () => {
    it('should get ticket details using AI', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: {
          id: 'org-123',
        },
      } as unknown as Installations;

      const mockPlatformTeam = {
        id: 'team-id',
        uid: 'team-uid',
      } as unknown as PlatformTeams;

      const event = {
        type: 'message',
        text: 'This is a test message',
        user: 'U12345',
        channel: 'C12345',
        team: 'T12345',
        ts: '**********.123456',
      };

      const mockPriorities = [
        { id: 'priority-1', name: 'Low', description: 'Low priority ticket' },
        {
          id: 'priority-2',
          name: 'Medium',
          description: 'Medium priority ticket',
        },
        { id: 'priority-3', name: 'High', description: 'High priority ticket' },
      ];

      // Mock the platformApiProvider
      (mockPlatformApiProvider.getPrioritiesForTeam as Mock).mockResolvedValue(
        mockPriorities,
      );

      // Mock the settingsCore
      (mockSettingsCore.getValue as Mock).mockResolvedValue('gpt-4');

      // Mock the aiService
      (mockAiService.setActiveProvider as Mock).mockReturnValue(mockAiService);
      (mockAiService.setActiveModel as Mock).mockReturnValue(mockAiService);
      (mockAiService.loadTeamPrompts as Mock).mockResolvedValue(undefined);
      (mockAiService.detectUrgency as Mock).mockResolvedValue('High');
      (mockAiService.generateTicketTitle as Mock).mockResolvedValue(
        'Test Ticket Title',
      );
      (mockAiService.generateTicketDescription as Mock).mockResolvedValue(
        'Test ticket description',
      );

      // Act
      const result = await service.aiGetTicketDetails(
        mockInstallation,
        mockPlatformTeam,
        event as any,
      );

      // Assert
      expect(mockPlatformApiProvider.getPrioritiesForTeam).toHaveBeenCalledWith(
        mockInstallation,
        mockPlatformTeam.uid,
      );
      expect(mockSettingsCore.getValue).toHaveBeenCalledWith('ai_model', {
        platformTeam: mockPlatformTeam,
        workspace: mockInstallation,
      });
      expect(mockAiService.loadTeamPrompts).toHaveBeenCalledWith(
        mockPlatformTeam.id,
        mockInstallation.id,
        mockInstallation.organization.id,
      );
      expect(mockAiService.detectUrgency).toHaveBeenCalledWith(
        event.text,
        expect.any(Array),
      );
      expect(mockAiService.generateTicketTitle).toHaveBeenCalledWith(
        event.text,
        mockPlatformTeam.id,
      );
      expect(mockAiService.generateTicketDescription).toHaveBeenCalledWith(
        event.text,
        mockPlatformTeam.id,
      );
      expect(result).toEqual({
        urgency: 'High',
        title: 'Test Ticket Title',
        description: 'Test ticket description',
        priorityId: 'priority-3',
        parsedDescription: 'Test ticket description',
      });
    });

    it('should handle errors in AI service calls', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: {
          id: 'org-123',
        },
      } as unknown as Installations;

      const mockPlatformTeam = {
        id: 'team-id',
        uid: 'team-uid',
      } as unknown as PlatformTeams;

      const event = {
        type: 'message',
        text: 'This is a test message',
        user: 'U12345',
        channel: 'C12345',
        team: 'T12345',
        ts: '**********.123456',
      };

      const mockPriorities = [
        { id: 'priority-1', name: 'Low', description: 'Low priority ticket' },
        {
          id: 'priority-2',
          name: 'Medium',
          description: 'Medium priority ticket',
        },
        { id: 'priority-3', name: 'High', description: 'High priority ticket' },
      ];

      // Mock the platformApiProvider
      (mockPlatformApiProvider.getPrioritiesForTeam as Mock).mockResolvedValue(
        mockPriorities,
      );

      // Mock the settingsCore
      (mockSettingsCore.getValue as Mock).mockResolvedValue('gpt-4');

      // Mock the aiService
      (mockAiService.setActiveProvider as Mock).mockReturnValue(mockAiService);
      (mockAiService.setActiveModel as Mock).mockReturnValue(mockAiService);
      (mockAiService.loadTeamPrompts as Mock).mockResolvedValue(undefined);
      (mockAiService.detectUrgency as Mock).mockRejectedValue(
        new Error('AI error'),
      );
      (mockAiService.generateTicketTitle as Mock).mockRejectedValue(
        new Error('AI error'),
      );
      (mockAiService.generateTicketDescription as Mock).mockRejectedValue(
        new Error('AI error'),
      );

      // Act
      const result = await service.aiGetTicketDetails(
        mockInstallation,
        mockPlatformTeam,
        event as any,
      );

      // Assert
      expect(result).toEqual(
        expect.objectContaining({
          urgency: 'Medium',
          title: event.text,
          description: event.text,
          parsedDescription: event.text,
        }),
      );
    });

    it('should use default priorities if none are returned from the API', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: {
          id: 'org-123',
        },
      } as unknown as Installations;

      const mockPlatformTeam = {
        id: 'team-id',
        uid: 'team-uid',
      } as unknown as PlatformTeams;

      const event = {
        type: 'message',
        text: 'This is a test message',
        user: 'U12345',
        channel: 'C12345',
        team: 'T12345',
        ts: '**********.123456',
      };

      // Mock the platformApiProvider to return empty priorities
      (mockPlatformApiProvider.getPrioritiesForTeam as Mock).mockResolvedValue(
        [],
      );

      // Mock the settingsCore
      (mockSettingsCore.getValue as Mock).mockResolvedValue('gpt-4');

      // Mock the aiService
      (mockAiService.setActiveProvider as Mock).mockReturnValue(mockAiService);
      (mockAiService.setActiveModel as Mock).mockReturnValue(mockAiService);
      (mockAiService.loadTeamPrompts as Mock).mockResolvedValue(undefined);
      (mockAiService.detectUrgency as Mock).mockResolvedValue('High');
      (mockAiService.generateTicketTitle as Mock).mockResolvedValue(
        'Test Ticket Title',
      );
      (mockAiService.generateTicketDescription as Mock).mockResolvedValue(
        'Test ticket description',
      );

      // Act
      const result = await service.aiGetTicketDetails(
        mockInstallation,
        mockPlatformTeam,
        event as any,
      );

      // Assert
      expect(result).toEqual({
        urgency: 'High',
        title: 'Test Ticket Title',
        description: 'Test ticket description',
        priorityId: undefined,
        parsedDescription: 'Test ticket description',
      });
      expect(mockAiService.detectUrgency).toHaveBeenCalledWith(
        event.text,
        expect.arrayContaining([
          { name: 'Low', description: 'Low priority ticket' },
          { name: 'Medium', description: 'Medium priority ticket' },
          { name: 'High', description: 'High priority ticket' },
          { name: 'Urgent', description: 'Urgent priority ticket' },
        ]),
      );
    });
  });

  describe('createTicketForTeam', () => {
    it('should create a ticket for a team', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
        organization: {
          id: 'org-123',
        },
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        platformTeamsToChannelMappings: [
          {
            relationshipType: TeamRelationshipType.PRIMARY,
            platformTeam: { id: 'team-id', uid: 'team-uid' },
          },
        ],
      } as unknown as Channels;

      const mockCustomer = {
        id: 'customer-id',
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
        displayName: 'Customer Name',
        realName: 'Real Customer Name',
      } as unknown as CustomerContacts;

      const event = {
        type: 'message',
        text: 'This is a test message',
        user: 'U12345',
        channel: 'C12345',
        team: 'T12345',
        ts: '**********.123456',
      };

      // Mock the settingsCore
      (mockSettingsCore.getValue as Mock).mockResolvedValue('gpt-4');

      // Mock the aiService
      (mockAiService.setActiveProvider as Mock).mockReturnValue(mockAiService);
      (mockAiService.setActiveModel as Mock).mockReturnValue(mockAiService);
      (mockAiService.loadTeamPrompts as Mock).mockResolvedValue(undefined);
      (mockAiService.detectUrgency as Mock).mockResolvedValue('High');
      (mockAiService.generateTicketTitle as Mock).mockResolvedValue(
        'Test Ticket Title',
      );
      (mockAiService.generateTicketDescription as Mock).mockResolvedValue(
        'Test ticket description',
      );

      // Mock the platformApiProvider
      (mockPlatformApiProvider.getPrioritiesForTeam as Mock).mockResolvedValue([
        { id: 'priority-3', name: 'High', description: 'High priority ticket' },
      ]);
      (mockPlatformApiProvider.createNewTicket as Mock).mockResolvedValue({
        id: 'ticket-id',
        title: 'Test Ticket Title',
      });

      // Mock the slackWebAPIService
      const mockSlackWebAPIService = {
        getPermalink: vi.fn().mockResolvedValue({
          ok: true,
          permalink: 'https://slack.com/permalink',
        }),
      };

      // Use property accessor to avoid readonly property error
      Object.defineProperty(service, 'slackWebAPIService', {
        value: mockSlackWebAPIService,
      });

      // Mock the subGroupsMapsRepository
      const mockSubGroupsMapsRepository = {
        findAll: vi.fn().mockResolvedValue([]),
      };

      // Use property accessor to avoid readonly property error
      Object.defineProperty(service, 'subGroupsMapsRepository', {
        value: mockSubGroupsMapsRepository,
      });

      // Act
      const result = await service.createTicketForTeam(
        mockInstallation,
        mockChannel,
        mockCustomer,
        event as any,
      );

      // Assert
      expect(mockPlatformApiProvider.createNewTicket).toHaveBeenCalledWith(
        mockInstallation,
        expect.objectContaining({
          requestorEmail: mockCustomer.slackProfileEmail,
          title: 'Test Ticket Title',
          teamId: 'team-uid',
          performRouting: true,
          metadata: {
            slack: {
              channel: event.channel,
              ts: event.ts,
              user: event.user,
            },
          },
        }),
      );
      expect(mockSlackWebAPIService.getPermalink).toHaveBeenCalledWith(
        mockInstallation.botToken,
        { channel: event.channel, message_ts: event.ts },
      );
      expect(result).toEqual(
        expect.objectContaining({
          ticket: { id: 'ticket-id', title: 'Test Ticket Title' },
          messagePermalink: {
            ok: true,
            permalink: 'https://slack.com/permalink',
          },
          ts: event.ts,
        }),
      );
    });

    it('should throw an error if the message is not a top level message', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        platformTeamsToChannelMappings: [
          {
            relationshipType: TeamRelationshipType.PRIMARY,
            platformTeam: { id: 'team-id', uid: 'team-uid' },
          },
        ],
      } as unknown as Channels;

      const mockCustomer = {
        id: 'customer-id',
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
      } as unknown as CustomerContacts;

      const event = {
        type: 'message',
        // Missing text property
        user: 'U12345',
        channel: 'C12345',
      };

      // Act & Assert
      await expect(
        service.createTicketForTeam(
          mockInstallation,
          mockChannel,
          mockCustomer,
          event as any,
        ),
      ).rejects.toThrow('Message is not a top level message');
    });

    it('should throw an error if no primary platform team mapping is found', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        platformTeamsToChannelMappings: [], // Empty mappings
      } as unknown as Channels;

      const mockCustomer = {
        id: 'customer-id',
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
      } as unknown as CustomerContacts;

      const event = {
        type: 'message',
        text: 'This is a test message',
        user: 'U12345',
        channel: 'C12345',
        team: 'T12345',
        ts: '**********.123456',
      };

      // Act & Assert
      await expect(
        service.createTicketForTeam(
          mockInstallation,
          mockChannel,
          mockCustomer,
          event as any,
        ),
      ).rejects.toThrow('No primary platform team mapping found for channel!');
    });
  });
});
