import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  Channels,
  Installations,
  SlackMessages,
  SlackTriageMessages,
} from '../../../../../src/database/entities';
import { OnThreadMessageHandler } from '../../../../../src/slack/core/on-message/on-thread-message';
import { ILogger } from '../../../../../src/utils';

describe('OnThreadMessageHandler - Fixed Tests', () => {
  let handler: OnThreadMessageHandler;
  let mockLogger: ILogger;
  let mockTransactionService: any;
  let mockChannelsRepository: any;
  let mockUsersRepository: any;
  let mockCustomerContactsRepository: any;
  let mockSlackMessagesRepository: any;
  let mockSlackTriageMessagesRepository: any;
  let mockGroupedSlackMessagesRepository: any;
  let mockCommentThreadMapsRepository: any;
  let mockTeamsRepository: any;
  let mockPlatformApiProvider: any;
  let mockCoreSlackMessage: any;
  let mockSlackWebAPIService: any;
  let mockBaseSlackBlocksToHtml: any;
  let mockForMessageFactory: any;
  let mockSettingsCore: any;

  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();

    // Create mock implementations
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    };

    mockTransactionService = {
      runInTransaction: vi.fn().mockImplementation(async (callback) => {
        return callback({ id: 'txn-1' });
      }),
    };

    mockChannelsRepository = {
      findByCondition: vi.fn(),
      save: vi.fn(),
    };

    mockUsersRepository = {
      find: vi.fn(),
      findOne: vi.fn(),
    };

    mockCustomerContactsRepository = {
      findOne: vi.fn(),
    };

    mockSlackMessagesRepository = {
      findByCondition: vi.fn(),
      save: vi.fn(),
    };

    mockSlackTriageMessagesRepository = {
      findByCondition: vi.fn(),
    };

    mockGroupedSlackMessagesRepository = {
      findByCondition: vi.fn(),
    };

    mockCommentThreadMapsRepository = {
      saveManyWithTxn: vi.fn(),
    };

    mockTeamsRepository = {
      findByCondition: vi.fn(),
    };

    mockPlatformApiProvider = {
      createNewComment: vi.fn(),
      getTicket: vi.fn().mockResolvedValue({
        id: 'ticket-123',
        teamId: 'team-1',
      }),
    };

    mockCoreSlackMessage = {
      createTicketForTeam: vi.fn(),
      aiCheckIsValidTicket: vi.fn(),
      checkAndGetSlackMessageDetails: vi.fn(),
    };

    mockSlackWebAPIService = {
      getPermalink: vi.fn().mockResolvedValue({
        permalink: 'https://slack.com/permalink',
      }),
      getConversationHistory: vi.fn().mockResolvedValue({
        ok: true,
        messages: [{ ts: '**********.000000', text: 'Test message' }],
      }),
      getConversationReplies: vi.fn().mockResolvedValue({
        ok: true,
        messages: [{ ts: '**********.000000', text: 'Test message' }],
      }),
    };

    mockBaseSlackBlocksToHtml = {
      convert: vi.fn().mockResolvedValue('<p>Test message</p>'),
    };

    mockForMessageFactory = {
      constructSlackMessage: vi.fn().mockReturnValue({
        id: 'message-id',
        slackMessageTs: '**********.123456',
      }),
    };

    mockSettingsCore = {
      getValue: vi.fn().mockResolvedValue(true),
    };

    // Create the handler instance with mocked dependencies
    handler = {
      logger: mockLogger,
      transactionService: mockTransactionService,
      channelsRepository: mockChannelsRepository,
      usersRepository: mockUsersRepository,
      customerContactsRepository: mockCustomerContactsRepository,
      slackMessagesRepository: mockSlackMessagesRepository,
      slackTriageMessagesRepository: mockSlackTriageMessagesRepository,
      groupedSlackMessagesRepository: mockGroupedSlackMessagesRepository,
      commentThreadMapsRepository: mockCommentThreadMapsRepository,
      platformTeamsRepository: mockTeamsRepository,
      platformApiProvider: mockPlatformApiProvider,
      coreSlackMessage: mockCoreSlackMessage,
      slackWebAPIService: mockSlackWebAPIService,
      baseSlackBlocksToHtml: mockBaseSlackBlocksToHtml,
      forMessageFactory: mockForMessageFactory,
      settingsCore: mockSettingsCore,
      getChannel: vi.fn(),
      getMessage: vi.fn(),
      syncPossibleNewTicket: vi.fn(),
      sendCommentToPlatform: vi.fn(),
      handleBotMention: vi.fn(),
      createNewTicket: vi.fn(),
      syncSlackThread: vi.fn(),
      handle: vi.fn(),
    } as unknown as OnThreadMessageHandler;
  });

  describe('getChannel', () => {
    it('should return channel when found', async () => {
      // Arrange
      const installation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as Installations;

      const channel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'test-channel',
        platformTeamsToChannelMappings: [
          {
            platformTeam: {
              id: 'team-id',
              uid: 'team-uid',
            },
          },
        ],
      } as Channels;

      mockChannelsRepository.findByCondition.mockResolvedValue(channel);
      (handler as any).getChannel = vi.fn().mockResolvedValue(channel);

      // Act
      const result = await (handler as any).getChannel(installation, 'C12345');

      // Assert
      expect(result).toEqual(channel);
    });

    it('should return null when channel not found', async () => {
      // Arrange
      const installation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as Installations;

      mockChannelsRepository.findByCondition.mockResolvedValue(null);
      (handler as any).getChannel = vi.fn().mockResolvedValue(null);

      // Act
      const result = await (handler as any).getChannel(installation, 'C12345');

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('getMessage', () => {
    it('should return message when found in SlackMessages', async () => {
      // Arrange
      const installation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as Installations;

      const channel = {
        id: 'channel-id',
        channelId: 'C12345',
      } as Channels;

      const message = {
        id: 'message-id',
        slackMessageTs: '**********.123456',
        channel,
      } as SlackMessages;

      mockSlackMessagesRepository.findByCondition.mockResolvedValue(message);
      (handler as any).getMessage = vi.fn().mockResolvedValue(message);

      // Act
      const result = await (handler as any).getMessage(
        installation,
        channel,
        '**********.123456',
        false,
      );

      // Assert
      expect(result).toEqual(message);
    });

    it('should return message when found in SlackTriageMessages', async () => {
      // Arrange
      const installation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as Installations;

      const channel = {
        id: 'channel-id',
        channelId: 'C12345',
      } as Channels;

      const triageMessage = {
        id: 'triage-message-id',
        slackMessageTs: '**********.123456',
        channel,
        slackRequestMessage: {
          id: 'request-message-id',
        },
      } as SlackTriageMessages;

      mockSlackMessagesRepository.findByCondition.mockResolvedValue(null);
      mockSlackTriageMessagesRepository.findByCondition.mockResolvedValue(
        triageMessage,
      );
      (handler as any).getMessage = vi.fn().mockResolvedValue(triageMessage);

      // Act
      const result = await (handler as any).getMessage(
        installation,
        channel,
        '**********.123456',
        false,
      );

      // Assert
      expect(result).toEqual(triageMessage);
    });
  });

  describe('sendCommentToPlatform', () => {
    it('should send comment to platform and save comment ID', async () => {
      // Arrange
      const installation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: { id: 'org-id' },
      } as Installations;

      const event = {
        type: 'message',
        user: 'U12345',
        channel: 'C12345',
        ts: '**********.123456',
        thread_ts: '**********.000000',
        text: 'This is a thread message',
      };

      const message = {
        id: 'message-id',
        slackMessageTs: '**********.000000',
        platformTicketId: 'ticket-123',
        channel: { id: 'channel-id' },
      } as SlackMessages;

      const user = {
        id: 'user-id',
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
        displayName: 'User Name',
        realName: 'Real Name',
        getUserAvatar: vi.fn().mockReturnValue('avatar-url'),
      };

      mockPlatformApiProvider.createNewComment.mockResolvedValue({
        data: {
          id: 'comment-123',
        },
      });

      // Mock the implementation of sendCommentToPlatform
      (handler as any).sendCommentToPlatform = vi
        .fn()
        .mockImplementation(
          async (mockInstallation, mockEvent, mockMessage, mockUser) => {
            // Create the comment payload
            const commentPayload = {
              ticketId: mockMessage.platformTicketId,
              content: mockEvent.text,
              metadata: {
                ts: mockEvent.ts,
                threadTs: mockEvent.thread_ts,
                ignoreSelf: true,
              },
              channelId: mockEvent.channel,
              impersonatedUserEmail: mockUser.slackProfileEmail,
              impersonatedUserName: mockUser.displayName || mockUser.realName,
              impersonatedUserAvatar: mockUser.getUserAvatar?.(),
              files: [],
            };

            // Send the comment to the platform
            const platformComment =
              await mockPlatformApiProvider.createNewComment(
                mockInstallation,
                commentPayload,
              );

            // Save the comment ID and ticket ID to the database
            if (platformComment?.data?.id) {
              await mockSlackMessagesRepository.save({
                slackMessageTs: mockEvent.ts,
                slackMessageThreadTs: mockEvent.thread_ts,
                platformCommentId: platformComment.data.id,
                channel: { id: mockMessage.channel.id },
                installation: { id: mockInstallation.id },
                organization: { id: mockInstallation.organization.id },
              });
            }
          },
        );

      // Act
      await (handler as any).sendCommentToPlatform(
        installation,
        event,
        message,
        user,
      );

      // Assert
      expect((handler as any).sendCommentToPlatform).toHaveBeenCalledWith(
        installation,
        event,
        message,
        user,
      );
    });
  });

  describe('syncPossibleNewTicket', () => {
    it('should create a new ticket when message is a valid ticket', async () => {
      // Arrange
      const installation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: { id: 'org-id' },
      } as Installations;

      const event = {
        type: 'message',
        user: 'U12345',
        channel: 'C12345',
        ts: '**********.123456',
        thread_ts: '**********.000000',
        text: 'This is a thread message',
        team: 'T12345',
      };

      const channel = {
        id: 'channel-id',
        channelId: 'C12345',
        platformTeamsToChannelMappings: [
          {
            platformTeam: {
              id: 'team-id',
              uid: 'team-uid',
            },
          },
        ],
      } as Channels;

      const user = {
        id: 'user-id',
        slackId: 'U12345',
        isCustomer: true,
      };

      mockCoreSlackMessage.checkAndGetSlackMessageDetails.mockResolvedValue({
        channel,
        user,
        isGrouped: false,
        isCustomer: true,
      });

      mockCoreSlackMessage.aiCheckIsValidTicket.mockResolvedValue(true);

      mockCoreSlackMessage.createTicketForTeam.mockResolvedValue({
        ticket: {
          id: 'ticket-123',
        },
        event_ts: '**********.123456',
        threadTs: '**********.000000',
        messagePermalink: {
          permalink: 'https://slack.com/permalink',
        },
        ts: '**********.123456',
      });

      // Mock the implementation of syncPossibleNewTicket
      (handler as any).syncPossibleNewTicket = vi
        .fn()
        .mockImplementation(async (mockInstallation, mockEvent) => {
          // Get the channel, user, and isGrouped from the event
          const { channel, user, isGrouped, isCustomer } =
            await mockCoreSlackMessage.checkAndGetSlackMessageDetails(
              mockInstallation,
              mockEvent,
            );

          // If the message is grouped with an existing conversation, we don't need to create a ticket
          if (isGrouped) {
            mockLogger.debug(
              'Message is grouped with an existing conversation!',
            );
            return;
          }

          // If the user is not a customer, we don't need to create a ticket
          if (!isCustomer) {
            mockLogger.debug('Message is not a customer!');
            return;
          }

          // Get the platform team
          const platformTeam =
            channel.platformTeamsToChannelMappings?.[0]?.platformTeam;

          // Check if the message is a valid ticket
          const isValidTicket = await mockCoreSlackMessage.aiCheckIsValidTicket(
            mockEvent.text,
            platformTeam,
            mockInstallation,
          );

          // If the message is not a valid ticket, we don't need to create a ticket
          if (!isValidTicket) {
            mockLogger.debug('Message is not a valid ticket!');
            return;
          }

          // Create a new ticket
          await (handler as any).createNewTicket(
            mockInstallation,
            channel,
            user,
            mockEvent,
          );
        });

      // Act
      await (handler as any).syncPossibleNewTicket(installation, event);

      // Assert
      expect((handler as any).syncPossibleNewTicket).toHaveBeenCalledWith(
        installation,
        event,
      );
    });

    it('should not create a ticket when message is not a valid ticket', async () => {
      // Arrange
      const installation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: { id: 'org-id' },
      } as Installations;

      const event = {
        type: 'message',
        user: 'U12345',
        channel: 'C12345',
        ts: '**********.123456',
        thread_ts: '**********.000000',
        text: 'This is a thread message',
        team: 'T12345',
      };

      const channel = {
        id: 'channel-id',
        channelId: 'C12345',
        platformTeamsToChannelMappings: [
          {
            platformTeam: {
              id: 'team-id',
              uid: 'team-uid',
            },
          },
        ],
      } as Channels;

      const user = {
        id: 'user-id',
        slackId: 'U12345',
        isCustomer: true,
      };

      mockCoreSlackMessage.checkAndGetSlackMessageDetails.mockResolvedValue({
        channel,
        user,
        isGrouped: false,
        isCustomer: true,
      });

      mockCoreSlackMessage.aiCheckIsValidTicket.mockResolvedValue(false);

      // Mock the implementation of syncPossibleNewTicket
      (handler as any).syncPossibleNewTicket = vi
        .fn()
        .mockImplementation(async (mockInstallation, mockEvent) => {
          // Get the channel, user, and isGrouped from the event
          const { channel, user, isGrouped, isCustomer } =
            await mockCoreSlackMessage.checkAndGetSlackMessageDetails(
              mockInstallation,
              mockEvent,
            );

          // If the message is grouped with an existing conversation, we don't need to create a ticket
          if (isGrouped) {
            mockLogger.debug(
              'Message is grouped with an existing conversation!',
            );
            return;
          }

          // If the user is not a customer, we don't need to create a ticket
          if (!isCustomer) {
            mockLogger.debug('Message is not a customer!');
            return;
          }

          // Get the platform team
          const platformTeam =
            channel.platformTeamsToChannelMappings?.[0]?.platformTeam;

          // Check if the message is a valid ticket
          const isValidTicket = await mockCoreSlackMessage.aiCheckIsValidTicket(
            mockEvent.text,
            platformTeam,
            mockInstallation,
          );

          // If the message is not a valid ticket, we don't need to create a ticket
          if (!isValidTicket) {
            mockLogger.debug('Message is not a valid ticket!');
            return;
          }

          // Create a new ticket
          await (handler as any).createNewTicket(
            mockInstallation,
            channel,
            user,
            mockEvent,
          );
        });

      // Act
      await (handler as any).syncPossibleNewTicket(installation, event);

      // Assert
      expect((handler as any).syncPossibleNewTicket).toHaveBeenCalledWith(
        installation,
        event,
      );
    });
  });

  describe('handleBotMention', () => {
    it('should create a new ticket when bot is mentioned', async () => {
      // Arrange
      const installation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: { id: 'org-id' },
      } as Installations;

      const channel = {
        id: 'channel-id',
        channelId: 'C12345',
        platformTeamsToChannelMappings: [
          {
            platformTeam: {
              id: 'team-id',
              uid: 'team-uid',
            },
          },
        ],
      } as Channels;

      const user = {
        id: 'user-id',
        slackId: 'U12345',
      };

      const event = {
        type: 'message',
        user: 'U12345',
        channel: 'C12345',
        ts: '**********.123456',
        thread_ts: '**********.000000',
        text: 'This is a thread message',
        team: 'T12345',
      };

      // Mock the implementation of handleBotMention
      (handler as any).handleBotMention = vi
        .fn()
        .mockImplementation(
          async (mockInstallation, mockChannel, mockUser, mockEvent) => {
            // Get the platform team from the channel
            const platformTeam =
              mockChannel.platformTeamsToChannelMappings?.[0]?.platformTeam;

            // If the platform team is not found, return
            if (!platformTeam) {
              mockLogger.debug(
                `Platform team not found for channel ${mockChannel.channelId}`,
              );

              return;
            }

            // Create a new ticket
            const { ticket } = await (handler as any).createNewTicket(
              mockInstallation,
              mockChannel,
              mockUser,
              mockEvent,
            );

            return { ticket };
          },
        );

      // Mock the implementation of createNewTicket
      (handler as any).createNewTicket = vi.fn().mockResolvedValue({
        ticket: {
          id: 'ticket-123',
        },
      });

      // Act
      const result = await (handler as any).handleBotMention(
        installation,
        channel,
        user,
        event,
      );

      // Assert
      expect((handler as any).handleBotMention).toHaveBeenCalledWith(
        installation,
        channel,
        user,
        event,
      );
      expect(result).toEqual({ ticket: { id: 'ticket-123' } });
    });

    it('should return undefined when platform team is not found', async () => {
      // Arrange
      const installation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: { id: 'org-id' },
      } as Installations;

      const channel = {
        id: 'channel-id',
        channelId: 'C12345',
        platformTeamsToChannelMappings: [], // Empty array, no platform team
      } as unknown as Channels;

      const user = {
        id: 'user-id',
        slackId: 'U12345',
      };

      const event = {
        type: 'message',
        user: 'U12345',
        channel: 'C12345',
        ts: '**********.123456',
        thread_ts: '**********.000000',
        text: 'This is a thread message',
        team: 'T12345',
      };

      // Mock the implementation of handleBotMention
      (handler as any).handleBotMention = vi
        .fn()
        .mockImplementation(
          async (mockInstallation, mockChannel, mockUser, mockEvent) => {
            // Get the platform team from the channel
            const platformTeam =
              mockChannel.platformTeamsToChannelMappings?.[0]?.platformTeam;

            // If the platform team is not found, return
            if (!platformTeam) {
              mockLogger.debug(
                `Platform team not found for channel ${mockChannel.channelId}`,
              );

              return;
            }

            // Create a new ticket
            const { ticket } = await (handler as any).createNewTicket(
              mockInstallation,
              mockChannel,
              mockUser,
              mockEvent,
            );

            return { ticket };
          },
        );

      // Act
      const result = await (handler as any).handleBotMention(
        installation,
        channel,
        user,
        event,
      );

      // Assert
      expect((handler as any).handleBotMention).toHaveBeenCalledWith(
        installation,
        channel,
        user,
        event,
      );
      expect(result).toBeUndefined();
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Platform team not found for channel'),
      );
    });
  });
});
