import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  CommentThreadMappings,
  Installations,
  SlackMessages,
} from '../../../../../src/database/entities';
import { TeamRelationshipType } from '../../../../../src/database/entities/mappings/platform-teams-to-channel-mappings.entity';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { SettingsCore } from '../../../../../src/slack/core/management';
import { CoreSlackMessage } from '../../../../../src/slack/core/on-message/core-slack-message';
import { OnMessageHandler } from '../../../../../src/slack/core/on-message/on-slack-message';
import { OnThreadMessageHandler } from '../../../../../src/slack/core/on-message/on-thread-message';
import { SlackEventMap } from '../../../../../src/slack/event-handlers';
import { ILogger } from '../../../../../src/utils';
import * as CommonUtils from '../../../../../src/utils/common';
import { BaseSlackBlocksToHtml } from '../../../../../src/utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';

describe('OnMessageHandler', () => {
  let handler: OnMessageHandler;
  let mockLogger: ILogger;
  let mockSlackMessagesRepository: Repository<SlackMessages>;
  let mockCommentThreadMappingsRepository: Repository<CommentThreadMappings>;
  let mockPlatformApiProvider: ThenaPlatformApiProvider;
  let mockCoreSlackMessage: CoreSlackMessage;
  let mockBaseSlackBlocksToHtml: BaseSlackBlocksToHtml;
  let mockOnThreadMessageHandler: OnThreadMessageHandler;
  let mockSettingsCore: SettingsCore;

  beforeEach(() => {
    // Create mock implementations
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    } as unknown as ILogger;

    mockSlackMessagesRepository = {
      findOne: vi.fn(),
      save: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
    } as unknown as Repository<SlackMessages>;

    mockCommentThreadMappingsRepository = {
      findOne: vi.fn(),
      save: vi.fn(),
      create: vi.fn(),
    } as unknown as Repository<CommentThreadMappings>;

    mockPlatformApiProvider = {
      createNewTicket: vi.fn(),
      createNewComment: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    mockCoreSlackMessage = {
      checkAndGetSlackMessageDetails: vi.fn(),
      aiCheckIsValidTicket: vi.fn(),
      ticketFromSourceSlack: vi.fn(),
      createTicketForTeam: vi.fn(),
      getChannel: vi.fn(),
      getUser: vi.fn(),
      shouldGroupWithExistingConversation: vi.fn(),
    } as unknown as CoreSlackMessage;

    mockBaseSlackBlocksToHtml = {
      initialize: vi.fn(),
      convert: vi.fn(),
    } as unknown as BaseSlackBlocksToHtml;

    mockOnThreadMessageHandler = {
      handle: vi.fn(),
    } as unknown as OnThreadMessageHandler;

    mockSettingsCore = {
      getValue: vi.fn(),
    } as unknown as SettingsCore;

    // Mock CommonUtils.getSlackMentions function
    vi.spyOn(CommonUtils, 'getSlackMentions').mockImplementation(() => ({
      mentionedUsers: [],
      mentionedUserGroups: [],
      botMentioned: false,
    }));

    // Create the handler instance
    handler = new OnMessageHandler(
      mockLogger,
      mockSlackMessagesRepository,
      mockCommentThreadMappingsRepository,
      mockPlatformApiProvider,
      mockCoreSlackMessage,
      mockBaseSlackBlocksToHtml,
      mockOnThreadMessageHandler,
      mockSettingsCore,
    );
  });

  describe('onMessage', () => {
    it('should handle thread messages by delegating to onThreadMessageHandler', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      } as unknown as Installations;

      const event = {
        event: {
          type: 'message',
          user: 'U12345',
          channel: 'C12345',
          ts: '**********.123456',
          text: 'This is a thread message',
          thread_ts: '**********.000000', // This makes it a thread message
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      // Act
      await handler.onMessage(event);

      // Assert
      expect(mockOnThreadMessageHandler.handle).toHaveBeenCalledWith(event);
      expect(
        mockCoreSlackMessage.checkAndGetSlackMessageDetails,
      ).not.toHaveBeenCalled();
    });

    it('should handle top-level messages by calling handleNewMessage', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
        organization: { id: 'org-1' },
      } as unknown as Installations;

      const event = {
        event: {
          type: 'message',
          user: 'U12345',
          channel: 'C12345',
          team: 'T12345',
          ts: '**********.123456',
          text: 'This is a top-level message',
          // No thread_ts makes it a top-level message
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      const channelWithTeam = {
        id: 'channel-id',
        channelId: 'C12345',
        platformTeamsToChannelMappings: [
          {
            relationshipType: TeamRelationshipType.PRIMARY,
            platformTeam: { 
              id: 'team-1', 
              uid: 'team-uid-1',
              installedBy: null,
              installation: null,
              organization: null,
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          },
        ],
      };

      // Mock the checkAndGetSlackMessageDetails method
      (
        mockCoreSlackMessage.checkAndGetSlackMessageDetails as Mock
      ).mockResolvedValue({
        channel: channelWithTeam,
        user: { id: 'user-id', slackId: 'U12345' },
        isCustomer: true,
        isGrouped: false,
      });

      // Mock getSlackMentions to return no bot mentions
      vi.spyOn(CommonUtils, 'getSlackMentions').mockReturnValue({
        mentionedUsers: [],
        mentionedUserGroups: [],
        botMentioned: false,
      });

      // Mock the getValue method for settings
      (mockSettingsCore.getValue as Mock).mockImplementation((key) => {
        if (key === 'thena_bot_tagging_enabled') return Promise.resolve(false);
        if (key === 'automatic_tickets') return Promise.resolve(true);
        if (key === 'ai_model') return Promise.resolve('gpt-4');
        return Promise.resolve(null);
      });

      // Mock the aiCheckIsValidTicket method
      (mockCoreSlackMessage.aiCheckIsValidTicket as Mock).mockResolvedValue(
        true,
      );

      // Setup the ticketFromSourceSlack mock
      handler['ticketFromSourceSlack'] = vi.fn().mockResolvedValue({
        ticket: { id: 'ticket-id', title: 'Test Ticket' },
        comment: { id: 'comment-id' },
      });

      // Act
      await handler.onMessage(event);

      // Assert - just verify that the message details were fetched
      expect(mockOnThreadMessageHandler.handle).not.toHaveBeenCalled();
      expect(
        mockCoreSlackMessage.checkAndGetSlackMessageDetails,
      ).toHaveBeenCalledWith(mockInstallation, event.event);
    });

    it('should skip processing for bot messages with ignoreSelf metadata', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      } as unknown as Installations;

      const event = {
        event: {
          type: 'message',
          user: 'U12345',
          channel: 'C12345',
          team: 'T12345',
          ts: '**********.123456',
          text: 'This is a bot message',
          bot_profile: {
            id: 'B12345',
            name: 'Test Bot',
          },
          metadata: {
            event_payload: {
              ignoreSelf: true,
            },
          },
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      // Act
      await handler.onMessage(event);

      // Assert
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining(
          'Bot is mentioned in the message! Skipping ticket creation.',
        ),
      );
      expect(mockOnThreadMessageHandler.handle).not.toHaveBeenCalled();
      expect(
        mockCoreSlackMessage.checkAndGetSlackMessageDetails,
      ).not.toHaveBeenCalled();
    });

    it('should skip ticket creation for direct message channels', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      } as unknown as Installations;

      const event = {
        event: {
          type: 'message',
          user: 'U12345',
          channel: 'D12345', // Direct message channel starts with D
          team: 'T12345',
          ts: '**********.123456',
          text: 'This is a direct message',
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      // Act
      await handler.onMessage(event);

      // Assert
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Message received in a direct message channel'),
      );
      expect(
        mockCoreSlackMessage.checkAndGetSlackMessageDetails,
      ).not.toHaveBeenCalled();
    });

    it('should create a ticket when bot is mentioned and setting is enabled', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
        botId: 'B12345',
        organization: { id: 'org-1' },
      } as unknown as Installations;

      const event = {
        event: {
          type: 'message',
          user: 'U12345',
          channel: 'C12345',
          team: 'T12345',
          ts: '**********.123456',
          text: 'Hey <@B12345> I need help!', // Mention the bot
          // No thread_ts makes it a top-level message
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      const channelWithTeam = {
        id: 'channel-id',
        channelId: 'C12345',
        platformTeamsToChannelMappings: [
          {
            relationshipType: TeamRelationshipType.PRIMARY,
            platformTeam: { 
              id: 'team-1', 
              uid: 'team-uid-1',
              installedBy: null,
              installation: null,
              organization: null,
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          },
        ],
      };

      // Mock the checkAndGetSlackMessageDetails method
      (
        mockCoreSlackMessage.checkAndGetSlackMessageDetails as Mock
      ).mockResolvedValue({
        channel: channelWithTeam,
        user: { id: 'user-id', slackId: 'U12345' },
        isCustomer: true,
        isGrouped: false,
      });

      // Mock getSlackMentions to return bot mentioned
      vi.spyOn(CommonUtils, 'getSlackMentions').mockReturnValue({
        mentionedUsers: ['B12345'],
        mentionedUserGroups: [],
        botMentioned: true,
      });

      // Mock the handleNewMessage method
      const handleNewMessageSpy = vi.spyOn(handler as any, 'handleNewMessage');
      handleNewMessageSpy.mockImplementation(async () => {
        // This implementation simulates what happens inside handleNewMessage
        await (mockSettingsCore.getValue as Mock)('thena_bot_tagging_enabled', {
          recursivelyLookup: false,
          workspace: mockInstallation,
          platformTeam:
            channelWithTeam.platformTeamsToChannelMappings[0].platformTeam,
        });

        return {
          ticket: { id: 'ticket-id', title: 'Test Ticket' },
          comment: { id: 'comment-id' },
        };
      });

      // Mock the getValue method for settings
      (mockSettingsCore.getValue as Mock).mockResolvedValue(true);

      // Act
      await handler.onMessage(event);

      // Assert
      expect(handleNewMessageSpy).toHaveBeenCalledWith(
        mockInstallation,
        event.event,
      );
      expect(mockSettingsCore.getValue).toHaveBeenCalledWith(
        'thena_bot_tagging_enabled',
        expect.objectContaining({
          workspace: mockInstallation,
          recursivelyLookup: false,
        }),
      );
    });

    it('should handle errors during processing', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      } as unknown as Installations;

      const event = {
        event: {
          type: 'message',
          user: 'U12345',
          channel: 'C12345',
          team: 'T12345',
          ts: '**********.123456',
          text: 'This is a message that will cause an error',
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      // Mock the checkAndGetSlackMessageDetails method to throw an error
      const error = new Error('Test error');
      (
        mockCoreSlackMessage.checkAndGetSlackMessageDetails as Mock
      ).mockRejectedValue(error);

      // Act
      await handler.onMessage(event);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error handling new message:'),
        error.stack,
      );
    });
  });

  describe('createTicketAndComment', () => {
    it('should create a ticket and comment', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
        organization: {
          id: 'org-1',
          name: 'Test Organization',
        },
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
      };

      const mockCustomer = {
        id: 'customer-id',
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
        displayName: 'Customer Name',
        realName: 'Real Customer Name',
        getUserAvatar: vi.fn().mockReturnValue('https://example.com/avatar.jpg'),
      };

      const mockEvent = {
        type: 'message',
        user: 'U12345',
        channel: 'C12345',
        team: 'T12345',
        ts: '**********.123456',
        text: 'This is a test message',
        blocks: [
          {
            type: 'rich_text',
            elements: [
              {
                type: 'rich_text_section',
                elements: [
                  {
                    type: 'text',
                    text: 'This is a test message'
                  }
                ]
              }
            ]
          }
        ]
      };

      const mockTicket = {
        id: 'ticket-id',
        status: 'Open',
        statusId: 'status-1',
        priority: 'High',
        priorityId: 'priority-1',
        comment: {
          id: 'comment-id'
        }
      };

      const mockMessagePermalink = {
        permalink: 'https://slack.com/permalink',
      };

      // Mock the baseSlackBlocksToHtml methods
      (mockBaseSlackBlocksToHtml.initialize as Mock).mockReturnValue(undefined);
      (mockBaseSlackBlocksToHtml.convert as Mock).mockResolvedValue('<p>This is a test message</p>');

      // Mock the createTicketForTeam method
      (mockCoreSlackMessage.createTicketForTeam as Mock).mockResolvedValue({
        ticket: mockTicket,
        event_ts: mockEvent.ts,
        threadTs: undefined,
        messagePermalink: mockMessagePermalink,
        ts: mockEvent.ts,
      });

      // Mock the slackMessagesRepository.save method
      (mockSlackMessagesRepository.save as Mock).mockResolvedValue({
        id: 'slack-message-id',
        platformTicketId: mockTicket.id,
      });

      // Mock commentThreadMappingsRepository.save method
      (mockCommentThreadMappingsRepository.save as Mock).mockResolvedValue({
        id: 'mapping-id'
      });

      // Access the private method
      const createTicketAndComment =
        handler['createTicketAndComment'].bind(handler);

      // Act
      const result = await createTicketAndComment(
        mockInstallation,
        mockChannel as any,
        mockCustomer as any,
        mockEvent as any,
      );

      // Assert
      expect(mockCoreSlackMessage.createTicketForTeam).toHaveBeenCalledWith(
        mockInstallation,
        mockChannel,
        mockCustomer,
        mockEvent,
        expect.any(Object), // commentPayload
      );

      expect(mockSlackMessagesRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          platformTicketId: mockTicket.id,
          slackEventCreatedAt: expect.any(String),
          slackMessageTs: mockEvent.ts,
          slackMessageThreadTs: undefined,
          slackPermalink: mockMessagePermalink.permalink,
          slackUserId: mockEvent.user,
          metadata: expect.objectContaining({
            customer: {
              email: mockCustomer.slackProfileEmail,
              name: mockCustomer.displayName,
            },
            ticket_details: {
              status: mockTicket.status,
              statusId: mockTicket.statusId,
              priority: mockTicket.priority,
              priorityId: mockTicket.priorityId,
            },
          }),
          channel: { id: mockChannel.id },
          installation: { id: mockInstallation.id },
          organization: { id: mockInstallation.organization.id },
          platformCommentId: mockTicket.comment.id,
        }),
      );

      expect(mockCommentThreadMappingsRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          organization: mockInstallation.organization,
          platformCommentThreadId: mockTicket.comment.id,
          platformCommentTicketId: mockTicket.id,
          slackThreadId: mockEvent.ts,
          slackChannelId: mockEvent.channel,
        }),
      );

      expect(result).toEqual({
        ticket: mockTicket,
        comment: mockTicket.comment,
      });
    });

    it('should throw an error if the message is not a top level message', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
      };

      const mockCustomer = {
        id: 'customer-id',
        slackId: 'U12345',
      };

      const mockEvent = {
        type: 'message',
        // Missing text property
        user: 'U12345',
        channel: 'C12345',
      };

      // Access the private method
      const createTicketAndComment =
        handler['createTicketAndComment'].bind(handler);

      // Act & Assert
      await expect(
        createTicketAndComment(
          mockInstallation,
          mockChannel as any,
          mockCustomer as any,
          mockEvent as any,
        ),
      ).rejects.toThrow('Message is not a top level message');
    });

    it('should throw an error if the message has no team', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
      };

      const mockCustomer = {
        id: 'customer-id',
        slackId: 'U12345',
      };

      const mockEvent = {
        type: 'message',
        text: 'This is a test message',
        user: 'U12345',
        channel: 'C12345',
        // Missing team property
      };

      // Access the private method
      const createTicketAndComment =
        handler['createTicketAndComment'].bind(handler);

      // Act & Assert
      await expect(
        createTicketAndComment(
          mockInstallation,
          mockChannel as any,
          mockCustomer as any,
          mockEvent as any,
        ),
      ).rejects.toThrow('Slack workspace not attached to the event message!');
    });
  });

  describe('createCommentAndMapping', () => {
    it('should create a comment and mapping for a ticket', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: {
          id: 'org-1',
          name: 'Test Organization',
        },
      } as unknown as Installations;

      const mockTicket = {
        id: 'ticket-id',
        status: 'Open',
      };

      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
        displayName: 'User Name',
        realName: 'Real User Name',
        getUserAvatar: vi
          .fn()
          .mockReturnValue('https://example.com/avatar.jpg'),
      };

      const mockEvent = {
        type: 'message',
        text: 'This is a test message',
        ts: '**********.123456',
        user: 'U12345',
        channel: 'C12345',
        team: 'T12345',
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: 'This is a test message',
            },
          },
        ],
      };

      // Mock the BaseSlackBlocksToHtml
      (mockBaseSlackBlocksToHtml.initialize as Mock).mockReturnValue(undefined);
      (mockBaseSlackBlocksToHtml.convert as Mock).mockResolvedValue(
        '<p>This is a test message</p>',
      );

      // Mock the platformApiProvider
      (mockPlatformApiProvider.createNewComment as Mock).mockResolvedValue({
        data: {
          id: 'comment-id',
          content: 'This is a test message',
        },
      });

      // Access the private method
      const createCommentAndMapping =
        handler['createCommentAndMapping'].bind(handler);

      // Act
      const result = await createCommentAndMapping(
        mockInstallation,
        mockTicket as any,
        mockUser as any,
        mockEvent as any,
      );

      // Assert
      expect(mockBaseSlackBlocksToHtml.initialize).toHaveBeenCalledWith(
        mockEvent.blocks,
        mockInstallation,
      );
      expect(mockBaseSlackBlocksToHtml.convert).toHaveBeenCalled();

      expect(mockPlatformApiProvider.createNewComment).toHaveBeenCalledWith(
        mockInstallation,
        expect.objectContaining({
          content: '<p>This is a test message</p>',
          htmlContent: '<p>This is a test message</p>',
          ticketId: mockTicket.id,
          metadata: expect.objectContaining({
            ignoreSelf: true,
            ts: mockEvent.ts,
            threadTs: mockEvent.ts,
          }),
          files: [],
          channelId: mockEvent.channel,
          impersonatedUserAvatar: 'https://example.com/avatar.jpg',
          impersonatedUserEmail: mockUser.slackProfileEmail,
          impersonatedUserName: mockUser.displayName,
        }),
      );

      expect(mockCommentThreadMappingsRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          organization: mockInstallation.organization,
          platformCommentThreadId: 'comment-id',
          platformCommentTicketId: mockTicket.id,
          slackThreadId: mockEvent.ts,
          slackChannelId: mockEvent.channel,
        }),
      );

      expect(result).toEqual({
        data: {
          id: 'comment-id',
          content: 'This is a test message',
        },
      });
    });

    it('should throw an error if the message is not a top level message', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const mockTicket = {
        id: 'ticket-id',
      };

      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
      };

      const mockEvent = {
        type: 'message',
        // Missing text property
        user: 'U12345',
        channel: 'C12345',
      };

      // Access the private method
      const createCommentAndMapping =
        handler['createCommentAndMapping'].bind(handler);

      // Act & Assert
      await expect(
        createCommentAndMapping(
          mockInstallation,
          mockTicket as any,
          mockUser as any,
          mockEvent as any,
        ),
      ).rejects.toThrow('Message is not a top level message');
    });

    it('should throw an error if the message has no blocks', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const mockTicket = {
        id: 'ticket-id',
      };

      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
      };

      const mockEvent = {
        type: 'message',
        text: 'This is a test message',
        ts: '**********.123456',
        user: 'U12345',
        channel: 'C12345',
        // Missing blocks property
      };

      // Access the private method
      const createCommentAndMapping =
        handler['createCommentAndMapping'].bind(handler);

      // Act & Assert
      await expect(
        createCommentAndMapping(
          mockInstallation,
          mockTicket as any,
          mockUser as any,
          mockEvent as any,
        ),
      ).rejects.toThrow('Blocks are required for comments');
    });
  });
});
