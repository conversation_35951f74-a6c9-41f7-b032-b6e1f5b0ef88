import { Test, TestingModule } from '@nestjs/testing';
import { beforeEach, describe, expect, it } from 'vitest';
import { Channels, Installations } from '../../../../../src/database/entities';
import { Person } from '../../../../../src/database/interfaces/person.interface';
import { Ticket } from '../../../../../src/platform/interfaces';
import { ForMessageFactory } from '../../../../../src/slack/core/factories/for-message.factory';

describe('ForMessageFactory', () => {
  let factory: ForMessageFactory;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ForMessageFactory],
    }).compile();

    factory = module.get<ForMessageFactory>(ForMessageFactory);
  });

  describe('constructSlackMessage', () => {
    it('should construct a slack message with the provided data', () => {
      const mockTicket = {
        id: 'ticket-123',
        status: 'Open',
        statusId: 'status-1',
        priority: 'High',
        priorityId: 'priority-1',
      } as Ticket;

      const mockChannel = { id: 'channel-id' } as Channels;
      const mockInstallation = {
        id: 'installation-id',
        organization: { id: 'org-id' },
      } as Installations;

      const mockCustomer = {
        slackProfileEmail: '<EMAIL>',
        displayName: 'Customer Name',
        realName: 'Real Customer Name',
      } as Person;

      const mockSlackDetails = {
        eventTs: '**********.123456',
        ts: '**********.123456',
        threadTs: '**********.123456',
        messagePermalink: 'https://slack.com/archives/C12345/p**********123456',
        user: 'U12345',
      };

      const result = factory.constructSlackMessage({
        ticket: mockTicket,
        channel: mockChannel,
        installation: mockInstallation,
        customer: mockCustomer,
        slackDetails: mockSlackDetails,
      });

      expect(result).toEqual({
        platformTicketId: 'ticket-123',
        slackEventCreatedAt: expect.any(String),
        slackMessageTs: '**********.123456',
        slackMessageThreadTs: '**********.123456',
        slackPermalink: 'https://slack.com/archives/C12345/p**********123456',
        slackUserId: 'U12345',
        metadata: {
          customer: {
            email: '<EMAIL>',
            name: 'Customer Name',
          },
          ticket_details: {
            status: 'Open',
            statusId: 'status-1',
            priority: 'High',
            priorityId: 'priority-1',
          },
        },
        channel: { id: 'channel-id' },
        installation: { id: 'installation-id' },
        organization: { id: 'org-id' },
      });
    });

    it('should use realName if displayName is not available', () => {
      const mockTicket = {
        id: 'ticket-123',
        status: 'Open',
        statusId: 'status-1',
        priority: 'High',
        priorityId: 'priority-1',
      } as Ticket;

      const mockChannel = { id: 'channel-id' } as Channels;
      const mockInstallation = {
        id: 'installation-id',
        organization: { id: 'org-id' },
      } as Installations;

      const mockCustomer = {
        slackProfileEmail: '<EMAIL>',
        displayName: null,
        realName: 'Real Customer Name',
      } as Person;

      const mockSlackDetails = {
        eventTs: '**********.123456',
        ts: '**********.123456',
        threadTs: '**********.123456',
        messagePermalink: 'https://slack.com/archives/C12345/p**********123456',
        user: 'U12345',
      };

      const result = factory.constructSlackMessage({
        ticket: mockTicket,
        channel: mockChannel,
        installation: mockInstallation,
        customer: mockCustomer,
        slackDetails: mockSlackDetails,
      });

      expect(result.metadata.customer.name).toBe('Real Customer Name');
    });

    it('should convert eventTs to ISO string date', () => {
      const mockTicket = {
        id: 'ticket-123',
        status: 'Open',
        statusId: 'status-1',
        priority: 'High',
        priorityId: 'priority-1',
      } as Ticket;

      const mockChannel = { id: 'channel-id' } as Channels;
      const mockInstallation = {
        id: 'installation-id',
        organization: { id: 'org-id' },
      } as Installations;

      const mockCustomer = {
        slackProfileEmail: '<EMAIL>',
        displayName: 'Customer Name',
        realName: 'Real Customer Name',
      } as Person;

      const mockSlackDetails = {
        eventTs: '**********.123456',
        ts: '**********.123456',
        threadTs: '**********.123456',
        messagePermalink: 'https://slack.com/archives/C12345/p**********123456',
        user: 'U12345',
      };

      const result = factory.constructSlackMessage({
        ticket: mockTicket,
        channel: mockChannel,
        installation: mockInstallation,
        customer: mockCustomer,
        slackDetails: mockSlackDetails,
      });

      // Check that the timestamp was converted to ISO string
      // The service does new Date(+eventTs), so eventTs '**********.123456' becomes number **********.123456
      const expectedDate = new Date(**********.123456).toISOString();
      expect(result.slackEventCreatedAt).toBe(expectedDate);
    });

    it('should handle missing customer email or name gracefully', () => {
      const mockTicket = {
        id: 'ticket-123',
        status: 'Open',
        statusId: 'status-1',
        priority: 'High',
        priorityId: 'priority-1',
      } as Ticket;

      const mockChannel = { id: 'channel-id' } as Channels;
      const mockInstallation = {
        id: 'installation-id',
        organization: { id: 'org-id' },
      } as Installations;

      const mockCustomer = {
        slackId: 'U12345',
        realName: 'Real Customer Name',
      } as Person;

      const mockSlackDetails = {
        eventTs: '**********.123456',
        ts: '**********.123456',
        threadTs: '**********.123456',
        messagePermalink: 'https://slack.com/archives/C12345/p**********123456',
        user: 'U12345',
      };

      const result = factory.constructSlackMessage({
        ticket: mockTicket,
        channel: mockChannel,
        installation: mockInstallation,
        customer: mockCustomer,
        slackDetails: mockSlackDetails,
      });

      expect(result.metadata.customer.email).toBeUndefined();
      expect(result.metadata.customer.name).toBe('Real Customer Name');
    });

    it('should handle missing realName gracefully', () => {
      const mockTicket = {
        id: 'ticket-123',
        status: 'Open',
        statusId: 'status-1',
        priority: 'High',
        priorityId: 'priority-1',
      } as Ticket;

      const mockChannel = { id: 'channel-id' } as Channels;
      const mockInstallation = {
        id: 'installation-id',
        organization: { id: 'org-id' },
      } as Installations;

      const mockCustomer = {
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
      } as Person;

      const mockSlackDetails = {
        eventTs: '**********.123456',
        ts: '**********.123456',
        threadTs: '**********.123456',
        messagePermalink: 'https://slack.com/archives/C12345/p**********123456',
        user: 'U12345',
      };

      const result = factory.constructSlackMessage({
        ticket: mockTicket,
        channel: mockChannel,
        installation: mockInstallation,
        customer: mockCustomer,
        slackDetails: mockSlackDetails,
      });

      expect(result.metadata.customer.email).toBe('<EMAIL>');
      expect(result.metadata.customer.name).toBeUndefined();
    });

    it('should include ticket details in metadata', () => {
      const mockTicket = {
        id: 'ticket-123',
        status: 'Open',
        statusId: 'status-1',
        priority: 'High',
        priorityId: 'priority-1',
      } as Ticket;

      const mockChannel = { id: 'channel-id' } as Channels;
      const mockInstallation = {
        id: 'installation-id',
        organization: { id: 'org-id' },
      } as Installations;

      const mockCustomer = {
        slackProfileEmail: '<EMAIL>',
        displayName: 'Customer Name',
      } as Person;

      const mockSlackDetails = {
        eventTs: '**********.123456',
        ts: '**********.123456',
        threadTs: '**********.123456',
        messagePermalink: 'https://slack.com/archives/C12345/p**********123456',
        user: 'U12345',
      };

      const result = factory.constructSlackMessage({
        ticket: mockTicket,
        channel: mockChannel,
        installation: mockInstallation,
        customer: mockCustomer,
        slackDetails: mockSlackDetails,
      });

      expect(result.metadata.ticket_details).toBeDefined();
      expect(result.metadata.ticket_details.status).toBe('Open');
      expect(result.metadata.ticket_details.statusId).toBe('status-1');
      expect(result.metadata.ticket_details.priority).toBe('High');
      expect(result.metadata.ticket_details.priorityId).toBe('priority-1');
    });
  });
});
