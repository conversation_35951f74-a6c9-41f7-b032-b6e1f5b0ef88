import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  Channels,
  Installations,
  SlackMessages,
} from '../../../../src/database/entities';
import { TeamRelationshipType } from '../../../../src/database/entities/mappings';
import { Person } from '../../../../src/database/interfaces/person.interface';
import { ThenaPlatformApiProvider } from '../../../../src/external/provider/thena-platform-api.provider';
import { ConversationGroupingService } from '../../../../src/slack/core/conversation-grouping';
import { SettingsCore } from '../../../../src/slack/core/management';
import { ILogger } from '../../../../src/utils';
import { BaseSlackBlocksToHtml } from '../../../../src/utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';
import { mockSentryService } from '../../../mocks/sentry.mock';

describe('ConversationGroupingService - Edge Cases', () => {
  let service: ConversationGroupingService;
  let mockLogger: ILogger;
  let mockSlackMessagesRepository: Repository<SlackMessages>;
  let mockThenaPlatformApiProvider: ThenaPlatformApiProvider;
  let mockSettingsCore: SettingsCore;
  let mockBaseSlackBlocksToHtml: BaseSlackBlocksToHtml;

  beforeEach(() => {
    // Create mock logger
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    // Create mock repository
    mockSlackMessagesRepository = {
      find: vi.fn(),
      findOne: vi.fn(),
      save: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
    } as unknown as Repository<SlackMessages>;

    // Create mock API provider
    mockThenaPlatformApiProvider = {
      getTicket: vi.fn(),
      createNewComment: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    // Create mock settings core
    mockSettingsCore = {
      getValue: vi.fn(),
    } as unknown as SettingsCore;

    // Create mock HTML converter
    mockBaseSlackBlocksToHtml = {
      initialize: vi.fn(),
      convert: vi.fn().mockResolvedValue('<p>Test HTML Content</p>'),
    } as unknown as BaseSlackBlocksToHtml;

    // Create service instance
    service = new ConversationGroupingService(
      mockLogger,
      mockSentryService,
      mockSlackMessagesRepository,
      mockThenaPlatformApiProvider,
      mockSettingsCore,
      mockBaseSlackBlocksToHtml,
    );
  });

  describe('shouldGroupWithExistingConversation', () => {
    it('should not group when no platform team is found', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'general',
        platformTeamsToChannelMappings: [], // No platform teams
      } as unknown as Channels;

      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
        displayName: 'Test User',
      } as unknown as Person;

      // Act
      const result = await service.shouldGroupWithExistingConversation(
        mockInstallation,
        mockChannel,
        mockUser,
      );

      // Assert
      expect(result.shouldGroup).toBe(false);
      expect(mockLogger.log).toHaveBeenCalledWith(
        'No platform team found for channel, cannot group conversations',
      );
    });

    it('should not group when conversation window is 0', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'general',
        platformTeamsToChannelMappings: [
          {
            relationshipType: TeamRelationshipType.PRIMARY,
            platformTeam: { id: 'team-id', uid: 'team-uid' },
          },
        ],
      } as unknown as Channels;

      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
        displayName: 'Test User',
      } as unknown as Person;

      // Mock conversation window setting
      (mockSettingsCore.getValue as Mock).mockResolvedValue(0);

      // Act
      const result = await service.shouldGroupWithExistingConversation(
        mockInstallation,
        mockChannel,
        mockUser,
      );

      // Assert
      expect(result.shouldGroup).toBe(false);
      expect(mockLogger.log).toHaveBeenCalledWith(
        'Conversation window is 0 or not set, skipping grouping',
      );
    });

    it('should not group when no recent messages are found', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'general',
        platformTeamsToChannelMappings: [
          {
            relationshipType: TeamRelationshipType.PRIMARY,
            platformTeam: { id: 'team-id', uid: 'team-uid' },
          },
        ],
      } as unknown as Channels;

      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
        displayName: 'Test User',
      } as unknown as Person;

      // Mock conversation window setting
      (mockSettingsCore.getValue as Mock).mockResolvedValue(30); // 30 minutes

      // Mock no recent messages
      (mockSlackMessagesRepository.find as Mock).mockResolvedValue([]);

      // Act
      const result = await service.shouldGroupWithExistingConversation(
        mockInstallation,
        mockChannel,
        mockUser,
      );

      // Assert
      expect(result.shouldGroup).toBe(false);
      expect(mockLogger.log).toHaveBeenCalledWith(
        'Conversation window: 30 minutes',
      );
    });

    it('should not group when ticket is not found', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'general',
        platformTeamsToChannelMappings: [
          {
            relationshipType: TeamRelationshipType.PRIMARY,
            platformTeam: { id: 'team-id', uid: 'team-uid' },
          },
        ],
      } as unknown as Channels;

      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
        displayName: 'Test User',
      } as unknown as Person;

      // Mock conversation window setting
      (mockSettingsCore.getValue as Mock).mockResolvedValue(30); // 30 minutes

      // Mock recent message with ticket ID
      (mockSlackMessagesRepository.find as Mock).mockResolvedValue([
        {
          id: 'message-id',
          platformTicketId: 'ticket-id',
          slackUserId: 'U12345',
          channel: { id: 'channel-id' },
        },
      ]);

      // Mock ticket not found
      (mockThenaPlatformApiProvider.getTicket as Mock).mockResolvedValue(null);

      // Act
      const result = await service.shouldGroupWithExistingConversation(
        mockInstallation,
        mockChannel,
        mockUser,
      );

      // Assert
      expect(result.shouldGroup).toBe(false);
    });

    it('should handle API errors gracefully', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'general',
        platformTeamsToChannelMappings: [
          {
            relationshipType: TeamRelationshipType.PRIMARY,
            platformTeam: { id: 'team-id', uid: 'team-uid' },
          },
        ],
      } as unknown as Channels;

      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
        displayName: 'Test User',
      } as unknown as Person;

      // Mock conversation window setting
      (mockSettingsCore.getValue as Mock).mockResolvedValue(30); // 30 minutes

      // Mock recent message with ticket ID
      (mockSlackMessagesRepository.find as Mock).mockResolvedValue([
        {
          id: 'message-id',
          platformTicketId: 'ticket-id',
          slackUserId: 'U12345',
          channel: { id: 'channel-id' },
        },
      ]);

      // Mock API error
      const apiError = new Error('API error');
      (mockThenaPlatformApiProvider.getTicket as Mock).mockRejectedValue(
        apiError,
      );

      // Act
      const result = await service.shouldGroupWithExistingConversation(
        mockInstallation,
        mockChannel,
        mockUser,
      );

      // Assert
      expect(result.shouldGroup).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error fetching ticket ticket-id: API error',
        expect.any(String),
      );
    });

    it('should group with existing ticket when all conditions are met', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'general',
        platformTeamsToChannelMappings: [
          {
            relationshipType: TeamRelationshipType.PRIMARY,
            platformTeam: { id: 'team-id', uid: 'team-uid' },
          },
        ],
      } as unknown as Channels;

      const mockUser = {
        id: 'user-id',
        slackId: 'U12345',
        displayName: 'Test User',
      } as unknown as Person;

      // Mock conversation window setting
      (mockSettingsCore.getValue as Mock).mockResolvedValue(30); // 30 minutes

      // Mock recent message with ticket ID
      const mockMessage = {
        id: 'message-id',
        platformTicketId: 'ticket-id',
        slackUserId: 'U12345',
        channel: { id: 'channel-id' },
      };
      (mockSlackMessagesRepository.find as Mock).mockResolvedValue([
        mockMessage,
      ]);

      // Mock ticket found
      (mockThenaPlatformApiProvider.getTicket as Mock).mockResolvedValue({
        id: 'ticket-id',
        status: 'Open',
      });

      // Act
      const result = await service.shouldGroupWithExistingConversation(
        mockInstallation,
        mockChannel,
        mockUser,
      );

      // Assert
      expect(result.shouldGroup).toBe(true);
      expect(result.existingTicketId).toBe('ticket-id');
      expect(result.slackMessage).toBe(mockMessage);
      expect(mockLogger.log).toHaveBeenCalledWith(
        'Grouping message with existing ticket ticket-id',
      );
    });
  });
});
