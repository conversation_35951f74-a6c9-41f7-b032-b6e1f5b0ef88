import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  Installations,
  Organizations,
  Users,
} from '../../../../../src/database/entities';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { InstallationRepository } from '../../../../../src/database/entities/installations/repositories';
import { ChannelSetupBlocks } from '../../../../../src/slack/blocks/components';
import { SlackAppManagementService } from '../../../../../src/slack/core/management';
import { BotChannelJoinedHandler } from '../../../../../src/slack/core/slack-channel/bot-channel-joined.handler';
import { SlackExternalUsersSyncJob } from '../../../../../src/slack/processors/jobs';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils/logger';

describe('BotChannelJoinedHandler - Final Push Tests', () => {
  let handler: BotChannelJoinedHandler;

  // Mock all dependencies
  const mockLogger = {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  };

  const mockChannelsRepository = {
    findByCondition: vi.fn(),
    update: vi.fn(),
  };

  const mockInstallationRepository = {
    findByCondition: vi.fn(),
  };

  const mockUserRepository = {
    findOne: vi.fn(),
  };

  const mockSlackWebAPIService = {
    sendEphemeral: vi.fn(),
    sendMessage: vi.fn(),
    getConversationInfo: vi.fn(),
  };

  const mockSlackAppManagementService = {
    upsertSlackChannel: vi.fn(),
    upsertSlackUser: vi.fn(),
  };

  const mockSlackExternalUsersSyncJob = {
    execute: vi.fn(),
  };

  const mockChannelSetupBlocks = {
    build: vi.fn(),
  };

  // Mock installation data
  const mockInstallation = {
    id: 'test-installation-id',
    teamId: 'test-team-id',
    botToken: 'test-bot-token',
    botSlackUserId: 'test-bot-user-id',
    installingUserId: 'test-installing-user-id',
    organization: {
      id: 'test-org-id',
    },
  } as Installations;

  beforeEach(async () => {
    // Reset all mocks
    vi.resetAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BotChannelJoinedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: mockUserRepository,
        },
        {
          provide: ChannelsRepository,
          useValue: mockChannelsRepository,
        },
        {
          provide: InstallationRepository,
          useValue: mockInstallationRepository,
        },
        {
          provide: ChannelSetupBlocks,
          useValue: mockChannelSetupBlocks,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackWebAPIService,
        },
        {
          provide: SlackAppManagementService,
          useValue: mockSlackAppManagementService,
        },
        {
          provide: SlackExternalUsersSyncJob,
          useValue: mockSlackExternalUsersSyncJob,
        },
      ],
    }).compile();

    handler = module.get<BotChannelJoinedHandler>(BotChannelJoinedHandler);

    // Manually set the repositories and services
    (handler as any).channelsRepository = mockChannelsRepository;
    (handler as any).installationRepository = mockInstallationRepository;
    (handler as any).userRepository = mockUserRepository;
    (handler as any).slackWebAPIService = mockSlackWebAPIService;
    (handler as any).slackAppManagementService = mockSlackAppManagementService;

    // Mock the private methods
    (handler as any).getEphemeralMessageBlocks = vi
      .fn()
      .mockReturnValue([
        { type: 'section', text: { type: 'mrkdwn', text: 'Test message' } },
      ]);

    (handler as any).getDirectMessageBlocks = vi.fn().mockReturnValue([
      {
        type: 'section',
        text: { type: 'mrkdwn', text: 'Test direct message' },
      },
    ]);

    (handler as any).getConfigureChannelButton = vi.fn().mockReturnValue({
      type: 'button',
      text: { type: 'plain_text', text: 'Configure Channel' },
      value: 'configure_channel',
    });
  });

  describe('handleThenaBotJoined - Final Push Tests', () => {
    it('should handle channel not found and create a new one', async () => {
      // Setup mocks
      mockChannelsRepository.findByCondition.mockResolvedValue(null);
      mockSlackWebAPIService.getConversationInfo.mockResolvedValue({
        ok: true,
        channel: {
          is_ext_shared: false,
          name: 'test-channel-name',
          is_private: false,
        },
      });

      // Mock upsertSlackChannel
      mockSlackAppManagementService.upsertSlackChannel.mockResolvedValue({
        id: 'new-channel-db-id',
        name: 'test-channel-name',
        channelId: 'test-channel-id',
        isBotActive: true,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      });

      // Mock sendChannelConfigurationMessage
      const sendChannelConfigurationMessageSpy = vi
        .spyOn(handler, 'sendChannelConfigurationMessage')
        .mockResolvedValue(true);

      await (handler as any).handleThenaBotJoined({
        inviter: 'inviter-user-id',
        channel: 'test-channel-id',
        slackUser: { id: 'test-user-id' },
        installation: mockInstallation,
        organization: { id: 'test-org-id' },
      });

      // Just verify that the methods were called
      expect(
        mockSlackAppManagementService.upsertSlackChannel,
      ).toHaveBeenCalled();
      expect(sendChannelConfigurationMessageSpy).toHaveBeenCalled();
    });

    it('should handle private channel', async () => {
      // Mock channel data
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        channelId: 'test-channel-id',
        isBotActive: false,
        isBotJoined: false,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };

      // Setup mocks
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      mockSlackWebAPIService.getConversationInfo.mockResolvedValue({
        ok: true,
        channel: {
          is_ext_shared: false,
          name: 'test-channel-name',
          is_private: true,
        },
      });

      // Mock sendChannelConfigurationMessage
      vi.spyOn(handler, 'sendChannelConfigurationMessage').mockResolvedValue(
        true,
      );

      await (handler as any).handleThenaBotJoined({
        inviter: 'inviter-user-id',
        channel: 'test-channel-id',
        slackUser: { id: 'test-user-id' },
        installation: mockInstallation,
        organization: { id: 'test-org-id' },
      });

      // Just verify that the update was called
      expect(mockChannelsRepository.update).toHaveBeenCalled();
    });
  });
});
