import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  Installations,
  Organizations,
  Users,
} from '../../../../../src/database/entities';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { InstallationRepository } from '../../../../../src/database/entities/installations/repositories';
import { ChannelSetupBlocks } from '../../../../../src/slack/blocks/components';
import { SlackAppManagementService } from '../../../../../src/slack/core/management';
import { BotChannelJoinedHandler } from '../../../../../src/slack/core/slack-channel/bot-channel-joined.handler';
import { SlackExternalUsersSyncJob } from '../../../../../src/slack/processors/jobs';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils/logger';

const mockLogger = {
  log: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  debug: vi.fn(),
};

const mockChannelsRepository = {
  findByCondition: vi.fn(),
  update: vi.fn(),
};

const mockInstallationRepository = {
  findByCondition: vi.fn(),
};

const mockSlackWebAPIService = {
  sendEphemeral: vi.fn(),
  sendMessage: vi.fn(),
};

const mockSlackAppManagementService = {
  upsertSlackChannel: vi.fn(),
};

const mockSlackExternalUsersSyncJob = {
  execute: vi.fn(),
};

const mockChannelSetupBlocks = {
  build: vi.fn().mockReturnValue({ blocks: [{ type: 'section', text: { type: 'mrkdwn', text: 'Test block' } }] }),
};

describe('BotChannelJoinedHandler - Helper Methods', () => {
  let handler: BotChannelJoinedHandler;

  const mockInstallation = {
    id: 'test-installation-id',
    teamId: 'test-team-id',
    botToken: 'test-bot-token',
    botSlackUserId: 'test-bot-user-id',
  } as Installations;

  const mockOrganization = {
    id: 'test-org-id',
  } as Organizations;

  const mockSlackUser = {
    id: 'test-user-id',
  } as Users;

  beforeEach(async () => {
    vi.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BotChannelJoinedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: ChannelsRepository,
          useValue: mockChannelsRepository,
        },
        {
          provide: InstallationRepository,
          useValue: mockInstallationRepository,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackWebAPIService,
        },
        {
          provide: SlackAppManagementService,
          useValue: mockSlackAppManagementService,
        },
        {
          provide: SlackExternalUsersSyncJob,
          useValue: mockSlackExternalUsersSyncJob,
        },
        {
          provide: ChannelSetupBlocks,
          useValue: mockChannelSetupBlocks,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: {
            findOne: vi.fn(),
          },
        },
        {
          provide: getRepositoryToken(Organizations),
          useValue: {
            findOne: vi.fn(),
          },
        },
      ],
    }).compile();

    handler = module.get<BotChannelJoinedHandler>(BotChannelJoinedHandler);
  });

  describe('getConfigureChannelButton', () => {
    it('should generate a button with correct properties', () => {
      const channelId = 'test-channel-id';
      const channelName = 'test-channel-name';
      
      const button = (handler as any).getConfigureChannelButton(channelId, channelName);
      
      expect(button).toEqual({
        type: 'button',
        text: {
          type: 'plain_text',
          text: 'Configure channel',
          emoji: true,
        },
        style: 'primary',
        action_id: 'configure_channel_action',
        value: JSON.stringify({
          channelId,
          channelName,
        }),
      });
    });
  });

  describe('getConfigureChannelActionBlock', () => {
    it('should generate an action block with the button', () => {
      const channelId = 'test-channel-id';
      const channelName = 'test-channel-name';
      
      const mockButton = {
        type: 'button',
        text: { type: 'plain_text', text: 'Configure Channel', emoji: true },
        style: 'primary',
        action_id: 'configure_channel_action',
        value: JSON.stringify({ channelId, channelName }),
      };
      (handler as any).getConfigureChannelButton = vi.fn().mockReturnValue(mockButton);
      
      const actionBlock = (handler as any).getConfigureChannelActionBlock(channelId, channelName);
      
      expect(actionBlock).toEqual({
        type: 'actions',
        elements: [mockButton],
      });
      
      expect((handler as any).getConfigureChannelButton).toHaveBeenCalledWith(channelId, channelName);
    });
  });

  describe('getEphemeralMessageBlocks', () => {
    it('should generate ephemeral message blocks with correct content', () => {
      const channelId = 'test-channel-id';
      const channelName = 'test-channel-name';
      
      const mockActionBlock = {
        type: 'actions',
        elements: [{
          type: 'button',
          text: { type: 'plain_text', text: 'Configure Channel', emoji: true },
          style: 'primary',
          action_id: 'configure_channel_action',
          value: JSON.stringify({ channelId, channelName }),
        }],
      };
      (handler as any).getConfigureChannelActionBlock = vi.fn().mockReturnValue(mockActionBlock);
      
      const blocks = (handler as any).getEphemeralMessageBlocks(channelId, channelName);
      
      expect(blocks).toEqual([
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'Configure the channel to get started.',
          },
        },
        mockActionBlock,
      ]);
      
      expect((handler as any).getConfigureChannelActionBlock).toHaveBeenCalledWith(channelId, channelName);
    });
  });

  describe('getDirectMessageBlocks', () => {
    it('should generate direct message blocks with correct content', () => {
      const channelId = 'test-channel-id';
      const channelName = 'test-channel-name';
      
      const mockActionBlock = {
        type: 'actions',
        elements: [{
          type: 'button',
          text: { type: 'plain_text', text: 'Configure Channel', emoji: true },
          style: 'primary',
          action_id: 'configure_channel_action',
          value: JSON.stringify({ channelId, channelName }),
        }],
      };
      (handler as any).getConfigureChannelActionBlock = vi.fn().mockReturnValue(mockActionBlock);
      
      const blocks = (handler as any).getDirectMessageBlocks(channelId, channelName);
      
      expect(blocks).toEqual([
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'You can configure the channel using the button below:',
          },
        },
        mockActionBlock,
      ]);
      
      expect((handler as any).getConfigureChannelActionBlock).toHaveBeenCalledWith(channelId, channelName);
    });
  });
});
