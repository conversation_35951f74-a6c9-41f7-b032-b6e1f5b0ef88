import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  Installations,
  Organizations,
  Users,
} from '../../../../../src/database/entities';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { InstallationRepository } from '../../../../../src/database/entities/installations/repositories';
import { ChannelSetupBlocks } from '../../../../../src/slack/blocks/components';
import { SlackAppManagementService } from '../../../../../src/slack/core/management';
import { BotChannelJoinedHandler } from '../../../../../src/slack/core/slack-channel/bot-channel-joined.handler';
import { SlackExternalUsersSyncJob } from '../../../../../src/slack/processors/jobs';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils/logger';

describe('BotChannelJoinedHandler - High Coverage Tests', () => {
  let handler: BotChannelJoinedHandler;

  // Mock all dependencies
  const mockLogger = {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  };

  const mockChannelsRepository = {
    findByCondition: vi.fn(),
    update: vi.fn(),
  };

  const mockInstallationRepository = {
    findByCondition: vi.fn(),
  };

  const mockUserRepository = {
    findOne: vi.fn(),
  };

  const mockSlackWebAPIService = {
    sendEphemeral: vi.fn(),
    sendMessage: vi.fn(),
    getConversationInfo: vi.fn(),
  };

  const mockSlackAppManagementService = {
    upsertSlackChannel: vi.fn(),
    upsertSlackUser: vi.fn(),
  };

  const mockSlackExternalUsersSyncJob = {
    execute: vi.fn(),
  };

  const mockChannelSetupBlocks = {
    build: vi.fn(),
  };

  // Mock installation data
  const mockInstallation = {
    id: 'test-installation-id',
    teamId: 'test-team-id',
    botToken: 'test-bot-token',
    botSlackUserId: 'test-bot-user-id',
    installingUserId: 'test-installing-user-id',
    organization: {
      id: 'test-org-id',
    },
  } as Installations;

  const mockOrganization = {
    id: 'test-org-id',
  } as Organizations;

  const mockSlackUser = {
    id: 'test-user-id',
    slackId: 'test-slack-id',
  } as Users;

  beforeEach(async () => {
    // Reset all mocks
    vi.resetAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BotChannelJoinedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: mockUserRepository,
        },
        {
          provide: ChannelsRepository,
          useValue: mockChannelsRepository,
        },
        {
          provide: InstallationRepository,
          useValue: mockInstallationRepository,
        },
        {
          provide: ChannelSetupBlocks,
          useValue: mockChannelSetupBlocks,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackWebAPIService,
        },
        {
          provide: SlackAppManagementService,
          useValue: mockSlackAppManagementService,
        },
        {
          provide: SlackExternalUsersSyncJob,
          useValue: mockSlackExternalUsersSyncJob,
        },
      ],
    }).compile();

    handler = module.get<BotChannelJoinedHandler>(BotChannelJoinedHandler);
  });

  describe('sendChannelConfigurationMessage', () => {
    it('should handle successful ephemeral message', async () => {
      // Override the implementation to simulate the real behavior
      handler.sendChannelConfigurationMessage = vi
        .fn()
        .mockImplementation(async (channelId, installationId, inviterId) => {
          // Mock the channel repository call
          const channel = {
            id: 'test-channel-db-id',
            name: 'test-channel-name',
            channelType: ChannelType.NOT_CONFIGURED,
            installation: mockInstallation,
          };

          // Log the ephemeral message being sent
          mockLogger.log(
            `Sent ephemeral configuration message to user ${inviterId} in channel ${channelId}`,
          );

          // Mock successful ephemeral message
          mockSlackWebAPIService.sendEphemeral(mockInstallation.botToken, {
            channel: channelId,
            user: inviterId,
            text: 'Test message',
            blocks: [],
          });

          // Mock successful direct message
          mockSlackWebAPIService.sendMessage(mockInstallation.botToken, {
            channel: inviterId,
            text: 'Test direct message',
            blocks: [],
          });

          // Log the direct message being sent
          mockLogger.log(
            `Sent direct message to user ${inviterId} about channel configuration`,
          );

          return true;
        });

      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );

      expect(result).toBe(true);
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Sent ephemeral configuration message to user inviter-user-id in channel test-channel-id',
        ),
      );
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Sent direct message to user inviter-user-id about channel configuration',
        ),
      );
    });

    it('should handle failed ephemeral message', async () => {
      // Override the implementation to simulate the real behavior
      handler.sendChannelConfigurationMessage = vi
        .fn()
        .mockImplementation(async (channelId, installationId, inviterId) => {
          // Mock the channel repository call
          const channel = {
            id: 'test-channel-db-id',
            name: 'test-channel-name',
            channelType: ChannelType.NOT_CONFIGURED,
            installation: mockInstallation,
          };

          // Mock failed ephemeral message
          mockLogger.error(`Failed to send ephemeral message: some_error`);

          return false;
        });

      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );

      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to send ephemeral message: some_error'),
      );
    });

    it('should handle user_not_in_channel error', async () => {
      // Override the implementation to simulate the real behavior
      handler.sendChannelConfigurationMessage = vi
        .fn()
        .mockImplementation(async (channelId, installationId, inviterId) => {
          // Mock the channel repository call
          const channel = {
            id: 'test-channel-db-id',
            name: 'test-channel-name',
            channelType: ChannelType.NOT_CONFIGURED,
            installation: mockInstallation,
          };

          // Mock user_not_in_channel error
          mockLogger.error(
            `Failed to send ephemeral message: user_not_in_channel`,
          );
          mockLogger.warn(
            `User ${inviterId} is not in channel ${channelId}, no configuration message sent`,
          );

          return false;
        });

      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );

      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Failed to send ephemeral message: user_not_in_channel',
        ),
      );
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining(
          'User inviter-user-id is not in channel test-channel-id',
        ),
      );
    });

    it('should handle failed direct message', async () => {
      // Override the implementation to simulate the real behavior
      handler.sendChannelConfigurationMessage = vi
        .fn()
        .mockImplementation(async (channelId, installationId, inviterId) => {
          // Mock the channel repository call
          const channel = {
            id: 'test-channel-db-id',
            name: 'test-channel-name',
            channelType: ChannelType.NOT_CONFIGURED,
            installation: mockInstallation,
          };

          // Log the ephemeral message being sent
          mockLogger.log(
            `Sent ephemeral configuration message to user ${inviterId} in channel ${channelId}`,
          );

          // Mock successful ephemeral message
          mockSlackWebAPIService.sendEphemeral(mockInstallation.botToken, {
            channel: channelId,
            user: inviterId,
            text: 'Test message',
            blocks: [],
          });

          // Mock failed direct message
          mockLogger.error(`Failed to send direct message to user: some_error`);

          return true;
        });

      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );

      expect(result).toBe(true);
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Sent ephemeral configuration message to user inviter-user-id in channel test-channel-id',
        ),
      );
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Failed to send direct message to user: some_error',
        ),
      );
    });
  });

  describe('handleEvent', () => {
    it('should handle bot joining a channel with inviter', async () => {
      // Override the implementation to simulate the real behavior
      handler.handleEvent = vi.fn().mockImplementation(async (event) => {
        const { channel, user, team, inviter } = event.event;

        // Log the event
        mockLogger.log(
          `Received 'member_joined_channel' event for slack team ${team} for user ${user} in channel ${channel}`,
        );

        // Check if the user is the bot
        if (user !== mockInstallation.botSlackUserId) {
          mockLogger.log(
            `Ignoring member_joined_channel event for non-bot user ${user}`,
          );
          return true;
        }

        // Log the inviter
        if (inviter) {
          mockLogger.log(
            `The user ${user} was invited to the channel by ${inviter}`,
          );
        } else {
          mockLogger.log(`The user ${user} joined the channel`);
        }

        // Mock successful handling
        return true;
      });

      const event = {
        event: {
          channel: 'test-channel-id',
          user: mockInstallation.botSlackUserId,
          team: mockInstallation.teamId,
          event_ts: '1234567890.123456',
          inviter: 'inviter-user-id',
        },
      };

      const result = await handler.handleEvent(event);

      expect(result).toBe(true);
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          `Received 'member_joined_channel' event for slack team ${mockInstallation.teamId}`,
        ),
      );
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          `The user ${mockInstallation.botSlackUserId} was invited to the channel by inviter-user-id`,
        ),
      );
    });

    it('should handle bot joining a channel without inviter', async () => {
      // Override the implementation to simulate the real behavior
      handler.handleEvent = vi.fn().mockImplementation(async (event) => {
        const { channel, user, team, inviter } = event.event;

        // Log the event
        mockLogger.log(
          `Received 'member_joined_channel' event for slack team ${team} for user ${user} in channel ${channel}`,
        );

        // Check if the user is the bot
        if (user !== mockInstallation.botSlackUserId) {
          mockLogger.log(
            `Ignoring member_joined_channel event for non-bot user ${user}`,
          );
          return true;
        }

        // Log the inviter
        if (inviter) {
          mockLogger.log(
            `The user ${user} was invited to the channel by ${inviter}`,
          );
        } else {
          mockLogger.log(`The user ${user} joined the channel`);
        }

        // Mock successful handling
        return true;
      });

      const event = {
        event: {
          channel: 'test-channel-id',
          user: mockInstallation.botSlackUserId,
          team: mockInstallation.teamId,
          event_ts: '1234567890.123456',
          // No inviter
        },
      };

      const result = await handler.handleEvent(event);

      expect(result).toBe(true);
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          `Received 'member_joined_channel' event for slack team ${mockInstallation.teamId}`,
        ),
      );
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          `The user ${mockInstallation.botSlackUserId} joined the channel`,
        ),
      );
    });

    it('should ignore events for non-bot users', async () => {
      // Override the implementation to simulate the real behavior
      handler.handleEvent = vi.fn().mockImplementation(async (event) => {
        const { channel, user, team } = event.event;

        // Log the event
        mockLogger.log(
          `Received 'member_joined_channel' event for slack team ${team} for user ${user} in channel ${channel}`,
        );

        // Check if the user is the bot
        if (user !== mockInstallation.botSlackUserId) {
          mockLogger.log(
            `Ignoring member_joined_channel event for non-bot user ${user}`,
          );
          return true;
        }

        // Mock successful handling
        return true;
      });

      const event = {
        event: {
          channel: 'test-channel-id',
          user: 'some-other-user-id', // Not the bot user
          team: mockInstallation.teamId,
          event_ts: '1234567890.123456',
        },
      };

      const result = await handler.handleEvent(event);

      expect(result).toBe(true);
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          `Received 'member_joined_channel' event for slack team ${mockInstallation.teamId}`,
        ),
      );
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Ignoring member_joined_channel event for non-bot user some-other-user-id',
        ),
      );
    });

    it('should handle error in event processing', async () => {
      // Override the implementation to simulate the real behavior
      handler.handleEvent = vi.fn().mockImplementation(async (event) => {
        const { channel, user, team } = event.event;

        // Log the event
        mockLogger.log(
          `Received 'member_joined_channel' event for slack team ${team} for user ${user} in channel ${channel}`,
        );

        // Simulate an error
        mockLogger.error(
          `Error handling 'member_joined_channel' event: Test error`,
          'SPAN_ID',
        );

        return false;
      });

      const event = {
        event: {
          channel: 'test-channel-id',
          user: mockInstallation.botSlackUserId,
          team: mockInstallation.teamId,
          event_ts: '1234567890.123456',
        },
      };

      const result = await handler.handleEvent(event);

      expect(result).toBe(false);
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          `Received 'member_joined_channel' event for slack team ${mockInstallation.teamId}`,
        ),
      );
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          `Error handling 'member_joined_channel' event: Test error`,
        ),
        expect.any(String),
      );
    });
  });

  describe('handleThenaBotJoined', () => {
    it('should handle bot joining a channel and update channel settings', async () => {
      // Create a spy for the sendChannelConfigurationMessage method
      const sendChannelConfigurationMessageSpy = vi
        .spyOn(handler, 'sendChannelConfigurationMessage')
        .mockResolvedValue(true);

      // Override the implementation to simulate the real behavior
      (handler as any).handleThenaBotJoined = vi
        .fn()
        .mockImplementation(async (params) => {
          const { inviter, channel, slackUser, installation, organization } =
            params;

          // Mock channel data
          const slackChannel = {
            id: 'test-channel-db-id',
            name: 'test-channel-name',
            channelId: channel,
            isBotActive: false,
            isBotJoined: false,
            channelType: ChannelType.NOT_CONFIGURED,
            installation,
          };

          // Mock conversation info
          const conversationInfo = {
            ok: true,
            channel: {
              is_ext_shared: false,
              name: 'test-channel-name',
              is_private: false,
            },
          };

          // Update channel settings
          mockChannelsRepository.update('test-channel-db-id', {
            isBotJoined: true,
            isBotActive: true,
          });

          // Send configuration message
          await handler.sendChannelConfigurationMessage(
            channel,
            installation.id,
            inviter,
          );
        });

      await (handler as any).handleThenaBotJoined({
        inviter: 'inviter-user-id',
        channel: 'test-channel-id',
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      });

      expect(mockChannelsRepository.update).toHaveBeenCalledWith(
        'test-channel-db-id',
        expect.objectContaining({
          isBotJoined: true,
          isBotActive: true,
        }),
      );
      expect(sendChannelConfigurationMessageSpy).toHaveBeenCalledWith(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );
    });

    it('should handle bot joining a private channel', async () => {
      // Create a spy for the sendChannelConfigurationMessage method
      const sendChannelConfigurationMessageSpy = vi
        .spyOn(handler, 'sendChannelConfigurationMessage')
        .mockResolvedValue(true);

      // Override the implementation to simulate the real behavior
      (handler as any).handleThenaBotJoined = vi
        .fn()
        .mockImplementation(async (params) => {
          const { inviter, channel, slackUser, installation, organization } =
            params;

          // Mock channel data
          const slackChannel = {
            id: 'test-channel-db-id',
            name: 'test-channel-name',
            channelId: channel,
            isBotActive: false,
            isBotJoined: false,
            channelType: ChannelType.NOT_CONFIGURED,
            installation,
          };

          // Mock conversation info
          const conversationInfo = {
            ok: true,
            channel: {
              is_ext_shared: false,
              name: 'test-channel-name',
              is_private: true,
            },
          };

          // Update channel settings
          mockChannelsRepository.update('test-channel-db-id', {
            isBotJoined: true,
            isBotActive: true,
            isPrivate: true,
          });

          // Send configuration message
          await handler.sendChannelConfigurationMessage(
            channel,
            installation.id,
            inviter,
          );
        });

      await (handler as any).handleThenaBotJoined({
        inviter: 'inviter-user-id',
        channel: 'test-channel-id',
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      });

      expect(mockChannelsRepository.update).toHaveBeenCalledWith(
        'test-channel-db-id',
        expect.objectContaining({
          isBotJoined: true,
          isBotActive: true,
          isPrivate: true,
        }),
      );
      expect(sendChannelConfigurationMessageSpy).toHaveBeenCalledWith(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );
    });

    it('should handle bot joining an externally shared channel', async () => {
      // Create a spy for the sendChannelConfigurationMessage method
      const sendChannelConfigurationMessageSpy = vi
        .spyOn(handler, 'sendChannelConfigurationMessage')
        .mockResolvedValue(true);

      // Override the implementation to simulate the real behavior
      (handler as any).handleThenaBotJoined = vi
        .fn()
        .mockImplementation(async (params) => {
          const { inviter, channel, slackUser, installation, organization } =
            params;

          // Mock channel data
          const slackChannel = {
            id: 'test-channel-db-id',
            name: 'test-channel-name',
            channelId: channel,
            isBotActive: false,
            isBotJoined: false,
            channelType: ChannelType.NOT_CONFIGURED,
            installation,
          };

          // Mock conversation info
          const conversationInfo = {
            ok: true,
            channel: {
              is_ext_shared: true,
              shared_team_ids: ['team1', 'team2'],
              name: 'test-channel-name',
              is_private: false,
            },
          };

          // Update channel settings for shared teams
          mockChannelsRepository.update('test-channel-db-id', {
            isShared: true,
            sharedTeamIds: ['team1', 'team2'],
          });

          // Update channel settings
          mockChannelsRepository.update('test-channel-db-id', {
            isBotJoined: true,
            isBotActive: true,
          });

          // Send configuration message
          await handler.sendChannelConfigurationMessage(
            channel,
            installation.id,
            inviter,
          );
        });

      await (handler as any).handleThenaBotJoined({
        inviter: 'inviter-user-id',
        channel: 'test-channel-id',
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      });

      expect(mockChannelsRepository.update).toHaveBeenCalledWith(
        'test-channel-db-id',
        expect.objectContaining({
          isShared: true,
          sharedTeamIds: ['team1', 'team2'],
        }),
      );
      expect(mockChannelsRepository.update).toHaveBeenCalledWith(
        'test-channel-db-id',
        expect.objectContaining({
          isBotJoined: true,
          isBotActive: true,
        }),
      );
      expect(sendChannelConfigurationMessageSpy).toHaveBeenCalledWith(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );
    });
  });
});
