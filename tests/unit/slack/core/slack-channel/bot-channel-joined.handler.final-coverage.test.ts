import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  Installations,
  Organizations,
  Users,
} from '../../../../../src/database/entities';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { InstallationRepository } from '../../../../../src/database/entities/installations/repositories';
import { ChannelSetupBlocks } from '../../../../../src/slack/blocks/components';
import { SlackAppManagementService } from '../../../../../src/slack/core/management';
import { BotChannelJoinedHandler } from '../../../../../src/slack/core/slack-channel/bot-channel-joined.handler';
import { SlackExternalUsersSyncJob } from '../../../../../src/slack/processors/jobs';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils/logger';

describe('BotChannelJoinedHandler - Final Coverage Tests', () => {
  let handler: BotChannelJoinedHandler;
  
  // Mock all dependencies
  const mockLogger = {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  };
  
  const mockChannelsRepository = {
    findByCondition: vi.fn(),
    update: vi.fn(),
  };
  
  const mockInstallationRepository = {
    findByCondition: vi.fn(),
  };
  
  const mockUserRepository = {
    findOne: vi.fn(),
  };
  
  const mockSlackWebAPIService = {
    sendEphemeral: vi.fn(),
    sendMessage: vi.fn(),
    getConversationInfo: vi.fn(),
  };
  
  const mockSlackAppManagementService = {
    upsertSlackChannel: vi.fn(),
    upsertSlackUser: vi.fn(),
  };
  
  const mockSlackExternalUsersSyncJob = {
    execute: vi.fn(),
  };
  
  const mockChannelSetupBlocks = {
    build: vi.fn(),
  };
  
  // Mock installation data
  const mockInstallation = {
    id: 'test-installation-id',
    teamId: 'test-team-id',
    botToken: 'test-bot-token',
    botSlackUserId: 'test-bot-user-id',
    installingUserId: 'test-installing-user-id',
    organization: {
      id: 'test-org-id',
    },
  } as Installations;

  beforeEach(async () => {
    // Reset all mocks
    vi.resetAllMocks();
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BotChannelJoinedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: mockUserRepository,
        },
        {
          provide: ChannelsRepository,
          useValue: mockChannelsRepository,
        },
        {
          provide: InstallationRepository,
          useValue: mockInstallationRepository,
        },
        {
          provide: ChannelSetupBlocks,
          useValue: mockChannelSetupBlocks,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackWebAPIService,
        },
        {
          provide: SlackAppManagementService,
          useValue: mockSlackAppManagementService,
        },
        {
          provide: SlackExternalUsersSyncJob,
          useValue: mockSlackExternalUsersSyncJob,
        },
      ],
    }).compile();
    
    handler = module.get<BotChannelJoinedHandler>(BotChannelJoinedHandler);
    
    // Manually set the repositories
    (handler as any).channelsRepository = mockChannelsRepository;
    (handler as any).installationRepository = mockInstallationRepository;
    (handler as any).userRepository = mockUserRepository;
    (handler as any).slackWebAPIService = mockSlackWebAPIService;
    
    // Mock the private methods
    (handler as any).getEphemeralMessageBlocks = vi.fn().mockReturnValue([
      { type: 'section', text: { type: 'mrkdwn', text: 'Test message' } },
    ]);
    
    (handler as any).getDirectMessageBlocks = vi.fn().mockReturnValue([
      { type: 'section', text: { type: 'mrkdwn', text: 'Test direct message' } },
    ]);
    
    (handler as any).getConfigureChannelButton = vi.fn().mockReturnValue({
      type: 'button',
      text: { type: 'plain_text', text: 'Configure Channel' },
      value: 'configure_channel',
    });
  });

  describe('sendChannelConfigurationMessage - Final Coverage Tests', () => {
    it('should handle successful ephemeral message and successful direct message', async () => {
      // Mock channel data
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        channelType: ChannelType.NOT_CONFIGURED,
        isBotActive: false,
        installation: mockInstallation,
      };
      
      // Setup mocks
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });
      mockSlackWebAPIService.sendMessage.mockResolvedValue({ ok: true });
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      // The method should return true on success
      expect(result).toBe(true);
      expect(mockSlackWebAPIService.sendEphemeral).toHaveBeenCalledWith(
        'test-bot-token',
        expect.objectContaining({
          channel: 'test-channel-id',
          user: 'inviter-user-id',
        })
      );
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalledWith(
        'test-bot-token',
        expect.objectContaining({
          channel: 'inviter-user-id',
        })
      );
    });
    
    it('should handle failed direct message but still return true if ephemeral succeeds', async () => {
      // Mock channel data
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        channelType: ChannelType.NOT_CONFIGURED,
        isBotActive: false,
        installation: mockInstallation,
      };
      
      // Setup mocks
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });
      mockSlackWebAPIService.sendMessage.mockResolvedValue({ ok: false, error: 'some_error' });
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      // The method should still return true if ephemeral message succeeds
      expect(result).toBe(true);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to send direct message to user: some_error'
      );
    });
    
    it('should handle exception in direct message but still return true', async () => {
      // Mock channel data
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        channelType: ChannelType.NOT_CONFIGURED,
        isBotActive: false,
        installation: mockInstallation,
      };
      
      // Setup mocks
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });
      
      // Mock sendMessage to throw an error
      mockSlackWebAPIService.sendMessage.mockImplementation(() => {
        throw new Error('direct_message_error');
      });
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      // The method should still return true if ephemeral message succeeds
      expect(result).toBe(true);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to send direct message to user inviter-user-id: direct_message_error'
      );
    });
    
    it('should handle user_not_in_channel error and return false', async () => {
      // Mock channel data
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        channelType: ChannelType.NOT_CONFIGURED,
        isBotActive: false,
        installation: mockInstallation,
      };
      
      // Setup mocks
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      
      // Mock sendEphemeral to throw a user_not_in_channel error
      const error = new Error('user_not_in_channel');
      mockSlackWebAPIService.sendEphemeral.mockRejectedValue(error);
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to send ephemeral message: user_not_in_channel'
      );
    });
  });
});
