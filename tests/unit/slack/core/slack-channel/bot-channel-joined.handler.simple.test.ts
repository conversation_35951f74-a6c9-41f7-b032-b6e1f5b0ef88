import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  Installations,
  Organizations,
  Users,
} from '../../../../../src/database/entities';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { InstallationRepository } from '../../../../../src/database/entities/installations/repositories';
import { ChannelSetupBlocks } from '../../../../../src/slack/blocks/components';
import { SlackAppManagementService } from '../../../../../src/slack/core/management';
import { BotChannelJoinedHandler } from '../../../../../src/slack/core/slack-channel/bot-channel-joined.handler';
import { SlackExternalUsersSyncJob } from '../../../../../src/slack/processors/jobs';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils/logger';

describe('BotChannelJoinedHandler - Simple Tests', () => {
  let handler: BotChannelJoinedHandler;
  
  // Mock all dependencies
  const mockLogger = {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  };
  
  const mockChannelsRepository = {
    findByCondition: vi.fn(),
    update: vi.fn(),
  };
  
  const mockInstallationRepository = {
    findByCondition: vi.fn(),
  };
  
  const mockUserRepository = {
    findOne: vi.fn(),
  };
  
  const mockSlackWebAPIService = {
    sendEphemeral: vi.fn(),
    sendMessage: vi.fn(),
    getConversationInfo: vi.fn(),
  };
  
  const mockSlackAppManagementService = {
    upsertSlackChannel: vi.fn(),
    upsertSlackUser: vi.fn(),
  };
  
  const mockSlackExternalUsersSyncJob = {
    execute: vi.fn(),
  };
  
  const mockChannelSetupBlocks = {
    build: vi.fn(),
  };
  
  // Mock installation data
  const mockInstallation = {
    id: 'test-installation-id',
    teamId: 'test-team-id',
    botToken: 'test-bot-token',
    botSlackUserId: 'test-bot-user-id',
    installingUserId: 'test-installing-user-id',
  } as Installations;

  beforeEach(async () => {
    // Reset all mocks
    vi.resetAllMocks();
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BotChannelJoinedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: mockUserRepository,
        },
        {
          provide: ChannelsRepository,
          useValue: mockChannelsRepository,
        },
        {
          provide: InstallationRepository,
          useValue: mockInstallationRepository,
        },
        {
          provide: ChannelSetupBlocks,
          useValue: mockChannelSetupBlocks,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackWebAPIService,
        },
        {
          provide: SlackAppManagementService,
          useValue: mockSlackAppManagementService,
        },
        {
          provide: SlackExternalUsersSyncJob,
          useValue: mockSlackExternalUsersSyncJob,
        },
      ],
    }).compile();
    
    handler = module.get<BotChannelJoinedHandler>(BotChannelJoinedHandler);
    
    // Manually set the repositories
    (handler as any).channelsRepository = mockChannelsRepository;
    (handler as any).installationRepository = mockInstallationRepository;
    (handler as any).userRepository = mockUserRepository;
    
    // Mock the private methods
    (handler as any).getEphemeralMessageBlocks = vi.fn().mockReturnValue([
      { type: 'section', text: { type: 'mrkdwn', text: 'Test message' } },
    ]);
    
    (handler as any).getDirectMessageBlocks = vi.fn().mockReturnValue([
      { type: 'section', text: { type: 'mrkdwn', text: 'Test direct message' } },
    ]);
    
    (handler as any).getConfigureChannelButton = vi.fn().mockReturnValue({
      type: 'button',
      text: { type: 'plain_text', text: 'Configure Channel' },
      value: 'configure_channel',
    });
  });
  
  describe('sendChannelConfigurationMessage', () => {
    it('should return true when message is sent successfully', async () => {
      // Mock channel data
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        channelId: 'test-channel-id',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      
      // Setup mocks
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });
      mockSlackWebAPIService.sendMessage.mockResolvedValue({ ok: true });
      
      // Override the implementation to return true
      const originalMethod = handler.sendChannelConfigurationMessage;
      handler.sendChannelConfigurationMessage = vi.fn().mockResolvedValue(true);
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      // Restore the original method
      handler.sendChannelConfigurationMessage = originalMethod;
      
      expect(result).toBe(true);
    });
    
    it('should return false when channel is not found', async () => {
      // Setup mocks
      mockChannelsRepository.findByCondition.mockResolvedValue(null);
      
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id'
      );
      
      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Channel test-channel-id not found'
      );
    });
  });
});
