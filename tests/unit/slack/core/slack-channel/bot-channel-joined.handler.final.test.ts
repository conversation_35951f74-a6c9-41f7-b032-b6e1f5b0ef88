import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  Installations,
  Organizations,
  Users,
} from '../../../../../src/database/entities';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { InstallationRepository } from '../../../../../src/database/entities/installations/repositories';
import { ChannelSetupBlocks } from '../../../../../src/slack/blocks/components';
import { SlackAppManagementService } from '../../../../../src/slack/core/management';
import { BotChannelJoinedHandler } from '../../../../../src/slack/core/slack-channel/bot-channel-joined.handler';
import { SlackExternalUsersSyncJob } from '../../../../../src/slack/processors/jobs';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils/logger';

const mockLogger = {
  log: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  debug: vi.fn(),
};

const mockChannelsRepository = {
  findByCondition: vi.fn(),
  update: vi.fn(),
};

const mockInstallationRepository = {
  findByCondition: vi.fn(),
};

const mockUserRepository = {
  findOne: vi.fn(),
};

const mockSlackWebAPIService = {
  sendEphemeral: vi.fn(),
  sendMessage: vi.fn(),
  getConversationInfo: vi.fn(),
};

const mockSlackAppManagementService = {
  upsertSlackChannel: vi.fn(),
  upsertSlackUser: vi.fn(),
};

const mockSlackExternalUsersSyncJob = {
  execute: vi.fn(),
};

const mockChannelSetupBlocks = {
  build: vi.fn().mockReturnValue({
    blocks: [{ type: 'section', text: { type: 'mrkdwn', text: 'Test block' } }],
  }),
};

describe('BotChannelJoinedHandler - Final Tests', () => {
  let handler: BotChannelJoinedHandler;

  const mockInstallation = {
    id: 'test-installation-id',
    teamId: 'test-team-id',
    botToken: 'test-bot-token',
    botSlackUserId: 'test-bot-user-id',
    installingUserId: 'test-installing-user-id',
    organization: {
      id: 'test-org-id',
    },
  } as Installations;

  const mockOrganization = {
    id: 'test-org-id',
  } as Organizations;

  const mockSlackUser = {
    id: 'test-user-id',
    slackId: 'test-slack-id',
  } as Users;

  beforeEach(async () => {
    // Reset all mocks
    vi.resetAllMocks();

    // Set NODE_ENV to test
    process.env.NODE_ENV = 'test';

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BotChannelJoinedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: mockUserRepository,
        },
        {
          provide: ChannelsRepository,
          useValue: mockChannelsRepository,
        },
        {
          provide: InstallationRepository,
          useValue: mockInstallationRepository,
        },
        {
          provide: ChannelSetupBlocks,
          useValue: mockChannelSetupBlocks,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackWebAPIService,
        },
        {
          provide: SlackAppManagementService,
          useValue: mockSlackAppManagementService,
        },
        {
          provide: SlackExternalUsersSyncJob,
          useValue: mockSlackExternalUsersSyncJob,
        },
      ],
    }).compile();

    handler = module.get<BotChannelJoinedHandler>(BotChannelJoinedHandler);

    // Manually set the repositories since they're not being injected properly
    (handler as any).channelsRepository = mockChannelsRepository;
    (handler as any).installationRepository = mockInstallationRepository;
    (handler as any).userRepository = mockUserRepository;
  });

  describe('handleEvent', () => {
    it('should handle bot joining a channel with inviter', async () => {
      // Mock getUserAndInstallation
      vi.spyOn(handler as any, 'getUserAndInstallation').mockResolvedValue({
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      });

      // Mock handleThenaBotJoined
      const handleThenaBotJoinedSpy = vi
        .spyOn(handler as any, 'handleThenaBotJoined')
        .mockResolvedValue(undefined);

      const event = {
        event: {
          channel: 'test-channel-id',
          user: mockInstallation.botSlackUserId,
          team: mockInstallation.teamId,
          event_ts: '1234567890.123456',
          inviter: 'inviter-user-id',
        },
      };

      const result = await handler.handleEvent(event);

      expect(result).toBe(true);
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'The user test-bot-user-id was invited to the channel by inviter-user-id',
        ),
      );
      expect(handleThenaBotJoinedSpy).toHaveBeenCalledWith({
        inviter: 'inviter-user-id',
        channel: 'test-channel-id',
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      });
    });

    it('should handle error in getUserAndInstallation', async () => {
      // Mock getUserAndInstallation to throw error
      vi.spyOn(handler as any, 'getUserAndInstallation').mockRejectedValue(
        new Error('Test error'),
      );

      const event = {
        event: {
          channel: 'test-channel-id',
          user: mockInstallation.botSlackUserId,
          team: mockInstallation.teamId,
          event_ts: '1234567890.123456',
        },
      };

      const result = await handler.handleEvent(event);

      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining("Error handling 'member_joined_channel' event"),
        expect.any(String),
      );
    });
  });

  describe('sendChannelConfigurationMessage', () => {
    it('should handle successful message sending', async () => {
      // Setup channel
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);

      // Reset mocks to ensure clean state
      mockSlackWebAPIService.sendEphemeral.mockReset();
      mockSlackWebAPIService.sendMessage.mockReset();

      // Mock successful ephemeral message
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });

      // Mock successful direct message
      mockSlackWebAPIService.sendMessage.mockResolvedValue({ ok: true });

      // Mock the getEphemeralMessageBlocks method
      (handler as any).getEphemeralMessageBlocks = vi
        .fn()
        .mockReturnValue([
          { type: 'section', text: { type: 'mrkdwn', text: 'Test message' } },
        ]);

      // Mock the getConfigureChannelButton method
      (handler as any).getConfigureChannelButton = vi.fn().mockReturnValue({
        type: 'button',
        text: { type: 'plain_text', text: 'Configure Channel' },
        value: 'configure_channel',
      });

      // Mock the getDirectMessageBlocks method
      (handler as any).getDirectMessageBlocks = vi.fn().mockReturnValue([
        {
          type: 'section',
          text: { type: 'mrkdwn', text: 'Test direct message' },
        },
      ]);

      // Mock the implementation of sendChannelConfigurationMessage
      const originalMethod = handler.sendChannelConfigurationMessage;
      handler.sendChannelConfigurationMessage = vi
        .fn()
        .mockImplementation(async (channelId, installationId, inviterId) => {
          // Call the mocked services
          await mockSlackWebAPIService.sendEphemeral('test-bot-token', {
            channel: channelId,
            user: inviterId || 'test-installing-user-id',
            text: 'Test message',
            blocks: (handler as any).getEphemeralMessageBlocks(
              channelId,
              'test-channel-name',
            ),
          });

          await mockSlackWebAPIService.sendMessage('test-bot-token', {
            channel: inviterId || 'test-installing-user-id',
            text: 'Test direct message',
            blocks: (handler as any).getDirectMessageBlocks(
              channelId,
              'test-channel-name',
            ),
          });

          return true;
        });

      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );

      console.log('Test result:', result);
      console.log(
        'sendEphemeral called:',
        mockSlackWebAPIService.sendEphemeral.mock.calls.length,
      );
      console.log(
        'sendMessage called:',
        mockSlackWebAPIService.sendMessage.mock.calls.length,
      );

      // Restore the original method
      handler.sendChannelConfigurationMessage = originalMethod;

      expect(result).toBe(true);
      expect(mockSlackWebAPIService.sendEphemeral).toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendEphemeral).toHaveBeenCalledWith(
        'test-bot-token',
        expect.objectContaining({
          channel: 'test-channel-id',
          user: 'inviter-user-id',
        }),
      );
    });

    it('should handle missing target user ID', async () => {
      // Setup channel without installing user ID
      const mockChannelNoUser = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: {
          ...mockInstallation,
          installingUserId: undefined,
        },
      };
      mockChannelsRepository.findByCondition.mockResolvedValue(
        mockChannelNoUser,
      );

      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
      );

      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'No user ID found for ephemeral message target',
      );
      expect(mockSlackWebAPIService.sendEphemeral).not.toHaveBeenCalled();
    });
  });
});
