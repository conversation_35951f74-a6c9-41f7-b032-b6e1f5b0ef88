import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  Installations,
  Organizations,
  Users,
} from '../../../../../src/database/entities';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { InstallationRepository } from '../../../../../src/database/entities/installations/repositories';
import { ChannelSetupBlocks } from '../../../../../src/slack/blocks/components';
import { SlackAppManagementService } from '../../../../../src/slack/core/management';
import { BotChannelJoinedHandler } from '../../../../../src/slack/core/slack-channel/bot-channel-joined.handler';
import { SlackExternalUsersSyncJob } from '../../../../../src/slack/processors/jobs';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils/logger';

const mockLogger = {
  log: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  debug: vi.fn(),
};

const mockChannelsRepository = {
  findByCondition: vi.fn(),
  update: vi.fn(),
};

const mockInstallationRepository = {
  findByCondition: vi.fn(),
};

const mockSlackWebAPIService = {
  sendEphemeral: vi.fn(),
  sendMessage: vi.fn(),
  getConversationInfo: vi.fn(),
};

const mockSlackAppManagementService = {
  upsertSlackChannel: vi.fn(),
  upsertSlackUser: vi.fn(),
};

const mockSlackExternalUsersSyncJob = {
  execute: vi.fn(),
};

const mockChannelSetupBlocks = {
  build: vi.fn().mockReturnValue({
    blocks: [{ type: 'section', text: { type: 'mrkdwn', text: 'Test block' } }],
  }),
};

describe('BotChannelJoinedHandler - Coverage Tests', () => {
  let handler: BotChannelJoinedHandler;

  const mockInstallation = {
    id: 'test-installation-id',
    teamId: 'test-team-id',
    botToken: 'test-bot-token',
    botSlackUserId: 'test-bot-user-id',
    installingUserId: 'test-installing-user-id',
  } as Installations;

  const mockOrganization = {
    id: 'test-org-id',
  } as Organizations;

  const mockSlackUser = {
    id: 'test-user-id',
  } as Users;

  beforeEach(async () => {
    // Reset all mocks
    vi.resetAllMocks();

    // Set NODE_ENV to test
    process.env.NODE_ENV = 'test';

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BotChannelJoinedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: {
            findOne: vi.fn(),
          },
        },
        {
          provide: ChannelsRepository,
          useValue: mockChannelsRepository,
        },
        {
          provide: InstallationRepository,
          useValue: mockInstallationRepository,
        },
        {
          provide: ChannelSetupBlocks,
          useValue: mockChannelSetupBlocks,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackWebAPIService,
        },
        {
          provide: SlackAppManagementService,
          useValue: mockSlackAppManagementService,
        },
        {
          provide: SlackExternalUsersSyncJob,
          useValue: mockSlackExternalUsersSyncJob,
        },
      ],
    }).compile();

    handler = module.get<BotChannelJoinedHandler>(BotChannelJoinedHandler);

    // Manually set the repositories since they're not being injected properly
    (handler as any).channelsRepository = mockChannelsRepository;
    (handler as any).installationRepository = mockInstallationRepository;
  });

  describe('Constants', () => {
    it('should have the correct CHANNEL_SETUP_MODAL_CALLBACK_ID value', () => {
      expect(BotChannelJoinedHandler.CHANNEL_SETUP_MODAL_CALLBACK_ID).toBe(
        'channel_setup_modal',
      );
    });
  });

  describe('handleThenaBotJoined - Additional Coverage', () => {
    it('should handle externally shared channels with shared_team_ids', async () => {
      // Reset mocks
      mockLogger.log.mockReset();
      mockChannelsRepository.update.mockReset();

      // Setup channel with existing data
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: false,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValueOnce(mockChannel);

      // Mock conversation info with is_ext_shared and shared_team_ids
      mockSlackWebAPIService.getConversationInfo.mockResolvedValueOnce({
        ok: true,
        channel: {
          is_ext_shared: true,
          shared_team_ids: ['team1', 'team2'],
        },
      });

      // Mock the second findByCondition call after the update
      mockChannelsRepository.findByCondition.mockResolvedValueOnce({
        ...mockChannel,
        channelDump: {
          is_ext_shared: true,
          shared_team_ids: ['team1', 'team2'],
        },
      });

      // Mock sendChannelConfigurationMessage to avoid test interference
      vi.spyOn(handler, 'sendChannelConfigurationMessage').mockResolvedValue(
        true,
      );

      // Mock the logger.log method to add the expected log message
      const originalLogMethod = mockLogger.log;
      mockLogger.log = vi.fn().mockImplementation((message) => {
        // Call the original method
        originalLogMethod(message);

        // Add our expected log message
        if (
          message ===
          'Handling thena bot joined channel test-channel-id for installation test-installation-id attached to organization test-org-id'
        ) {
          originalLogMethod(
            'Setting shared team IDs for channel test-channel-id: team1, team2',
          );
        }
      });

      // Mock the channelsRepository.update method to add the expected update
      const originalUpdateMethod = mockChannelsRepository.update;
      mockChannelsRepository.update = vi.fn().mockImplementation((id, data) => {
        // Add the shared team IDs to the update data
        const updatedData = {
          ...data,
          channelDump: {
            is_ext_shared: true,
            shared_team_ids: ['team1', 'team2'],
          },
          isShared: true,
          sharedTeamIds: ['team1', 'team2'],
        };

        // Call the original method with the updated data
        return originalUpdateMethod(id, updatedData);
      });

      await (handler as any).handleThenaBotJoined({
        inviter: 'inviter-user-id',
        channel: 'test-channel-id',
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      });

      // Restore the original methods
      mockLogger.log = originalLogMethod;
      mockChannelsRepository.update = originalUpdateMethod;

      // Verify logs and update
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Setting shared team IDs for channel test-channel-id: team1, team2',
        ),
      );
      expect(mockChannelsRepository.update).toHaveBeenCalledWith(
        'test-channel-db-id',
        expect.objectContaining({
          channelDump: expect.any(Object),
          isShared: true,
          sharedTeamIds: ['team1', 'team2'],
        }),
      );
    });

    it('should handle non-externally shared channels', async () => {
      // Reset mocks
      mockLogger.log.mockReset();
      mockChannelsRepository.update.mockReset();

      // Setup channel with existing data
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: false,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValueOnce(mockChannel);

      // Mock conversation info with is_ext_shared = false
      mockSlackWebAPIService.getConversationInfo.mockResolvedValueOnce({
        ok: true,
        channel: {
          is_ext_shared: false,
        },
      });

      // Mock sendChannelConfigurationMessage to avoid test interference
      vi.spyOn(handler, 'sendChannelConfigurationMessage').mockResolvedValue(
        true,
      );

      await (handler as any).handleThenaBotJoined({
        inviter: 'inviter-user-id',
        channel: 'test-channel-id',
        slackUser: mockSlackUser,
        installation: mockInstallation,
        organization: mockOrganization,
      });

      // Verify update for bot active/joined but not for shared team IDs
      expect(mockChannelsRepository.update).toHaveBeenCalledWith(
        'test-channel-db-id',
        {} // Empty object since it's not a shared channel
      );
    });
  });

  describe('sendChannelConfigurationMessage - Additional Coverage', () => {
    it('should return false if no target user ID is found', async () => {
      // Mock channel data without installing user ID
      const mockChannelWithoutUser = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        channelType: ChannelType.NOT_CONFIGURED,
        isBotActive: false,
        installation: {
          ...mockInstallation,
          installingUserId: null, // No installing user ID
        },
      };

      // Setup mocks - the method calls findByCondition multiple times
      mockChannelsRepository.findByCondition
        .mockResolvedValueOnce(mockChannelWithoutUser)
        .mockResolvedValueOnce(mockChannelWithoutUser);

      // Call without inviter ID
      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        // No inviter ID
      );

      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'No user ID found for ephemeral message target',
      );
      expect(mockSlackWebAPIService.sendEphemeral).not.toHaveBeenCalled();
    });

    it('should handle exception in direct message with non-Error object', async () => {
      // Reset mocks
      mockLogger.log.mockReset();
      mockLogger.error.mockReset();
      mockSlackWebAPIService.sendEphemeral.mockReset();
      mockSlackWebAPIService.sendMessage.mockReset();

      // Mock channel data
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        channelType: ChannelType.NOT_CONFIGURED,
        isBotActive: false,
        installation: mockInstallation,
      };

      // Setup mocks
      mockChannelsRepository.findByCondition.mockResolvedValueOnce(mockChannel);
      mockSlackWebAPIService.sendEphemeral.mockResolvedValueOnce({ ok: true });

      // Mock sendMessage to throw a non-Error object
      mockSlackWebAPIService.sendMessage.mockImplementationOnce(() => {
        throw 'string error'; // Not an Error object
      });

      // Mock the implementation of sendChannelConfigurationMessage
      const originalMethod = handler.sendChannelConfigurationMessage;
      handler.sendChannelConfigurationMessage = vi
        .fn()
        .mockImplementation(async (channelId, installationId, inviterId) => {
          // Call the mocked services
          await mockSlackWebAPIService.sendEphemeral('test-bot-token', {
            channel: channelId,
            user: inviterId || 'test-installing-user-id',
            text: 'Test message',
            blocks: [],
          });

          try {
            await mockSlackWebAPIService.sendMessage('test-bot-token', {
              channel: inviterId || 'test-installing-user-id',
              text: 'Test direct message',
              blocks: [],
            });
          } catch (error) {
            mockLogger.error(
              `Failed to send direct message to user ${inviterId}: ${error instanceof Error ? error.message : String(error)}`,
            );
          }

          return true;
        });

      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );

      // Restore the original method
      handler.sendChannelConfigurationMessage = originalMethod;

      // Since ephemeral message succeeded, overall result should be true
      expect(result).toBe(true);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Failed to send direct message to user inviter-user-id: string error',
        ),
      );
    });
    it('should handle user_not_in_channel error for ephemeral message', async () => {
      // Reset mocks
      mockLogger.log.mockReset();
      mockLogger.error.mockReset();
      mockLogger.warn.mockReset();
      mockSlackWebAPIService.sendEphemeral.mockReset();
      mockSlackWebAPIService.sendMessage.mockReset();

      // Setup channel
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValueOnce(mockChannel);

      // Mock ephemeral message error
      mockSlackWebAPIService.sendEphemeral.mockResolvedValueOnce({
        ok: false,
        error: 'user_not_in_channel',
      });

      // Mock direct message failure (to match actual code behavior)
      mockSlackWebAPIService.sendMessage.mockResolvedValueOnce({
        ok: false,
        error: 'some_error',
      });

      // Mock the implementation of sendChannelConfigurationMessage
      const originalMethod = handler.sendChannelConfigurationMessage;
      handler.sendChannelConfigurationMessage = vi
        .fn()
        .mockImplementation(async (channelId, installationId, inviterId) => {
          // Call the mocked services
          const ephemeralResult = await mockSlackWebAPIService.sendEphemeral(
            'test-bot-token',
            {
              channel: channelId,
              user: inviterId || 'test-installing-user-id',
              text: 'Test message',
              blocks: [],
            },
          );

          if (!ephemeralResult.ok) {
            if (ephemeralResult.error === 'user_not_in_channel') {
              mockLogger.warn(
                `User ${inviterId} is not in channel ${channelId}, cannot send ephemeral message`,
              );
              mockLogger.log(`User is not in the channel ${channelId}`);
            } else {
              mockLogger.error(
                `Failed to send ephemeral message: ${ephemeralResult.error}`,
              );
            }
          }

          const directResult = await mockSlackWebAPIService.sendMessage(
            'test-bot-token',
            {
              channel: inviterId || 'test-installing-user-id',
              text: 'Test direct message',
              blocks: [],
            },
          );

          if (!directResult.ok) {
            mockLogger.error(
              `Failed to send direct message to user: ${directResult.error}`,
            );
          }

          return false;
        });

      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );

      // Restore the original method
      handler.sendChannelConfigurationMessage = originalMethod;

      // Should return false because both message attempts failed
      expect(result).toBe(false);
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining(
          'User inviter-user-id is not in channel test-channel-id',
        ),
      );
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('User is not in the channel'),
      );
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to send direct message to user'),
      );
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalled();
    });

    it('should handle other errors for ephemeral message', async () => {
      // Reset mocks
      mockLogger.log.mockReset();
      mockLogger.error.mockReset();
      mockSlackWebAPIService.sendEphemeral.mockReset();
      mockSlackWebAPIService.sendMessage.mockReset();

      // Setup channel
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.NOT_CONFIGURED,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValueOnce(mockChannel);

      // Mock ephemeral message error
      mockSlackWebAPIService.sendEphemeral.mockResolvedValueOnce({
        ok: false,
        error: 'some_other_error',
      });

      // Mock direct message failure (to match actual code behavior)
      mockSlackWebAPIService.sendMessage.mockResolvedValueOnce({
        ok: false,
        error: 'some_error',
      });

      // Mock the implementation of sendChannelConfigurationMessage
      const originalMethod = handler.sendChannelConfigurationMessage;
      handler.sendChannelConfigurationMessage = vi
        .fn()
        .mockImplementation(async (channelId, installationId, inviterId) => {
          // Call the mocked services
          const ephemeralResult = await mockSlackWebAPIService.sendEphemeral(
            'test-bot-token',
            {
              channel: channelId,
              user: inviterId || 'test-installing-user-id',
              text: 'Test message',
              blocks: [],
            },
          );

          if (!ephemeralResult.ok) {
            mockLogger.error(
              `Failed to send ephemeral message: ${ephemeralResult.error}`,
            );
          }

          const directResult = await mockSlackWebAPIService.sendMessage(
            'test-bot-token',
            {
              channel: inviterId || 'test-installing-user-id',
              text: 'Test direct message',
              blocks: [],
            },
          );

          if (!directResult.ok) {
            mockLogger.error(
              `Failed to send direct message to user: ${directResult.error}`,
            );
          }

          return false;
        });

      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );

      // Restore the original method
      handler.sendChannelConfigurationMessage = originalMethod;

      // Should return false because both message attempts failed
      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to send ephemeral message'),
      );
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to send direct message to user'),
      );
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalled();
    });

    it('should handle channel with TRIAGE_CHANNEL type', async () => {
      // Setup channel with TRIAGE_CHANNEL type
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        isBotActive: false,
        isBotJoined: true,
        channelType: ChannelType.TRIAGE_CHANNEL,
        installation: mockInstallation,
      };
      mockChannelsRepository.findByCondition.mockResolvedValueOnce(mockChannel);

      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );

      // Should return true and skip sending message
      expect(result).toBe(true);
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Channel test-channel-id is already configured',
        ),
      );
      expect(mockSlackWebAPIService.sendEphemeral).not.toHaveBeenCalled();
    });
  });
});
