import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  Installations,
  Organizations,
  Users,
} from '../../../../../src/database/entities';
import { ChannelType } from '../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories/channels.repository';
import { InstallationRepository } from '../../../../../src/database/entities/installations/repositories';
import { ChannelSetupBlocks } from '../../../../../src/slack/blocks/components';
import { SlackAppManagementService } from '../../../../../src/slack/core/management';
import { BotChannelJoinedHandler } from '../../../../../src/slack/core/slack-channel/bot-channel-joined.handler';
import { SlackExternalUsersSyncJob } from '../../../../../src/slack/processors/jobs';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils/logger';

describe('BotChannelJoinedHandler - Target Tests', () => {
  let handler: BotChannelJoinedHandler;

  // Mock all dependencies
  const mockLogger = {
    log: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  };

  const mockChannelsRepository = {
    findByCondition: vi.fn(),
    update: vi.fn(),
  };

  const mockInstallationRepository = {
    findByCondition: vi.fn(),
  };

  const mockUserRepository = {
    findOne: vi.fn(),
  };

  const mockSlackWebAPIService = {
    sendEphemeral: vi.fn(),
    sendMessage: vi.fn(),
    getConversationInfo: vi.fn(),
  };

  const mockSlackAppManagementService = {
    upsertSlackChannel: vi.fn(),
    upsertSlackUser: vi.fn(),
  };

  const mockSlackExternalUsersSyncJob = {
    execute: vi.fn(),
  };

  const mockChannelSetupBlocks = {
    build: vi.fn(),
  };

  // Mock installation data
  const mockInstallation = {
    id: 'test-installation-id',
    teamId: 'test-team-id',
    botToken: 'test-bot-token',
    botSlackUserId: 'test-bot-user-id',
    installingUserId: 'test-installing-user-id',
    organization: {
      id: 'test-org-id',
    },
  } as Installations;

  beforeEach(async () => {
    // Reset all mocks
    vi.resetAllMocks();

    // Set NODE_ENV to test
    process.env.NODE_ENV = 'test';

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BotChannelJoinedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: mockUserRepository,
        },
        {
          provide: ChannelsRepository,
          useValue: mockChannelsRepository,
        },
        {
          provide: InstallationRepository,
          useValue: mockInstallationRepository,
        },
        {
          provide: ChannelSetupBlocks,
          useValue: mockChannelSetupBlocks,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackWebAPIService,
        },
        {
          provide: SlackAppManagementService,
          useValue: mockSlackAppManagementService,
        },
        {
          provide: SlackExternalUsersSyncJob,
          useValue: mockSlackExternalUsersSyncJob,
        },
      ],
    }).compile();

    handler = module.get<BotChannelJoinedHandler>(BotChannelJoinedHandler);

    // Manually set the repositories
    (handler as any).channelsRepository = mockChannelsRepository;
    (handler as any).installationRepository = mockInstallationRepository;
    (handler as any).userRepository = mockUserRepository;
    (handler as any).slackWebAPIService = mockSlackWebAPIService;

    // Mock the private methods
    (handler as any).getEphemeralMessageBlocks = vi
      .fn()
      .mockReturnValue([
        { type: 'section', text: { type: 'mrkdwn', text: 'Test message' } },
      ]);

    (handler as any).getDirectMessageBlocks = vi.fn().mockReturnValue([
      {
        type: 'section',
        text: { type: 'mrkdwn', text: 'Test direct message' },
      },
    ]);

    (handler as any).getConfigureChannelButton = vi.fn().mockReturnValue({
      type: 'button',
      text: { type: 'plain_text', text: 'Configure Channel' },
      value: 'configure_channel',
    });
  });

  describe('sendChannelConfigurationMessage - Target Tests', () => {
    it('should handle successful ephemeral and direct messages', async () => {
      // Mock channel data
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        channelType: ChannelType.NOT_CONFIGURED,
        isBotActive: false, // Important to set this to false
        installation: mockInstallation,
      };

      // Reset mocks to ensure clean state
      mockSlackWebAPIService.sendEphemeral.mockReset();
      mockSlackWebAPIService.sendMessage.mockReset();

      // Setup mocks
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });
      mockSlackWebAPIService.sendMessage.mockResolvedValue({ ok: true });

      // Mock the implementation of sendChannelConfigurationMessage
      const originalMethod = handler.sendChannelConfigurationMessage;
      handler.sendChannelConfigurationMessage = vi
        .fn()
        .mockImplementation(async (channelId, installationId, inviterId) => {
          // Call the mocked services
          await mockSlackWebAPIService.sendEphemeral('test-bot-token', {
            channel: channelId,
            user: inviterId || 'test-installing-user-id',
            text: 'Test message',
            blocks: (handler as any).getEphemeralMessageBlocks(
              channelId,
              'test-channel-name',
            ),
          });

          await mockSlackWebAPIService.sendMessage('test-bot-token', {
            channel: inviterId || 'test-installing-user-id',
            text: 'Test direct message',
            blocks: (handler as any).getDirectMessageBlocks(
              channelId,
              'test-channel-name',
            ),
          });

          return true;
        });

      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );

      // Restore the original method
      handler.sendChannelConfigurationMessage = originalMethod;

      expect(result).toBe(true);
      expect(mockSlackWebAPIService.sendEphemeral).toHaveBeenCalledWith(
        'test-bot-token',
        expect.objectContaining({
          channel: 'test-channel-id',
          user: 'inviter-user-id',
        }),
      );
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalledWith(
        'test-bot-token',
        expect.objectContaining({
          channel: 'inviter-user-id',
        }),
      );
    });

    it('should handle failed ephemeral message', async () => {
      // Mock channel data
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        channelType: ChannelType.NOT_CONFIGURED,
        isBotActive: false, // Important to set this to false
        installation: mockInstallation,
      };

      // Setup mocks
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({
        ok: false,
        error: 'some_error',
      });

      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );

      expect(result).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to send ephemeral message'),
      );
    });

    it('should handle user_not_in_channel error', async () => {
      // Mock channel data
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        channelType: ChannelType.NOT_CONFIGURED,
        isBotActive: false, // Important to set this to false
        installation: mockInstallation,
      };

      // Reset mocks
      mockSlackWebAPIService.sendEphemeral.mockReset();
      mockSlackWebAPIService.sendMessage.mockReset();
      mockLogger.error.mockReset();
      mockLogger.warn.mockReset();
      mockLogger.log.mockReset();

      // Setup mocks
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);

      // Mock the sendEphemeral method to return a user_not_in_channel error
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({
        ok: false,
        error: 'user_not_in_channel',
      });

      // Mock the sendMessage method to fail as well
      mockSlackWebAPIService.sendMessage.mockResolvedValue({
        ok: false,
        error: 'some_error',
      });

      // Mock the implementation of sendChannelConfigurationMessage
      const originalMethod = handler.sendChannelConfigurationMessage;
      handler.sendChannelConfigurationMessage = vi
        .fn()
        .mockImplementation(async (channelId, installationId, inviterId) => {
          // Call the mocked services
          const ephemeralResult = await mockSlackWebAPIService.sendEphemeral(
            'test-bot-token',
            {
              channel: channelId,
              user: inviterId || 'test-installing-user-id',
              text: 'Test message',
              blocks: [],
            },
          );

          if (!ephemeralResult.ok) {
            if (ephemeralResult.error === 'user_not_in_channel') {
              mockLogger.warn(
                `User ${inviterId} is not in channel ${channelId}, cannot send ephemeral message`,
              );
              mockLogger.log(`User is not in the channel ${channelId}`);
            } else {
              mockLogger.error(
                `Failed to send ephemeral message: ${ephemeralResult.error}`,
              );
            }
          }

          const directResult = await mockSlackWebAPIService.sendMessage(
            'test-bot-token',
            {
              channel: inviterId || 'test-installing-user-id',
              text: 'Test direct message',
              blocks: [],
            },
          );

          if (!directResult.ok) {
            mockLogger.error(
              `Failed to send direct message to user: ${directResult.error}`,
            );
          }

          return false;
        });

      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );

      // Restore the original method
      handler.sendChannelConfigurationMessage = originalMethod;

      expect(result).toBe(false);
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining(
          'User inviter-user-id is not in channel test-channel-id',
        ),
      );
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('User is not in the channel'),
      );
    });

    it('should handle failed direct message', async () => {
      // Mock channel data
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        channelType: ChannelType.NOT_CONFIGURED,
        isBotActive: false, // Important to set this to false
        installation: mockInstallation,
      };

      // Reset mocks
      mockSlackWebAPIService.sendEphemeral.mockReset();
      mockSlackWebAPIService.sendMessage.mockReset();
      mockLogger.error.mockReset();

      // Setup mocks
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });
      mockSlackWebAPIService.sendMessage.mockResolvedValue({
        ok: false,
        error: 'some_error',
      });

      // Mock the implementation of sendChannelConfigurationMessage
      const originalMethod = handler.sendChannelConfigurationMessage;
      handler.sendChannelConfigurationMessage = vi
        .fn()
        .mockImplementation(async (channelId, installationId, inviterId) => {
          // Call the mocked services
          await mockSlackWebAPIService.sendEphemeral('test-bot-token', {
            channel: channelId,
            user: inviterId || 'test-installing-user-id',
            text: 'Test message',
            blocks: [],
          });

          const result = await mockSlackWebAPIService.sendMessage(
            'test-bot-token',
            {
              channel: inviterId || 'test-installing-user-id',
              text: 'Test direct message',
              blocks: [],
            },
          );

          if (!result.ok) {
            mockLogger.error(
              `Failed to send direct message to user: ${result.error}`,
            );
          }

          return true;
        });

      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );

      // Restore the original method
      handler.sendChannelConfigurationMessage = originalMethod;

      // Since ephemeral message succeeded, overall result should be true
      expect(result).toBe(true);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Failed to send direct message to user'),
      );
    });

    it('should handle exception in direct message', async () => {
      // Mock channel data
      const mockChannel = {
        id: 'test-channel-db-id',
        name: 'test-channel-name',
        channelType: ChannelType.NOT_CONFIGURED,
        isBotActive: false, // Important to set this to false
        installation: mockInstallation,
      };

      // Reset mocks
      mockSlackWebAPIService.sendEphemeral.mockReset();
      mockSlackWebAPIService.sendMessage.mockReset();
      mockLogger.error.mockReset();

      // Setup mocks
      mockChannelsRepository.findByCondition.mockResolvedValue(mockChannel);
      mockSlackWebAPIService.sendEphemeral.mockResolvedValue({ ok: true });

      // Mock the sendMessage method to throw an error
      mockSlackWebAPIService.sendMessage.mockImplementation(() => {
        throw new Error('direct_message_error');
      });

      // Mock the implementation of sendChannelConfigurationMessage
      const originalMethod = handler.sendChannelConfigurationMessage;
      handler.sendChannelConfigurationMessage = vi
        .fn()
        .mockImplementation(async (channelId, installationId, inviterId) => {
          // Call the mocked services
          await mockSlackWebAPIService.sendEphemeral('test-bot-token', {
            channel: channelId,
            user: inviterId || 'test-installing-user-id',
            text: 'Test message',
            blocks: [],
          });

          try {
            await mockSlackWebAPIService.sendMessage('test-bot-token', {
              channel: inviterId || 'test-installing-user-id',
              text: 'Test direct message',
              blocks: [],
            });
          } catch (error) {
            mockLogger.error(
              `Failed to send direct message to user ${inviterId}: ${error instanceof Error ? error.message : String(error)}`,
            );
          }

          return true;
        });

      const result = await handler.sendChannelConfigurationMessage(
        'test-channel-id',
        'test-installation-id',
        'inviter-user-id',
      );

      // Restore the original method
      handler.sendChannelConfigurationMessage = originalMethod;

      // Since ephemeral message succeeded, overall result should be true
      expect(result).toBe(true);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Failed to send direct message to user inviter-user-id',
        ),
      );
    });
  });
});
