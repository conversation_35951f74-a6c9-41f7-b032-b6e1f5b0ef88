import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { InstallationWithOrganization } from '../../../../../src/common/interfaces/common.interface';
import { ConfigService } from '../../../../../src/config/config.service';
import { TransactionService } from '../../../../../src/database/common';
import { Installations, Users } from '../../../../../src/database/entities';
import {
  ChannelType,
  Channels,
} from '../../../../../src/database/entities/channels/channels.entity';
import { ChannelsRepository } from '../../../../../src/database/entities/channels/repositories';
import { TriageMapsRepository } from '../../../../../src/database/entities/mappings/repositories/triage-maps.repository';
import { SlackTriageMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/slack-triage-messages.repository';
import { SlackMessages } from '../../../../../src/database/entities/slack-messages/slack-messages.entity';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { TriageMessageBlock } from '../../../../../src/slack/blocks/components';
import { CoreTriageService } from '../../../../../src/slack/core/messages/triage.core';
import { SlackLinkSharedHandler } from '../../../../../src/slack/event-handlers/handlers/links/link-shared.handler';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { ILogger } from '../../../../../src/utils/logger/logger.interface';
import { parseWithMentions } from '../../../../../src/utils/parsers/slack/mentions.parser';

describe('CoreTriageService', () => {
  let service: CoreTriageService;
  let mockLogger: ILogger;
  let mockThenaPlatformApi: ThenaPlatformApiProvider;
  let mockSlackWebAPIService: SlackWebAPIService;
  let mockConfigService: ConfigService;
  let mockTransactionService: TransactionService;
  let mockLinkSharedHandler: SlackLinkSharedHandler;
  let mockSlackUsersRepository: Repository<Users>;
  let mockTriageMapsRepository: TriageMapsRepository;
  let mockSlackTriageMessagesRepository: SlackTriageMessagesRepository;
  let mockChannelsRepository: ChannelsRepository;
  let mockSlackMessagesRepository: any;
  let mockTriageMessageBlock: TriageMessageBlock;

  beforeEach(() => {
    vi.resetAllMocks();

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockThenaPlatformApi = {
      createNewComment: vi.fn(),
      getTicket: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    mockSlackWebAPIService = {
      sendMessage: vi
        .fn()
        .mockResolvedValue({ ok: true, ts: '**********.123456' }),
      getTeamInfo: vi.fn(),
      unfurlLink: vi.fn(),
      joinConversation: vi.fn(),
      updateMessage: vi.fn(),
      getPermalink: vi.fn().mockResolvedValue({ ok: true, permalink: 'https://example.com/permalink' }),
    } as unknown as SlackWebAPIService;

    mockConfigService = {
      get: vi.fn(),
    } as unknown as ConfigService;

    mockTransactionService = {
      runInTransaction: vi.fn(async (callback) => callback({})),
    } as unknown as TransactionService;

    mockLinkSharedHandler = {
      generateUnfurlContent: vi.fn(),
    } as unknown as SlackLinkSharedHandler;

    mockSlackUsersRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<Users>;

    mockTriageMapsRepository = {
      findAll: vi.fn(),
      saveWithTxn: vi.fn(),
    } as unknown as TriageMapsRepository;

    mockSlackTriageMessagesRepository = {
      saveWithTxn: vi.fn(),
      saveManyWithTxn: vi.fn(),
      findByCondition: vi.fn().mockResolvedValue([]),
      findAll: vi.fn(),
    } as unknown as SlackTriageMessagesRepository;

    mockChannelsRepository = {
      findByCondition: vi.fn().mockResolvedValue([]),
      findAll: vi.fn(),
      updateWithTxn: vi.fn(),
    } as unknown as ChannelsRepository;

    mockSlackMessagesRepository = {
      saveWithTxn: vi.fn(),
      findByCondition: vi.fn(),
    };

    mockTriageMessageBlock = {
      build: vi.fn().mockReturnValue({ blocks: [] }),
      getTicketPermalink: vi
        .fn()
        .mockReturnValue('https://example.com/ticket/123'),
    } as unknown as TriageMessageBlock;

    service = new CoreTriageService(
      mockLogger,
      mockThenaPlatformApi,
      mockSlackWebAPIService,
      mockConfigService,
      mockTransactionService,
      mockLinkSharedHandler,
      mockSlackUsersRepository,
      mockTriageMapsRepository,
      mockSlackTriageMessagesRepository,
      mockChannelsRepository,
      mockSlackMessagesRepository,
      mockTriageMessageBlock,
    );
  });

  describe('sendTriageMessageToChannel', () => {
    it('should send triage messages to matching channels', async () => {
      const ticket = {
        id: 'ticket-123',
        title: 'Test Ticket',
        description: 'Test Description',
        priority: 'High',
        priorityId: 'high',
        status: 'Open',
        statusId: 'open',
        teamId: 'team-123',
        ticketId: 'T-123',
        createdAt: new Date().toISOString(),
        customerContactEmail: '<EMAIL>',
        customerContactFirstName: 'Test',
        customerContactLastName: 'Customer',
      };

      // Mock the ticketDetails that would be returned from platform API
      const ticketDetails = {
        requestorEmail: '<EMAIL>',
        customerContactEmail: '<EMAIL>',
        customerContactFirstName: 'Test',
        customerContactLastName: 'Customer',
      };

      // Set up the platform API to return the ticket details
      (mockThenaPlatformApi.getTicket as Mock).mockResolvedValue(ticketDetails);

      const installation = {
        id: 'installation-id',
        name: 'Test Workspace',
        teamId: 'T12345',
        teamName: 'Test Team',
        botToken: 'mock-bot-token',
        botSlackId: 'B12345',
        botSlackUserId: 'U12345',
        installingUserSlackId: 'U67890',
        installingUserName: 'Test User',
        organization: { id: 'org-id' },
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-1',
        channelId: 'C12345',
        channelName: 'triage-channel-1',
        channelType: ChannelType.TRIAGE_CHANNEL,
        name: 'triage-channel-1',
        channelDump: {},
        slackCreatedAt: new Date(),
        isBotActive: true,
        isArchived: false,
        isPrivate: false,
        isShared: false,
        memberCount: 10,
        purpose: 'Test purpose',
        topic: 'Test topic',
        installation: { id: installation.id },
        organization: { id: 'org-id' },
      } as unknown as Channels;

      const mockSlackMessage = {
        id: 'message-id',
        platformTicketId: ticket.id,
        platformCommentId: null,
        slackMessageTs: '**********.123456',
        slackMessageThreadTs: null,
        slackChannelId: 'C12345',
        slackUserId: 'U12345',
        slackTeamId: 'T12345',
        slackPermalink: 'https://slack.com/archives/C12345/p**********123456',
        messageText: 'Test message',
        isDeleted: false,
        isThreadParent: false,
        isThreadMessage: false,
        installation: { id: installation.id },
        organization: { id: 'org-id' },
      } as unknown as SlackMessages;

      await service.sendTriageMessageToChannel(installation, {
        channel: mockChannel,
        ticket,
        slackMessage: mockSlackMessage,
      }, false);

      expect(mockTriageMessageBlock.build).toHaveBeenCalled();
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalled();
      expect(mockSlackTriageMessagesRepository.saveWithTxn).toHaveBeenCalled();
    });

    it('should return existing triage message when returnExisting is true and message exists', async () => {
      const ticket = {
        id: 'ticket-123',
        title: 'Test Ticket',
        teamId: 'team-123',
        ticketId: 'T-123',
      };

      const installation = {
        id: 'installation-id',
        botToken: 'mock-bot-token',
        organization: { id: 'org-id' },
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-1',
        channelId: 'C12345',
        name: 'triage-channel-1',
        installation: { id: installation.id },
        organization: { id: 'org-id' },
      } as unknown as Channels;

      const mockSlackMessage = {
        id: 'message-id',
        platformTicketId: ticket.id,
      } as unknown as SlackMessages;

      const existingTriageMessage = {
        id: 'triage-message-id',
        slackMessageTs: '**********.123456',
        channel: { id: mockChannel.id },
        platformThreadId: 'thread-123',
        slackRequestMessage: { id: mockSlackMessage.id },
      };

      // Mock findByCondition to return an existing triage message
      (
        mockSlackTriageMessagesRepository.findByCondition as Mock
      ).mockResolvedValue(existingTriageMessage);

      const result = await service.sendTriageMessageToChannel(installation, {
        channel: mockChannel,
        ticket,
        slackMessage: mockSlackMessage,
        returnExisting: true,
      }, false);

      // Verify that the existing message was returned
      expect(result).toBe(existingTriageMessage);

      // Verify that no new message was sent
      expect(mockSlackWebAPIService.sendMessage).not.toHaveBeenCalled();
      expect(
        mockSlackTriageMessagesRepository.saveWithTxn,
      ).not.toHaveBeenCalled();
      expect(mockThenaPlatformApi.getTicket).not.toHaveBeenCalled();
    });

    it('should handle message with commentAs user', async () => {
      const ticket = {
        id: 'ticket-123',
        title: 'Test Ticket',
        priority: 'High',
        priorityId: 'high',
        status: 'Open',
        statusId: 'open',
        teamId: 'team-123',
        ticketId: 'T-123',
        createdAt: new Date().toISOString(),
      };

      const ticketDetails = {
        requestorEmail: '<EMAIL>',
        customerContactFirstName: 'Test',
      };

      const installation = {
        id: 'installation-id',
        botToken: 'mock-bot-token',
        organization: { id: 'org-id' },
      } as unknown as Installations;

      const mockChannel = {
        id: 'channel-1',
        channelId: 'C12345',
        name: 'triage-channel-1',
        installation: { id: installation.id },
        organization: { id: 'org-id' },
      } as unknown as Channels;

      const mockSlackMessage = {
        id: 'message-id',
        platformTicketId: ticket.id,
        slackPermalink: 'https://slack.com/archives/C12345/p**********123456',
      } as unknown as SlackMessages;

      const commentAsUser = {
        id: 'user-id',
        slackId: 'U12345',
        slackProfileDisplayName: 'Test User',
        slackProfileRealName: 'Test User Real Name',
        getUserAvatar: vi
          .fn()
          .mockReturnValue('https://example.com/avatar.png'),
      };

      // Set up the platform API to return the ticket details
      (mockThenaPlatformApi.getTicket as Mock).mockResolvedValue(ticketDetails);

      // Mock joinConversation to succeed
      (mockSlackWebAPIService.joinConversation as Mock).mockResolvedValue({});

      await service.sendTriageMessageToChannel(installation, {
        channel: mockChannel,
        ticket,
        slackMessage: mockSlackMessage,
        commentAs: commentAsUser as unknown as Users,
        message: 'This is a test message',
      }, false);

      // Verify that the message was sent with the user's details
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalledTimes(2);
      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalledWith(
        installation.botToken,
        expect.objectContaining({
          thread_ts: expect.any(String),
          text: 'This is a test message',
          username: commentAsUser.slackProfileDisplayName,
          icon_url: commentAsUser.getUserAvatar(),
        }),
      );
    });
  });

  describe('createTriageMapping', () => {
    it('should create a triage mapping successfully', async () => {
      const installation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: { id: 'org-id' },
      } as unknown as InstallationWithOrganization;

      const triageChannel = {
        id: 'channel-id',
        channelId: 'C12345',
        channelName: 'triage-channel',
        installation: { id: installation.id },
        organization: { id: 'org-id' },
      } as unknown as Channels;

      const customerChannel = {
        id: 'customer-channel-id',
        channelId: 'C67890',
        channelName: 'customer-channel',
        installation: { id: installation.id },
        organization: { id: 'org-id' },
      } as unknown as Channels;

      const triageMapping = {
        id: 'mapping-id',
        triageChannel: { id: triageChannel.id },
        activeChannel: { id: customerChannel.id },
        installation: { id: installation.id },
        organization: { id: 'org-id' },
      };

      (mockChannelsRepository.findByCondition as Mock)
        .mockResolvedValueOnce(triageChannel)
        .mockResolvedValueOnce(customerChannel);
      (mockTriageMapsRepository.saveWithTxn as Mock).mockResolvedValue(
        triageMapping,
      );

      const result = await service.createTriageMapping(installation, {
        channelId: 'C12345',
        triageForChannelId: 'C67890',
      });

      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledTimes(2);
      expect(mockChannelsRepository.updateWithTxn).toHaveBeenCalled();
      expect(mockTriageMapsRepository.saveWithTxn).toHaveBeenCalled();
      expect(result).toBe(triageMapping);
    });

    it('should throw an error if channel is not found', async () => {
      const installation = {
        id: 'installation-id',
        teamId: 'T12345',
        organization: { id: 'org-id' },
      } as unknown as InstallationWithOrganization;

      (mockChannelsRepository.findByCondition as Mock).mockResolvedValue(null);

      await expect(
        service.createTriageMapping(installation, {
          channelId: 'C12345',
        }),
      ).rejects.toThrow('Triage channel not found');

      expect(mockChannelsRepository.findByCondition).toHaveBeenCalledTimes(1);
      expect(mockChannelsRepository.updateWithTxn).not.toHaveBeenCalled();
      expect(mockTriageMapsRepository.saveWithTxn).not.toHaveBeenCalled();
    });
  });

  describe('sendTriageMessages', () => {
    it('should send triage messages to all mapped channels', async () => {
      // Setup test data
      const installation = {
        id: 'installation-id',
        botToken: 'mock-bot-token',
        organization: { id: 'org-id' },
      } as unknown as Installations;

      const channel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'customer-channel',
      } as unknown as Channels;

      const ticket = {
        data: {
          id: 'ticket-123',
          ticketId: 'T-123',
          teamId: 'team-123',
          priority: 'High',
          priorityId: 'high',
          status: 'Open',
          statusId: 'open',
          title: 'Test Ticket',
          createdAt: new Date().toISOString(),
        },
      };

      const slackMessage = {
        id: 'message-id',
        slackPermalink: 'https://slack.com/archives/C12345/p**********123456',
      } as unknown as SlackMessages;

      const customer = {
        slackId: 'U12345',
        displayName: 'Test User',
        realName: 'Test User',
        slackProfileEmail: '<EMAIL>',
        userDump: { team_id: 'T12345' },
      };

      const triageMappings = [
        {
          id: 'mapping-1',
          triageChannel: {
            id: 'triage-channel-1',
            channelId: 'C67890',
            name: 'triage-channel-1',
          },
        },
        {
          id: 'mapping-2',
          triageChannel: {
            id: 'triage-channel-2',
            channelId: 'C54321',
            name: 'triage-channel-2',
          },
        },
      ];

      const teamInfo = {
        ok: true,
        team: {
          name: 'Test Team',
          icon: {
            image_132: 'https://example.com/icon.png',
          },
        },
      };

      const triageSent = {
        ok: true,
        ts: '**********.123456',
        channel: 'C67890',
      };

      const platformComment = {
        data: {
          id: 'comment-123',
        },
      };

      // Setup mocks
      (mockTriageMapsRepository.findAll as Mock).mockResolvedValue(
        triageMappings,
      );
      (mockSlackWebAPIService.getTeamInfo as Mock).mockResolvedValue(teamInfo);
      (mockSlackWebAPIService.sendMessage as Mock).mockResolvedValue(
        triageSent,
      );
      (mockThenaPlatformApi.createNewComment as Mock).mockResolvedValue(
        platformComment,
      );
      (mockChannelsRepository.findAll as Mock).mockResolvedValue([
        {
          id: 'triage-channel-1',
          channelId: 'C67890',
        },
      ]);

      // Call the method
      await service.sendTriageMessages(
        installation,
        channel,
        ticket,
        slackMessage,
        customer as any,
      );

      // Assertions
      expect(mockTriageMapsRepository.findAll).toHaveBeenCalledWith({
        where: {
          activeChannel: { id: channel.id },
          installation: { id: installation.id },
          organization: { id: installation.organization.id },
        },
        relations: { triageChannel: true },
        select: { triageChannel: { channelId: true, name: true } },
      });

      expect(mockSlackWebAPIService.getTeamInfo).toHaveBeenCalledWith(
        installation.botToken,
        {
          team: 'T12345',
        },
      );

      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalledTimes(
        triageMappings.length,
      );

      expect(mockThenaPlatformApi.createNewComment).toHaveBeenCalledTimes(
        triageMappings.length,
      );

      expect(
        mockSlackTriageMessagesRepository.saveManyWithTxn,
      ).toHaveBeenCalled();
    });
  });

  describe('sendDiscussionThreadToChannel', () => {
    it('should send a discussion thread to a channel', async () => {
      // Setup test data
      const installation = {
        id: 'installation-id',
        botToken: 'mock-bot-token',
        organization: { id: 'org-id' },
      } as unknown as Installations;

      const channel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'discussion-channel',
      } as unknown as Channels;

      const ticket = {
        id: 'ticket-123',
        ticketId: 'T-123',
        teamId: 'team-123',
      };

      const slackMessage = {
        id: 'message-id',
      } as unknown as SlackMessages;

      const message = 'This is a discussion thread';
      const platformCommentId = 'comment-123';

      const threadSent = {
        ok: true,
        ts: '**********.123456',
      };

      // Setup mocks
      (mockSlackWebAPIService.joinConversation as Mock).mockResolvedValue({});
      (mockSlackWebAPIService.sendMessage as Mock).mockResolvedValue(
        threadSent,
      );

      // Call the method
      await service.sendDiscussionThreadToChannel(installation, {
        channel,
        ticket,
        slackMessage,
        message,
        platformCommentId,
      });

      // Assertions
      expect(mockSlackWebAPIService.joinConversation).toHaveBeenCalledWith(
        installation.botToken,
        {
          channel: channel.channelId,
        },
      );

      expect(mockSlackWebAPIService.sendMessage).toHaveBeenCalledWith(
        installation.botToken,
        {
          channel: channel.channelId,
          metadata: {
            event_type: 'ticket_details',
            event_payload: {
              ticket_id: ticket.id,
              ticket_team_id: ticket.teamId,
              installation_id: installation.id,
              channel_id: channel.channelId,
            },
          },
          text: message,
          unfurl_links: true,
          unfurl_media: true,
        },
      );

      expect(
        mockSlackTriageMessagesRepository.saveWithTxn,
      ).toHaveBeenCalledWith(
        {},
        {
          slackMessageTs: threadSent.ts,
          channel: { id: channel.id },
          platformThreadId: platformCommentId,
          slackRequestMessage: { id: slackMessage.id },
          shouldUpdateThread: false,
          installation: { id: installation.id },
          organization: { id: installation.organization.id },
        },
      );
    });

    it('should throw an error if joining the channel fails', async () => {
      // Setup test data
      const installation = {
        id: 'installation-id',
        botToken: 'mock-bot-token',
        organization: { id: 'org-id' },
      } as unknown as Installations;

      const channel = {
        id: 'channel-id',
        channelId: 'C12345',
        name: 'discussion-channel',
      } as unknown as Channels;

      const ticket = {
        id: 'ticket-123',
        ticketId: 'T-123',
        teamId: 'team-123',
      };

      const slackMessage = {
        id: 'message-id',
      } as unknown as SlackMessages;

      // Setup mocks
      (mockSlackWebAPIService.joinConversation as Mock).mockRejectedValue(
        new Error('Failed to join channel'),
      );

      // Call the method and expect it to throw
      await expect(
        service.sendDiscussionThreadToChannel(installation, {
          channel,
          ticket,
          slackMessage,
        }),
      ).rejects.toThrow('Bot failed to join channel');

      // Assertions
      expect(mockSlackWebAPIService.joinConversation).toHaveBeenCalledWith(
        installation.botToken,
        {
          channel: channel.channelId,
        },
      );

      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('updateTriageMessagesForSlackMessage', () => {
    it('should update triage messages for a slack message', async () => {
      // Setup test data
      const installation = {
        id: 'installation-id',
        botToken: 'mock-bot-token',
        organization: { id: 'org-id' },
      } as unknown as Installations;

      const slackMessage = {
        id: 'message-id',
        platformTicketId: 'ticket-123',
        slackPermalink: 'https://slack.com/archives/C12345/p**********123456',
      } as unknown as SlackMessages;

      const ticket = {
        id: 'ticket-123',
        ticketId: 'T-123',
        teamId: 'team-123',
        priority: 'High',
        priorityId: 'high',
        status: 'Open',
        statusId: 'open',
        title: 'Test Ticket',
        createdAt: new Date().toISOString(),
        assignedAgent: 'Test Agent',
        assignedAgentEmail: '<EMAIL>',
        requestorEmail: '<EMAIL>',
      };

      const slackUser = {
        id: 'user-id',
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
      };

      const triageMessages = [
        {
          id: 'triage-1',
          slackMessageTs: '**********.123456',
          channel: {
            id: 'channel-id',
            channelId: 'C12345',
          },
          shouldUpdateThread: true,
        },
        {
          id: 'triage-2',
          slackMessageTs: '**********.654321',
          channel: {
            id: 'channel-id-2',
            channelId: 'C54321',
          },
          shouldUpdateThread: true,
        },
      ];

      // Setup mocks
      (mockThenaPlatformApi.getTicket as Mock).mockResolvedValue(ticket);
      (mockSlackUsersRepository.findOne as Mock).mockResolvedValue(slackUser);
      (mockSlackTriageMessagesRepository.findAll as Mock).mockResolvedValue(
        triageMessages,
      );

      // Call the method
      await service.updateTriageMessagesForSlackMessage(
        installation,
        slackMessage,
      );

      // Assertions
      expect(mockThenaPlatformApi.getTicket).toHaveBeenCalledWith(
        installation,
        slackMessage.platformTicketId,
      );

      expect(mockSlackUsersRepository.findOne).toHaveBeenCalledWith({
        where: {
          slackProfileEmail: ticket.assignedAgentEmail,
          installation: { id: installation.id },
          organization: { id: installation.organization.id },
        },
      });

      expect(mockSlackTriageMessagesRepository.findAll).toHaveBeenCalledWith({
        where: {
          slackRequestMessage: [
            { id: slackMessage.id },
            { platformTicketId: ticket.id },
          ],
          shouldUpdateThread: true,
          installation: { id: installation.id },
          organization: { id: installation.organization.id },
        },
        relations: ['channel'],
      });

      expect(mockSlackWebAPIService.updateMessage).toHaveBeenCalledTimes(
        triageMessages.length,
      );
    });

    it('should log a warning if no triage messages are found', async () => {
      // Setup test data
      const installation = {
        id: 'installation-id',
        botToken: 'mock-bot-token',
        organization: { id: 'org-id' },
      } as unknown as Installations;

      const slackMessage = {
        id: 'message-id',
        platformTicketId: 'ticket-123',
      } as unknown as SlackMessages;

      const ticket = {
        id: 'ticket-123',
        ticketId: 'T-123',
        teamId: 'team-123',
      };

      // Setup mocks
      (mockThenaPlatformApi.getTicket as Mock).mockResolvedValue(ticket);
      (mockSlackTriageMessagesRepository.findAll as Mock).mockResolvedValue([]);

      // Call the method
      await service.updateTriageMessagesForSlackMessage(
        installation,
        slackMessage,
      );

      // Assertions
      expect(mockThenaPlatformApi.getTicket).toHaveBeenCalledWith(
        installation,
        slackMessage.platformTicketId,
      );

      expect(mockSlackTriageMessagesRepository.findAll).toHaveBeenCalled();
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('No triage messages found for slack message'),
      );
      expect(mockSlackWebAPIService.updateMessage).not.toHaveBeenCalled();
    });

    it('should log a warning if ticket is not found', async () => {
      // Setup test data
      const installation = {
        id: 'installation-id',
        botToken: 'mock-bot-token',
        organization: { id: 'org-id' },
      } as unknown as Installations;

      const slackMessage = {
        id: 'message-id',
        platformTicketId: 'ticket-123',
      } as unknown as SlackMessages;

      // Setup mocks
      (mockThenaPlatformApi.getTicket as Mock).mockResolvedValue(null);

      // Call the method
      await service.updateTriageMessagesForSlackMessage(
        installation,
        slackMessage,
      );

      // Assertions
      expect(mockThenaPlatformApi.getTicket).toHaveBeenCalledWith(
        installation,
        slackMessage.platformTicketId,
      );

      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('Ticket not found for slack message'),
      );
      expect(mockSlackTriageMessagesRepository.findAll).not.toHaveBeenCalled();
      expect(mockSlackWebAPIService.updateMessage).not.toHaveBeenCalled();
    });
  });
});

describe('parseWithMentions', () => {
  let userRepository: Repository<Users>;
  let installation: Installations;

  beforeEach(() => {
    userRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<Users>;

    installation = {
      id: 'test-installation-id',
    } as unknown as Installations;
  });

  it('should return empty string for empty input', async () => {
    const result = await parseWithMentions('', userRepository, installation);
    expect(result).toBe('');
  });

  it('should return the original text if no mentions are found', async () => {
    const text = 'This is a test message with no mentions';
    const result = await parseWithMentions(text, userRepository, installation);
    expect(result).toBe(text);
  });

  it('should replace mentions with <@userId> when user is found', async () => {
    const text = 'Hello <@U123|<EMAIL>>, how are you?';

    const user = {
      slackId: 'U456',
    } as unknown as Users;

    (userRepository.findOne as Mock).mockResolvedValueOnce(user);

    const result = await parseWithMentions(text, userRepository, installation);
    expect(result).toBe('Hello <@U456>, how are you?');
    expect(userRepository.findOne).toHaveBeenCalledWith({
      where: {
        installation: { id: installation.id },
        slackProfileEmail: '<EMAIL>',
      },
    });
  });

  it('should replace mentions with @label when user is not found', async () => {
    const text = 'Hello <@U123|<EMAIL>>, how are you?';

    (userRepository.findOne as Mock).mockResolvedValueOnce(null);

    const result = await parseWithMentions(text, userRepository, installation);
    expect(result).toBe('Hello @<EMAIL>, how are you?');
  });

  it('should handle multiple mentions correctly', async () => {
    const text =
      'Hello <@U123|<EMAIL>> and <@U456|<EMAIL>>';

    const user1 = {
      slackId: 'U789',
    } as unknown as Users;

    (userRepository.findOne as Mock)
      .mockResolvedValueOnce(user1)
      .mockResolvedValueOnce(null);

    const result = await parseWithMentions(text, userRepository, installation);
    expect(result).toBe('Hello <@U789> and @<EMAIL>');
  });
});
