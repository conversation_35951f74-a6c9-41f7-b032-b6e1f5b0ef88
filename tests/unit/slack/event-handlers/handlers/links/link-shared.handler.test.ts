import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { Installations } from '../../../../../../src/database/entities';
import { ThenaPlatformApiProvider } from '../../../../../../src/external/provider/thena-platform-api.provider';
import { Ticket } from '../../../../../../src/platform/interfaces';
import { SlackLinkSharedHandler } from '../../../../../../src/slack/event-handlers/handlers/links/link-shared.handler';
import { SlackEventMap } from '../../../../../../src/slack/event-handlers/interface';
import { SlackWebAPIService } from '../../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { ILogger } from '../../../../../../src/utils';

describe('SlackLinkSharedHandler', () => {
  let handler: SlackLinkSharedHandler;
  let mockLogger: ILogger;
  let mockSlackWebAPIService: SlackWebAPIService;
  let mockPlatformApiProvider: ThenaPlatformApiProvider;

  beforeEach(() => {
    vi.resetAllMocks();

    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockSlackWebAPIService = {
      unfurlLink: vi.fn().mockResolvedValue({ ok: true }),
    } as unknown as SlackWebAPIService;

    mockPlatformApiProvider = {
      getTicket: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    handler = new SlackLinkSharedHandler(
      mockLogger,
      mockSlackWebAPIService,
      mockPlatformApiProvider,
    );
  });

  describe('canHandle', () => {
    it('should return true for link_shared events', () => {
      const event = {
        event: {
          type: 'link_shared',
        },
      } as unknown as SlackEventMap['link_shared'];

      const result = handler.canHandle(event);

      expect(result).toBe(true);
    });

    it('should return false for non-link_shared events', () => {
      const event = {
        event: {
          type: 'message',
        },
      } as unknown as SlackEventMap['link_shared'];

      const result = handler.canHandle(event);

      expect(result).toBe(false);
    });
  });

  describe('handle', () => {
    it('should process link_shared event and unfurl links', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
        organization: {
          uid: 'org-id',
          apiKey: 'api-key',
        },
      } as unknown as Installations;

      const event = {
        event: {
          type: 'link_shared',
          user: 'U12345',
          channel: 'C12345',
          message_ts: '**********.123456',
          event_ts: '**********.123456',
          links: [
            {
              url: 'https://example.com/dashboard/team1?ticketId=123',
              domain: 'example.com',
            },
          ],
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['link_shared'];

      const mockTicket = {
        ticketId: 123,
        id: '123',
        title: 'Test Ticket',
        status: 'Open',
        priority: 'High',
        assignedAgent: 'John Doe',
        customerContactFirstName: 'Jane',
        customerContactLastName: 'Smith',
        customerContactEmail: '<EMAIL>',
        teamIdentifier: 'TEAM',
        createdAt: '2023-01-01T00:00:00Z',
      } as unknown as Ticket;

      (mockPlatformApiProvider.getTicket as Mock).mockResolvedValue(mockTicket);

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining("Handling 'link_shared' event"),
      );
      expect(mockPlatformApiProvider.getTicket).toHaveBeenCalledWith(
        mockInstallation,
        '123',
      );
      expect(mockSlackWebAPIService.unfurlLink).toHaveBeenCalledWith(
        'xoxb-token',
        {
          channel: 'C12345',
          ts: '**********.123456',
          unfurls: expect.any(Object),
        },
      );

      // Verify the unfurl content structure
      const unfurlCall = (mockSlackWebAPIService.unfurlLink as Mock).mock
        .calls[0][1];
      const unfurlContent =
        unfurlCall.unfurls['https://example.com/dashboard/team1?ticketId=123'];
      expect(unfurlContent).toHaveProperty('blocks');
      expect(unfurlContent.blocks).toHaveLength(3);
      expect(unfurlContent.blocks[0].text.text).toContain(
        'TEAM-123 Test Ticket',
      );
    });

    it('should use default unfurl for links without ticketId', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      } as unknown as Installations;

      const event = {
        event: {
          type: 'link_shared',
          user: 'U12345',
          channel: 'C12345',
          message_ts: '**********.123456',
          event_ts: '**********.123456',
          links: [
            {
              url: 'https://example.com/dashboard/team1',
              domain: 'example.com',
            },
          ],
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['link_shared'];

      // Act
      await handler.handle(event);

      // Assert
      expect(mockPlatformApiProvider.getTicket).not.toHaveBeenCalled();
      expect(mockSlackWebAPIService.unfurlLink).toHaveBeenCalledWith(
        'xoxb-token',
        {
          channel: 'C12345',
          ts: '**********.123456',
          unfurls: expect.any(Object),
        },
      );

      // Verify the default unfurl content
      const unfurlCall = (mockSlackWebAPIService.unfurlLink as Mock).mock
        .calls[0][1];
      const unfurlContent =
        unfurlCall.unfurls['https://example.com/dashboard/team1'];
      expect(unfurlContent).toHaveProperty('blocks');
      expect(unfurlContent.blocks).toHaveLength(3);
      expect(unfurlContent.blocks[0].text.text).toBe('*Thena dashboard link*');
    });

    it('should handle errors during processing', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
        teamId: 'T12345',
        botToken: 'xoxb-token',
      } as unknown as Installations;

      const event = {
        event: {
          type: 'link_shared',
          user: 'U12345',
          channel: 'C12345',
          message_ts: '**********.123456',
          event_ts: '**********.123456',
          links: [
            {
              url: 'https://example.com/dashboard/team1?ticketId=123',
              domain: 'example.com',
            },
          ],
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['link_shared'];

      const error = new Error('API error');
      (mockPlatformApiProvider.getTicket as Mock).mockRejectedValue(error);

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining("Failed to handle 'link_shared' event"),
        error.stack,
      );
      expect(mockSlackWebAPIService.unfurlLink).not.toHaveBeenCalled();
    });
  });

  describe('generateUnfurlContent', () => {
    it('should generate ticket details unfurl for URLs with ticketId', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
      } as unknown as Installations;

      const url = 'https://example.com/dashboard/team1?ticketId=123';

      const mockTicket = {
        ticketId: 123,
        id: '123',
        title: 'Test Ticket',
        status: 'Open',
        priority: 'High',
        assignedAgent: 'John Doe',
        customerContactFirstName: 'Jane',
        customerContactLastName: 'Smith',
        customerContactEmail: '<EMAIL>',
        teamIdentifier: 'TEAM',
        createdAt: '2023-01-01T00:00:00Z',
      } as unknown as Ticket;

      (mockPlatformApiProvider.getTicket as Mock).mockResolvedValue(mockTicket);

      // Act
      const result = await handler.generateUnfurlContent(mockInstallation, url);

      // Assert
      expect(mockPlatformApiProvider.getTicket).toHaveBeenCalledWith(
        mockInstallation,
        '123',
      );
      expect(result).toHaveProperty('blocks');
      expect(result.blocks).toHaveLength(3);
      expect(result.blocks[0].text.text).toContain('TEAM-123 Test Ticket');
    });

    it('should generate default unfurl for URLs without ticketId', async () => {
      // Arrange
      const mockInstallation = {
        id: 'installation-id',
      } as unknown as Installations;

      const url = 'https://example.com/dashboard/team1';

      // Act
      const result = await handler.generateUnfurlContent(mockInstallation, url);

      // Assert
      expect(mockPlatformApiProvider.getTicket).not.toHaveBeenCalled();
      expect(result).toHaveProperty('blocks');
      expect(result.blocks).toHaveLength(3);
      expect(result.blocks[0].text.text).toBe('*Thena dashboard link*');
    });

    it('should handle ticket not found gracefully', async () => {
      const mockInstallation = {
        id: 'installation-id',
      } as unknown as Installations;

      const url = 'https://example.com/dashboard/team1?ticketId=999';

      (mockPlatformApiProvider.getTicket as Mock).mockResolvedValue(null);

      // The service doesn't handle null tickets gracefully, so it should throw an error
      await expect(
        handler.generateUnfurlContent(mockInstallation, url),
      ).rejects.toThrow();

      expect(mockPlatformApiProvider.getTicket).toHaveBeenCalledWith(
        mockInstallation,
        '999',
      );
    });

    it('should handle API errors gracefully', async () => {
      const mockInstallation = {
        id: 'installation-id',
      } as unknown as Installations;

      const url = 'https://example.com/dashboard/team1?ticketId=123';

      const error = new Error('API error');
      (mockPlatformApiProvider.getTicket as Mock).mockRejectedValue(error);

      await expect(
        handler.generateUnfurlContent(mockInstallation, url),
      ).rejects.toThrow('API error');
    });

    it('should handle malformed URLs gracefully', async () => {
      const mockInstallation = {
        id: 'installation-id',
      } as unknown as Installations;

      const url = 'invalid-url';

      // The service doesn't handle invalid URLs gracefully, so it should throw an error
      await expect(
        handler.generateUnfurlContent(mockInstallation, url),
      ).rejects.toThrow('Invalid URL');

      expect(mockPlatformApiProvider.getTicket).not.toHaveBeenCalled();
    });
  });
});
