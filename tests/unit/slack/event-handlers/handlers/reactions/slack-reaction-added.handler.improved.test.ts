import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  ConfigKeys,
  ConfigService,
} from '../../../../../../src/config/config.service';
import { EventDeduplicationService } from '../../../../../../src/common/redis/event-deduplication.service';
import { TransactionService } from '../../../../../../src/database/common/transactions.service';
import { CustomerContacts } from '../../../../../../src/database/entities';
import { ChannelsRepository } from '../../../../../../src/database/entities/channels/repositories';
import { CommentThreadMapsRepository } from '../../../../../../src/database/entities/mappings/repositories/comment-thread-maps.repository';
import { TeamChannelMapsRepository } from '../../../../../../src/database/entities/mappings/repositories/team-channel-maps.repository';
import { GroupedSlackMessagesRepository } from '../../../../../../src/database/entities/slack-messages/repositories/grouped-slack-messages.repository';
import { SlackMessagesRepository } from '../../../../../../src/database/entities/slack-messages/repositories/slack-messages.repository';
import { PlatformTeams } from '../../../../../../src/database/entities/teams/teams.entity';
import { Users } from '../../../../../../src/database/entities/users/users.entity';
import { EmittableSlackEvents } from '../../../../../../src/external/provider/constants/platform-events.constants';
import { ThenaAppsPlatformApiProvider } from '../../../../../../src/external/provider/thena-apps-platform-api.provider';
import { ThenaPlatformApiProvider } from '../../../../../../src/external/provider/thena-platform-api.provider';
import { CreateNewTicketBlocks } from '../../../../../../src/slack/blocks/components/composite/channels';
import {
  ConditionalFormBuilderComposite,
  FormSelectorComposite,
} from '../../../../../../src/slack/blocks/components/composite/form-builder';
import { SlackAppManagementService } from '../../../../../../src/slack/core';
import { SettingsCore } from '../../../../../../src/slack/core/management/settings.management';
import { SlackReactionAddedHandler } from '../../../../../../src/slack/event-handlers/handlers/reactions/slack-reaction-added.handler';
import { SlackEventMap } from '../../../../../../src/slack/event-handlers/interface';
import { SlackWebAPIService } from '../../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { AiTicketGeneratorService } from '../../../../../../src/slack/services/ai-ticket-generator.service';
import { FormBuilderService } from '../../../../../../src/slack/services/form-builder.service';
import { BaseSlackBlocksToHtml } from '../../../../../../src/utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';
import { ILogger } from '../../../../../../src/utils';

describe('SlackReactionAddedHandler', () => {
  let handler: SlackReactionAddedHandler;
  let mockLogger: ILogger;
  let mockUserRepository: Repository<Users>;
  let mockTeamChannelMapRepo: TeamChannelMapsRepository;
  let mockSlackMessagesRepository: any;
  let mockGroupedSlackMessagesRepository: any;
  let mockChannelRepository: any;
  let mockCommentThreadMapsRepository: any;
  let mockFormBuilder: ConditionalFormBuilderComposite;
  let mockFormSelector: FormSelectorComposite;
  let mockFormBuilderService: FormBuilderService;
  let mockSettingsCore: SettingsCore;
  let mockSlackAppManagementService: SlackAppManagementService;
  let mockTransactionService: TransactionService;
  let mockAiTicketGeneratorService: AiTicketGeneratorService;
  let mockThenaPlatformApiProvider: any;
  let mockThenaAppsPlatformApiProvider: ThenaAppsPlatformApiProvider;
  let mockSlackApiProvider: SlackWebAPIService;
  let mockCreateNewTicketBlock: CreateNewTicketBlocks;
  let mockConfigService: ConfigService;
  let mockBaseSlackBlocksToHtml: BaseSlackBlocksToHtml;
  let mockEventDeduplicationService: EventDeduplicationService;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    } as unknown as ILogger;

    mockUserRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<Users>;

    mockTeamChannelMapRepo = {
      findByCondition: vi.fn(),
    } as unknown as TeamChannelMapsRepository;

    mockSlackMessagesRepository = {
      findByCondition: vi.fn(),
      saveWithTxn: vi.fn(),
    };

    mockGroupedSlackMessagesRepository = {
      findByCondition: vi.fn(),
    };

    mockChannelRepository = {
      findByCondition: vi.fn(),
    };

    mockCommentThreadMapsRepository = {
      saveWithTxn: vi.fn(),
    };

    mockFormBuilder = {
      build: vi.fn(),
    } as unknown as ConditionalFormBuilderComposite;

    mockFormSelector = {
      build: vi.fn().mockReturnValue({ blocks: [] }),
    } as unknown as FormSelectorComposite;

    mockFormBuilderService = {
      getForms: vi.fn(),
    } as unknown as FormBuilderService;

    mockSettingsCore = {
      getValue: vi.fn(),
    } as unknown as SettingsCore;

    mockSlackAppManagementService = {
      upsertPersonWithIdentification: vi.fn(),
    } as unknown as SlackAppManagementService;

    mockTransactionService = {
      runInTransaction: vi
        .fn()
        .mockImplementation((callback) => callback('txn')),
    } as unknown as TransactionService;

    mockAiTicketGeneratorService = {
      generateTicketTitle: vi.fn(),
    } as unknown as AiTicketGeneratorService;

    mockThenaPlatformApiProvider = {
      createNewTicket: vi.fn(),
      getCommentThreads: vi.fn(),
      addReaction: vi.fn(),
    };

    mockThenaAppsPlatformApiProvider = {
      postEventsToPlatform: vi.fn(),
    } as unknown as ThenaAppsPlatformApiProvider;

    mockSlackApiProvider = {
      getUserInfo: vi.fn(),
      getConversationHistory: vi.fn(),
      getPermalink: vi.fn(),
    } as unknown as SlackWebAPIService;

    mockCreateNewTicketBlock = {
      build: vi.fn().mockReturnValue({ blocks: [] }),
    } as unknown as CreateNewTicketBlocks;

    mockConfigService = {
      get: vi.fn().mockReturnValue('https://example.com'),
    } as unknown as ConfigService;

    mockBaseSlackBlocksToHtml = {
      parse: vi.fn().mockReturnValue('<p>Mock HTML</p>'),
    } as unknown as BaseSlackBlocksToHtml;

    mockEventDeduplicationService = {
      processIdempotently: vi.fn().mockImplementation((eventId, eventType, callback) => callback()),
    } as unknown as EventDeduplicationService;

    handler = new SlackReactionAddedHandler(
      mockLogger,
      mockUserRepository,
      mockTeamChannelMapRepo,
      mockSlackMessagesRepository,
      mockGroupedSlackMessagesRepository,
      mockChannelRepository,
      mockCommentThreadMapsRepository,
      mockFormBuilder,
      mockFormSelector,
      mockFormBuilderService,
      mockSettingsCore,
      mockSlackAppManagementService,
      mockTransactionService,
      mockAiTicketGeneratorService,
      mockThenaPlatformApiProvider,
      mockThenaAppsPlatformApiProvider,
      mockSlackApiProvider,
      mockConfigService,
      mockCreateNewTicketBlock,
      mockBaseSlackBlocksToHtml,
      mockEventDeduplicationService,
    );
  });

  describe('handle', () => {
    it('should handle a reaction_added event successfully', async () => {
      // Setup
      const mockEvent = {
        event: {
          type: 'reaction_added',
          user: 'U12345',
          reaction: 'ticket',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
        context: {
          installation: {
            id: 'inst-1',
            name: 'Test Installation',
            teamId: 'T12345',
            botToken: 'xoxb-token',
            organization: { id: 'org-1' },
          },
          organization: { id: 'org-1' },
        },
      } as SlackEventMap['reaction_added'];

      // Mock handleTicketCreation
      const handleTicketCreationSpy = vi
        .spyOn(handler, 'handleTicketCreation')
        .mockResolvedValue();

      // Mock processReactionForComment
      const processReactionForCommentSpy = vi
        .spyOn(handler as any, 'processReactionForComment')
        .mockResolvedValue(undefined);

      // Execute
      await handler.handle(mockEvent);

      // Verify
      expect(handleTicketCreationSpy).toHaveBeenCalledWith(mockEvent);
      expect(
        mockThenaAppsPlatformApiProvider.postEventsToPlatform,
      ).toHaveBeenCalledWith(mockEvent.context.installation.organization, {
        ...mockEvent.event,
        type: EmittableSlackEvents.REACTION_ADDED,
      });
      expect(processReactionForCommentSpy).toHaveBeenCalledWith(
        mockEvent.context.installation,
        mockEvent.event,
      );
    });

    it('should handle errors when the event is invalid', async () => {
      // Setup
      const mockEvent = {
        event: {
          type: 'reaction_added',
          // Missing user property
          reaction: 'ticket',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
        },
        context: {
          installation: {
            id: 'inst-1',
            name: 'Test Installation',
            teamId: 'T12345',
            organization: { id: 'org-1' },
          },
          organization: { id: 'org-1' },
        },
      } as SlackEventMap['reaction_added'];

      // Execute
      await handler.handle(mockEvent);

      // Verify
      expect(mockLogger.error).toHaveBeenCalled();
      expect(
        mockThenaAppsPlatformApiProvider.postEventsToPlatform,
      ).not.toHaveBeenCalled();
    });

    it('should handle errors in ticket creation but continue with other operations', async () => {
      // Setup
      const mockEvent = {
        event: {
          type: 'reaction_added',
          user: 'U12345',
          reaction: 'ticket',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
        context: {
          installation: {
            id: 'inst-1',
            name: 'Test Installation',
            teamId: 'T12345',
            botToken: 'xoxb-token',
            organization: { id: 'org-1' },
          },
          organization: { id: 'org-1' },
        },
      } as SlackEventMap['reaction_added'];

      // Mock handleTicketCreation to throw an error
      vi.spyOn(handler, 'handleTicketCreation').mockRejectedValue(
        new Error('Test error'),
      );

      // Mock processReactionForComment
      const processReactionForCommentSpy = vi
        .spyOn(handler as any, 'processReactionForComment')
        .mockResolvedValue(undefined);

      // Execute
      await handler.handle(mockEvent);

      // Verify
      expect(mockLogger.error).toHaveBeenCalled();
      expect(
        mockThenaAppsPlatformApiProvider.postEventsToPlatform,
      ).toHaveBeenCalledWith(mockEvent.context.installation.organization, {
        ...mockEvent.event,
        type: EmittableSlackEvents.REACTION_ADDED,
      });
      expect(processReactionForCommentSpy).toHaveBeenCalledWith(
        mockEvent.context.installation,
        mockEvent.event,
      );
    });
  });

  describe('processReactionForComment', () => {
    it('should process a reaction for a comment successfully', async () => {
      // Setup
      const installation = {
        id: 'inst-1',
        botToken: 'xoxb-token',
        organization: { id: 'org-1' },
      };

      const event = {
        user: 'U12345',
        reaction: 'thumbsup',
        item: {
          type: 'message',
          channel: 'C12345',
          ts: '**********.123456',
        },
      };

      // Mock getUserInfo
      (mockSlackApiProvider.getUserInfo as any).mockResolvedValue({
        user: {
          profile: {
            display_name: 'Test User',
            email: '<EMAIL>',
            image_512: 'https://example.com/image.png',
          },
        },
      });

      // Mock findByCondition for channel
      (mockChannelRepository.findByCondition as any).mockResolvedValue({
        id: 'channel-1',
        channelId: 'C12345',
      });

      // Mock getPlatformCommentId
      vi.spyOn(handler as any, 'getPlatformCommentId').mockResolvedValue(
        'comment-123',
      );

      // Execute
      await (handler as any).processReactionForComment(installation, event);

      // Verify
      expect(mockSlackApiProvider.getUserInfo).toHaveBeenCalledWith(
        'xoxb-token',
        { user: 'U12345' },
      );
      expect(mockChannelRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          channelId: 'C12345',
          installation: { id: 'inst-1' },
        },
      });
      expect(mockThenaPlatformApiProvider.addReaction).toHaveBeenCalledWith(
        installation,
        'comment-123',
        'thumbsup',
        'Test User',
        '<EMAIL>',
        'https://example.com/image.png',
      );
    });

    it('should handle errors when the item is not a message', async () => {
      // Setup
      const installation = {
        id: 'inst-1',
        botToken: 'xoxb-token',
        organization: { id: 'org-1' },
      };

      const event = {
        user: 'U12345',
        reaction: 'thumbsup',
        item: {
          type: 'file', // Not a message
          channel: 'C12345',
          ts: '**********.123456',
        },
      };

      // Execute
      await (handler as any).processReactionForComment(installation, event);

      // Verify
      expect(mockLogger.error).toHaveBeenCalled();
      expect(mockThenaPlatformApiProvider.addReaction).not.toHaveBeenCalled();
    });

    it('should handle errors when the channel is not found', async () => {
      // Setup
      const installation = {
        id: 'inst-1',
        botToken: 'xoxb-token',
        organization: { id: 'org-1' },
      };

      const event = {
        user: 'U12345',
        reaction: 'thumbsup',
        item: {
          type: 'message',
          channel: 'C12345',
          ts: '**********.123456',
        },
      };

      // Mock getUserInfo
      (mockSlackApiProvider.getUserInfo as any).mockResolvedValue({
        user: {
          profile: {
            display_name: 'Test User',
            email: '<EMAIL>',
            image_512: 'https://example.com/image.png',
          },
        },
      });

      // Mock findByCondition for channel to return null
      (mockChannelRepository.findByCondition as any).mockResolvedValue(null);

      // Execute
      await (handler as any).processReactionForComment(installation, event);

      // Verify
      expect(mockLogger.error).toHaveBeenCalled();
      expect(mockThenaPlatformApiProvider.addReaction).not.toHaveBeenCalled();
    });
  });

  describe('getPlatformCommentId', () => {
    it('should return the platform comment ID for a direct message', async () => {
      // Setup
      const installation = {
        id: 'inst-1',
        botToken: 'xoxb-token',
        organization: { id: 'org-1' },
      };
      const channelId = 'channel-1';
      const ts = '**********.123456';

      // Mock findByCondition for slack message
      mockSlackMessagesRepository.findByCondition.mockResolvedValue({
        id: 'message-1',
        slackMessageTs: ts,
        platformCommentId: 'comment-123',
      });

      // Execute
      const result = await (handler as any).getPlatformCommentId(
        installation,
        channelId,
        ts,
      );

      // Verify
      expect(result).toBe('comment-123');
      expect(mockSlackMessagesRepository.findByCondition).toHaveBeenCalledWith({
        where: [
          {
            channel: { id: channelId },
            installation: { id: 'inst-1' },
            slackMessageTs: ts,
          },
          {
            channel: { id: channelId },
            installation: { id: 'inst-1' },
            slackMessageThreadTs: ts,
          },
        ],
      });
    });

    it('should return the platform comment ID for a threaded message', async () => {
      // Setup
      const installation = {
        id: 'inst-1',
        botToken: 'xoxb-token',
        organization: { id: 'org-1' },
      };
      const channelId = 'channel-1';
      const ts = '**********.123456';
      const parentTs = '**********.000000';

      // Mock findByCondition for slack message
      mockSlackMessagesRepository.findByCondition.mockResolvedValue({
        id: 'message-1',
        slackMessageTs: parentTs,
        slackMessageThreadTs: null,
        platformCommentId: 'parent-comment-123',
      });

      // Mock getCommentThreads
      mockThenaPlatformApiProvider.getCommentThreads.mockResolvedValue([
        {
          id: 'thread-comment-123',
          metadata: {
            external_sinks: {
              slack: {
                ts: ts,
              },
            },
          },
        },
      ]);

      // Execute
      const result = await (handler as any).getPlatformCommentId(
        installation,
        channelId,
        ts,
      );

      // Verify
      expect(result).toBe('thread-comment-123');
      expect(
        mockThenaPlatformApiProvider.getCommentThreads,
      ).toHaveBeenCalledWith(installation, 'parent-comment-123');
    });

    it('should return the platform comment ID for a grouped message', async () => {
      // Setup
      const installation = {
        id: 'inst-1',
        botToken: 'xoxb-token',
        organization: { id: 'org-1' },
      };
      const channelId = 'channel-1';
      const ts = '**********.123456';

      // Mock findByCondition for slack message to return null
      mockSlackMessagesRepository.findByCondition.mockResolvedValue(null);

      // Mock findByCondition for grouped message
      mockGroupedSlackMessagesRepository.findByCondition.mockResolvedValue({
        id: 'grouped-message-1',
        slackMessageTs: ts,
        parentCommentId: 'parent-comment-123',
      });

      // Execute
      const result = await (handler as any).getPlatformCommentId(
        installation,
        channelId,
        ts,
      );

      // Verify
      expect(result).toBe('parent-comment-123');
      expect(
        mockGroupedSlackMessagesRepository.findByCondition,
      ).toHaveBeenCalledWith({
        where: {
          slackMessageTs: ts,
          channel: { id: channelId },
          installation: { id: 'inst-1' },
        },
      });
    });

    it('should return null when no related slack message is found', async () => {
      // Setup
      const installation = {
        id: 'inst-1',
        botToken: 'xoxb-token',
        organization: { id: 'org-1' },
      };
      const channelId = 'channel-1';
      const ts = '**********.123456';

      // Mock findByCondition for slack message to return null
      mockSlackMessagesRepository.findByCondition.mockResolvedValue(null);

      // Mock findByCondition for grouped message to return null
      mockGroupedSlackMessagesRepository.findByCondition.mockResolvedValue(
        null,
      );

      // Execute
      const result = await (handler as any).getPlatformCommentId(installation, channelId, ts);

      // Verify
      expect(result).toBeNull();
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining(`No related slack message found for ts: ${ts}`)
      );
    });
  });
});
