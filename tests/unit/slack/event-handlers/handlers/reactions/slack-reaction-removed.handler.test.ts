import { beforeEach, describe, expect, it, vi } from 'vitest';
import { EmittableSlackEvents } from '../../../../../../src/external/provider/constants/platform-events.constants';
import { SlackReactionRemovedHandler } from '../../../../../../src/slack/event-handlers/handlers/reactions/slack-reaction-removed.handler';

describe('SlackReactionRemovedHandler', () => {
  let handler: SlackReactionRemovedHandler;
  let mockLogger: any;
  let mockSlackMessagesRepository: any;
  let mockGroupedSlackMessagesRepository: any;
  let mockChannelRepository: any;
  let mockThenaPlatformApiProvider: any;
  let mockThenaAppsPlatformApiProvider: any;
  let mockSlackApiProvider: any;

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      error: vi.fn(),
    };

    mockSlackMessagesRepository = {
      findByCondition: vi.fn().mockResolvedValue(null),
    };

    mockGroupedSlackMessagesRepository = {
      findByCondition: vi.fn().mockResolvedValue(null),
    };

    mockChannelRepository = {
      findByCondition: vi.fn().mockResolvedValue(null),
    };

    mockThenaPlatformApiProvider = {
      removeReaction: vi.fn().mockResolvedValue(undefined),
      getCommentThreads: vi.fn().mockResolvedValue([]),
    };

    mockThenaAppsPlatformApiProvider = {
      postEventsToPlatform: vi.fn().mockResolvedValue(undefined),
    };

    mockSlackApiProvider = {
      getUserInfo: vi.fn().mockResolvedValue({
        user: {
          profile: {
            email: '<EMAIL>',
          },
        },
      }),
    };

    handler = new SlackReactionRemovedHandler(
      mockLogger,
      mockSlackMessagesRepository,
      mockGroupedSlackMessagesRepository,
      mockChannelRepository,
      mockThenaPlatformApiProvider,
      mockThenaAppsPlatformApiProvider,
      mockSlackApiProvider,
    );
  });

  describe('canHandle', () => {
    it('should return true for reaction_removed events', () => {
      const event = {
        event: {
          type: 'reaction_removed',
        },
      } as any;

      expect(handler.canHandle(event)).toBe(true);
    });

    it('should return false for other event types', () => {
      const event = {
        event: {
          type: 'reaction_added',
        },
      } as any;

      expect(handler.canHandle(event)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should post events to platform for valid reaction_removed events', async () => {
      const event = {
        context: {
          installation: {
            id: 'test-installation-id',
            teamId: 'T12345',
            organization: { id: 'test-org-id' },
            botToken: 'xoxb-test-token',
          },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'reaction_removed',
          user: 'U12345',
          reaction: 'thumbsup',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
      } as any;

      mockChannelRepository.findByCondition.mockResolvedValue({
        id: 'channel-1',
        channelId: 'C12345',
      });

      mockSlackMessagesRepository.findByCondition.mockResolvedValue({
        id: 'message-1',
        slackMessageTs: '**********.123456',
        platformCommentId: 'comment-123',
      });

      await handler.handle(event);

      expect(
        mockThenaAppsPlatformApiProvider.postEventsToPlatform,
      ).toHaveBeenCalledWith(event.context.installation.organization, {
        ...event.event,
        type: EmittableSlackEvents.REACTION_REMOVED,
      });

      expect(mockSlackApiProvider.getUserInfo).toHaveBeenCalledWith(
        'xoxb-test-token',
        { user: 'U12345' },
      );

      expect(mockThenaPlatformApiProvider.removeReaction).toHaveBeenCalledWith(
        event.context.installation,
        'comment-123',
        'thumbsup',
        '<EMAIL>',
      );
    });

    it('should throw an error for invalid events without user property', async () => {
      const event = {
        context: {
          installation: {
            id: 'test-installation-id',
            teamId: 'T12345',
            organization: { id: 'test-org-id' },
          },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'reaction_removed',
          reaction: 'thumbsup',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
      } as any;

      await handler.handle(event);

      expect(
        mockThenaAppsPlatformApiProvider.postEventsToPlatform,
      ).not.toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          "[SlackReactionRemovedHandler] Failed to handle 'reaction_removed' event for team T12345",
        ),
        expect.any(String),
      );
    });

    it('should throw an error for invalid events without reaction property', async () => {
      const event = {
        context: {
          installation: {
            id: 'test-installation-id',
            teamId: 'T12345',
            organization: { id: 'test-org-id' },
          },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'reaction_removed',
          user: 'U12345',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
      } as any;

      await handler.handle(event);

      expect(
        mockThenaAppsPlatformApiProvider.postEventsToPlatform,
      ).not.toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          "[SlackReactionRemovedHandler] Failed to handle 'reaction_removed' event for team T12345",
        ),
        expect.any(String),
      );
    });

    it('should handle errors and log them properly', async () => {
      const event = {
        context: {
          installation: {
            id: 'test-installation-id',
            teamId: 'T12345',
            organization: { id: 'test-org-id' },
            botToken: 'xoxb-test-token',
          },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'reaction_removed',
          user: 'U12345',
          reaction: 'thumbsup',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
      } as any;

      const error = new Error('Test error');
      mockThenaAppsPlatformApiProvider.postEventsToPlatform.mockRejectedValue(
        error,
      );

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          "[SlackReactionRemovedHandler] Failed to handle 'reaction_removed' event for team T12345",
        ),
        error.stack,
      );
    });

    it('should handle non-Error errors and log them properly', async () => {
      const event = {
        context: {
          installation: {
            id: 'test-installation-id',
            teamId: 'T12345',
            organization: { id: 'test-org-id' },
            botToken: 'xoxb-test-token',
          },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'reaction_removed',
          user: 'U12345',
          reaction: 'thumbsup',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
      } as any;

      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});
      mockThenaAppsPlatformApiProvider.postEventsToPlatform.mockRejectedValue(
        'String error',
      );

      await handler.handle(event);

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          "[SlackReactionRemovedHandler] Failed to handle 'reaction_removed' event for team T12345",
        ),
        'String error',
      );

      consoleErrorSpy.mockRestore();
    });

    it('should handle events with wrong type', async () => {
      const event = {
        context: {
          installation: {
            id: 'test-installation-id',
            teamId: 'T12345',
            organization: { id: 'test-org-id' },
          },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'reaction_added', // Wrong type
          user: 'U12345',
          reaction: 'thumbsup',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
      } as any;

      await handler.handle(event);

      expect(
        mockThenaAppsPlatformApiProvider.postEventsToPlatform,
      ).not.toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          "[SlackReactionRemovedHandler] Failed to handle 'reaction_removed' event for team T12345",
        ),
        expect.any(String),
      );
    });

    it('should handle reaction removed from non-message items', async () => {
      const event = {
        context: {
          installation: {
            id: 'test-installation-id',
            teamId: 'T12345',
            organization: { id: 'test-org-id' },
            botToken: 'xoxb-test-token',
          },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'reaction_removed',
          user: 'U12345',
          reaction: 'thumbsup',
          item: {
            type: 'file', // Not a message
            file: 'F12345',
          },
          event_ts: '**********.123456',
        },
      } as any;

      await handler.handle(event);

      // Should still post the event to platform
      expect(
        mockThenaAppsPlatformApiProvider.postEventsToPlatform,
      ).toHaveBeenCalledWith(event.context.installation.organization, {
        ...event.event,
        type: EmittableSlackEvents.REACTION_REMOVED,
      });

      // But should log an error for the reaction removal part
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Error removing reaction from comment: Reaction was not removed from a message',
        ),
      );
    });

    it('should handle when channel is not found', async () => {
      const event = {
        context: {
          installation: {
            id: 'test-installation-id',
            teamId: 'T12345',
            organization: { id: 'test-org-id' },
            botToken: 'xoxb-test-token',
          },
          organization: { id: 'test-org-id' },
        },
        event: {
          type: 'reaction_removed',
          user: 'U12345',
          reaction: 'thumbsup',
          item: {
            type: 'message',
            channel: 'C12345',
            ts: '**********.123456',
          },
          event_ts: '**********.123456',
        },
      } as any;

      // Mock channel repository to return null
      mockChannelRepository.findByCondition.mockResolvedValue(null);

      await handler.handle(event);

      // Should still post the event to platform
      expect(
        mockThenaAppsPlatformApiProvider.postEventsToPlatform,
      ).toHaveBeenCalledWith(event.context.installation.organization, {
        ...event.event,
        type: EmittableSlackEvents.REACTION_REMOVED,
      });

      // But should log an error for the channel not found
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Error removing reaction from comment: Channel not found for T12345',
        ),
      );
    });
  });

  describe('getPlatformCommentId', () => {
    it('should get platform comment ID from a direct message', async () => {
      const installation = {
        id: 'test-installation-id',
        botToken: 'xoxb-test-token',
      };
      const channelId = 'channel-1';
      const ts = '**********.123456';

      mockSlackMessagesRepository.findByCondition.mockResolvedValue({
        id: 'message-1',
        slackMessageTs: ts,
        platformCommentId: 'comment-123',
      });

      const result = await (handler as any).getPlatformCommentId(
        installation,
        channelId,
        ts,
      );

      expect(result).toBe('comment-123');
      expect(mockSlackMessagesRepository.findByCondition).toHaveBeenCalledWith({
        where: [
          {
            channel: { id: channelId },
            installation: { id: installation.id },
            slackMessageTs: ts,
          },
          {
            channel: { id: channelId },
            installation: { id: installation.id },
            slackMessageThreadTs: ts,
          },
        ],
      });
    });

    it('should get platform comment ID from a thread message', async () => {
      const installation = {
        id: 'test-installation-id',
        botToken: 'xoxb-test-token',
      };
      const channelId = 'channel-1';
      const ts = '**********.123456';
      const parentTs = '**********.000000';

      mockSlackMessagesRepository.findByCondition.mockResolvedValue({
        id: 'message-1',
        slackMessageTs: parentTs,
        slackMessageThreadTs: parentTs,
        platformCommentId: 'parent-comment-123',
      });

      mockThenaPlatformApiProvider.getCommentThreads.mockResolvedValue([
        {
          id: 'thread-comment-123',
          metadata: {
            external_sinks: {
              slack: {
                ts: ts,
              },
            },
          },
        },
      ]);

      const result = await (handler as any).getPlatformCommentId(
        installation,
        channelId,
        ts,
      );

      expect(result).toBe('thread-comment-123');
      expect(
        mockThenaPlatformApiProvider.getCommentThreads,
      ).toHaveBeenCalledWith(installation, 'parent-comment-123');
    });

    it('should get platform comment ID from a grouped message', async () => {
      const installation = {
        id: 'test-installation-id',
        botToken: 'xoxb-test-token',
      };
      const channelId = 'channel-1';
      const ts = '**********.123456';

      mockSlackMessagesRepository.findByCondition.mockResolvedValue(null);

      mockGroupedSlackMessagesRepository.findByCondition.mockResolvedValue({
        id: 'grouped-message-1',
        slackMessageTs: ts,
        parentCommentId: 'parent-comment-123',
      });

      const result = await (handler as any).getPlatformCommentId(
        installation,
        channelId,
        ts,
      );

      expect(result).toBe('parent-comment-123');
      expect(
        mockGroupedSlackMessagesRepository.findByCondition,
      ).toHaveBeenCalledWith({
        where: {
          slackMessageTs: ts,
          channel: { id: channelId },
        },
      });
    });

    it('should return null when no message is found', async () => {
      const installation = {
        id: 'test-installation-id',
        botToken: 'xoxb-test-token',
      };
      const channelId = 'channel-1';
      const ts = '**********.123456';

      mockSlackMessagesRepository.findByCondition.mockResolvedValue(null);
      mockGroupedSlackMessagesRepository.findByCondition.mockResolvedValue(
        null,
      );

      const result = await (handler as any).getPlatformCommentId(installation, channelId, ts);
      
      expect(result).toBe(null);
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'No related slack message found for ts: **********.123456, skipping reaction removal',
      );
    });
  });
});
