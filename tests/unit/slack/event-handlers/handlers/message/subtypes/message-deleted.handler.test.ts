import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  CustomerContacts,
  Installations,
  SlackMessages,
  Users,
} from '../../../../../../../src/database/entities';
import { ChannelsRepository } from '../../../../../../../src/database/entities/channels/repositories';
import { SlackMessagesRepository } from '../../../../../../../src/database/entities/slack-messages/repositories/slack-messages.repository';
import { ThenaPlatformApiProvider } from '../../../../../../../src/external/provider/thena-platform-api.provider';
import { MessageDeletedHandler } from '../../../../../../../src/slack/event-handlers/handlers/message/subtypes/message-deleted.handler';
import { SlackEventMap } from '../../../../../../../src/slack/event-handlers/interface';
import { ILogger } from '../../../../../../../src/utils';
import { BaseSlackBlocksToHtml } from '../../../../../../../src/utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';

describe('MessageDeletedHandler', () => {
  let handler: MessageDeletedHandler;
  let mockLogger: ILogger;
  let mockUsersRepository: Repository<Users>;
  let mockCustomersRepository: Repository<CustomerContacts>;
  let mockChannelRepository: ChannelsRepository;
  let mockSlackMessagesRepository: SlackMessagesRepository;
  let mockThenaPlatformApiProvider: ThenaPlatformApiProvider;
  let mockBaseSlackBlocksToHtml: BaseSlackBlocksToHtml;

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' },
  } as unknown as Installations;

  const mockChannel = {
    id: 'channel-123',
    channelId: 'C12345',
  };

  const mockSlackMessage = {
    id: 'slack-message-123',
    platformTicketId: 'ticket-123',
    platformCommentId: 'comment-123',
    slackMessageTs: '**********.123456',
    slackMessageThreadTs: '**********.123456',
    channel: { id: 'channel-123' },
    installation: { id: 'installation-123' },
  } as unknown as SlackMessages;

  const mockThreadComments = [
    {
      id: 'thread-comment-123',
      metadata: {
        external_sinks: {
          slack: {
            ts: '**********.654321',
          },
        },
      },
    },
  ];

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockUsersRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<Users>;

    mockCustomersRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<CustomerContacts>;

    mockChannelRepository = {
      findByCondition: vi.fn(),
    } as unknown as ChannelsRepository;

    mockSlackMessagesRepository = {
      findByCondition: vi.fn(),
    } as unknown as SlackMessagesRepository;

    mockThenaPlatformApiProvider = {
      deleteComment: vi.fn(),
      getCommentThreads: vi.fn(),
    } as unknown as ThenaPlatformApiProvider;

    mockBaseSlackBlocksToHtml = {
      initialize: vi.fn(),
      convert: vi.fn(),
    } as unknown as BaseSlackBlocksToHtml;

    handler = new MessageDeletedHandler(
      mockLogger,
      mockUsersRepository,
      mockCustomersRepository,
      mockChannelRepository,
      mockSlackMessagesRepository,
      mockThenaPlatformApiProvider,
      mockBaseSlackBlocksToHtml,
    );
  });

  describe('handle', () => {
    it('should throw an error if event subtype is not message_deleted', async () => {
      const event = {
        event: {
          type: 'message',
          subtype: 'message_changed',
          channel: 'C12345',
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      await expect(handler.handle(event)).rejects.toThrow(
        'Subtype message_changed not supported',
      );
    });

    it('should skip processing for bot messages', async () => {
      const event = {
        event: {
          type: 'message',
          subtype: 'message_deleted',
          channel: 'C12345',
          previous_message: {
            subtype: 'bot_message',
            text: 'Bot message',
          },
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      await handler.handle(event);

      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Bot message event received'),
      );
      expect(mockChannelRepository.findByCondition).not.toHaveBeenCalled();
    });

    it('should throw an error if channel is not found', async () => {
      const event = {
        event: {
          type: 'message',
          subtype: 'message_deleted',
          channel: 'C12345',
          deleted_ts: '**********.123456',
          previous_message: {
            text: 'Message to delete',
            thread_ts: '**********.123456',
          },
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      (mockChannelRepository.findByCondition as Mock).mockResolvedValue(null);

      await expect(handler.handle(event)).rejects.toThrow('Channel not found');
    });

    it('should throw an error if related slack message is not found', async () => {
      const event = {
        event: {
          type: 'message',
          subtype: 'message_deleted',
          channel: 'C12345',
          deleted_ts: '**********.123456',
          previous_message: {
            text: 'Message to delete',
            thread_ts: '**********.123456',
          },
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      (mockChannelRepository.findByCondition as Mock).mockResolvedValue(mockChannel);
      (mockSlackMessagesRepository.findByCondition as Mock).mockResolvedValue(null);

      await expect(handler.handle(event)).rejects.toThrow(
        'No related slack message found',
      );
    });

    it('should throw an error if platform comment ID is not found', async () => {
      const event = {
        event: {
          type: 'message',
          subtype: 'message_deleted',
          channel: 'C12345',
          deleted_ts: '**********.123456',
          previous_message: {
            text: 'Message to delete',
            thread_ts: '**********.123456',
          },
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      (mockChannelRepository.findByCondition as Mock).mockResolvedValue(mockChannel);
      (mockSlackMessagesRepository.findByCondition as Mock).mockResolvedValue({
        ...mockSlackMessage,
        platformCommentId: null,
      });

      await expect(handler.handle(event)).rejects.toThrow(
        'No platform comment ID found',
      );
    });

    it('should delete the parent comment if deleted message is the top-level message', async () => {
      const event = {
        event: {
          type: 'message',
          subtype: 'message_deleted',
          channel: 'C12345',
          deleted_ts: '**********.123456', // Same as slackMessageTs
          previous_message: {
            text: 'Message to delete',
            thread_ts: '**********.123456',
          },
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      (mockChannelRepository.findByCondition as Mock).mockResolvedValue(mockChannel);
      (mockSlackMessagesRepository.findByCondition as Mock).mockResolvedValue(
        mockSlackMessage,
      );
      (mockThenaPlatformApiProvider.deleteComment as Mock).mockResolvedValue({
        success: true,
      });

      await handler.handle(event);

      expect(mockThenaPlatformApiProvider.deleteComment).toHaveBeenCalledWith(
        mockInstallation,
        mockSlackMessage.platformCommentId,
      );
      expect(
        mockThenaPlatformApiProvider.getCommentThreads,
      ).not.toHaveBeenCalled();
    });

    it('should delete a thread comment if deleted message is a thread reply', async () => {
      const event = {
        event: {
          type: 'message',
          subtype: 'message_deleted',
          channel: 'C12345',
          deleted_ts: '**********.654321', // Different from slackMessageTs
          previous_message: {
            text: 'Thread reply to delete',
            thread_ts: '**********.123456',
          },
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      (mockChannelRepository.findByCondition as Mock).mockResolvedValue(mockChannel);
      (mockSlackMessagesRepository.findByCondition as Mock).mockResolvedValue(
        mockSlackMessage,
      );
      (mockThenaPlatformApiProvider.getCommentThreads as Mock).mockResolvedValue(
        mockThreadComments,
      );
      (mockThenaPlatformApiProvider.deleteComment as Mock).mockResolvedValue({
        success: true,
      });

      await handler.handle(event);

      expect(
        mockThenaPlatformApiProvider.getCommentThreads,
      ).toHaveBeenCalledWith(
        mockInstallation,
        mockSlackMessage.platformCommentId,
      );
      expect(mockThenaPlatformApiProvider.deleteComment).toHaveBeenCalledWith(
        mockInstallation,
        'thread-comment-123',
      );
    });

    it('should throw an error if thread comment is not found', async () => {
      const event = {
        event: {
          type: 'message',
          subtype: 'message_deleted',
          channel: 'C12345',
          deleted_ts: '**********.999999', // Not matching any thread comment
          previous_message: {
            text: 'Thread reply to delete',
            thread_ts: '**********.123456',
          },
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      (mockChannelRepository.findByCondition as Mock).mockResolvedValue(mockChannel);
      (mockSlackMessagesRepository.findByCondition as Mock).mockResolvedValue(
        mockSlackMessage,
      );
      (mockThenaPlatformApiProvider.getCommentThreads as Mock).mockResolvedValue(
        mockThreadComments,
      );

      await expect(handler.handle(event)).rejects.toThrow(
        'No thread comment found',
      );
    });
  });

  describe('getThreadTs', () => {
    it('should return thread_ts from previous_message', async () => {
      const event = {
        previous_message: {
          thread_ts: '**********.123456',
        },
      };

      const result = await handler['getThreadTs'](event as any);

      expect(result).toBe('**********.123456');
    });

    it('should return undefined if previous_message has no thread_ts', async () => {
      const event = {
        previous_message: {
          text: 'Message with no thread_ts',
        },
      };

      const result = await handler['getThreadTs'](event as any);

      expect(result).toBeUndefined();
    });

    it('should return undefined if event has no previous_message', async () => {
      const event = {
        // No previous_message property
        channel: 'C12345',
        ts: '**********.123456',
      };

      // The service doesn't handle undefined previousMessage gracefully, so it should throw an error
      await expect(handler['getThreadTs'](event as any)).rejects.toThrow();
    });
  });

  describe('getRelatedSlackMessage', () => {
    it('should throw an error if threadTs is not provided', async () => {
      await expect(
        handler['getRelatedSlackMessage'](
          mockInstallation,
          'channel-123',
          undefined as any,
        ),
      ).rejects.toThrow('Thread timestamp not found');
    });

    it('should find slack message by slackMessageTs or slackMessageThreadTs', async () => {
      (mockSlackMessagesRepository.findByCondition as Mock).mockResolvedValue(
        mockSlackMessage,
      );

      const result = await handler['getRelatedSlackMessage'](
        mockInstallation,
        'channel-123',
        '**********.123456',
      );

      expect((mockSlackMessagesRepository.findByCondition as Mock)).toHaveBeenCalledWith({
        where: [
          {
            channel: { id: 'channel-123' },
            installation: { id: 'installation-123' },
            slackMessageTs: '**********.123456',
          },
          {
            channel: { id: 'channel-123' },
            installation: { id: 'installation-123' },
            slackMessageThreadTs: '**********.123456',
          },
        ],
      });
      expect(result).toEqual(mockSlackMessage);
    });
  });

  describe('getPlatformCommentId', () => {
    it('should return parent comment ID if ts matches slackMessageTs', async () => {
      const result = await handler['getPlatformCommentId'](
        mockInstallation,
        mockSlackMessage,
        mockSlackMessage.slackMessageTs,
      );

      expect(result).toBe(mockSlackMessage.platformCommentId);
      expect(
        mockThenaPlatformApiProvider.getCommentThreads,
      ).not.toHaveBeenCalled();
    });

    it('should find thread comment ID if ts does not match slackMessageTs', async () => {
      (mockThenaPlatformApiProvider.getCommentThreads as Mock).mockResolvedValue(
        mockThreadComments,
      );

      const result = await handler['getPlatformCommentId'](
        mockInstallation,
        mockSlackMessage,
        '**********.654321',
      );

      expect(
        mockThenaPlatformApiProvider.getCommentThreads,
      ).toHaveBeenCalledWith(
        mockInstallation,
        mockSlackMessage.platformCommentId,
      );
      expect(result).toBe('thread-comment-123');
    });
  });
});
