import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  CustomerContacts,
  Installations,
  SlackMessages,
  Users,
} from '../../../../../../../src/database/entities';
import { ChannelsRepository } from '../../../../../../../src/database/entities/channels/repositories';
import { GroupedSlackMessagesRepository } from '../../../../../../../src/database/entities/slack-messages/repositories/grouped-slack-messages.repository';
import { SlackMessagesRepository } from '../../../../../../../src/database/entities/slack-messages/repositories/slack-messages.repository';
import { ThenaPlatformApiProvider } from '../../../../../../../src/external/provider/thena-platform-api.provider';
import { Message<PERSON>hangedHandler } from '../../../../../../../src/slack/event-handlers/handlers/message/subtypes/message-changed.handler';
import { SlackEventMap } from '../../../../../../../src/slack/event-handlers/interface';
import { ILogger } from '../../../../../../../src/utils';
import { BaseSlackBlocksToHtml } from '../../../../../../../src/utils/parsers/slack/slack-blocks-to-html/base-slack-to-html.parser';

describe('MessageChangedHandler', () => {
  let handler: MessageChangedHandler;
  let mockLogger: ILogger;
  let mockUsersRepository: Repository<Users>;
  let mockCustomersRepository: Repository<CustomerContacts>;
  let mockChannelRepository: ChannelsRepository;
  let mockSlackMessagesRepository: SlackMessagesRepository;
  let mockGroupedSlackMessagesRepository: GroupedSlackMessagesRepository;
  let mockThenaPlatformApiProvider: ThenaPlatformApiProvider;
  let mockBaseSlackBlocksToHtml: BaseSlackBlocksToHtml;

  const mockInstallation = {
    id: 'installation-123',
    teamId: 'T12345',
    botToken: 'xoxb-token',
    organization: { id: 'org-123' },
  } as unknown as Installations;

  const mockChannel = {
    id: 'channel-123',
    channelId: 'C12345',
  };

  const mockSlackMessage = {
    id: 'slack-message-123',
    platformTicketId: 'ticket-123',
    platformCommentId: 'comment-123',
    slackMessageTs: '**********.123456',
    slackMessageThreadTs: '**********.123456',
    channel: { id: 'channel-123' },
    installation: { id: 'installation-123' },
  } as unknown as SlackMessages;

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    mockUsersRepository = {
      findOne: vi.fn().mockResolvedValue({
        id: 'user-123',
        slackId: 'U12345',
        slackProfileEmail: '<EMAIL>',
      }),
    } as unknown as Repository<Users>;

    mockCustomersRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<CustomerContacts>;

    mockChannelRepository = {
      findByCondition: vi.fn(),
    } as unknown as ChannelsRepository;

    mockSlackMessagesRepository = {
      findByCondition: vi.fn(),
      update: vi.fn().mockResolvedValue({ affected: 1 }),
    } as unknown as SlackMessagesRepository;

    mockGroupedSlackMessagesRepository = {
      findByCondition: vi.fn(),
    } as unknown as GroupedSlackMessagesRepository;

    mockThenaPlatformApiProvider = {
      updateComment: vi.fn().mockResolvedValue({ ok: true }),
      getCommentThreads: vi.fn().mockResolvedValue([]),
    } as unknown as ThenaPlatformApiProvider;

    mockBaseSlackBlocksToHtml = {
      initialize: vi.fn(),
      convert: vi.fn().mockResolvedValue('<p>Converted HTML</p>'),
    } as unknown as BaseSlackBlocksToHtml;

    handler = new MessageChangedHandler(
      mockLogger,
      mockUsersRepository,
      mockCustomersRepository,
      mockChannelRepository,
      mockSlackMessagesRepository,
      mockGroupedSlackMessagesRepository,
      mockThenaPlatformApiProvider,
      mockBaseSlackBlocksToHtml,
    );
  });

  describe('handle', () => {
    it('should throw an error if event subtype is not message_changed', async () => {
      const event = {
        event: {
          type: 'message',
          subtype: 'message_deleted',
          channel: 'C12345',
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      await expect(handler.handle(event)).rejects.toThrow(
        'Subtype message_deleted not supported',
      );
    });

    it('should skip processing for bot messages', async () => {
      const event = {
        event: {
          type: 'message',
          subtype: 'message_changed',
          channel: 'C12345',
          message: {
            subtype: 'bot_message',
            text: 'Bot message',
          },
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      await handler.handle(event);

      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Bot message event received'),
      );
      expect(mockChannelRepository.findByCondition).not.toHaveBeenCalled();
    });

    it('should throw an error if channel is not found', async () => {
      const event = {
        event: {
          type: 'message',
          subtype: 'message_changed',
          channel: 'C12345',
          message: {
            text: 'Updated message',
            ts: '**********.123456',
            blocks: [
              {
                type: 'section',
                text: { type: 'mrkdwn', text: 'Updated message' },
              },
            ],
            user: 'U12345',
          },
          previous_message: {
            text: 'Original message',
            ts: '**********.123456',
          },
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      (mockChannelRepository.findByCondition as Mock).mockResolvedValue(null);

      await expect(handler.handle(event)).rejects.toThrow('Channel not found');
    });

    it('should throw an error if related slack message is not found', async () => {
      const event = {
        event: {
          type: 'message',
          subtype: 'message_changed',
          channel: 'C12345',
          message: {
            text: 'Updated message',
            ts: '**********.123456',
            blocks: [
              {
                type: 'section',
                text: { type: 'mrkdwn', text: 'Updated message' },
              },
            ],
            user: 'U12345',
          },
          previous_message: {
            text: 'Original message',
            ts: '**********.123456',
          },
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      (mockChannelRepository.findByCondition as Mock).mockResolvedValue(
        mockChannel,
      );
      (mockSlackMessagesRepository.findByCondition as Mock).mockResolvedValue(
        null,
      );
      (
        mockGroupedSlackMessagesRepository.findByCondition as Mock
      ).mockResolvedValue(null);

      await expect(handler.handle(event)).rejects.toThrow(
        'No related slack message found',
      );
    });

    it.skip('should update the comment on the platform', async () => {
      const event = {
        event: {
          type: 'message',
          subtype: 'message_changed',
          channel: 'C12345',
          message: {
            text: 'Updated message',
            ts: '**********.123456',
            blocks: [
              {
                type: 'section',
                text: { type: 'mrkdwn', text: 'Updated message' },
              },
            ],
            user: 'U12345',
          },
          previous_message: {
            text: 'Original message',
            ts: '**********.123456',
          },
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      (mockChannelRepository.findByCondition as Mock).mockResolvedValue(
        mockChannel,
      );
      (mockSlackMessagesRepository.findByCondition as Mock).mockResolvedValue(
        mockSlackMessage,
      );
      (
        mockGroupedSlackMessagesRepository.findByCondition as Mock
      ).mockResolvedValue(null);
      (mockBaseSlackBlocksToHtml.convert as Mock).mockResolvedValue(
        '<p>Updated message</p>',
      );

      await handler.handle(event);

      expect(mockBaseSlackBlocksToHtml.initialize).toHaveBeenCalledWith(
        expect.arrayContaining([
          {
            type: 'section',
            text: { type: 'mrkdwn', text: 'Updated message' },
          },
        ]),
        mockInstallation,
      );
      expect(mockThenaPlatformApiProvider.updateComment).toHaveBeenCalledWith(
        mockInstallation,
        expect.objectContaining({
          commentId: mockSlackMessage.platformCommentId,
          content: 'Updated message',
          htmlContent: '<p>Updated message</p>',
          commentAs: '<EMAIL>',
        }),
      );
      expect(mockSlackMessagesRepository.update).toHaveBeenCalledWith(
        mockSlackMessage.id,
        expect.objectContaining({
          metadata: expect.objectContaining({
            messageText: 'Updated message',
          }),
        }),
      );
    });

    it.skip('should handle errors during processing', async () => {
      const event = {
        event: {
          type: 'message',
          subtype: 'message_changed',
          channel: 'C12345',
          message: {
            text: 'Updated message',
            ts: '**********.123456',
            blocks: [
              {
                type: 'section',
                text: { type: 'mrkdwn', text: 'Updated message' },
              },
            ],
            user: 'U12345',
          },
          previous_message: {
            text: 'Original message',
            ts: '**********.123456',
          },
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      (mockChannelRepository.findByCondition as Mock).mockResolvedValue(
        mockChannel,
      );
      (mockSlackMessagesRepository.findByCondition as Mock).mockResolvedValue(
        mockSlackMessage,
      );
      (
        mockGroupedSlackMessagesRepository.findByCondition as Mock
      ).mockResolvedValue(null);

      // Simulate an error during HTML conversion
      const conversionError = new Error('HTML conversion failed');
      (mockBaseSlackBlocksToHtml.convert as Mock).mockRejectedValue(
        conversionError,
      );

      // Simulate an error during platform API call
      const apiError = new Error('API error');
      (mockThenaPlatformApiProvider.updateComment as Mock).mockRejectedValue(
        apiError,
      );

      // The handler should catch the error and log it
      await expect(handler.handle(event)).rejects.toThrow(
        'HTML conversion failed',
      );

      expect(mockLogger.error).toHaveBeenCalled();
    });

    it.skip('should handle the case when user is not found', async () => {
      const event = {
        event: {
          type: 'message',
          subtype: 'message_changed',
          channel: 'C12345',
          message: {
            text: 'Updated message',
            ts: '**********.123456',
            blocks: [
              {
                type: 'section',
                text: { type: 'mrkdwn', text: 'Updated message' },
              },
            ],
            user: 'U12345',
          },
          previous_message: {
            text: 'Original message',
            ts: '**********.123456',
          },
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as SlackEventMap['message'];

      (mockChannelRepository.findByCondition as Mock).mockResolvedValue(
        mockChannel,
      );
      (mockSlackMessagesRepository.findByCondition as Mock).mockResolvedValue(
        mockSlackMessage,
      );
      (
        mockGroupedSlackMessagesRepository.findByCondition as Mock
      ).mockResolvedValue(null);
      (mockBaseSlackBlocksToHtml.convert as Mock).mockResolvedValue(
        '<p>Updated message</p>',
      );

      // Simulate user not found
      (mockUsersRepository.findOne as Mock).mockResolvedValue(null);

      // Mock the getUser method to avoid throwing an error
      vi.spyOn(handler, 'getUser').mockResolvedValue(null);

      // Should still work but use a default email
      await handler.handle(event);

      expect(mockThenaPlatformApiProvider.updateComment).toHaveBeenCalledWith(
        mockInstallation,
        expect.objectContaining({
          commentId: mockSlackMessage.platformCommentId,
          content: 'Updated message',
          htmlContent: '<p>Updated message</p>',
          // Should use a default or null for commentAs
          commentAs: null,
        }),
      );
    });
  });
});
