import { beforeEach, describe, expect, it, vi } from 'vitest';
import { TransactionService } from '../../../../../../src/database/common';
import { ChannelsRepository } from '../../../../../../src/database/entities/channels/repositories';
import { ChannelType } from '../../../../../../src/database/entities/channels/channels.entity';
import { TeamChannelMapsRepository } from '../../../../../../src/database/entities/mappings/repositories/team-channel-maps.repository';
import { SlackChannelLeftHandler } from '../../../../../../src/slack/event-handlers/handlers/channel/channel-left.handler';
import { SlackWebAPIService } from '../../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../../src/utils';

describe('SlackChannelLeftHandler', () => {
  let handler: SlackChannelLeftHandler;
  let mockLogger: any;
  let mockSlackWebApiService: any;
  let mockTransactionService: any;
  let mockChannelsRepository: any;
  let mockTeamChannelMapsRepository: any;
  let mockInstallation: any;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    mockSlackWebApiService = {
      getConversationHistory: vi.fn(),
    };

    mockTransactionService = {
      runInTransaction: vi.fn().mockImplementation(async (callback) => {
        return callback('mock-txn-context');
      }),
    };

    mockChannelsRepository = {
      updateWithTxn: vi.fn(),
    };

    mockTeamChannelMapsRepository = {
      findByCondition: vi.fn(),
      removeWithTxn: vi.fn(),
    };

    mockInstallation = {
      id: 'test-installation-id',
      teamId: 'T12345',
      botToken: 'xoxb-test-token',
    };

    handler = new SlackChannelLeftHandler(
      mockLogger,
      mockSlackWebApiService,
      mockTransactionService,
      mockChannelsRepository,
      mockTeamChannelMapsRepository
    );
  });

  describe('canHandle', () => {
    it('should return true for channel_left events', () => {
      const event = {
        event: {
          type: 'channel_left',
          channel: 'C12345',
          event_ts: '1234567890.123456',
        },
      } as any;

      expect(handler.canHandle(event)).toBe(true);
    });

    it('should return false for other event types', () => {
      const event = {
        event: {
          type: 'channel_joined',
          channel: 'C12345',
          event_ts: '1234567890.123456',
        },
      } as any;

      expect(handler.canHandle(event)).toBe(false);
    });
  });

  describe('handle', () => {
    it('should handle valid channel_left events when bot is not in channel', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_left',
          channel: 'C12345',
          event_ts: '1234567890.123456',
        },
      } as any;

      const notInChannelError = new Error('not_in_channel');
      (notInChannelError as any).data = { error: 'not_in_channel' };
      
      mockSlackWebApiService.getConversationHistory.mockRejectedValue(notInChannelError);
      mockTeamChannelMapsRepository.findByCondition.mockResolvedValue(null);

      await handler.handle(event);

      expect(mockLogger.log).toHaveBeenCalledWith(
        `Received 'channel_left' event for slack team T12345 for channel C12345 at 1234567890.123456`
      );

      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Encountered an error from Slack')
      );

      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Expected errors encountered from Slack, proceeding with marking the channel as left from Bot!'
      );

      expect(mockTransactionService.runInTransaction).toHaveBeenCalled();

      expect(mockChannelsRepository.updateWithTxn).toHaveBeenCalledWith(
        'mock-txn-context',
        { channelId: 'C12345', installation: { id: 'test-installation-id' } },
        { isBotActive: false, isBotJoined: false, lastBotLeftAt: '1234567890.123456', channelType: ChannelType.NOT_CONFIGURED }
      );

      expect(mockLogger.log).toHaveBeenCalledWith(
        `Bot left channel C12345 at 1234567890.123456 for slack team T12345`
      );
    });

    it('should handle valid channel_left events when channel is not found', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_left',
          channel: 'C12345',
          event_ts: '1234567890.123456',
        },
      } as any;

      const channelNotFoundError = new Error('channel_not_found');
      (channelNotFoundError as any).data = { error: 'channel_not_found' };
      
      mockSlackWebApiService.getConversationHistory.mockRejectedValue(channelNotFoundError);
      mockTeamChannelMapsRepository.findByCondition.mockResolvedValue(null);

      await handler.handle(event);

      expect(mockLogger.log).toHaveBeenCalledWith(
        `Received 'channel_left' event for slack team T12345 for channel C12345 at 1234567890.123456`
      );

      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Encountered an error from Slack')
      );

      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Expected errors encountered from Slack, proceeding with marking the channel as left from Bot!'
      );

      expect(mockTransactionService.runInTransaction).toHaveBeenCalled();

      expect(mockChannelsRepository.updateWithTxn).toHaveBeenCalledWith(
        'mock-txn-context',
        { channelId: 'C12345', installation: { id: 'test-installation-id' } },
        { isBotActive: false, isBotJoined: false, lastBotLeftAt: '1234567890.123456', channelType: ChannelType.NOT_CONFIGURED }
      );

      expect(mockLogger.log).toHaveBeenCalledWith(
        `Bot left channel C12345 at 1234567890.123456 for slack team T12345`
      );
    });

    it('should not update channel when bot is still a member of the channel', async () => {
      vi.resetAllMocks();
      
      mockLogger = {
        log: vi.fn(),
        debug: vi.fn(),
        error: vi.fn(),
        warn: vi.fn(),
      };
      
      mockSlackWebApiService = {
        getConversationHistory: vi.fn(),
      };
      
      mockTransactionService = {
        runInTransaction: vi.fn(),
      };
      
      mockChannelsRepository = {
        updateWithTxn: vi.fn(),
      };
      
      mockTeamChannelMapsRepository = {
        findByCondition: vi.fn(),
        removeWithTxn: vi.fn(),
      };
      
      handler = new SlackChannelLeftHandler(
        mockLogger,
        mockSlackWebApiService,
        mockTransactionService,
        mockChannelsRepository,
        mockTeamChannelMapsRepository
      );
      
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_left',
          channel: 'C12345',
          event_ts: '1234567890.123456',
        },
      } as any;

      mockSlackWebApiService.getConversationHistory.mockResolvedValue({
        ok: true,
        messages: [{ text: 'test message' }],
      });

      await handler.handle(event);

      expect(mockLogger.log).toHaveBeenCalledWith(
        `Received 'channel_left' event for slack team T12345 for channel C12345 at 1234567890.123456`
      );

      expect(mockLogger.warn).toHaveBeenCalledWith(
        `Bot is still a member of this channel C12345, not proceeding further with the event process.`
      );

      expect(mockChannelsRepository.updateWithTxn).not.toHaveBeenCalled();
    });

    it('should handle unexpected errors from Slack API', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_left',
          channel: 'C12345',
          event_ts: '1234567890.123456',
        },
      } as any;

      const unexpectedError = new Error('unexpected_error');
      (unexpectedError as any).data = { error: 'unexpected_error' };
      
      mockSlackWebApiService.getConversationHistory.mockRejectedValue(unexpectedError);

      await handler.handle(event);

      expect(mockLogger.log).toHaveBeenCalledWith(
        `Received 'channel_left' event for slack team T12345 for channel C12345 at 1234567890.123456`
      );

      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Encountered an error from Slack')
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        `Unexpected error encountered from Slack, unexpected_error, rethrowing the error!`
      );
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('[ChannelLeftHandler] Failed to handle channel left event'),
        expect.any(String)
      );

      expect(mockTransactionService.runInTransaction).not.toHaveBeenCalled();
      expect(mockChannelsRepository.updateWithTxn).not.toHaveBeenCalled();
    });

    it('should handle non-Error errors from Slack API', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_left',
          channel: 'C12345',
          event_ts: '1234567890.123456',
        },
      } as any;

      mockSlackWebApiService.getConversationHistory.mockRejectedValue('String error');

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      await handler.handle(event);

      expect(mockLogger.log).toHaveBeenCalledWith(
        `Received 'channel_left' event for slack team T12345 for channel C12345 at 1234567890.123456`
      );

      expect(consoleErrorSpy).toHaveBeenCalledWith(
        '[ChannelLeftHandler] Failed to handle channel left event, String error'
      );

      consoleErrorSpy.mockRestore();
    });

    it('should handle transaction errors', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_left',
          channel: 'C12345',
          event_ts: '1234567890.123456',
        },
      } as any;

      const notInChannelError = new Error('not_in_channel');
      (notInChannelError as any).data = { error: 'not_in_channel' };
      
      mockSlackWebApiService.getConversationHistory.mockRejectedValue(notInChannelError);
      mockTeamChannelMapsRepository.findByCondition.mockResolvedValue(null);

      mockTransactionService.runInTransaction.mockRejectedValue(new Error('Transaction failed'));

      await handler.handle(event);

      expect(mockLogger.log).toHaveBeenCalledWith(
        `Received 'channel_left' event for slack team T12345 for channel C12345 at 1234567890.123456`
      );

      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Encountered an error from Slack')
      );

      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Expected errors encountered from Slack, proceeding with marking the channel as left from Bot!'
      );

      expect(mockTransactionService.runInTransaction).toHaveBeenCalled();

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('[ChannelLeftHandler] Failed to handle channel left event'),
        expect.any(String)
      );
    });

    it('should handle invalid events', async () => {
      const event = {
        context: {
          installation: mockInstallation,
        },
        event: {
          type: 'channel_left',
          event_ts: '1234567890.123456',
        },
      } as any;

      await handler.handle(event);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('[ChannelLeftHandler] Failed to handle channel left event'),
        expect.any(String)
      );

      expect(mockTransactionService.runInTransaction).not.toHaveBeenCalled();
      expect(mockChannelsRepository.updateWithTxn).not.toHaveBeenCalled();
    });
  });
});
