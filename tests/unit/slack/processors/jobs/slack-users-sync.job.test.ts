import { beforeEach, describe, expect, it, vi, afterEach } from 'vitest';
import { SlackUsersSyncJob } from '../../../../../src/slack/processors/jobs/slack-users-sync.job';
import { ILogger } from '../../../../../src/utils/logger';
import { Installations, Users } from '../../../../../src/database/entities';
import { Repository } from 'typeorm';
import { BotsRepository } from '../../../../../src/database/entities/bots/repositories';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { ThenaPlatformApiProvider } from '../../../../../src/external/provider/thena-platform-api.provider';
import { TransactionService } from '../../../../../src/database/common';
import { Member } from '@slack/web-api/dist/types/response/UsersListResponse';

describe('SlackUsersSyncJob', () => {
  let job: SlackUsersSyncJob;
  let mockLogger: ILogger;
  let mockUsersRepository: Repository<Users>;
  let mockBotsRepository: BotsRepository;
  let mockTransactionService: TransactionService;
  let mockSlackWebAPIService: SlackWebAPIService;
  let mockThenaPlatformService: ThenaPlatformApiProvider;
  let defaultInstallation: Installations;

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    } as unknown as ILogger;

    mockUsersRepository = {
      upsert: vi.fn().mockResolvedValue(undefined),
    } as unknown as Repository<Users>;

    mockBotsRepository = {
      upsertWithTxn: vi.fn().mockResolvedValue(undefined),
    } as unknown as BotsRepository;

    mockTransactionService = {
      runInTransaction: vi.fn().mockImplementation(async (callback) => {
        const mockTxnContext = {
          manager: {
            upsert: vi.fn().mockResolvedValue(undefined),
          },
        };
        return await callback(mockTxnContext);
      }),
    } as unknown as TransactionService;

    mockSlackWebAPIService = {
      listUsers: vi.fn().mockResolvedValue({
        ok: true,
        members: [],
        response_metadata: { next_cursor: '' },
      }),
    } as unknown as SlackWebAPIService;

    mockThenaPlatformService = {
      linkUsersToPlatform: vi.fn().mockResolvedValue([]),
    } as unknown as ThenaPlatformApiProvider;

    defaultInstallation = {
      id: 'installation1',
      botToken: 'xoxb-123456',
      teamId: 'team1',
      organization: { id: 'org1' },
    } as Installations;

    job = new SlackUsersSyncJob(
      mockLogger,
      mockUsersRepository,
      mockBotsRepository,
      mockTransactionService,
      mockSlackWebAPIService,
      mockThenaPlatformService
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('execute', () => {
    it('should sync users for an installation', async () => {
      const members = [
        {
          id: 'U12345',
          name: 'johndoe',
          is_bot: false,
          profile: {
            email: '<EMAIL>',
          },
          deleted: false,
        },
        {
          id: 'B67890',
          name: 'botuser',
          is_bot: true,
          profile: {
            title: 'A Helpful Bot',
          },
          deleted: false,
        },
      ];

      vi.mocked(mockSlackWebAPIService.listUsers).mockResolvedValueOnce({
        ok: true,
        members,
        response_metadata: { next_cursor: '' },
      });

      // Act
      await job.execute(defaultInstallation);

      // Assert
      expect(mockSlackWebAPIService.listUsers).toHaveBeenCalledWith(
        'xoxb-123456',
        expect.objectContaining({ limit: 200 })
      );

      expect(mockTransactionService.runInTransaction).toHaveBeenCalledTimes(1);
      expect(mockBotsRepository.upsertWithTxn).toHaveBeenCalledTimes(1);
      expect(mockThenaPlatformService.linkUsersToPlatform).toHaveBeenCalledTimes(1);
      expect(mockThenaPlatformService.linkUsersToPlatform).toHaveBeenCalledWith(
        defaultInstallation,
        expect.objectContaining({
          externalType: 'slack',
          details: expect.arrayContaining([
            expect.objectContaining({
              email: '<EMAIL>',
              slackSinkDetails: {
                id: 'U12345',
                teamId: 'team1',
              },
            }),
          ]),
        })
      );
    });

    it('should handle Slack API pagination correctly', async () => {
      vi.mocked(mockSlackWebAPIService.listUsers).mockResolvedValueOnce({
        ok: true,
        members: [
          {
            id: 'U1',
            name: 'user1',
            is_bot: false,
            profile: { email: '<EMAIL>' },
            deleted: false,
          },
        ],
        response_metadata: { next_cursor: 'next_page_cursor' },
      });

      vi.mocked(mockSlackWebAPIService.listUsers).mockResolvedValueOnce({
        ok: true,
        members: [
          {
            id: 'U2',
            name: 'user2',
            is_bot: false,
            profile: { email: '<EMAIL>' },
            deleted: false,
          },
        ],
        response_metadata: { next_cursor: '' },
      });

      // Act
      await job.execute(defaultInstallation);

      // Assert
      expect(mockSlackWebAPIService.listUsers).toHaveBeenCalledTimes(2);
      expect(mockSlackWebAPIService.listUsers).toHaveBeenCalledWith(
        'xoxb-123456',
        expect.objectContaining({ limit: 200, cursor: '' })
      );
      expect(mockSlackWebAPIService.listUsers).toHaveBeenCalledWith(
        'xoxb-123456',
        expect.objectContaining({ limit: 200, cursor: 'next_page_cursor' })
      );

      expect(mockTransactionService.runInTransaction).toHaveBeenCalledTimes(2);
      expect(mockBotsRepository.upsertWithTxn).toHaveBeenCalledTimes(2);
      expect(mockThenaPlatformService.linkUsersToPlatform).toHaveBeenCalledTimes(2);
    });

    it('should handle API errors gracefully', async () => {
      vi.mocked(mockSlackWebAPIService.listUsers).mockRejectedValueOnce(
        new Error('API rate limit exceeded')
      );

      await expect(job.execute(defaultInstallation)).rejects.toThrow('API rate limit exceeded');
      
      expect(mockLogger.error).toHaveBeenCalled();
      expect(mockTransactionService.runInTransaction).not.toHaveBeenCalled();
      expect(mockBotsRepository.upsertWithTxn).not.toHaveBeenCalled();
      expect(mockThenaPlatformService.linkUsersToPlatform).not.toHaveBeenCalled();
    });

    it('should handle Slack API error response', async () => {
      vi.mocked(mockSlackWebAPIService.listUsers)
        .mockResolvedValueOnce({
          ok: false,
          error: 'token_expired',
          members: [],
          response_metadata: { next_cursor: '' },
        })
        .mockResolvedValueOnce({
          ok: true,
          members: [],
          response_metadata: { next_cursor: '' },
        });

      await job.execute(defaultInstallation);

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Slack users sync failed, error: token_expired')
      );
      expect(mockTransactionService.runInTransaction).toHaveBeenCalledTimes(1);
      expect(mockBotsRepository.upsertWithTxn).toHaveBeenCalledTimes(1);
      expect(mockThenaPlatformService.linkUsersToPlatform).not.toHaveBeenCalled();
    });

    it('should handle empty user data gracefully', async () => {
      vi.mocked(mockSlackWebAPIService.listUsers).mockResolvedValueOnce({
        ok: true,
        members: [],
        response_metadata: { next_cursor: '' },
      });

      await job.execute(defaultInstallation);

      expect(mockTransactionService.runInTransaction).toHaveBeenCalledTimes(1);
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'No users with a valid email, skipping platform linking'
      );
      expect(mockThenaPlatformService.linkUsersToPlatform).not.toHaveBeenCalled();
    });

    it('should handle invalid user data gracefully', async () => {
      const members = [
        {
          id: 'U12345',
          name: 'johndoe',
          is_bot: false,
          profile: undefined,
          deleted: false,
        },
        {
          id: 'B67890',
          name: 'botuser',
          is_bot: true,
          profile: undefined,
          deleted: false,
        },
      ] as Member[];

      vi.mocked(mockSlackWebAPIService.listUsers).mockResolvedValueOnce({
        ok: true,
        members,
        response_metadata: { next_cursor: '' },
      });

      await job.execute(defaultInstallation);

      expect(mockTransactionService.runInTransaction).toHaveBeenCalledTimes(1);
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'No users with a valid email, skipping platform linking'
      );
      expect(mockThenaPlatformService.linkUsersToPlatform).not.toHaveBeenCalled();
    });
  });
}); 