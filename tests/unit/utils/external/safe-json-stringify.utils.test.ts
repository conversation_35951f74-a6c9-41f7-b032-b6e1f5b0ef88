import { describe, expect, it, vi } from 'vitest';
import { safeJsonStringify } from '../../../../src/utils/external/safe-json-stringify.utils';

describe('safeJsonStringify', () => {
  describe('Basic Functionality', () => {
    it('should stringify primitive values correctly', () => {
      expect(safeJsonStringify('test')).toBe('"test"');
      expect(safeJsonStringify(123)).toBe('123');
      expect(safeJsonStringify(true)).toBe('true');
      expect(safeJsonStringify(null)).toBe('null');
      expect(safeJsonStringify(undefined)).toBe('undefined');
    });

    it('should stringify simple objects correctly', () => {
      const obj = { name: 'John', age: 30 };
      expect(JSON.parse(safeJsonStringify(obj)!)).toEqual(obj);
    });

    it('should stringify arrays correctly', () => {
      const arr = [1, 'two', { three: 3 }];
      expect(JSON.parse(safeJsonStringify(arr)!)).toEqual(arr);
    });
  });

  describe('Special Types Handling', () => {
    it('should handle Date objects', () => {
      const date = new Date('2024-02-02');
      const result = safeJsonStringify({ date });
      expect(JSON.parse(result!).date).toBe(date.toISOString());
    });

    it('should handle RegExp objects', () => {
      const regex = /test/gi;
      const result = safeJsonStringify({ regex });
      expect(JSON.parse(result!).regex).toBe(regex.toString());
    });

    it('should handle Error objects', () => {
      const error = new Error('test error');
      const result = JSON.parse(safeJsonStringify({ error })!);
      expect(result.error).toHaveProperty('message', 'test error');
      expect(result.error).toHaveProperty('name', 'Error');
      expect(result.error).toHaveProperty('stack');
    });

    it('should handle objects with toJSON method', () => {
      const obj = {
        toJSON: () => ({ custom: 'value' }),
      };
      expect(safeJsonStringify(obj)).toBe('{"custom":"value"}');
    });
  });

  describe('Circular References', () => {
    it('should handle circular references when enabled', () => {
      const circular: any = { prop: 'value' };
      circular.self = circular;

      const result = safeJsonStringify(circular, { handleCircular: true });
      expect(result).toContain('[Circular Reference]');
    });

    it.skip('should fail gracefully with circular references when disabled', () => {
      const circular: any = { prop: 'value' };
      circular.self = circular;

      const result = safeJsonStringify(circular, {
        handleCircular: false,
        fallback: '{"error":"circular"}',
      });
      expect(result).toBe('{"error":"circular"}');
    });
  });

  describe('Depth Handling', () => {
    it('should respect maxDepth option', () => {
      const deep = {
        l1: {
          l2: {
            l3: {
              l4: 'deep',
            },
          },
        },
      };

      const result = JSON.parse(safeJsonStringify(deep, { maxDepth: 2 })!);
      expect(result.l1.l2).toBe('[Max Depth Exceeded]');

      // Test at different depths
      const result2 = JSON.parse(safeJsonStringify(deep, { maxDepth: 3 })!);
      expect(result2.l1.l2.l3).toBe('[Max Depth Exceeded]');

      // Test that primitive values at max depth are preserved
      const primitiveAtDepth = {
        l1: {
          l2: 'value',
        },
      };
      const result3 = JSON.parse(
        safeJsonStringify(primitiveAtDepth, { maxDepth: 2 })!,
      );
      expect(result3.l1.l2).toBe('value');
    });
  });

  describe('Fallback Handling', () => {
    it('should return undefined when no fallback provided', () => {
      const badObj = {
        toJSON: () => {
          throw new Error();
        },
      };
      expect(safeJsonStringify(badObj)).toBeUndefined();
    });

    it('should return fallback string when provided', () => {
      const badObj = {
        toJSON: () => {
          throw new Error();
        },
      };
      expect(safeJsonStringify(badObj, { fallback: '{}' })).toBe('{}');
    });

    it('should handle null fallback', () => {
      const badObj = {
        toJSON: () => {
          throw new Error();
        },
      };
      expect(safeJsonStringify(badObj, { fallback: null })).toBe('null');
    });

    it('should stringify fallback object if possible', () => {
      const badObj = {
        toJSON: () => {
          throw new Error();
        },
      };
      expect(
        safeJsonStringify(badObj, {
          fallback: { status: 'error' },
        }),
      ).toBe('{"status":"error"}');
    });
  });

  describe('Pretty Printing', () => {
    it('should apply spacing when specified', () => {
      const obj = { name: 'John', age: 30 };
      const result = safeJsonStringify(obj, { space: 2 });
      expect(result).toBe('{\n  "name": "John",\n  "age": 30\n}');
    });
  });

  describe('Custom Replacer', () => {
    it('should apply custom replacer function', () => {
      const obj = { password: 'secret', public: 'visible' };
      const replacer = (key: string, value: any) =>
        key === 'password' ? '[REDACTED]' : value;

      const result = JSON.parse(safeJsonStringify(obj, { replacer })!);
      expect(result).toEqual({
        password: '[REDACTED]',
        public: 'visible',
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle BigInt values gracefully', () => {
      const obj = { big: BigInt(9007199254740991) };
      expect(safeJsonStringify(obj, { fallback: '{}' })).toBe('{}');
    });

    it('should handle invalid JSON values', () => {
      const obj = {
        fn: () => {},
        symbol: Symbol('test'),
        bigint: BigInt(123),
      };
      expect(safeJsonStringify(obj, { fallback: '{}' })).toBe('{}');
    });
  });

  describe('Development Warnings', () => {
    it('should log warnings in development', () => {
      const originalNodeEnv = process.env.NODE_ENV;
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      process.env.NODE_ENV = 'development';
      const badObj = {
        toJSON: () => {
          throw new Error('Test error');
        },
      };

      safeJsonStringify(badObj);

      expect(consoleSpy).toHaveBeenCalled();
      expect(consoleSpy.mock.calls[0][0]).toBe('safeJsonStringify failed:');

      consoleSpy.mockRestore();
      process.env.NODE_ENV = originalNodeEnv;
    });

    it('should not log warnings in production', () => {
      const originalNodeEnv = process.env.NODE_ENV;
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      process.env.NODE_ENV = 'production';
      const badObj = {
        toJSON: () => {
          throw new Error('Test error');
        },
      };

      safeJsonStringify(badObj);

      expect(consoleSpy).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
      process.env.NODE_ENV = originalNodeEnv;
    });
  });
});
