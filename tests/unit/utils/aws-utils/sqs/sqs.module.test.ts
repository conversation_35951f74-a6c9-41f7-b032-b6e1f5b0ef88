import { describe, expect, it, vi } from 'vitest';
import { SQSConsumerService } from '../../../../../src/utils/aws-utils/sqs/sqs-consumer.service';
import { SQSProducerService } from '../../../../../src/utils/aws-utils/sqs/sqs-producer.service';
import { SQSModule } from '../../../../../src/utils/aws-utils/sqs/sqs.module';

describe('SQSModule', () => {
  describe('Producer', () => {
    it('should create a dynamic module with SQSProducerService', () => {
      // Arrange
      const options = {
        region: 'us-east-1',
        queueUrl: 'https://sqs.us-east-1.amazonaws.com/123456789012/test-queue',
        accessKeyId: 'test-access-key',
        secretAccessKey: 'test-secret-key',
      };

      // Act
      const module = SQSModule.Producer(options);

      // Assert
      expect(module).toBeDefined();
      expect(module.module).toBe(SQSModule);
      expect(module.providers).toBeDefined();
      expect(module.exports).toContain(SQSProducerService);

      // Check that the config provider is correctly set up
      const configProvider = module.providers.find(
        (provider) => provider.provide === 'SQSConfig',
      );
      expect(configProvider).toBeDefined();
      expect(configProvider.useValue).toEqual(options);

      // Check that the service provider is included in the providers
      expect(module.providers).toContainEqual(SQSProducerService);
    });
  });

  describe('Consumer', () => {
    it('should create a dynamic module with SQSConsumerService', () => {
      // Arrange
      const options = {
        region: 'us-east-1',
        queueUrl: 'https://sqs.us-east-1.amazonaws.com/123456789012/test-queue',
        accessKeyId: 'test-access-key',
        secretAccessKey: 'test-secret-key',
        handleMessage: vi.fn(),
        batchSize: 10,
        visibilityTimeout: 30,
        waitTimeSeconds: 20,
      };

      // Act
      const module = SQSModule.Consumer(options);

      // Assert
      expect(module).toBeDefined();
      expect(module.module).toBe(SQSModule);
      expect(module.providers).toBeDefined();
      expect(module.exports).toContain(SQSConsumerService);

      // Check that the config provider is correctly set up
      const configProvider = module.providers.find(
        (provider) => provider.provide === 'SQSConfig',
      );
      expect(configProvider).toBeDefined();
      expect(configProvider.useValue).toEqual(options);

      // Check that the service provider is included in the providers
      expect(module.providers).toContainEqual(SQSConsumerService);
    });
  });
});
