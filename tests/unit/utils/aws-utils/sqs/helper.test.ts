import { Message } from '@aws-sdk/client-sqs';
import { describe, expect, it } from 'vitest';
import { transformSQSMessage } from '../../../../../src/utils/aws-utils/sqs/helper';

describe('SQS Helper Functions', () => {
  describe('transformSQSMessage', () => {
    it('should transform a valid SQS message with JSON body', () => {
      // Arrange
      const mockMessage: Message = {
        ReceiptHandle: 'receipt-handle-123',
        Body: JSON.stringify({ key: 'value', nested: { prop: 'test' } }),
        MessageAttributes: {
          event_name: {
            DataType: 'String',
            StringValue: 'test_event',
          },
          event_id: {
            DataType: 'String',
            StringValue: 'event-123',
          },
          custom_attribute: {
            DataType: 'String',
            StringValue: 'custom-value',
          },
        },
      };

      // Act
      const result = transformSQSMessage(mockMessage);

      // Assert
      expect(result).toEqual({
        id: 'receipt-handle-123',
        message: { key: 'value', nested: { prop: 'test' } },
        messageAttributes: {
          event_name: 'test_event',
          event_id: 'event-123',
          custom_attribute: 'custom-value',
        },
      });
    });

    it('should transform a valid SQS message with string body', () => {
      // Arrange
      const mockMessage: Message = {
        ReceiptHandle: 'receipt-handle-123',
        Body: 'plain text message',
        MessageAttributes: {
          event_name: {
            DataType: 'String',
            StringValue: 'test_event',
          },
          event_id: {
            DataType: 'String',
            StringValue: 'event-123',
          },
        },
      };

      // Act
      const result = transformSQSMessage(mockMessage);

      // Assert
      expect(result).toEqual({
        id: 'receipt-handle-123',
        message: 'plain text message',
        messageAttributes: {
          event_name: 'test_event',
          event_id: 'event-123',
        },
      });
    });

    it.skip('should handle message with no attributes', () => {
      // Arrange
      const mockMessage: Message = {
        ReceiptHandle: 'receipt-handle-123',
        Body: JSON.stringify({ key: 'value' }),
        // No MessageAttributes
      };

      // Act
      const result = transformSQSMessage(mockMessage);

      // Assert
      expect(result).toEqual({
        id: 'receipt-handle-123',
        message: { key: 'value' },
        messageAttributes: {}, // Empty object for attributes
      });
    });

    it('should handle message with invalid JSON body', () => {
      // Arrange
      const mockMessage: Message = {
        ReceiptHandle: 'receipt-handle-123',
        Body: '{invalid json',
        MessageAttributes: {
          event_name: {
            DataType: 'String',
            StringValue: 'test_event',
          },
          event_id: {
            DataType: 'String',
            StringValue: 'event-123',
          },
        },
      };

      // Act
      const result = transformSQSMessage(mockMessage);

      // Assert
      expect(result).toEqual({
        id: 'receipt-handle-123',
        message: '{invalid json',
        messageAttributes: {
          event_name: 'test_event',
          event_id: 'event-123',
        },
      });
    });

    it.skip('should handle message with null or undefined body', () => {
      // Test with null body
      const mockMessageWithNullBody: Message = {
        ReceiptHandle: 'receipt-handle-123',
        Body: null,
        MessageAttributes: {
          event_name: {
            DataType: 'String',
            StringValue: 'test_event',
          },
        },
      };

      const resultWithNullBody = transformSQSMessage(mockMessageWithNullBody);
      expect(resultWithNullBody).toEqual({
        id: 'receipt-handle-123',
        message: null,
        messageAttributes: {
          event_name: 'test_event',
        },
      });

      // Test with undefined body
      const mockMessageWithUndefinedBody: Message = {
        ReceiptHandle: 'receipt-handle-123',
        // Body is undefined
        MessageAttributes: {
          event_name: {
            DataType: 'String',
            StringValue: 'test_event',
          },
        },
      };

      const resultWithUndefinedBody = transformSQSMessage(
        mockMessageWithUndefinedBody,
      );
      expect(resultWithUndefinedBody).toEqual({
        id: 'receipt-handle-123',
        message: undefined,
        messageAttributes: {
          event_name: 'test_event',
        },
      });
    });

    it.skip('should handle message with non-string attributes', () => {
      // Arrange
      const mockMessage: Message = {
        ReceiptHandle: 'receipt-handle-123',
        Body: 'test message',
        MessageAttributes: {
          // String attribute
          string_attr: {
            DataType: 'String',
            StringValue: 'string-value',
          },
          // Number attribute
          number_attr: {
            DataType: 'Number',
            StringValue: '123',
          },
          // Binary attribute
          binary_attr: {
            DataType: 'Binary',
            BinaryValue: Buffer.from('binary-data'),
          },
          // Attribute with missing StringValue
          missing_value: {
            DataType: 'String',
            // No StringValue
          },
        },
      };

      // Act
      const result = transformSQSMessage(mockMessage);

      // Assert
      expect(result).toEqual({
        id: 'receipt-handle-123',
        message: 'test message',
        messageAttributes: {
          string_attr: 'string-value',
          number_attr: '123',
          binary_attr: Buffer.from('binary-data'),
          missing_value: undefined,
        },
      });
    });
  });
});
