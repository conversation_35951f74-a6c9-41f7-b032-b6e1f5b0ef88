import { Repository } from 'typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  CustomerContacts,
  Installations,
  Users,
} from '../../../../../src/database/entities';
import { convertTiptapJSONToSlackBlocks } from '../../../../../src/utils/parsers/tiptap/tiptap-json-to-slack-blocks.parser';

describe('TipTap JSON to Slack Blocks Parser', () => {
  let mockUserRepository: Repository<Users>;
  let mockCustomerContactRepository: Repository<CustomerContacts>;
  let mockInstallationsRepository: Repository<Installations>;
  let mockInstallation: Installations;

  beforeEach(() => {
    mockUserRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<Users>;

    mockCustomerContactRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<CustomerContacts>;

    mockInstallationsRepository = {
      findOne: vi.fn(),
    } as unknown as Repository<Installations>;

    mockInstallation = {
      id: 'test-installation-id',
    } as Installations;
  });

  describe('convertTiptapJSONToSlackBlocks', () => {
    it('should return undefined for empty content', async () => {
      const result = await convertTiptapJSONToSlackBlocks(
        { type: 'doc' },
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );
      expect(result).toBeUndefined();
    });

    it('should convert paragraph to rich_text block', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'Hello world',
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toEqual([
        {
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_section',
              elements: [
                {
                  type: 'text',
                  text: 'Hello world',
                },
              ],
            },
          ],
        },
      ]);
    });

    it('should convert headings to rich_text blocks', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'heading',
            content: [
              {
                type: 'text',
                text: 'This is a heading',
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toEqual([
        {
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_section',
              elements: [
                {
                  type: 'text',
                  text: 'This is a heading',
                },
              ],
            },
          ],
        },
      ]);
    });

    it('should convert formatted text with bold, italic, and strike marks', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'Bold',
                marks: [{ type: 'bold' }],
              },
              {
                type: 'text',
                text: 'Italic',
                marks: [{ type: 'italic' }],
              },
              {
                type: 'text',
                text: 'Strike',
                marks: [{ type: 'strike' }],
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result[0].type).toBe('rich_text');
      expect(result[0].elements[0].type).toBe('rich_text_section');
      expect(result[0].elements[0].elements).toHaveLength(3);

      expect(result[0].elements[0].elements[0].type).toBe('text');
      expect(result[0].elements[0].elements[0].text).toBe('Bold');
      expect(result[0].elements[0].elements[0].style.bold).toBe(true);

      expect(result[0].elements[0].elements[1].type).toBe('text');
      expect(result[0].elements[0].elements[1].text).toBe('Italic');
      expect(result[0].elements[0].elements[1].style.italic).toBe(true);

      expect(result[0].elements[0].elements[2].type).toBe('text');
      expect(result[0].elements[0].elements[2].text).toBe('Strike');
      expect(result[0].elements[0].elements[2].style.strike).toBe(true);
    });

    it('should convert links', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'Visit',
              },
              {
                type: 'text',
                text: 'Thena',
                marks: [
                  {
                    type: 'link',
                    attrs: {
                      href: 'https://thena.ai',
                    },
                  },
                ],
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toEqual([
        {
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_section',
              elements: [
                {
                  type: 'text',
                  text: 'Visit',
                },
                {
                  type: 'link',
                  url: 'https://thena.ai',
                  text: 'Thena',
                  style: {
                    bold: false,
                    italic: false,
                    strike: false,
                  },
                },
              ],
            },
          ],
        },
      ]);
    });

    it('should convert links with empty href as plain text', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'Here is a ',
              },
              {
                type: 'text',
                text: 'link',
                marks: [
                  {
                    type: 'link',
                    attrs: {
                      href: '',
                    },
                  },
                ],
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
      );

      expect(result).toEqual([
        {
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_section',
              elements: [
                {
                  type: 'text',
                  text: 'Here is a ',
                },
                {
                  type: 'text',
                  text: 'link',
                  style: {
                    bold: false,
                    italic: false,
                    strike: false,
                  },
                },
              ],
            },
          ],
        },
      ]);
    });

    it('should convert code blocks', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'codeBlock',
            content: [
              {
                type: 'text',
                text: 'const x = 1;',
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toEqual([
        {
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_preformatted',
              elements: [
                {
                  type: 'text',
                  text: 'const x = 1;',
                },
              ],
            },
          ],
        },
      ]);
    });

    it('should convert inline code', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'code example: ',
              },
              {
                type: 'text',
                text: 'const x = 1;',
                marks: [{ type: 'code' }],
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toEqual([
        {
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_section',
              elements: [
                {
                  type: 'text',
                  text: 'code example: ',
                },
                {
                  type: 'text',
                  text: 'const x = 1;',
                  style: {
                    code: true,
                  },
                },
              ],
            },
          ],
        },
      ]);
    });

    it('should convert horizontal rules', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'horizontalRule',
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toEqual([
        {
          type: 'divider',
        },
      ]);
    });

    it('should convert images', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'image',
            attrs: {
              src: 'https://example.com/image.jpg',
              alt: 'An example image',
              title: 'Example Image',
            },
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toEqual([
        {
          type: 'image',
          image_url: 'https://example.com/image.jpg',
          alt_text: 'An example image',
          title: {
            emoji: true,
            text: 'Example Image',
            type: 'plain_text',
          },
        },
      ]);
    });

    it('should convert blockquotes', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'blockquote',
            content: [
              {
                type: 'paragraph',
                content: [
                  {
                    type: 'text',
                    text: 'This is a quote',
                  },
                ],
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toEqual([
        {
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_quote',
              elements: [
                {
                  type: 'text',
                  text: 'This is a quote',
                },
              ],
            },
          ],
        },
      ]);
    });

    it('should convert bullet lists', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'bulletList',
            content: [
              {
                type: 'listItem',
                content: [
                  {
                    type: 'paragraph',
                    content: [
                      {
                        type: 'text',
                        text: 'Item 1',
                      },
                    ],
                  },
                ],
              },
              {
                type: 'listItem',
                content: [
                  {
                    type: 'paragraph',
                    content: [
                      {
                        type: 'text',
                        text: 'Item 2',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toEqual([
        {
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_list',
              style: 'bullet',
              indent: 0,
              border: 0,
              elements: [
                {
                  type: 'rich_text_section',
                  elements: [
                    {
                      type: 'text',
                      text: 'Item 1',
                    },
                  ],
                },
              ],
            },
            {
              type: 'rich_text_list',
              style: 'bullet',
              indent: 0,
              border: 0,
              elements: [
                {
                  type: 'rich_text_section',
                  elements: [
                    {
                      type: 'text',
                      text: 'Item 2',
                    },
                  ],
                },
              ],
            },
          ],
        },
      ]);
    });

    it('should convert ordered lists', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'orderedList',
            content: [
              {
                type: 'listItem',
                content: [
                  {
                    type: 'paragraph',
                    content: [
                      {
                        type: 'text',
                        text: 'First item',
                      },
                    ],
                  },
                ],
              },
              {
                type: 'listItem',
                content: [
                  {
                    type: 'paragraph',
                    content: [
                      {
                        type: 'text',
                        text: 'Second item',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toEqual([
        {
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_list',
              style: 'ordered',
              indent: 0,
              border: 0,
              elements: [
                {
                  type: 'rich_text_section',
                  elements: [
                    {
                      type: 'text',
                      text: 'First item',
                    },
                  ],
                },
              ],
            },
            {
              type: 'rich_text_list',
              style: 'ordered',
              indent: 0,
              border: 0,
              elements: [
                {
                  type: 'rich_text_section',
                  elements: [
                    {
                      type: 'text',
                      text: 'Second item',
                    },
                  ],
                },
              ],
            },
          ],
        },
      ]);
    });

    it('should convert emojis', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'Hello ',
              },
              {
                type: 'emoji',
                attrs: {
                  name: 'smile',
                },
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toEqual([
        {
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_section',
              elements: [
                {
                  type: 'text',
                  text: 'Hello ',
                },
                {
                  type: 'emoji',
                  name: 'smile',
                },
              ],
            },
          ],
        },
      ]);
    });

    it('should convert user mentions', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'Hello ',
              },
              {
                type: 'mention',
                attrs: {
                  id: 'U12345',
                  email: '<EMAIL>',
                  label: 'User Name',
                },
              },
            ],
          },
        ],
      };

      // Mock the user repository to return a user
      mockUserRepository.findOne = vi.fn().mockResolvedValue({
        slackId: 'U12345',
      });

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toEqual([
        {
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_section',
              elements: [
                {
                  type: 'text',
                  text: 'Hello ',
                },
                {
                  type: 'user',
                  user_id: 'U12345',
                },
              ],
            },
          ],
        },
      ]);

      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: {
          installation: { id: mockInstallation.id },
          slackProfileEmail: '<EMAIL>',
        },
      });
    });

    it('should handle unknown user mentions', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'Hello ',
              },
              {
                type: 'mention',
                attrs: {
                  id: 'U12345',
                  email: '<EMAIL>',
                  label: 'User Name',
                },
              },
            ],
          },
        ],
      };

      // Mock the user repository to return null (user not found)
      mockUserRepository.findOne = vi.fn().mockResolvedValue(null);

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toEqual([
        {
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_section',
              elements: [
                {
                  type: 'text',
                  text: 'Hello ',
                },
                {
                  type: 'text',
                  text: 'User Name',
                },
              ],
            },
          ],
        },
      ]);
    });

    it('should convert usergroup mentions', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'Hello ',
              },
              {
                type: 'mention',
                attrs: {
                  id: 'S12345',
                  label: 'Subteam',
                },
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toEqual([
        {
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_section',
              elements: [
                {
                  type: 'text',
                  text: 'Hello ',
                },
                {
                  type: 'text',
                  text: 'Subteam',
                },
              ],
            },
          ],
        },
      ]);
    });

    it('should convert videos', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'video',
            attrs: {
              src: 'https://example.com/video.mp4',
              videoDetails: {
                title: 'Example Video',
                alt_text: 'A sample video',
                thumbnail_url: 'https://example.com/thumbnail.jpg',
                title_url: 'https://example.com/video-page',
                author_name: 'Example Author',
                provider_name: 'Example Provider',
                provider_icon_url: 'https://example.com/provider-icon.jpg',
                description: 'This is a sample video description',
              },
            },
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toEqual([
        {
          type: 'video',
          video_url: 'https://example.com/video.mp4',
          alt_text: 'A sample video',
          title: {
            type: 'plain_text',
            text: 'Example Video',
            emoji: true,
          },
          thumbnail_url: 'https://example.com/thumbnail.jpg',
          title_url: 'https://example.com/video-page',
          author_name: 'Example Author',
          provider_name: 'Example Provider',
          provider_icon_url: 'https://example.com/provider-icon.jpg',
          description: {
            type: 'plain_text',
            text: 'This is a sample video description',
            emoji: true,
          },
        },
      ]);
    });

    it('should convert text with image', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'textWithImage',
            content: [
              {
                type: 'text',
                text: 'Image description',
              },
            ],
            attrs: {
              src: 'https://example.com/image.jpg',
              alt: 'An example image',
            },
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result[0].type).toBe('section');
      expect(result[0].accessory.type).toBe('image');
      expect(result[0].accessory.image_url).toBe(
        'https://example.com/image.jpg',
      );
      expect(result[0].accessory.alt_text).toBe('An example image');
      expect(result[0].text.type).toBe('mrkdwn');
    });

    it('should convert text with button', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'textWithButton',
            content: [
              {
                type: 'text',
                text: 'Click the button',
              },
            ],
            attrs: {
              buttonText: 'Click me',
              href: 'https://example.com',
            },
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result[0].type).toBe('section');
      expect(result[0].text.type).toBe('mrkdwn');
      expect(result[0].accessory.type).toBe('button');
      expect(result[0].accessory.text.type).toBe('plain_text');
      expect(result[0].accessory.text.text).toBe('Click me');
      expect(result[0].accessory.url).toBe('https://example.com');
      expect(result[0].accessory.value).toBe('click_me_123');
      expect(result[0].accessory.action_id).toBe('button-action');
    });

    it('should convert files', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'files',
            content: [
              {
                name: 'example.pdf',
                permalink: 'https://example.com/files/example.pdf',
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toEqual([
        {
          type: 'rich_text',
          elements: [
            {
              type: 'rich_text_section',
              elements: [
                {
                  type: 'link',
                  url: 'https://example.com/files/example.pdf',
                  text: 'example.pdf',
                },
              ],
            },
          ],
        },
      ]);
    });

    it('should handle empty paragraphs', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [],
          },
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'Some text after empty paragraph',
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toHaveLength(2);
      expect(result[1]).toEqual({
        type: 'rich_text',
        elements: [
          {
            type: 'rich_text_section',
            elements: [
              {
                type: 'text',
                text: 'Some text after empty paragraph',
              },
            ],
          },
        ],
      });
    });

    it('should handle hard breaks', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'Line 1',
              },
              {
                type: 'hardBreak',
              },
              {
                type: 'text',
                text: 'Line 2',
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('rich_text');
      expect(result[0].elements[0].type).toBe('rich_text_section');
      expect(result[0].elements[0].elements).toHaveLength(3);
      expect(result[0].elements[0].elements[0].text).toBe('Line 1');
      expect(result[0].elements[0].elements[1].text).toBe('\n');
      expect(result[0].elements[0].elements[2].text).toBe('Line 2');
    });

    it('should handle nested blockquotes with code blocks', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'blockquote',
            content: [
              {
                type: 'codeBlock',
                content: [
                  {
                    type: 'text',
                    text: 'const x = 123;',
                  },
                ],
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('rich_text');
      expect(result[0].elements).toHaveLength(1);
      expect(result[0].elements[0].type).toBe('rich_text_preformatted');
      expect(result[0].elements[0].border).toBe(1);
    });

    it('should handle nested blockquotes with lists', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'blockquote',
            content: [
              {
                type: 'bulletList',
                content: [
                  {
                    type: 'listItem',
                    content: [
                      {
                        type: 'paragraph',
                        content: [
                          {
                            type: 'text',
                            text: 'List item in blockquote',
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('rich_text');
      expect(result[0].elements).toHaveLength(1);
      expect(result[0].elements[0].type).toBe('rich_text_list');
      expect(result[0].elements[0].style).toBe('bullet');
      expect(result[0].elements[0].border).toBe(1);
    });

    it('should handle nested lists', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'bulletList',
            content: [
              {
                type: 'listItem',
                content: [
                  {
                    type: 'paragraph',
                    content: [
                      {
                        type: 'text',
                        text: 'Parent item',
                      },
                    ],
                  },
                  {
                    type: 'bulletList',
                    content: [
                      {
                        type: 'listItem',
                        content: [
                          {
                            type: 'paragraph',
                            content: [
                              {
                                type: 'text',
                                text: 'Nested item',
                              },
                            ],
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('rich_text');
      expect(result[0].elements).toHaveLength(2);

      // First element is the parent item
      expect(result[0].elements[0].type).toBe('rich_text_list');
      expect(result[0].elements[0].style).toBe('bullet');
      expect(result[0].elements[0].indent).toBe(0);

      // Second element is the nested item
      expect(result[0].elements[1].type).toBe('rich_text_list');
      expect(result[0].elements[1].style).toBe('bullet');
      expect(result[0].elements[1].indent).toBe(1);
    });

    it('should handle multiple marks on text', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'Bold and italic',
                marks: [{ type: 'bold' }, { type: 'italic' }],
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('rich_text');
      expect(result[0].elements[0].elements[0].type).toBe('text');
      expect(result[0].elements[0].elements[0].style.bold).toBe(true);
      expect(result[0].elements[0].elements[0].style.italic).toBe(true);
    });

    it('should handle empty blockquotes', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'blockquote',
            content: [],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('rich_text');
      expect(Array.isArray(result[0].elements)).toBe(true);
    });

    it('should handle elements with no content field', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      // The function returns an empty array when the paragraph has no content
      expect(result).toEqual([]);
    });

    it('should handle unknown types gracefully', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'unknownType',
            content: [
              {
                type: 'text',
                text: 'Unknown content',
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      // Should just skip unknown types
      expect(Array.isArray(result)).toBe(true);
    });

    it('should handle inline text with multiple styles', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'Bold and Strike',
                marks: [{ type: 'bold' }, { type: 'strike' }],
              },
            ],
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toHaveLength(1);
      expect(result[0].elements[0].elements[0].style.bold).toBe(true);
      expect(result[0].elements[0].elements[0].style.strike).toBe(true);
    });

    it('should handle videos with minimal attributes', async () => {
      const tiptapJSON = {
        type: 'doc',
        content: [
          {
            type: 'video',
            attrs: {
              src: 'https://example.com/video.mp4',
            },
          },
        ],
      };

      const result = await convertTiptapJSONToSlackBlocks(
        tiptapJSON,
        mockUserRepository,
        mockInstallation,
        mockCustomerContactRepository,
        mockInstallationsRepository,
      );

      expect(result).toHaveLength(1);
      expect(result[0].type).toBe('video');
      expect(result[0].video_url).toBe('https://example.com/video.mp4');
    });
  });
});
