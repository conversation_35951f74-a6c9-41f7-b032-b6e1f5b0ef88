import * as cheerio from 'cheerio';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { HtmlToSlackBlocks } from '../../../../../src/utils/parsers/slack/html-to-slack-blocks.parser';

describe('HtmlToSlackBlocks', () => {
  describe('constructor', () => {
    it('should initialize with HTML content', () => {
      const html = '<p>Test content</p>';
      const parser = new HtmlToSlackBlocks(html);
      expect(parser).toBeDefined();
    });
  });

  describe('convert', () => {
    it('should convert simple paragraph to Slack blocks', async () => {
      const html = '<p>Test content</p>';
      const parser = new HtmlToSlackBlocks(html);
      const result = await parser.convert();

      expect(result).toBeDefined();
      expect(result.blocks).toBeInstanceOf(Array);
      expect(result.blocks.length).toBeGreaterThan(0);
      expect(result.blocks[0]).toHaveProperty('type');
      expect(result.usersMentioned).toBeInstanceOf(Array);
    });

    it('should handle empty HTML', async () => {
      const html = '';
      const parser = new HtmlToSlackBlocks(html);
      const result = await parser.convert();

      expect(result).toBeDefined();
      expect(result.blocks).toBeInstanceOf(Array);
      expect(result.usersMentioned).toBeInstanceOf(Array);
    });

    it('should extract pre code content', async () => {
      const html =
        '<p>Text before</p><pre><code>const x = 1;</code></pre><p>Text after</p>';
      const parser = new HtmlToSlackBlocks(html);
      const result = await parser.convert();

      expect(result.blocks).toHaveLength(3);
      expect(result.blocks[1].text.text).toContain('```');
      expect(result.blocks[1].text.text).toContain('const x = 1;');
    });
  });

  describe('getUsersMentioned', () => {
    it('should extract user IDs from mentions', () => {
      const html =
        '<p>Hello <span class="mention" data-type="mention" data-id="U12345" data-email="<EMAIL>">@User</span></p>';
      const parser = new HtmlToSlackBlocks(html);
      const usersMentioned = parser.getUsersMentioned();

      expect(usersMentioned).toContain('U12345');
    });

    it('should extract user group IDs from mentions', () => {
      const html =
        '<p>Hello <span class="mention" data-type="mention" data-id="S12345" data-email="<EMAIL>">@Group</span></p>';
      const parser = new HtmlToSlackBlocks(html);
      parser.getUsersMentioned();

      // We can't directly test userGroupsMentioned as it's private, but we can verify it doesn't add to usersMentioned
      expect(parser['usersMentioned']).not.toContain('S12345');
      expect(parser['userGroupsMentiond']).toContain('S12345');
    });

    it('should handle multiple mentions', () => {
      const html =
        '<p>Hello <span class="mention" data-type="mention" data-id="U12345" data-email="<EMAIL>">@User1</span> and <span class="mention" data-type="mention" data-id="U67890" data-email="<EMAIL>">@User2</span></p>';
      const parser = new HtmlToSlackBlocks(html);
      const usersMentioned = parser.getUsersMentioned();

      expect(usersMentioned).toContain('U12345');
      expect(usersMentioned).toContain('U67890');
      expect(usersMentioned).toHaveLength(2);
    });
  });

  describe('extractPreCodeContent', () => {
    it('should extract code content from pre tags', () => {
      const html = '<pre><code>const x = 1;</code></pre>';
      const parser = new HtmlToSlackBlocks(html);
      parser.extractPreCodeContent();

      expect(parser['preCodeContents']).toHaveLength(1);
      expect(parser['preCodeContents'][0]).toBe('const x = 1;');
    });

    it('should handle multiple pre tags', () => {
      const html =
        '<pre><code>const x = 1;</code></pre><pre><code>const y = 2;</code></pre>';
      const parser = new HtmlToSlackBlocks(html);
      parser.extractPreCodeContent();

      expect(parser['preCodeContents']).toHaveLength(2);
      expect(parser['preCodeContents'][0]).toBe('const x = 1;');
      expect(parser['preCodeContents'][1]).toBe('const y = 2;');
    });

    it('should store code content in preCodeContents array', () => {
      const html = '<pre><code>const x = 1;</code></pre>';
      const parser = new HtmlToSlackBlocks(html);
      parser.extractPreCodeContent();

      // Instead of checking the HTML transformation which might be implementation-specific,
      // we'll verify that the code content was extracted correctly
      expect(parser['preCodeContents']).toHaveLength(1);
      expect(parser['preCodeContents'][0]).toBe('const x = 1;');
    });
  });

  describe('processNode', () => {
    let parser: HtmlToSlackBlocks;

    beforeEach(() => {
      parser = new HtmlToSlackBlocks('<div></div>');
      vi.spyOn(parser as any, 'addSectionBlock');
      vi.spyOn(parser as any, 'addLineBreak');
      vi.spyOn(parser as any, 'addListBlock');
      vi.spyOn(parser as any, 'addQuoteBlock');
      vi.spyOn(parser as any, 'addCodeBlock');
      vi.spyOn(parser as any, 'processInlineElements');
    });

    it('should process paragraphs with content', async () => {
      const $ = cheerio.load('<p>Test paragraph</p>');
      const node = $('p')[0];

      await parser['processNode'](node);

      expect(parser['processInlineElements']).toHaveBeenCalled();
      expect(parser['addSectionBlock']).toHaveBeenCalled();
    });

    it('should process empty paragraphs', async () => {
      const $ = cheerio.load('<p></p>');
      const node = $('p')[0];

      await parser['processNode'](node);

      expect(parser['addLineBreak']).toHaveBeenCalled();
    });

    it('should process unordered lists', async () => {
      const $ = cheerio.load('<ul><li>Item 1</li><li>Item 2</li></ul>');
      const node = $('ul')[0];

      await parser['processNode'](node);

      expect(parser['addListBlock']).toHaveBeenCalledWith(node, true);
    });

    it('should process ordered lists', async () => {
      const $ = cheerio.load('<ol><li>Item 1</li><li>Item 2</li></ol>');
      const node = $('ol')[0];

      await parser['processNode'](node);

      expect(parser['addListBlock']).toHaveBeenCalledWith(node, false);
    });

    it('should process blockquotes', async () => {
      const $ = cheerio.load('<blockquote>Quote text</blockquote>');
      const node = $('blockquote')[0];

      await parser['processNode'](node);

      expect(parser['addQuoteBlock']).toHaveBeenCalled();
    });

    it('should process pre tags with code', async () => {
      parser = new HtmlToSlackBlocks('<pre>PRE_CODE_PLACEHOLDER_0</pre>');
      parser['preCodeContents'] = ['const x = 1;'];

      const $ = cheerio.load(parser['html']);
      const node = $('pre')[0];

      vi.spyOn(parser as any, 'addCodeBlock');

      await parser['processNode'](node);

      expect(parser['addCodeBlock']).toHaveBeenCalledWith('const x = 1;');
    });

    it('should process other nodes by recursively processing children', async () => {
      const $ = cheerio.load('<div><p>Paragraph in div</p></div>');
      const node = $('div')[0];

      const processSpy = vi.spyOn(parser as any, 'processNode');

      await parser['processNode'](node);

      // Called once for the div and once for the paragraph
      expect(processSpy).toHaveBeenCalledTimes(2);
    });
  });

  describe('addLineBreak', () => {
    it('should add a line break block', () => {
      const parser = new HtmlToSlackBlocks('');
      parser['addLineBreak']();

      expect(parser['blocks']).toHaveLength(1);
      expect(parser['blocks'][0]).toEqual({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '\n',
        },
      });
    });
  });

  describe('addSectionBlock', () => {
    it('should add a section block with the provided text', () => {
      const parser = new HtmlToSlackBlocks('');
      parser['addSectionBlock']('Test section text');

      expect(parser['blocks']).toHaveLength(1);
      expect(parser['blocks'][0]).toEqual({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: 'Test section text',
        },
      });
    });
  });

  describe('addListBlock', () => {
    it('should add a bullet list block', async () => {
      const html = '<ul><li><p>Item 1</p></li><li><p>Item 2</p></li></ul>';
      const parser = new HtmlToSlackBlocks(html);
      const $ = cheerio.load(html);
      const node = $('ul')[0];

      await parser['addListBlock'](node, true);

      expect(parser['blocks']).toHaveLength(1);
      expect(parser['blocks'][0].type).toBe('rich_text');
      expect(parser['blocks'][0].elements).toHaveLength(2);
      expect(parser['blocks'][0].elements[0].style).toBe('bullet');
    });

    it('should add an ordered list block', async () => {
      const html = '<ol><li><p>Item 1</p></li><li><p>Item 2</p></li></ol>';
      const parser = new HtmlToSlackBlocks(html);
      const $ = cheerio.load(html);
      const node = $('ol')[0];

      await parser['addListBlock'](node, false);

      expect(parser['blocks']).toHaveLength(1);
      expect(parser['blocks'][0].type).toBe('rich_text');
      expect(parser['blocks'][0].elements).toHaveLength(2);
      expect(parser['blocks'][0].elements[0].style).toBe('ordered');
    });

    it('should handle nested lists with proper indentation', async () => {
      const html =
        '<ul><li><p>Item 1</p><ul><li><p>Nested Item</p></li></ul></li><li><p>Item 2</p></li></ul>';
      const parser = new HtmlToSlackBlocks(html);
      const $ = cheerio.load(html);
      const node = $('ul')[0];

      await parser['addListBlock'](node, true);

      expect(parser['blocks']).toHaveLength(1);
      expect(parser['blocks'][0].elements.length).toBeGreaterThan(2); // Should have more than 2 elements due to nesting

      // Check that we have elements with different indentation levels
      const indentLevels = parser['blocks'][0].elements.map((el) => el.indent);
      expect(indentLevels).toContain(0); // Top level
      expect(indentLevels).toContain(1); // Nested level
    });
  });

  describe('addQuoteBlock', () => {
    it('should add a quote block with the provided text', () => {
      const parser = new HtmlToSlackBlocks('');
      const $ = cheerio.load('<blockquote>Quote text</blockquote>');
      const node = $('blockquote');

      vi.spyOn(parser as any, 'processInlineElements').mockReturnValue(
        'Quote text',
      );

      parser['addQuoteBlock'](node);

      expect(parser['blocks']).toHaveLength(1);
      expect(parser['blocks'][0]).toEqual({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '> Quote text',
        },
      });
    });
  });

  describe('addCodeBlock', () => {
    it('should add a code block with the provided code', () => {
      const parser = new HtmlToSlackBlocks('');
      parser['addCodeBlock']('const x = 1;');

      expect(parser['blocks']).toHaveLength(1);
      expect(parser['blocks'][0]).toEqual({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '```\nconst x = 1;\n```',
        },
      });
    });
  });

  describe('processInlineElements', () => {
    let parser: HtmlToSlackBlocks;

    beforeEach(() => {
      parser = new HtmlToSlackBlocks('');
    });

    it('should process strong/bold text', () => {
      const $ = cheerio.load('<p>This is <strong>bold</strong> text</p>');
      const node = $('p');

      const result = parser['processInlineElements'](node);

      expect(result).toContain('*bold*');
    });

    it('should process em/italic text', () => {
      const $ = cheerio.load('<p>This is <em>italic</em> text</p>');
      const node = $('p');

      const result = parser['processInlineElements'](node);

      expect(result).toContain('_italic_');
    });

    it('should process strikethrough text', () => {
      const $ = cheerio.load('<p>This is <s>strikethrough</s> text</p>');
      const node = $('p');

      const result = parser['processInlineElements'](node);

      expect(result).toContain('~strikethrough~');
    });

    it('should process code text', () => {
      const $ = cheerio.load('<p>This is <code>code</code> text</p>');
      const node = $('p');

      const result = parser['processInlineElements'](node);

      expect(result).toContain('`code`');
    });

    it('should process links', () => {
      const $ = cheerio.load(
        '<p>This is a <a href="https://example.com">link</a> text</p>',
      );
      const node = $('p');

      const result = parser['processInlineElements'](node);

      expect(result).toContain('<https://example.com|link>');
    });

    it('should process emoji spans', () => {
      const $ = cheerio.load(
        '<p>This is a <span data-type="emoji" data-name="smile">😊</span> emoji</p>',
      );
      const node = $('p');

      const result = parser['processInlineElements'](node);

      expect(result).toContain(':smile:');
    });

    it('should process user mentions', () => {
      const $ = cheerio.load(
        '<p>Hello <span class="mention" data-id="U12345" data-email="<EMAIL>" data-label="User">@User</span></p>',
      );
      const node = $('p');

      const result = parser['processInlineElements'](node);

      expect(result).toContain('<@U12345>');
      expect(parser['usersMentioned']).toContain('U12345');
    });

    it('should handle string input with HTML tags', () => {
      const input = 'This is <strong>bold</strong> and <em>italic</em> text';

      const result = parser['processInlineElements'](input);

      expect(result).toContain('*bold*');
      expect(result).toContain('_italic_');
    });

    it('should handle array input by processing each element', () => {
      // Mock the processInlineElements method to handle the recursive call
      vi.spyOn(parser as any, 'processInlineElements').mockImplementation(
        (input) => {
          if (typeof input === 'string') {
            return input;
          }
          if (Array.isArray(input)) {
            return input
              .map((item) => (item.name === 'p' ? 'processed paragraph' : ''))
              .join('');
          }
          return '';
        },
      );

      const result = parser['processInlineElements']([
        { name: 'p', type: 'tag' },
        { name: 'p', type: 'tag' },
      ]);

      expect(result).toBe('processed paragraphprocessed paragraph');
    });
  });
});
