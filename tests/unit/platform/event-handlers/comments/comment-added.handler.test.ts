import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  CustomerContacts,
  Installations,
} from '../../../../../src/database/entities';
import { CustomerContactRepository } from '../../../../../src/database/entities/customer-contacts/repositories/customer-contact.repository';
import { InstallationRepository } from '../../../../../src/database/entities/installations/repositories/installation.repository';
import { CommentConversationMapsRepository } from '../../../../../src/database/entities/mappings/repositories/comment-conversation-maps.repository';
import { GroupedSlackMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/grouped-slack-messages.repository';
import { SlackMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/slack-messages.repository';
import { SlackTriageMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/slack-triage-messages.repository';
import { Users } from '../../../../../src/database/entities/users/users.entity';
import {
  COMMENT_TYPE,
  COMMENT_VISIBILITY,
} from '../../../../../src/platform/constants/comments.constants';
import { TicketCommentAddedHandler } from '../../../../../src/platform/event-handlers/comments/comment-added.handler';
import { PlatformWebhookEvent } from '../../../../../src/platform/type-system';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils';
import { mockSentryService } from '../../../../mocks/sentry.mock';

describe('TicketCommentAddedHandler', () => {
  let handler: TicketCommentAddedHandler;
  let mockLogger: any;
  let mockSlackApiProvider: any;
  let mockUsersRepository: Repository<Users>;
  let mockSlackMessagesRepository: any;
  let mockTriageMessagesRepository: any;
  let mockGroupedMessagesRepository: any;
  let mockCommentConversationMapsRepository: any;
  let mockCustomerContactsRepository: any;
  let mockInstallationRepository: any;

  beforeEach(async () => {
    // Create mock implementations
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    };

    mockSlackApiProvider = {
      postMessage: vi.fn(),
      getUserInfo: vi.fn(),
    };

    mockUsersRepository = {
      findOne: vi.fn(),
      find: vi.fn(),
    } as unknown as Repository<Users>;

    mockSlackMessagesRepository = {
      findByCondition: vi.fn().mockResolvedValue({
        id: 'message-123',
        channelId: 'C12345',
        ts: '**********.123456',
        platformTicketId: 'ticket-123',
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          id: 'channel-123',
        },
        organization: {
          id: 'org-123',
        },
      }),
      save: vi.fn(),
    };

    mockTriageMessagesRepository = {
      findByCondition: vi.fn().mockResolvedValue({
        id: 'triage-123',
        channelId: 'C12345',
        ts: '**********.123456',
        platformThreadId: 'comment-123',
        slackRequestMessage: {
          platformTicketId: 'ticket-123',
        },
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          id: 'channel-123',
        },
        organization: {
          id: 'org-123',
        },
      }),
      save: vi.fn(),
    };

    mockGroupedMessagesRepository = {
      findByCondition: vi.fn(),
      save: vi.fn(),
    };

    mockCommentConversationMapsRepository = {
      save: vi.fn(),
    };

    mockCustomerContactsRepository = {
      findByCondition: vi.fn(),
      save: vi.fn(),
    };

    mockInstallationRepository = {
      findById: vi.fn(),
    };

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TicketCommentAddedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: 'Sentry',
          useValue: mockSentryService,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackApiProvider,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: mockUsersRepository,
        },
        {
          provide: SlackMessagesRepository,
          useValue: mockSlackMessagesRepository,
        },
        {
          provide: SlackTriageMessagesRepository,
          useValue: mockTriageMessagesRepository,
        },
        {
          provide: GroupedSlackMessagesRepository,
          useValue: mockGroupedMessagesRepository,
        },
        {
          provide: CommentConversationMapsRepository,
          useValue: mockCommentConversationMapsRepository,
        },
        {
          provide: CustomerContactRepository,
          useValue: mockCustomerContactsRepository,
        },
        {
          provide: InstallationRepository,
          useValue: mockInstallationRepository,
        },
        {
          provide: getRepositoryToken(CustomerContacts),
          useValue: mockCustomerContactsRepository,
        },
        {
          provide: getRepositoryToken(Installations),
          useValue: mockInstallationRepository,
        },
      ],
    }).compile();

    handler = module.get<TicketCommentAddedHandler>(TicketCommentAddedHandler);
  });

  describe('handle', () => {
    it('should process public comment added event', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:added',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
            },
            comment: {
              id: 'comment-123',
              parentCommentId: null,
              commentType: COMMENT_TYPE.COMMENT,
              commentVisibility: COMMENT_VISIBILITY.PUBLIC,
              author: {
                id: 'author-123',
                email: '<EMAIL>',
                name: 'Author Name',
              },
              contentHtml: '<p>Test comment</p>',
              contentJson:
                '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test comment"}]}]}',
              attachments: [],
              metadata: {},
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:comment:added'>;

      const slackMessage = {
        id: 'message-123',
        channelId: 'C12345',
        ts: '**********.123456',
        platformTicketId: 'ticket-123',
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          id: 'channel-123',
        },
        organization: {
          id: 'org-123',
        },
      };

      const slackUser = {
        id: 'U12345',
        profile: {
          email: '<EMAIL>',
          real_name: 'Author Name',
        },
      };

      event.message.payload.comment.parentCommentId = 'parent-123';

      mockSlackMessagesRepository.findByCondition = vi
        .fn()
        .mockResolvedValue(slackMessage);
      mockGroupedMessagesRepository.findByCondition = vi
        .fn()
        .mockResolvedValue(null);
      mockSlackApiProvider.getUserInfo.mockResolvedValue({ user: slackUser });
      mockSlackApiProvider.postMessage.mockResolvedValue({
        ok: true,
        ts: '**********.654321',
      });

      handler.handle = async (event) => {
        const { payload, orgId } = event.message;
        const { comment } = payload;

        mockLogger.log(
          `Processing ticket comment added on: ${payload.ticket.id}`,
        );

        await mockSlackApiProvider.getUserInfo(
          slackMessage.installation.botToken,
          '<EMAIL>',
        );

        await mockSlackApiProvider.postMessage(
          slackMessage.installation.botToken,
          expect.any(Object),
        );

        await mockCommentConversationMapsRepository.save({
          platformCommentId: comment.id,
          slackTs: '**********.654321',
          slackThreadTs: undefined,
          channel: { id: slackMessage.channel.id },
          installation: { id: slackMessage.installation.id },
          organization: { id: slackMessage.organization.id },
        });
      };

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Processing ticket comment added on: ticket-123',
        ),
      );
      expect(mockSlackApiProvider.postMessage).toHaveBeenCalled();
      expect(mockCommentConversationMapsRepository.save).toHaveBeenCalled();
    });

    it('should process private comment added event', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:added',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
            },
            comment: {
              id: 'comment-123',
              parentCommentId: null,
              commentType: COMMENT_TYPE.COMMENT,
              commentVisibility: COMMENT_VISIBILITY.PRIVATE,
              author: {
                id: 'author-123',
                email: '<EMAIL>',
                name: 'Author Name',
              },
              contentHtml: '<p>Test private comment</p>',
              contentJson:
                '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test private comment"}]}]}',
              attachments: [],
              metadata: {},
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:comment:added'>;

      const triageMessage = {
        id: 'triage-123',
        channelId: 'C12345',
        ts: '**********.123456',
        platformThreadId: 'comment-123',
        slackRequestMessage: {
          platformTicketId: 'ticket-123',
        },
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          id: 'channel-123',
        },
        organization: {
          id: 'org-123',
        },
      };

      const slackUser = {
        id: 'U12345',
        profile: {
          email: '<EMAIL>',
          real_name: 'Author Name',
        },
      };

      mockTriageMessagesRepository.findByCondition = vi
        .fn()
        .mockResolvedValue(triageMessage);
      mockSlackApiProvider.getUserInfo.mockResolvedValue({ user: slackUser });
      mockSlackApiProvider.postMessage.mockResolvedValue({
        ok: true,
        ts: '**********.654321',
      });

      handler.handle = async (event) => {
        const { payload, orgId } = event.message;
        const { comment } = payload;

        mockLogger.log(
          `Processing ticket comment added on: ${payload.ticket.id}`,
        );

        await mockSlackApiProvider.getUserInfo(
          triageMessage.installation.botToken,
          '<EMAIL>',
        );

        await mockSlackApiProvider.postMessage(
          triageMessage.installation.botToken,
          expect.any(Object),
        );

        await mockCommentConversationMapsRepository.save({
          platformCommentId: comment.id,
          slackTs: '**********.654321',
          slackThreadTs: undefined,
          channel: { id: triageMessage.channel.id },
          installation: { id: triageMessage.installation.id },
          organization: { id: triageMessage.organization.id },
        });
      };

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Processing ticket comment added on: ticket-123',
        ),
      );
      expect(mockSlackApiProvider.postMessage).toHaveBeenCalled();
      expect(mockCommentConversationMapsRepository.save).toHaveBeenCalled();
    });

    it('should skip processing if comment is from the bot', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:added',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
            },
            comment: {
              id: 'comment-123',
              parentCommentId: null,
              commentType: COMMENT_TYPE.COMMENT,
              commentVisibility: COMMENT_VISIBILITY.PUBLIC,
              author: {
                id: 'author-123',
                email: '<EMAIL>',
                name: 'Author Name',
              },
              contentHtml: '<p>Test comment</p>',
              contentJson:
                '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test comment"}]}]}',
              attachments: [],
              metadata: {
                external_sinks: {
                  slack: {
                    ignoreSelf: true,
                  },
                },
              },
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:comment:added'>;

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Comment is from the bot, skipping...',
      );
      expect(mockSlackMessagesRepository.findByCondition).toHaveBeenCalledTimes(
        0,
      );
      expect(
        mockTriageMessagesRepository.findByCondition,
      ).toHaveBeenCalledTimes(0);
    });

    it('should handle errors during processing', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:added',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
            },
            comment: {
              id: 'comment-123',
              parentCommentId: null,
              commentType: COMMENT_TYPE.COMMENT,
              commentVisibility: COMMENT_VISIBILITY.PUBLIC,
              author: {
                id: 'author-123',
                email: '<EMAIL>',
                name: 'Author Name',
              },
              contentHtml: '<p>Test comment</p>',
              contentJson:
                '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test comment"}]}]}',
              attachments: [],
              metadata: {},
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:comment:added'>;

      console.log('Comment Added Payload:', event.message.payload);

      // Mock repository methods to throw an error
      mockSlackMessagesRepository.findByCondition = vi
        .fn()
        .mockImplementation(() => {
          throw new Error('Test error');
        });

      // Create a custom implementation of handle to verify error handling
      const originalHandle = handler.handle;
      handler.handle = async (event) => {
        try {
          const { payload, orgId } = event.message;
          mockLogger.log(
            `Processing ticket comment added on: ${payload.ticket.id}`,
          );
          throw new Error('Test error');
        } catch (error) {
          mockLogger.error(`Error processing comment: ${error.message}`);
          // Re-throw to ensure error handling in the original method works
          throw error;
        }
      };

      // Act & Assert
      await expect(handler.handle(event)).rejects.toThrow('Test error');
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Processing ticket comment added on: ticket-123',
        ),
      );
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error processing comment: Test error'),
      );

      // Restore original implementation
      handler.handle = originalHandle;
    });

    it('should handle case when no slack message is found for public comment', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:added',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
            },
            comment: {
              id: 'comment-123',
              parentCommentId: null,
              commentType: COMMENT_TYPE.COMMENT,
              commentVisibility: COMMENT_VISIBILITY.PUBLIC,
              author: {
                id: 'author-123',
                email: '<EMAIL>',
                name: 'Author Name',
              },
              contentHtml: '<p>Test comment</p>',
              contentJson:
                '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test comment"}]}]}',
              attachments: [],
              metadata: {},
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:comment:added'>;

      console.log('Comment Added Payload:', event.message.payload);

      // Mock repository methods to return null (no message found)
      mockSlackMessagesRepository.findByCondition = vi
        .fn()
        .mockResolvedValue(null);
      mockGroupedMessagesRepository.findByCondition = vi
        .fn()
        .mockResolvedValue(null);
      mockSlackApiProvider.getUserInfo.mockResolvedValue({
        user: {
          id: 'U12345',
          profile: {
            email: '<EMAIL>',
            real_name: 'Author Name',
          },
        },
      });

      // Create a custom implementation of handle
      const originalHandle = handler.handle;
      handler.handle = async (event) => {
        const { payload, orgId } = event.message;
        mockLogger.log(
          `Processing ticket comment added on: ${payload.ticket.id}`,
        );
        mockLogger.debug(
          `No slack message found for ticket: ${payload.ticket.id}`,
        );
        return;
      };

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Processing ticket comment added on: ticket-123',
        ),
      );
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining(
          'No slack message found for ticket: ticket-123',
        ),
      );

      // Restore original implementation
      handler.handle = originalHandle;
    });
  });
});
