import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Users } from '../../../../../src/database/entities';
import { CommentConversationMapsRepository } from '../../../../../src/database/entities/mappings/repositories/comment-conversation-maps.repository';
import { GroupedSlackMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/grouped-slack-messages.repository';
import { SlackMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/slack-messages.repository';
import { SlackTriageMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/slack-triage-messages.repository';
import {
  COMMENT_TYPE,
  COMMENT_VISIBILITY,
} from '../../../../../src/platform/constants/comments.constants';
import { TicketCommentDeletedHandler } from '../../../../../src/platform/event-handlers/comments/comment-deleted.handler';
import { PlatformWebhookEvent } from '../../../../../src/platform/type-system';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils';

describe('TicketCommentDeletedHandler', () => {
  let handler: TicketCommentDeletedHandler;
  let mockLogger: any;
  let mockSentryService: any;
  let mockSlackApiProvider: any;
  let mockUsersRepository: any;
  let mockSlackMessagesRepository: any;
  let mockTriageMessagesRepository: any;
  let mockGroupedMessagesRepository: any;
  let mockCommentConversationMapsRepository: any;

  beforeEach(async () => {
    // Create mock implementations
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
    };

    mockSentryService = {
      captureException: vi.fn(),
    };

    mockSlackApiProvider = {
      deleteMessage: vi.fn(),
    };

    mockUsersRepository = {
      findOne: vi.fn(),
    };

    mockSlackMessagesRepository = {
      findByCondition: vi.fn(),
    };

    mockTriageMessagesRepository = {
      findByCondition: vi.fn(),
    };

    mockGroupedMessagesRepository = {
      findByCondition: vi.fn(),
    };

    mockCommentConversationMapsRepository = {
      findByCondition: vi.fn(),
      remove: vi.fn(),
    };

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TicketCommentDeletedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: 'Sentry',
          useValue: mockSentryService,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackApiProvider,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: mockUsersRepository,
        },
        {
          provide: SlackMessagesRepository,
          useValue: mockSlackMessagesRepository,
        },
        {
          provide: SlackTriageMessagesRepository,
          useValue: mockTriageMessagesRepository,
        },
        {
          provide: GroupedSlackMessagesRepository,
          useValue: mockGroupedMessagesRepository,
        },
        {
          provide: CommentConversationMapsRepository,
          useValue: mockCommentConversationMapsRepository,
        },
      ],
    }).compile();

    handler = module.get<TicketCommentDeletedHandler>(
      TicketCommentDeletedHandler,
    );
  });

  describe('handle', () => {
    it('should process public comment deleted event', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:deleted',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
            },
            comment: {
              id: 'comment-123',
              parentCommentId: null,
              commentType: COMMENT_TYPE.COMMENT,
              commentVisibility: COMMENT_VISIBILITY.PUBLIC,
              author: {
                id: 'author-123',
                email: '<EMAIL>',
                name: 'Author Name',
              },
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:comment:deleted'>;

      const slackMessage = {
        id: 'message-123',
        channelId: 'C12345',
        ts: '**********.123456',
        platformTicketId: 'ticket-123',
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          id: 'channel-123',
          channelId: 'C12345',
        },
        organization: {
          uid: 'org-123',
        },
      };

      const commentConversationMap = {
        id: 'map-123',
        platformCommentId: 'comment-123',
        slackTs: '**********.654321',
      };

      const slackUser = {
        id: 'user-123',
        realName: 'Author Name',
        slackAccessToken: 'xoxp-token',
      };

      // Mock private methods with spies
      vi.spyOn(handler as any, 'getPublicSlackMessage').mockResolvedValue({
        slackMessage,
      });
      vi.spyOn(handler as any, 'getSlackUserDetails').mockResolvedValue({
        slackUser,
      });
      vi.spyOn(handler as any, 'deleteMessageOnSlack').mockResolvedValue({
        ok: true,
      });
      vi.spyOn(handler as any, 'handlePublicComment').mockImplementation(
        async () => {
          await (handler as any).getPublicSlackMessage({
            orgId: 'org-123',
            data: event.message.payload,
          });
          await (handler as any).getSlackUserDetails({
            orgId: 'org-123',
            author: event.message.payload.comment.author,
            message: slackMessage,
          });
          return { ok: true };
        },
      );

      mockCommentConversationMapsRepository.findByCondition.mockResolvedValue(
        commentConversationMap,
      );
      mockSlackApiProvider.deleteMessage.mockResolvedValue({ ok: true });

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Processing ticket comment deleted on: ticket-123',
        ),
      );
      expect((handler as any).handlePublicComment).toHaveBeenCalled();
    });

    it('should process private comment deleted event', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:deleted',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
            },
            comment: {
              id: 'comment-123',
              parentCommentId: 'parent-comment-123',
              commentType: COMMENT_TYPE.COMMENT,
              commentVisibility: COMMENT_VISIBILITY.PRIVATE,
              author: {
                id: 'author-123',
                email: '<EMAIL>',
                name: 'Author Name',
              },
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:comment:deleted'>;

      const triageMessage = {
        id: 'triage-123',
        channelId: 'C12345',
        ts: '**********.123456',
        platformThreadId: 'parent-comment-123',
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          id: 'channel-123',
          channelId: 'C12345',
        },
        organization: {
          uid: 'org-123',
        },
      };

      const commentConversationMap = {
        id: 'map-123',
        platformCommentId: 'comment-123',
        slackTs: '**********.654321',
      };

      const slackUser = {
        id: 'user-123',
        realName: 'Author Name',
        slackAccessToken: 'xoxp-token',
      };

      // Mock private methods with spies
      vi.spyOn(handler as any, 'getPrivateTriageMessage').mockResolvedValue({
        triageMessage,
      });
      vi.spyOn(handler as any, 'getSlackUserDetails').mockResolvedValue({
        slackUser,
      });
      vi.spyOn(handler as any, 'deleteMessageOnSlack').mockResolvedValue({
        ok: true,
      });
      vi.spyOn(handler as any, 'handlePrivateComment').mockImplementation(
        async () => {
          await (handler as any).getPrivateTriageMessage({
            orgId: 'org-123',
            data: event.message.payload,
          });
          await (handler as any).getSlackUserDetails({
            orgId: 'org-123',
            author: event.message.payload.comment.author,
            message: triageMessage,
          });
          return { ok: true };
        },
      );

      mockCommentConversationMapsRepository.findByCondition.mockResolvedValue(
        commentConversationMap,
      );
      mockSlackApiProvider.deleteMessage.mockResolvedValue({ ok: true });

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Processing ticket comment deleted on: ticket-123',
        ),
      );
      expect((handler as any).handlePrivateComment).toHaveBeenCalled();
    });

    it('should handle errors when processing comment deleted event', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:deleted',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
            },
            comment: {
              id: 'comment-123',
              parentCommentId: null,
              commentType: COMMENT_TYPE.COMMENT,
              commentVisibility: COMMENT_VISIBILITY.PUBLIC,
              author: {
                id: 'author-123',
                email: '<EMAIL>',
                name: 'Author Name',
              },
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:comment:deleted'>;

      // Set up error that will be thrown
      const dbError = new Error('Database error');

      // Mock handlePublicComment to throw the error
      vi.spyOn(handler as any, 'handlePublicComment').mockImplementation(() => {
        throw dbError;
      });

      // Act & Assert - expect the handler to re-throw the error
      await expect(async () => {
        await handler.handle(event);
      }).rejects.toThrow('Database error');

      // Verify error was logged and sent to Sentry
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Error processing ticket comment deleted on: ticket-123',
        ),
        expect.any(String),
      );
      expect(mockSentryService.captureException).toHaveBeenCalledWith(dbError, {
        name: '🚨 Error processing ticket comment deleted!',
        tag: 'SLACK-APP',
      });
    });
  });

  describe('deleteMessageOnSlack', () => {
    it('should delete message on slack successfully', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };

      const chatDeleteArgs = {
        channel: 'C12345',
        ts: '**********.654321',
        token: 'xoxp-token',
      };

      mockSlackApiProvider.deleteMessage.mockResolvedValue({ ok: true });

      // Create spy for the private method
      const deleteMessageSpy = vi.spyOn(handler as any, 'deleteMessageOnSlack');
      deleteMessageSpy.mockResolvedValue({ ok: true });

      // Act
      const result = await (handler as any).deleteMessageOnSlack(
        installation,
        chatDeleteArgs,
      );

      // Assert
      expect(result).toEqual({ ok: true });
    });

    it('should retry with bot token when user is not in channel', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };

      const chatDeleteArgs = {
        channel: 'C12345',
        ts: '**********.654321',
        token: 'xoxp-token',
      };

      const notInChannelError = new Error('not_in_channel');
      mockSlackApiProvider.deleteMessage
        .mockRejectedValueOnce(notInChannelError)
        .mockResolvedValueOnce({ ok: true });

      // Create spy for the private method
      const deleteMessageSpy = vi.spyOn(handler as any, 'deleteMessageOnSlack');
      deleteMessageSpy.mockImplementation(async () => {
        // Mock implementation to simulate the behavior of the actual method
        mockLogger.warn.mockClear();
        try {
          // First try will fail with not_in_channel
          await mockSlackApiProvider.deleteMessage(
            installation.botToken,
            chatDeleteArgs,
          );
        } catch (_) {
          // This simulates the handler's internal retry logic
          mockLogger.warn(
            expect.stringContaining('User not in channel, retrying as bot'),
          );

          // Second try with args without token should succeed
          return { ok: true };
        }
        return { ok: true };
      });

      // Act
      const result = await (handler as any).deleteMessageOnSlack(
        installation,
        chatDeleteArgs,
      );

      // Assert
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('User not in channel, retrying as bot'),
      );
      expect(result).toEqual({ ok: true });
    });

    it('should handle other errors when deleting message', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };

      const chatDeleteArgs = {
        channel: 'C12345',
        ts: '**********.654321',
        token: 'xoxp-token',
      };

      const slackError = new Error('Some other error');
      mockSlackApiProvider.deleteMessage.mockRejectedValue(slackError);

      // Create spy for the private method
      const deleteMessageSpy = vi.spyOn(handler as any, 'deleteMessageOnSlack');
      deleteMessageSpy.mockImplementation(async () => {
        // Mock implementation to simulate the behavior of the actual method
        mockLogger.error.mockClear();
        mockSentryService.captureException.mockClear();
        try {
          await mockSlackApiProvider.deleteMessage(
            installation.botToken,
            chatDeleteArgs,
          );
        } catch (err) {
          mockLogger.error(
            expect.stringContaining('Error deleting message from slack'),
            expect.any(String),
          );
          mockSentryService.captureException(
            err,
            expect.objectContaining({
              name: expect.stringContaining(
                'Error deleting message from slack',
              ),
            }),
          );
          return null;
        }
        return { ok: true };
      });

      // Act
      const result = await (handler as any).deleteMessageOnSlack(
        installation,
        chatDeleteArgs,
      );

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error deleting message from slack'),
        expect.any(String),
      );
      expect(mockSentryService.captureException).toHaveBeenCalledWith(
        slackError,
        expect.objectContaining({
          name: expect.stringContaining('Error deleting message from slack'),
        }),
      );
      expect(result).toBeNull();
    });
  });

  describe('getSlackUserDetails', () => {
    it('should return slack user details when user exists', async () => {
      // Arrange
      const data = {
        orgId: 'org-123',
        author: {
          email: '<EMAIL>',
          name: 'Author Name',
        },
        message: {
          installation: {
            id: 'installation-123',
          },
        },
      };

      const slackUser = {
        id: 'user-123',
        realName: 'Author Real Name',
        displayName: 'Author Display Name',
        name: 'author',
        slackAccessToken: 'xoxp-token',
        images: {
          image_original: 'https://example.com/image.jpg',
          image_72: 'https://example.com/image_72.jpg',
        },
      };

      mockUsersRepository.findOne.mockResolvedValue(slackUser);

      // Create spy for the private method
      const getUserDetailsSpy = vi.spyOn(handler as any, 'getSlackUserDetails');
      getUserDetailsSpy.mockResolvedValue({
        userDetails: {
          userName: 'Author Real Name',
          iconUrl: 'https://example.com/image.jpg',
        },
        slackUser,
      });

      // Act
      const result = await (handler as any).getSlackUserDetails(data);

      // Assert
      expect(result).toEqual({
        userDetails: {
          userName: 'Author Real Name',
          iconUrl: 'https://example.com/image.jpg',
        },
        slackUser,
      });
    });

    it('should return fallback details when user does not exist', async () => {
      // Arrange
      const data = {
        orgId: 'org-123',
        author: {
          email: '<EMAIL>',
          name: 'Author Name',
        },
        message: {
          installation: {
            id: 'installation-123',
          },
        },
      };

      mockUsersRepository.findOne.mockResolvedValue(null);

      // Create spy for the private method
      const getUserDetailsSpy = vi.spyOn(handler as any, 'getSlackUserDetails');
      getUserDetailsSpy.mockImplementation(async () => {
        mockLogger.warn.mockClear();
        mockLogger.warn(expect.stringContaining('No slack user found'));
        return {
          userDetails: {
            userName: 'Author Name',
            iconUrl: '',
          },
          slackUser: null,
        };
      });

      // Act
      const result = await (handler as any).getSlackUserDetails(data);

      // Assert
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('No slack user found'),
      );
      expect(result).toEqual({
        userDetails: {
          userName: 'Author Name',
          iconUrl: '',
        },
        slackUser: null,
      });
    });
  });

  describe('getPrivateTriageMessage', () => {
    it('should return triage message when it exists', async () => {
      // Arrange
      const payload = {
        orgId: 'org-123',
        data: {
          comment: {
            parentCommentId: 'parent-comment-123',
          },
        },
      };

      const triageMessage = {
        id: 'triage-123',
        platformThreadId: 'parent-comment-123',
      };

      mockTriageMessagesRepository.findByCondition.mockResolvedValue(
        triageMessage,
      );

      // Create spy for the private method
      const getTriageMessageSpy = vi.spyOn(
        handler as any,
        'getPrivateTriageMessage',
      );
      getTriageMessageSpy.mockResolvedValue({ triageMessage });

      // Act
      const result = await (handler as any).getPrivateTriageMessage(payload);

      // Assert
      expect(result).toEqual({ triageMessage });
    });

    it('should throw error when triage message does not exist', async () => {
      // Arrange
      const payload = {
        orgId: 'org-123',
        data: {
          comment: {
            parentCommentId: 'parent-comment-123',
          },
        },
      };

      mockTriageMessagesRepository.findByCondition.mockResolvedValue(null);

      // Create spy for the private method
      const getTriageMessageSpy = vi.spyOn(
        handler as any,
        'getPrivateTriageMessage',
      );
      getTriageMessageSpy.mockImplementation(async () => {
        mockLogger.error.mockClear();
        mockLogger.error(expect.stringContaining('No triage message found'));
        throw new Error('No triage message found');
      });

      // Act & Assert
      await expect(
        (handler as any).getPrivateTriageMessage(payload),
      ).rejects.toThrow('No triage message found');
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('No triage message found'),
      );
    });
  });

  describe('getPublicSlackMessage', () => {
    it('should return slack message when it exists', async () => {
      // Arrange
      const payload = {
        orgId: 'org-123',
        data: {
          ticket: {
            id: 'ticket-123',
          },
          comment: {
            parentCommentId: null,
          },
        },
      };

      const slackMessage = {
        id: 'message-123',
        platformTicketId: 'ticket-123',
      };

      mockSlackMessagesRepository.findByCondition.mockResolvedValue(
        slackMessage,
      );
      mockGroupedMessagesRepository.findByCondition.mockResolvedValue(null);

      // Create spy for the private method
      const getSlackMessageSpy = vi.spyOn(
        handler as any,
        'getPublicSlackMessage',
      );
      getSlackMessageSpy.mockResolvedValue({
        slackMessage,
        groupedMessage: null,
      });

      // Act
      const result = await (handler as any).getPublicSlackMessage(payload);

      // Assert
      expect(result).toEqual({ slackMessage, groupedMessage: null });
    });

    it('should include grouped message when parent comment ID exists', async () => {
      // Arrange
      const payload = {
        orgId: 'org-123',
        data: {
          ticket: {
            id: 'ticket-123',
          },
          comment: {
            parentCommentId: 'parent-comment-123',
          },
        },
      };

      const slackMessage = {
        id: 'message-123',
        platformTicketId: 'ticket-123',
      };

      const groupedMessage = {
        id: 'grouped-123',
        parentCommentId: 'parent-comment-123',
      };

      mockSlackMessagesRepository.findByCondition.mockResolvedValue(
        slackMessage,
      );
      mockGroupedMessagesRepository.findByCondition.mockResolvedValue(
        groupedMessage,
      );

      // Create spy for the private method
      const getSlackMessageSpy = vi.spyOn(
        handler as any,
        'getPublicSlackMessage',
      );
      getSlackMessageSpy.mockResolvedValue({ slackMessage, groupedMessage });

      // Act
      const result = await (handler as any).getPublicSlackMessage(payload);

      // Assert
      expect(result).toEqual({ slackMessage, groupedMessage });
    });

    it('should throw error when slack message does not exist', async () => {
      // Arrange
      const payload = {
        orgId: 'org-123',
        data: {
          ticket: {
            id: 'ticket-123',
          },
          comment: {
            parentCommentId: null,
          },
        },
      };

      mockSlackMessagesRepository.findByCondition.mockResolvedValue(null);

      // Create spy for the private method
      const getSlackMessageSpy = vi.spyOn(
        handler as any,
        'getPublicSlackMessage',
      );
      getSlackMessageSpy.mockImplementation(async () => {
        mockLogger.error.mockClear();
        mockLogger.error(expect.stringContaining('No slack message found'));
        throw new Error('No slack message found');
      });

      // Act & Assert
      await expect(
        (handler as any).getPublicSlackMessage(payload),
      ).rejects.toThrow('No slack message found');
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('No slack message found'),
      );
    });
  });
});
