import { beforeEach, describe, expect, it, vi } from 'vitest';
import { TicketCommentAddedHandler } from '../../../../../src/platform/event-handlers/comments/comment-added.handler';
import { SLACK_SENTRY_TAG } from '../../../../../src/utils';

// Mock the HtmlToPlainText class
vi.mock('../../../../../src/utils/parsers', () => {
  return {
    HtmlToPlainText: function () {
      return {
        convert: async function () {
          return 'Plain text content';
        },
      };
    },
    convertTiptapJSONToSlackBlocks: async function () {
      return [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: 'Mocked slack blocks',
          },
        },
      ];
    },
  };
});

vi.mock('../../../../../src/utils/common', () => ({
  appendAttachmentsToSlackBlocks: vi
    .fn()
    .mockImplementation((blocks) => blocks),
}));

describe('TicketCommentAddedHandler - Private Methods', () => {
  let handler: TicketCommentAddedHandler;
  let mockLogger: any;
  let mockSentryService: any;
  let mockSlackApiProvider: any;
  let mockUsersRepository: any;
  let mockSlackMessagesRepository: any;
  let mockTriageMessagesRepository: any;
  let mockGroupedMessagesRepository: any;
  let mockCommentConversationMapsRepository: any;
  let mockCustomerContactsRepository: any;
  let mockInstallationsRepository: any;

  beforeEach(() => {
    // Create mocks
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    mockSentryService = {
      captureException: vi.fn(),
    };

    mockSlackApiProvider = {
      sendMessage: vi.fn().mockResolvedValue({
        ok: true,
        message: { ts: '**********.654321', thread_ts: '**********.123456' },
      }),
    };

    mockUsersRepository = {
      findOne: vi.fn(),
      find: vi.fn(),
    };

    mockSlackMessagesRepository = {
      findByCondition: vi.fn(),
      save: vi.fn(),
      update: vi.fn(),
    };

    mockTriageMessagesRepository = {
      findByCondition: vi.fn(),
      save: vi.fn(),
    };

    mockGroupedMessagesRepository = {
      findByCondition: vi.fn(),
      save: vi.fn(),
    };

    mockCommentConversationMapsRepository = {
      save: vi.fn(),
    };

    mockCustomerContactsRepository = {
      findOne: vi.fn(),
      find: vi.fn(),
    };

    mockInstallationsRepository = {
      findOne: vi.fn(),
      find: vi.fn(),
    };

    // Create handler instance directly with mocks
    handler = new TicketCommentAddedHandler(
      mockLogger,
      mockSentryService,
      mockSlackApiProvider,
      mockSlackMessagesRepository,
      mockTriageMessagesRepository,
      mockGroupedMessagesRepository,
      mockCommentConversationMapsRepository,
      mockUsersRepository,
      mockCustomerContactsRepository,
      mockInstallationsRepository,
    );
  });

  describe('getSlackUserDetails', () => {
    it('should return slack user details when user exists', async () => {
      // Arrange
      const data = {
        orgId: 'org-123',
        author: {
          email: '<EMAIL>',
          name: 'Author Name',
        },
        message: {
          installation: {
            id: 'installation-123',
          },
          id: 'message-123',
        },
      };

      const slackUser = {
        id: 'user-123',
        realName: 'Author Real Name',
        displayName: 'Author Display Name',
        name: 'author',
        slackAccessToken: 'xoxp-token',
        images: {
          image_original: 'https://example.com/image.jpg',
          image_72: 'https://example.com/image_72.jpg',
        },
      };

      mockUsersRepository.findOne.mockResolvedValue(slackUser);

      // Act
      const result = await (handler as any).getSlackUserDetails(data);

      // Assert
      expect(mockUsersRepository.findOne).toHaveBeenCalledWith({
        where: {
          organization: { uid: 'org-123' },
          slackProfileEmail: '<EMAIL>',
          installation: { id: 'installation-123' },
        },
      });
      expect(result).toEqual({
        userDetails: {
          userName: 'Author Real Name',
          iconUrl: 'https://example.com/image.jpg',
        },
        slackUser,
      });
    });

    it('should return fallback details when user does not exist', async () => {
      // Arrange
      const data = {
        orgId: 'org-123',
        author: {
          email: '<EMAIL>',
          name: 'Author Name',
        },
        message: {
          installation: {
            id: 'installation-123',
          },
          id: 'message-123',
        },
      };

      mockUsersRepository.findOne.mockResolvedValue(null);

      // Act
      const result = await (handler as any).getSlackUserDetails(data);

      // Assert
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining(
          `No slack user found for comment ${data.message.id} by ${data.author.email}`,
        ),
      );
      expect(result).toEqual({
        userDetails: {
          userName: 'Author Name',
          iconUrl: '',
        },
        slackUser: null,
      });
    });

    it('should handle errors when getting slack user details', async () => {
      // Arrange
      const data = {
        orgId: 'org-123',
        author: {
          email: '<EMAIL>',
          name: 'Author Name',
        },
        message: {
          installation: {
            id: 'installation-123',
          },
          id: 'message-123',
        },
      };

      const error = new Error('Database error');
      mockUsersRepository.findOne.mockRejectedValue(error);

      // Act & Assert
      await expect(async () => {
        await (handler as any).getSlackUserDetails(data);
      }).rejects.toThrow('Database error');

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Error getting slack user details: Database error',
        ),
      );
      expect(mockSentryService.captureException).toHaveBeenCalledWith(error, {
        name: '🚨 Error getting slack user details!',
        tag: SLACK_SENTRY_TAG,
      });
    });
  });

  describe('sendMessageToSlack', () => {
    it('should send message to slack successfully', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };

      const chatPostMessageArgs = {
        text: 'Test message',
        channel: 'C12345',
        thread_ts: '**********.123456',
        username: 'Author Name',
        icon_url: 'https://example.com/image.jpg',
        blocks: [],
        token: 'xoxp-token',
        unfurl_links: true,
        unfurl_media: true,
        metadata: {
          event_type: 'message',
          event_payload: {
            ignoreSelf: true,
          },
        },
      };

      mockSlackApiProvider.sendMessage.mockResolvedValue({
        ok: true,
        message: { ts: '**********.654321', thread_ts: '**********.123456' },
      });

      // Act
      const result = await (handler as any).sendMessageToSlack(
        installation,
        chatPostMessageArgs,
      );

      // Assert
      expect(mockSlackApiProvider.sendMessage).toHaveBeenCalledWith(
        installation.botToken,
        chatPostMessageArgs,
      );
      expect(result).toEqual({
        ok: true,
        message: { ts: '**********.654321', thread_ts: '**********.123456' },
      });
    });

    it('should retry with bot token when user is not in channel', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };

      const chatPostMessageArgs = {
        text: 'Test message',
        channel: 'C12345',
        thread_ts: '**********.123456',
        username: 'Author Name',
        icon_url: 'https://example.com/image.jpg',
        blocks: [],
        token: 'xoxp-token',
        unfurl_links: true,
        unfurl_media: true,
        metadata: {
          event_type: 'message',
          event_payload: {
            ignoreSelf: true,
          },
        },
      };

      const notInChannelError = new Error('not_in_channel');
      mockSlackApiProvider.sendMessage
        .mockRejectedValueOnce(notInChannelError)
        .mockResolvedValueOnce({
          ok: true,
          message: { ts: '**********.654321', thread_ts: '**********.123456' },
        });

      // Act
      const result = await (handler as any).sendMessageToSlack(
        installation,
        chatPostMessageArgs,
      );

      // Assert
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('User not in channel, retrying as bot'),
      );
      expect(mockSlackApiProvider.sendMessage).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        ok: true,
        message: { ts: '**********.654321', thread_ts: '**********.123456' },
      });
    });

    it('should handle other errors when sending message', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };

      const chatPostMessageArgs = {
        text: 'Test message',
        channel: 'C12345',
        thread_ts: '**********.123456',
        username: 'Author Name',
        icon_url: 'https://example.com/image.jpg',
        blocks: [],
        token: 'xoxp-token',
        unfurl_links: true,
        unfurl_media: true,
        metadata: {
          event_type: 'message',
          event_payload: {
            ignoreSelf: true,
          },
        },
      };

      const slackError = new Error('Some other error');
      mockSlackApiProvider.sendMessage.mockRejectedValue(slackError);

      // Act & Assert
      await expect(async () => {
        await (handler as any).sendMessageToSlack(
          installation,
          chatPostMessageArgs,
        );
      }).rejects.toThrow('Some other error');

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Error posting message to slack: Some other error',
        ),
      );
      expect(mockSentryService.captureException).toHaveBeenCalledWith(
        slackError,
        {
          name: '🚨 Error posting message to slack!',
          tag: SLACK_SENTRY_TAG,
        },
      );
    });
  });

  describe('handlePublicComment', () => {
    it('should handle public comment successfully', async () => {
      // Arrange
      const payload = {
        orgId: 'org-123',
        data: {
          ticket: {
            id: 'ticket-123',
            title: 'Test Ticket',
          },
          comment: {
            id: 'comment-123',
            parentCommentId: 'parent-123',
            author: {
              id: 'author-123',
              email: '<EMAIL>',
              name: 'Author Name',
            },
            contentHtml: '<p>Test comment</p>',
            contentJson:
              '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test comment"}]}]}',
            attachments: [],
          },
        },
      };

      const slackMessage = {
        id: 'message-123',
        channelId: 'C12345',
        slackMessageTs: '**********.123456',
        slackMessageThreadTs: '**********.123456',
        platformTicketId: 'ticket-123',
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          id: 'channel-123',
          channelId: 'C12345',
        },
        organization: {
          id: 'org-123',
          uid: 'org-123',
        },
      };

      const groupedMessage = {
        id: 'grouped-123',
        slackMessageTs: '**********.987654',
        parentCommentId: 'parent-123',
      };

      const slackUser = {
        id: 'user-123',
        realName: 'Author Real Name',
        displayName: 'Author Display Name',
        name: 'author',
        slackAccessToken: 'xoxp-token',
        images: {
          image_original: 'https://example.com/image.jpg',
          image_72: 'https://example.com/image_72.jpg',
        },
      };

      // Mock repository methods
      mockSlackMessagesRepository.findByCondition.mockResolvedValue(
        slackMessage,
      );
      mockGroupedMessagesRepository.findByCondition.mockResolvedValue(
        groupedMessage,
      );
      mockUsersRepository.findOne.mockResolvedValue(slackUser);
      mockSlackMessagesRepository.findByCondition
        .mockResolvedValueOnce(slackMessage)
        .mockResolvedValueOnce(null); // For the second call checking existing record

      // Mock sendMessageToSlack
      vi.spyOn(handler as any, 'sendMessageToSlack').mockResolvedValue({
        ok: true,
        message: { ts: '**********.654321', thread_ts: '**********.123456' },
      });

      // Mock getSlackUserDetails
      vi.spyOn(handler as any, 'getSlackUserDetails').mockResolvedValue({
        userDetails: {
          userName: 'Author Real Name',
          iconUrl: 'https://example.com/image.jpg',
        },
        slackUser,
      });

      // Act
      const result = await (handler as any).handlePublicComment(payload);

      // Assert
      expect(mockSlackMessagesRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          platformTicketId: 'ticket-123',
          organization: { uid: 'org-123' },
        },
        relations: ['installation', 'channel', 'organization'],
      });
      expect(
        mockGroupedMessagesRepository.findByCondition,
      ).toHaveBeenCalledWith({
        where: {
          parentCommentId: 'parent-123',
          organization: { uid: 'org-123' },
        },
      });
      expect((handler as any).getSlackUserDetails).toHaveBeenCalled();
      expect((handler as any).sendMessageToSlack).toHaveBeenCalled();
      expect(mockCommentConversationMapsRepository.save).toHaveBeenCalled();
      expect(mockSlackMessagesRepository.save).toHaveBeenCalled();
      expect(result).toEqual({
        ok: true,
        message: { ts: '**********.654321', thread_ts: '**********.123456' },
      });
    });

    it('should handle case when no slack message is found', async () => {
      // Arrange
      const payload = {
        orgId: 'org-123',
        data: {
          ticket: {
            id: 'ticket-123',
            title: 'Test Ticket',
          },
          comment: {
            id: 'comment-123',
            parentCommentId: 'parent-123',
            author: {
              id: 'author-123',
              email: '<EMAIL>',
              name: 'Author Name',
            },
            contentHtml: '<p>Test comment</p>',
            contentJson:
              '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test comment"}]}]}',
            attachments: [],
          },
        },
      };

      // Mock repository methods
      mockSlackMessagesRepository.findByCondition.mockResolvedValue(null);

      // Act
      const result = await (handler as any).handlePublicComment(payload);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          `No slack message found for ticket ${payload.data.ticket.id}`,
        ),
      );
      expect(result).toBeUndefined();
    });

    it('should handle case when slack message exists but no parent comment ID', async () => {
      // Arrange
      const payload = {
        orgId: 'org-123',
        data: {
          ticket: {
            id: 'ticket-123',
            title: 'Test Ticket',
          },
          comment: {
            id: 'comment-123',
            parentCommentId: null, // No parent comment ID
            author: {
              id: 'author-123',
              email: '<EMAIL>',
              name: 'Author Name',
            },
            contentHtml: '<p>Test comment</p>',
            contentJson:
              '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test comment"}]}]}',
            attachments: [],
          },
        },
      };

      const slackMessage = {
        id: 'message-123',
        channelId: 'C12345',
        slackMessageTs: '**********.123456',
        slackMessageThreadTs: '**********.123456',
        platformTicketId: 'ticket-123',
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          id: 'channel-123',
          channelId: 'C12345',
        },
        organization: {
          id: 'org-123',
          uid: 'org-123',
        },
      };

      // Mock repository methods
      mockSlackMessagesRepository.findByCondition.mockResolvedValue(
        slackMessage,
      );

      // Act
      const result = await (handler as any).handlePublicComment(payload);

      // Assert
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining(
          `Ignore message for ticket ${payload.data.ticket.id} with parent comment id as this comment does not belong to any possible slack thread!`,
        ),
      );
      expect(result).toBeUndefined();
    });

    it('should handle error during processing', async () => {
      // Arrange
      const payload = {
        orgId: 'org-123',
        data: {
          ticket: {
            id: 'ticket-123',
            title: 'Test Ticket',
          },
          comment: {
            id: 'comment-123',
            parentCommentId: 'parent-123',
            author: {
              id: 'author-123',
              email: '<EMAIL>',
              name: 'Author Name',
            },
            contentHtml: '<p>Test comment</p>',
            contentJson:
              '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test comment"}]}]}',
            attachments: [],
          },
        },
      };

      const error = new Error('Database error');
      mockSlackMessagesRepository.findByCondition.mockRejectedValue(error);

      // Act & Assert
      await expect(async () => {
        await (handler as any).handlePublicComment(payload);
      }).rejects.toThrow('Database error');

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          `Error handling public comment on ticket ${payload.data.ticket.id}: Database error`,
        ),
      );
      expect(mockSentryService.captureException).toHaveBeenCalledWith(error, {
        name: '🚨 Error handling public comment!',
        tag: SLACK_SENTRY_TAG,
        ticketId: payload.data.ticket.id,
        commentId: payload.data.comment.id,
      });
    });
  });

  describe('handlePrivateComment', () => {
    it('should handle private comment successfully', async () => {
      // Arrange
      const payload = {
        orgId: 'org-123',
        data: {
          ticket: {
            id: 'ticket-123',
            title: 'Test Ticket',
          },
          comment: {
            id: 'comment-123',
            parentCommentId: 'parent-123',
            author: {
              id: 'author-123',
              email: '<EMAIL>',
              name: 'Author Name',
            },
            contentHtml: '<p>Test private comment</p>',
            contentJson:
              '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test private comment"}]}]}',
            content: '<p>Test private comment</p>',
            attachments: [],
          },
        },
      };

      const triageMessage = {
        id: 'triage-123',
        channelId: 'C12345',
        slackMessageTs: '**********.123456',
        platformThreadId: 'parent-123',
        slackRequestMessage: {
          platformTicketId: 'ticket-123',
        },
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          id: 'channel-123',
          channelId: 'C12345',
        },
        organization: {
          id: 'org-123',
          uid: 'org-123',
        },
      };

      const slackUser = {
        id: 'user-123',
        realName: 'Author Real Name',
        displayName: 'Author Display Name',
        name: 'author',
        slackAccessToken: 'xoxp-token',
        images: {
          image_original: 'https://example.com/image.jpg',
          image_72: 'https://example.com/image_72.jpg',
        },
      };

      // Mock repository methods
      mockTriageMessagesRepository.findByCondition.mockResolvedValue(
        triageMessage,
      );
      mockUsersRepository.findOne.mockResolvedValue(slackUser);

      // Mock sendMessageToSlack
      vi.spyOn(handler as any, 'sendMessageToSlack').mockResolvedValue({
        ok: true,
        message: { ts: '**********.654321', thread_ts: '**********.123456' },
      });

      // Mock getSlackUserDetails
      vi.spyOn(handler as any, 'getSlackUserDetails').mockResolvedValue({
        userDetails: {
          userName: 'Author Real Name',
          iconUrl: 'https://example.com/image.jpg',
        },
        slackUser,
      });

      // Act
      const result = await (handler as any).handlePrivateComment(payload);

      // Assert
      expect(mockTriageMessagesRepository.findByCondition).toHaveBeenCalledWith(
        {
          where: {
            platformThreadId: 'parent-123',
            organization: { uid: 'org-123' },
            slackRequestMessage: { platformTicketId: 'ticket-123' },
          },
          relations: { installation: true, channel: true, organization: true },
        },
      );
      expect((handler as any).getSlackUserDetails).toHaveBeenCalled();
      expect((handler as any).sendMessageToSlack).toHaveBeenCalled();
      expect(mockCommentConversationMapsRepository.save).toHaveBeenCalled();
      expect(mockSlackMessagesRepository.save).toHaveBeenCalled();
      expect(result).toEqual({
        ok: true,
        message: { ts: '**********.654321', thread_ts: '**********.123456' },
      });
    });

    it('should handle case when no triage message is found', async () => {
      // Arrange
      const payload = {
        orgId: 'org-123',
        data: {
          ticket: {
            id: 'ticket-123',
            title: 'Test Ticket',
          },
          comment: {
            id: 'comment-123',
            parentCommentId: 'parent-123',
            author: {
              id: 'author-123',
              email: '<EMAIL>',
              name: 'Author Name',
            },
            contentHtml: '<p>Test private comment</p>',
            contentJson:
              '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test private comment"}]}]}',
            content: '<p>Test private comment</p>',
            attachments: [],
          },
        },
      };

      // Mock repository methods
      mockTriageMessagesRepository.findByCondition.mockResolvedValue(null);

      // Act
      const result = await (handler as any).handlePrivateComment(payload);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          `No slack message found for ticket ${payload.data.ticket.id}`,
        ),
      );
      expect(result).toBeUndefined();
    });

    it('should handle error during processing', async () => {
      // Arrange
      const payload = {
        orgId: 'org-123',
        data: {
          ticket: {
            id: 'ticket-123',
            title: 'Test Ticket',
          },
          comment: {
            id: 'comment-123',
            parentCommentId: 'parent-123',
            author: {
              id: 'author-123',
              email: '<EMAIL>',
              name: 'Author Name',
            },
            contentHtml: '<p>Test private comment</p>',
            contentJson:
              '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Test private comment"}]}]}',
            content: '<p>Test private comment</p>',
            attachments: [],
          },
        },
      };

      const error = new Error('Database error');
      mockTriageMessagesRepository.findByCondition.mockRejectedValue(error);

      // Act & Assert
      await expect(async () => {
        await (handler as any).handlePrivateComment(payload);
      }).rejects.toThrow('Database error');

      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Error handling private comment: Database error',
        ),
      );
      expect(mockSentryService.captureException).toHaveBeenCalledWith(error, {
        name: '🚨 Error handling private comment!',
        tag: SLACK_SENTRY_TAG,
        ticketId: payload.data.ticket.id,
        commentId: payload.data.comment.id,
      });
    });
  });
});
