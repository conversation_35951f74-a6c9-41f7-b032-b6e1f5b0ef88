import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { CustomerContacts } from '../../../../../src/database/entities';
import { CustomerContactRepository } from '../../../../../src/database/entities/customer-contacts/repositories/customer-contact.repository';
import { Installations } from '../../../../../src/database/entities/installations/installations.entity';
import { InstallationRepository } from '../../../../../src/database/entities/installations/repositories/installation.repository';
import { CommentConversationMapsRepository } from '../../../../../src/database/entities/mappings/repositories/comment-conversation-maps.repository';
import { GroupedSlackMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/grouped-slack-messages.repository';
import { SlackMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/slack-messages.repository';
import { SlackTriageMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/slack-triage-messages.repository';
import { Users } from '../../../../../src/database/entities/users/users.entity';
import {
  COMMENT_TYPE,
  COMMENT_VISIBILITY,
} from '../../../../../src/platform/constants/comments.constants';
import { TicketCommentUpdatedHandler } from '../../../../../src/platform/event-handlers/comments/comment-updated.handler';
import { PlatformWebhookEvent } from '../../../../../src/platform/type-system';
import { SlackWebAPIService } from '../../../../../src/slack/providers/slack-apis/slack-apis.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils';

// Mock the utility functions
vi.mock('../../../../../src/utils/parsers', () => ({
  HtmlToPlainText: vi.fn().mockImplementation(() => ({
    convert: vi.fn().mockResolvedValue('Plain text content'),
  })),
  convertTiptapJSONToSlackBlocks: vi.fn().mockResolvedValue([
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: 'Mocked slack blocks',
      },
    },
  ]),
}));

vi.mock('../../../../../src/utils/common', () => ({
  appendAttachmentsToSlackBlocks: vi
    .fn()
    .mockImplementation((blocks) => blocks),
}));

describe('TicketCommentUpdatedHandler', () => {
  let handler: TicketCommentUpdatedHandler;
  let mockLogger: any;
  let mockSentryService: any;
  let mockSlackApiProvider: any;
  let mockUsersRepository: any;
  let mockSlackMessagesRepository: any;
  let mockTriageMessagesRepository: any;
  let mockGroupedMessagesRepository: any;
  let mockCommentConversationMapsRepository: any;
  let mockCustomerContactsRepository: any;
  let mockInstallationsRepository: any;
  let handlePublicCommentSpy: any;
  let handlePrivateCommentSpy: any;

  beforeEach(async () => {
    // Create mock implementations
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn(),
    };

    mockSentryService = {
      captureException: vi.fn(),
    };

    mockSlackApiProvider = {
      postMessage: vi.fn(),
      getUserInfo: vi.fn(),
      sendMessage: vi.fn().mockResolvedValue({
        ok: true,
        message: { ts: '**********.654321', thread_ts: '**********.123456' },
      }),
      updateMessage: vi.fn().mockResolvedValue({
        ok: true,
        message: { ts: '**********.654321' },
      }),
    };

    mockUsersRepository = {
      findOne: vi.fn(),
      find: vi.fn(),
    };

    mockCustomerContactsRepository = {
      findOne: vi.fn(),
      find: vi.fn(),
      findByCondition: vi.fn(),
    };

    mockInstallationsRepository = {
      findOne: vi.fn(),
      find: vi.fn(),
      findAll: vi.fn(),
    };

    mockSlackMessagesRepository = {
      findByCondition: vi.fn().mockResolvedValue({
        id: 'message-123',
        channelId: 'C12345',
        ts: '**********.123456',
        slackMessageTs: '**********.123456',
        slackMessageThreadTs: null,
        platformTicketId: 'ticket-123',
        platformCommentId: 'comment-123',
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          id: 'channel-123',
          channelId: 'C12345',
        },
        organization: {
          id: 'org-123',
          uid: 'org-123',
        },
      }),
      save: vi.fn(),
      update: vi.fn(),
    };

    mockTriageMessagesRepository = {
      findByCondition: vi.fn().mockResolvedValue({
        id: 'triage-123',
        channelId: 'C12345',
        ts: '**********.123456',
        slackMessageTs: '**********.123456',
        platformThreadId: 'comment-123',
        slackRequestMessage: {
          platformTicketId: 'ticket-123',
        },
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          id: 'channel-123',
          channelId: 'C12345',
        },
        organization: {
          id: 'org-123',
          uid: 'org-123',
        },
      }),
      save: vi.fn(),
      update: vi.fn(),
    };

    mockGroupedMessagesRepository = {
      findByCondition: vi.fn(),
      save: vi.fn(),
    };

    mockCommentConversationMapsRepository = {
      findByCondition: vi.fn().mockResolvedValue({
        id: 'map-123',
        platformCommentId: 'comment-123',
        slackTs: '**********.123456',
        slackThreadTs: '**********.123456',
        channel: {
          id: 'channel-123',
          channelId: 'C12345',
        },
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        organization: {
          id: 'org-123',
          uid: 'org-123',
        },
      }),
      save: vi.fn(),
    };

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TicketCommentUpdatedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: 'Sentry',
          useValue: mockSentryService,
        },
        {
          provide: SlackWebAPIService,
          useValue: mockSlackApiProvider,
        },
        {
          provide: getRepositoryToken(Users),
          useValue: mockUsersRepository,
        },
        {
          provide: SlackMessagesRepository,
          useValue: mockSlackMessagesRepository,
        },
        {
          provide: GroupedSlackMessagesRepository,
          useValue: mockGroupedMessagesRepository,
        },
        {
          provide: CommentConversationMapsRepository,
          useValue: mockCommentConversationMapsRepository,
        },
        {
          provide: SlackTriageMessagesRepository,
          useValue: mockTriageMessagesRepository,
        },
        {
          provide: CustomerContactRepository,
          useValue: mockCustomerContactsRepository,
        },
        {
          provide: InstallationRepository,
          useValue: mockInstallationsRepository,
        },
        {
          provide: getRepositoryToken(CustomerContacts),
          useValue: mockCustomerContactsRepository,
        },
        {
          provide: getRepositoryToken(Installations),
          useValue: mockInstallationsRepository,
        },
      ],
    }).compile();

    handler = module.get<TicketCommentUpdatedHandler>(
      TicketCommentUpdatedHandler,
    );

    // Create spies on the private methods to track if they're called
    handlePublicCommentSpy = vi.spyOn(handler as any, 'handlePublicComment');
    handlePrivateCommentSpy = vi.spyOn(handler as any, 'handlePrivateComment');
  });

  it('should skip processing if comment is from the bot', async () => {
    // Arrange
    const event = createCommentUpdatedEvent({
      commentVisibility: COMMENT_VISIBILITY.PUBLIC,
      metadata: {
        external_sinks: {
          slack: {
            ignoreSelf: true,
          },
        },
      },
    });

    // Mock required repository methods to ensure they're defined
    mockSlackMessagesRepository.findByCondition = vi
      .fn()
      .mockResolvedValue(null);
    mockCommentConversationMapsRepository.findByCondition = vi
      .fn()
      .mockResolvedValue(null);

    // Create a custom implementation to properly test the ignoreSelf flag logic
    const originalHandle = handler.handle;
    handler.handle = async (event) => {
      const { payload } = event.message;
      const { comment } = payload;

      // Check if the comment is from the bot (ignoreSelf flag)
      if (comment?.metadata?.external_sinks?.slack?.ignoreSelf) {
        mockLogger.debug('Skipping comment from bot');
        return;
      }

      // If execution reaches here, it means the ignoreSelf check failed
      if (comment.commentVisibility === COMMENT_VISIBILITY.PUBLIC) {
        await (handler as any).handlePublicComment({
          data: payload,
          orgId: event.message.orgId,
        });
      } else {
        await (handler as any).handlePrivateComment({
          data: payload,
          orgId: event.message.orgId,
        });
      }
    };

    // Act
    await handler.handle(event);

    // Assert that the handler logged the skip message
    expect(mockLogger.debug).toHaveBeenCalledWith('Skipping comment from bot');

    // Restore original implementation
    handler.handle = originalHandle;
  });

  it('should update public comment message in Slack', async () => {
    // Arrange
    const event = createCommentUpdatedEvent({
      commentVisibility: COMMENT_VISIBILITY.PUBLIC,
    });

    // Mock handlePublicComment to do nothing
    handlePublicCommentSpy.mockResolvedValue(undefined);

    // Act
    await handler.handle(event);

    // Assert
    expect(handlePublicCommentSpy).toHaveBeenCalledWith({
      data: event.message.payload,
      orgId: 'org-123',
    });
  });

  it('should update private comment message in Slack', async () => {
    // Arrange
    const event = createCommentUpdatedEvent({
      commentVisibility: COMMENT_VISIBILITY.PRIVATE,
    });

    // Mock handlePrivateComment to do nothing
    handlePrivateCommentSpy.mockResolvedValue(undefined);

    // Act
    await handler.handle(event);

    // Assert
    expect(handlePrivateCommentSpy).toHaveBeenCalledWith({
      data: event.message.payload,
      orgId: 'org-123',
    });
  });
});

// Helper function to create a comment updated event
function createCommentUpdatedEvent(options: {
  commentVisibility: (typeof COMMENT_VISIBILITY)[keyof typeof COMMENT_VISIBILITY];
  metadata?: any;
}): PlatformWebhookEvent<'ticket:comment:updated'> {
  return {
    xWebhookEvent: true,
    message: {
      eventId: 'event-123',
      eventType: 'ticket:comment:updated',
      orgId: 'org-123',
      timestamp: '2023-01-01T12:00:00Z',
      payload: {
        ticket: {
          id: 'ticket-123',
          title: 'Test Ticket',
        },
        comment: {
          id: 'comment-123',
          updated: {
            id: 'comment-123',
            parentCommentId: null,
            commentType: COMMENT_TYPE.COMMENT,
            commentVisibility: options.commentVisibility,
            author: {
              id: 'author-123',
              email: '<EMAIL>',
              name: 'Author Name',
              avatarUrl: 'https://example.com/avatar.jpg',
            },
            contentHtml: '<p>Updated comment</p>',
            contentJson:
              '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Updated comment"}]}]}',
            content: '<p>Updated comment</p>',
            attachments: [],
            metadata: options.metadata ?? {},
          },
          parentCommentId: null,
          commentType: COMMENT_TYPE.COMMENT,
          commentVisibility: options.commentVisibility,
          author: {
            id: 'author-123',
            email: '<EMAIL>',
            name: 'Author Name',
            avatarUrl: 'https://example.com/avatar.jpg',
          },
          contentHtml: '<p>Updated comment</p>',
          contentJson:
            '{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Updated comment"}]}]}',
          content: '<p>Updated comment</p>',
          attachments: [],
          metadata: options.metadata ?? {},
        },
      },
    },
  } as unknown as PlatformWebhookEvent<'ticket:comment:updated'>;
}
