import { beforeEach, describe, expect, it, vi } from 'vitest';
import { SlackMessages } from '../../../../../src/database/entities/slack-messages';
import { TicketUpdatedHandler } from '../../../../../src/platform/event-handlers/tickets/ticket-updated.handler';
import { PlatformWebhookEvent } from '../../../../../src/platform/type-system';

describe('TicketUpdatedHandler - Improved Tests', () => {
  let handler: TicketUpdatedHandler;
  let mockLogger: any;
  let mockSentryService: any;
  let mockCoreTriageService: any;
  let mockSlackMessagesRepository: any;

  const mockTicket = {
    id: 'ticket-123',
    title: 'Test Ticket',
    statusId: 'status-123',
    statusName: 'Open',
    priorityId: 'priority-123',
    priorityName: 'High',
    teamId: 'team-123',
  };

  const mockPreviousTicket = {
    id: 'ticket-123',
    title: 'Previous Test Ticket',
    statusId: 'status-456',
    statusName: 'New',
    priorityId: 'priority-456',
    priorityName: 'Low',
    teamId: 'team-123',
  };

  const mockInstallation = {
    id: 'installation-123',
    botToken: 'xoxb-token',
    organization: { id: 'org-123', uid: 'org-123' },
  };

  const mockSlackMessage = {
    id: 'message-123',
    platformTicketId: 'ticket-123',
    ts: '1234567890.123456',
    channel: { id: 'channel-123', channelId: 'C12345' },
    installation: mockInstallation,
    organization: { id: 'org-123', uid: 'org-123' },
    metadata: {
      ticket_details: {
        statusId: 'status-123',
        priorityId: 'priority-123',
      },
    },
  };

  beforeEach(() => {
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    };

    mockSentryService = {
      captureException: vi.fn(),
    };

    mockCoreTriageService = {
      updateTriageMessagesForSlackMessage: vi.fn(),
    };

    mockSlackMessagesRepository = {
      findOne: vi.fn().mockResolvedValue(mockSlackMessage),
      find: vi.fn(),
    };

    handler = new TicketUpdatedHandler(
      mockLogger,
      mockSentryService,
      mockCoreTriageService,
      mockSlackMessagesRepository,
    );

    // Mock the wasTicketUpdated method
    vi.spyOn(handler as any, 'wasTicketUpdated').mockImplementation(
      (slackMessage, payload) => {
        if (payload.ticket.statusId !== payload.previousTicket?.statusId) {
          return true;
        }
        if (payload.ticket.priorityId !== payload.previousTicket?.priorityId) {
          return true;
        }
        return false;
      },
    );
  });

  describe('handle', () => {
    it('should handle ticket updated event successfully', async () => {
      // Setup
      const event = {
        xWebhookEvent: true as const,
        message: {
          actor: {
            email: '<EMAIL>',
            id: 'user-123',
            type: 'user',
          },
          eventId: 'event-123',
          eventType: 'ticket:updated',
          orgId: 'org-123',
          payload: {
            ticket: mockTicket,
            previousTicket: mockPreviousTicket,
          },
          timestamp: '2023-01-01T12:00:00Z',
        },
        context: {
          installation: mockInstallation,
        },
      } as PlatformWebhookEvent<'ticket:updated'>;

      // Execute
      await handler.handle(event);

      // Verify
      expect(mockLogger.log).toHaveBeenCalledWith(
        `[TicketUpdatedHandler] [event-123] Processing ticket updated: ${mockTicket.id}`,
      );
      expect(mockSlackMessagesRepository.findOne).toHaveBeenCalledWith({
        where: {
          platformTicketId: mockTicket.id,
          organization: { uid: 'org-123' },
        },
        relations: ['installation', 'organization'],
      });
      expect(
        mockCoreTriageService.updateTriageMessagesForSlackMessage,
      ).toHaveBeenCalledWith(
        expect.objectContaining({ id: 'installation-123' }),
        mockSlackMessage,
      );
    });

    it('should skip processing if event type is not ticket:updated', async () => {
      // Setup
      const event = {
        xWebhookEvent: true as const,
        message: {
          actor: {
            email: '<EMAIL>',
            id: 'user-123',
            type: 'user',
          },
          eventId: 'event-123',
          eventType: 'ticket:created', // Different event type
          orgId: 'org-123',
          payload: {
            ticket: mockTicket,
            previousTicket: mockPreviousTicket,
          },
          timestamp: '2023-01-01T12:00:00Z',
        },
        context: {
          installation: mockInstallation,
        },
      } as unknown as PlatformWebhookEvent<'ticket:updated'>;

      // Execute
      await handler.handle(event);

      // Verify
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Skipping ticket updated event processor'),
      );
      expect(mockSlackMessagesRepository.findOne).not.toHaveBeenCalled();
      expect(
        mockCoreTriageService.updateTriageMessagesForSlackMessage,
      ).not.toHaveBeenCalled();
    });

    it('should handle case when no slack message is found', async () => {
      // Setup
      const event = {
        xWebhookEvent: true as const,
        message: {
          actor: {
            email: '<EMAIL>',
            id: 'user-123',
            type: 'user',
          },
          eventId: 'event-123',
          eventType: 'ticket:updated',
          orgId: 'org-123',
          payload: {
            ticket: mockTicket,
            previousTicket: mockPreviousTicket,
          },
          timestamp: '2023-01-01T12:00:00Z',
        },
        context: {
          installation: mockInstallation,
        },
      } as PlatformWebhookEvent<'ticket:updated'>;

      mockSlackMessagesRepository.findOne.mockResolvedValue(null);

      // Execute
      await handler.handle(event);

      // Verify
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining(
          `No slack message found for ticket ${mockTicket.id}`,
        ),
      );
      expect(
        mockCoreTriageService.updateTriageMessagesForSlackMessage,
      ).not.toHaveBeenCalled();
    });

    it('should handle case when ticket has not changed significantly', async () => {
      // Setup
      const event = {
        xWebhookEvent: true as const,
        message: {
          actor: {
            email: '<EMAIL>',
            id: 'user-123',
            type: 'user',
          },
          eventId: 'event-123',
          eventType: 'ticket:updated',
          orgId: 'org-123',
          payload: {
            ticket: mockTicket,
            previousTicket: { ...mockTicket }, // Same ticket data
          },
          timestamp: '2023-01-01T12:00:00Z',
        },
        context: {
          installation: mockInstallation,
        },
      } as PlatformWebhookEvent<'ticket:updated'>;

      // Mock wasTicketUpdated to return false for this test
      (handler as any).wasTicketUpdated.mockReturnValueOnce(false);

      // Execute
      await handler.handle(event);

      // Verify
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining(
          `Ticket ${mockTicket.id} in org org-123 was not updated`,
        ),
      );
      expect(
        mockCoreTriageService.updateTriageMessagesForSlackMessage,
      ).not.toHaveBeenCalled();
    });

    it('should handle errors during processing', async () => {
      // Setup
      const event = {
        xWebhookEvent: true as const,
        message: {
          actor: {
            email: '<EMAIL>',
            id: 'user-123',
            type: 'user',
          },
          eventId: 'event-123',
          eventType: 'ticket:updated',
          orgId: 'org-123',
          payload: {
            ticket: mockTicket,
            previousTicket: mockPreviousTicket,
          },
          timestamp: '2023-01-01T12:00:00Z',
        },
        context: {
          installation: mockInstallation,
        },
      } as PlatformWebhookEvent<'ticket:updated'>;

      mockSlackMessagesRepository.findOne.mockRejectedValue(
        new Error('Database error'),
      );

      // Execute
      await handler.handle(event);

      // Verify
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          `[TicketUpdatedHandler] [event-123] Error processing ticket updated event`,
        ),
        expect.any(String),
      );
      expect(mockSentryService.captureException).toHaveBeenCalled();
    });
  });

  describe('wasTicketUpdated', () => {
    beforeEach(() => {
      // Restore the original implementation for these tests
      (handler as any).wasTicketUpdated.mockRestore();
    });

    it('should return true when ticket status has changed', () => {
      // Setup
      const slackMessage = {
        ...mockSlackMessage,
        metadata: {
          ticket_details: {
            statusId: 'status-123',
            priorityId: 'priority-123',
          },
        },
      } as SlackMessages;

      const payload = {
        ticket: {
          ...mockTicket,
          statusId: 'status-456', // Changed status
        },
        previousTicket: {
          ...mockPreviousTicket,
          statusId: 'status-123',
        },
      };

      // Execute
      const result = (handler as any).wasTicketUpdated(slackMessage, payload);

      // Verify
      expect(result).toBe(true);
    });

    it('should return true when ticket priority has changed', () => {
      // Setup
      const slackMessage = {
        ...mockSlackMessage,
        metadata: {
          ticket_details: {
            statusId: 'status-123',
            priorityId: 'priority-123',
          },
        },
      } as SlackMessages;

      const payload = {
        ticket: {
          ...mockTicket,
          priorityId: 'priority-456', // Changed priority
        },
        previousTicket: {
          ...mockPreviousTicket,
          priorityId: 'priority-123',
        },
      };

      // Execute
      const result = (handler as any).wasTicketUpdated(slackMessage, payload);

      // Verify
      expect(result).toBe(true);
    });

    it('should return false when no significant changes', () => {
      // Setup
      const slackMessage = {
        ...mockSlackMessage,
        metadata: {
          ticket_details: {
            statusId: 'status-123',
            priorityId: 'priority-123',
          },
        },
      } as SlackMessages;

      const payload = {
        ticket: {
          ...mockTicket,
          statusId: 'status-123',
          priorityId: 'priority-123',
        },
        previousTicket: {
          ...mockPreviousTicket,
          statusId: 'status-123',
          priorityId: 'priority-123',
        },
      };

      // Execute
      const result = (handler as any).wasTicketUpdated(slackMessage, payload);

      // Verify
      expect(result).toBe(false);
    });
  });
});
