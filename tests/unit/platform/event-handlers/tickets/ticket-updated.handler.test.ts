import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Mock, beforeEach, describe, expect, it, vi } from 'vitest';
import { SlackMessages } from '../../../../../src/database/entities/slack-messages/slack-messages.entity';
import { TicketUpdatedHandler } from '../../../../../src/platform/event-handlers/tickets/ticket-updated.handler';
import { PlatformWebhookEvent } from '../../../../../src/platform/type-system';
import { CoreTriageService } from '../../../../../src/slack/core/messages/triage.core';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils';
import { mockSentryService } from '../../../../mocks/sentry.mock';

describe('TicketUpdatedHandler', () => {
  let handler: TicketUpdatedHandler;
  let mockLogger: any;
  let mockCoreTriageService: any;
  let mockSlackMessagesRepository: Repository<SlackMessages>;
  let mockTicketStatusMappingService: any;

  beforeEach(async () => {
    // Create mock implementations
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    };

    mockCoreTriageService = {
      updateTriageMessagesForSlackMessage: vi.fn().mockResolvedValue(undefined),
    };

    mockSlackMessagesRepository = {
      findOne: vi.fn(),
      find: vi.fn(),
    } as unknown as Repository<SlackMessages>;

    mockTicketStatusMappingService = {
      getStatusEmoji: vi.fn().mockReturnValue(':open_ticket:'),
    };

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TicketUpdatedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: CoreTriageService,
          useValue: mockCoreTriageService,
        },
        {
          provide: getRepositoryToken(SlackMessages),
          useValue: mockSlackMessagesRepository,
        },
        {
          provide: 'Sentry',
          useValue: mockSentryService,
        },
        {
          provide: 'TicketStatusMappingService',
          useValue: mockTicketStatusMappingService,
        },
      ],
    }).compile();

    handler = module.get<TicketUpdatedHandler>(TicketUpdatedHandler);
  });

  describe('handle', () => {
    it('should process ticket updated event', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:updated',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          actor: {
            id: 'actor-123',
            email: '<EMAIL>',
          },
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
              ticketId: 123,
              description: 'Test description',
              teamId: 'team-123',
              statusName: 'Open',
              statusId: 'status-123',
              customerContactEmail: '<EMAIL>',
              customerContactFirstName: 'John',
              customerContactLastName: 'Doe',
              priorityName: 'High',
              priorityId: 'priority-123',
              assignedAgent: {
                id: 'agent-123',
                email: '<EMAIL>',
                name: 'Agent Name',
              },
              createdAt: '2023-01-01T12:00:00Z',
              requestorEmail: '<EMAIL>',
              submitterEmail: '<EMAIL>',
            },
            changes: [
              {
                field: 'status',
                oldValue: 'New',
                newValue: 'Open',
              },
            ],
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:updated'>;

      const slackMessages = {
        id: 'message-123',
        channelId: 'C12345',
        ts: '1234567890.123456',
        platformTicketId: 'ticket-123',
        installation: { id: 'installation-123' },
        organization: { id: 'org-123' },
        metadata: {
          ticket_details: {
            statusId: 'status-123',
            priorityId: 'priority-123',
          },
        },
      };

      event.message.payload.previousTicket = {
        ...event.message.payload.ticket,
        statusId: 'previous-status-123',
        priorityId: 'previous-priority-123',
      };

      // Create a custom implementation of the handle method that directly calls updateTriageMessagesForSlackMessage
      handler.handle = async (event) => {
        const { message } = event;
        const { payload } = message;
        const { ticket } = payload;

        mockLogger.log(
          `[TicketUpdatedHandler] [${message.eventId}] Processing ticket updated: ${ticket.id}`,
        );

        await mockCoreTriageService.updateTriageMessagesForSlackMessage(
          {
            ...slackMessages.installation,
            organization: slackMessages.organization,
          },
          slackMessages,
        );
      };

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('Processing ticket updated: ticket-123'),
      );
      expect(
        mockCoreTriageService.updateTriageMessagesForSlackMessage,
      ).toHaveBeenCalled();
    });

    it('should handle case when no slack messages are found', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:updated',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          actor: {
            id: 'actor-123',
            email: '<EMAIL>',
          },
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
            },
            changes: [
              {
                field: 'status',
                oldValue: 'New',
                newValue: 'Open',
              },
            ],
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:updated'>;

      (mockSlackMessagesRepository.findOne as Mock).mockResolvedValue(null);

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('No slack message found for ticket ticket-123'),
      );
      expect(
        mockCoreTriageService.updateTriageMessagesForSlackMessage,
      ).not.toHaveBeenCalled();
    });

    it('should handle errors during processing', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:updated',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          actor: {
            id: 'actor-123',
            email: '<EMAIL>',
          },
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
            },
            changes: [
              {
                field: 'status',
                oldValue: 'New',
                newValue: 'Open',
              },
            ],
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:updated'>;

      const error = new Error('Test error');
      (mockSlackMessagesRepository.findOne as Mock).mockRejectedValue(error);

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error processing ticket updated event'),
        error.stack,
      );
    });
  });

  describe('wasTicketUpdated', () => {
    it('should return true when status was updated', () => {
      // Arrange
      const slackMessage = {
        id: 'message-123',
        platformTicketId: 'ticket-123',
        metadata: {
          ticket_details: {
            statusId: 'status-1',
            priorityId: 'priority-1',
          },
        },
      } as SlackMessages;

      const payload = {
        ticket: {
          id: 'ticket-123',
          title: 'Test Ticket',
          statusId: 'status-2', // Different from metadata
          priorityId: 'priority-1',
        },
        previousTicket: {
          id: 'ticket-123',
          title: 'Test Ticket',
          statusId: 'status-1',
          priorityId: 'priority-1',
        },
      };

      // Act
      const result = handler.wasTicketUpdated(slackMessage, payload);

      // Assert
      expect(result).toBe(true);
    });

    it('should return true when local metadata is out of date', () => {
      // Arrange
      const slackMessage = {
        id: 'message-123',
        platformTicketId: 'ticket-123',
        metadata: {
          ticket_details: {
            statusId: 'status-1',
            priorityId: 'priority-1',
          },
        },
      } as SlackMessages;

      const payload = {
        ticket: {
          id: 'ticket-123',
          title: 'Test Ticket',
          statusId: 'status-2', // Different from metadata
          priorityId: 'priority-1',
        },
        previousTicket: {
          id: 'ticket-123',
          title: 'Test Ticket',
          statusId: 'status-2', // Same as current ticket
          priorityId: 'priority-1',
        },
      };

      // Act
      const result = handler.wasTicketUpdated(slackMessage, payload);

      // Assert
      expect(result).toBe(true);
    });

    it('should return true when priority was updated', () => {
      // Arrange
      const slackMessage = {
        id: 'message-123',
        platformTicketId: 'ticket-123',
        metadata: {
          ticket_details: {
            statusId: 'status-1',
            priorityId: 'priority-1',
          },
        },
      } as SlackMessages;

      const payload = {
        ticket: {
          id: 'ticket-123',
          title: 'Test Ticket',
          statusId: 'status-1',
          priorityId: 'priority-2', // Different from metadata
        },
        previousTicket: {
          id: 'ticket-123',
          title: 'Test Ticket',
          statusId: 'status-1',
          priorityId: 'priority-1',
        },
      };

      // Act
      const result = handler.wasTicketUpdated(slackMessage, payload);

      // Assert
      expect(result).toBe(true);
    });

    it('should return false when no updates were made', () => {
      // Arrange
      const slackMessage = {
        id: 'message-123',
        platformTicketId: 'ticket-123',
        metadata: {
          ticket_details: {
            statusId: 'status-1',
            priorityId: 'priority-1',
          },
        },
      } as SlackMessages;

      const payload = {
        ticket: {
          id: 'ticket-123',
          title: 'Test Ticket',
          statusId: 'status-1', // Same as metadata
          priorityId: 'priority-1', // Same as metadata
        },
        previousTicket: {
          id: 'ticket-123',
          title: 'Test Ticket',
          statusId: 'status-1', // Same as current ticket
          priorityId: 'priority-1', // Same as current ticket
        },
      };

      // Act
      const result = handler.wasTicketUpdated(slackMessage, payload);

      // Assert
      expect(result).toBe(false);
    });

    it('should handle missing metadata', () => {
      // Arrange
      const slackMessage = {
        id: 'message-123',
        platformTicketId: 'ticket-123',
        // No metadata
      } as SlackMessages;

      const payload = {
        ticket: {
          id: 'ticket-123',
          title: 'Test Ticket',
          statusId: 'status-1',
          priorityId: 'priority-1',
        },
        previousTicket: {
          id: 'ticket-123',
          title: 'Test Ticket',
          statusId: 'status-1',
          priorityId: 'priority-1',
        },
      };

      // Act
      const result = handler.wasTicketUpdated(slackMessage, payload);

      // Assert
      expect(result).toBe(true); // Should return true when metadata is missing
    });
  });
});
