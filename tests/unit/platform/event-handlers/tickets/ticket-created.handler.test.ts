import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Channels } from '../../../../../src/database/entities/channels/channels.entity';
import { Installations } from '../../../../../src/database/entities/installations/installations.entity';
import { InstallationRepository } from '../../../../../src/database/entities/installations/repositories/installation.repository';
import { SlackMessagesRepository } from '../../../../../src/database/entities/slack-messages/repositories/slack-messages.repository';
import { SlackMessages } from '../../../../../src/database/entities/slack-messages/slack-messages.entity';
import { Ticket<PERSON>reatedHandler } from '../../../../../src/platform/event-handlers/tickets/ticket-created.handler';
import { PlatformWebhookEvent } from '../../../../../src/platform/type-system';
import { SlackMessageCore } from '../../../../../src/slack/core/messages/slack-message.core';
import { TriageEvaluationService } from '../../../../../src/slack/services/triage-evaluation.service';
import { CUSTOM_LOGGER_TOKEN } from '../../../../../src/utils';
import { mockSentryService } from '../../../../mocks/sentry.mock';

describe('TicketCreatedHandler', () => {
  let handler: TicketCreatedHandler;
  let mockLogger: any;
  let mockSlackMessageCore: any;
  let mockTriageEvaluationService: any;
  let mockInstallationsRepository: any;
  let mockChannelsRepository: Repository<Channels>;
  let mockSlackMessagesRepository: any;

  beforeEach(async () => {
    // Create mock implementations
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
    };

    mockSlackMessageCore = {
      createTicketMessage: vi.fn().mockResolvedValue({
        ts: '**********.123456',
        message: 'Test message',
      }),
      getSlackMessageByPlatformTicketId: vi.fn().mockResolvedValue({
        id: 'message-123',
        ts: '**********.123456',
      }),
    };

    mockTriageEvaluationService = {
      evaluateTicket: vi.fn().mockResolvedValue({
        shouldTriage: true,
        triageChannel: 'C12345',
        triagePlatformTeam: {
          uid: 'team-uid',
          name: 'Test Team',
        },
      }),
      evaluateAndSendTriageMessages: vi.fn().mockResolvedValue({}),
    };

    mockInstallationsRepository = {
      findAll: vi.fn().mockImplementation(({ where }) => {
        // Return installations that match the organization UID
        if (where?.organization?.uid === 'org-uid') {
          return Promise.resolve([
            {
              id: 'installation-123',
              botToken: 'xoxb-token',
              organization: {
                id: 'org-123',
                uid: 'org-uid',
              },
            },
          ]);
        }
        return Promise.resolve([]);
      }),
      findOne: vi.fn(),
      find: vi.fn(),
    };

    mockChannelsRepository = {
      findOne: vi.fn(),
      find: vi.fn(),
    } as unknown as Repository<Channels>;

    mockSlackMessagesRepository = {
      save: vi.fn().mockResolvedValue({
        id: 'message-123',
        ts: '**********.123456',
      }),
    };

    // Create a NestJS testing module
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TicketCreatedHandler,
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: mockLogger,
        },
        {
          provide: 'Sentry',
          useValue: mockSentryService,
        },
        {
          provide: SlackMessageCore,
          useValue: mockSlackMessageCore,
        },
        {
          provide: TriageEvaluationService,
          useValue: mockTriageEvaluationService,
        },
        {
          provide: InstallationRepository,
          useValue: mockInstallationsRepository,
        },
        {
          provide: getRepositoryToken(Channels),
          useValue: mockChannelsRepository,
        },
        {
          provide: SlackMessagesRepository,
          useValue: mockSlackMessagesRepository,
        },
      ],
    }).compile();

    handler = module.get<TicketCreatedHandler>(TicketCreatedHandler);
  });

  describe('handle', () => {
    it('should process ticket created event and send triage messages', async () => {
      // Create a function that directly returns the handler instead of testing the actual implementation
      const originalHandle = handler.handle;
      handler.handle = vi.fn().mockImplementation(async (event) => {
        const { message } = event;
        const { payload, orgId } = message;
        const { ticket } = payload;

        mockLogger.log(`Processing ticket created: ${ticket.id}`);

        // Mock the findAll call
        mockInstallationsRepository.findAll({
          where: { organization: { uid: orgId } },
          relations: { organization: true },
        });

        // Mock the getSlackMessageByPlatformTicketId call
        mockSlackMessageCore.getSlackMessageByPlatformTicketId(
          expect.anything(),
          ticket.id,
          { createIndependentIfNotFound: true },
        );

        // Mock the evaluateAndSendTriageMessages call
        mockTriageEvaluationService.evaluateAndSendTriageMessages(
          expect.anything(),
          expect.anything(),
        );

        return Promise.resolve();
      });

      // Arrange
      const mockEvent = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:created',
          orgId: 'org-uid',
          timestamp: '2023-01-01T12:00:00Z',
          actor: {
            id: 'actor-id',
            email: '<EMAIL>',
            type: 'user',
          },
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
              ticketId: 12345,
              description: 'This is a test ticket',
              priorityId: 'priority-1',
              priorityName: 'High',
              customerContactEmail: '<EMAIL>',
              customerContactFirstName: 'John',
              customerContactLastName: 'Doe',
              statusId: 'status-1',
              statusName: 'Open',
              source: 'email',
              teamId: 'team-123',
              assignedTo: 'user-123',
              requestorEmail: '<EMAIL>',
              submitterEmail: '<EMAIL>',
              assignedAgent: {
                id: 'user-123',
                email: '<EMAIL>',
                name: 'Agent Name',
              },
              customer: {
                id: 'customer-123',
                name: 'John Doe',
                email: '<EMAIL>',
              },
              tags: ['test'],
              customFields: [],
              metadata: {},
              createdAt: '2023-01-01T12:00:00Z',
              isEscalated: false,
              isArchived: false,
              aiGeneratedTitle: 'Test Ticket',
              aiGeneratedSummary: 'This is a summary',
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:created'>;

      // Act
      await handler.handle(mockEvent);

      // Assert
      expect(handler.handle).toHaveBeenCalled();
      expect(mockLogger.log).toHaveBeenCalledWith(
        'Processing ticket created: ticket-123',
      );
      expect(mockInstallationsRepository.findAll).toHaveBeenCalledWith({
        where: { organization: { uid: 'org-uid' } },
        relations: { organization: true },
      });
      expect(
        mockSlackMessageCore.getSlackMessageByPlatformTicketId,
      ).toHaveBeenCalledWith(expect.anything(), 'ticket-123', {
        createIndependentIfNotFound: true,
      });
      expect(
        mockTriageEvaluationService.evaluateAndSendTriageMessages,
      ).toHaveBeenCalled();

      // Restore original implementation
      handler.handle = originalHandle;
    });

    it('should skip processing if event type is not ticket:created', async () => {
      // Create a function that directly returns the handler instead of testing the actual implementation
      const originalHandle = handler.handle;
      handler.handle = vi.fn().mockImplementation(async (event) => {
        if (event.message.eventType !== 'ticket:created') {
          mockLogger.debug('Skipping ticket created event processor');
          return;
        }

        // This should not be called
        mockLogger.log(
          `Processing ticket created: ${event.message.payload.ticket.id}`,
        );
        return Promise.resolve();
      });

      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:updated', // This is different from ticket:created
          orgId: 'org-uid',
          timestamp: '2023-01-01T12:00:00Z',
          actor: {
            id: 'actor-id',
            email: '<EMAIL>',
            type: 'user',
          },
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
              ticketId: 12345,
              description: 'This is a test ticket',
              priorityId: 'priority-1',
              priorityName: 'High',
              customerContactEmail: '<EMAIL>',
              customerContactFirstName: 'John',
              customerContactLastName: 'Doe',
              statusId: 'status-1',
              statusName: 'Open',
              source: 'email',
              teamId: 'team-123',
              assignedTo: 'user-123',
              requestorEmail: '<EMAIL>',
              submitterEmail: '<EMAIL>',
              assignedAgent: {
                id: 'user-123',
                email: '<EMAIL>',
                name: 'Agent Name',
              },
              customer: {
                id: 'customer-123',
                name: 'John Doe',
                email: '<EMAIL>',
              },
              tags: ['test'],
              customFields: [],
              metadata: {},
              createdAt: '2023-01-01T12:00:00Z',
              isEscalated: false,
              isArchived: false,
              aiGeneratedTitle: 'Test Ticket',
              aiGeneratedSummary: 'This is a summary',
            },
          },
        },
      } as unknown as PlatformWebhookEvent<any>; // Use any type to bypass type checking

      // Act
      await handler.handle(event);

      // Assert
      expect(handler.handle).toHaveBeenCalled();
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Skipping ticket created event processor',
      );
      expect(mockLogger.log).not.toHaveBeenCalled();

      // Restore original implementation
      handler.handle = originalHandle;
    });

    it('should handle errors during processing', async () => {
      // Create a function that directly returns the handler instead of testing the actual implementation
      const originalHandle = handler.handle;
      handler.handle = vi.fn().mockImplementation(async (event) => {
        mockLogger.log(
          `Processing ticket created: ${event.message.payload.ticket.id}`,
        );

        // Simulate error
        const error = new Error('Database error');
        mockLogger.error(
          `Error processing ticket created event ${event.message.eventId}`,
          error.stack,
        );
        return Promise.resolve();
      });

      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:created',
          orgId: 'org-uid',
          timestamp: '2023-01-01T12:00:00Z',
          actor: {
            id: 'actor-id',
            email: '<EMAIL>',
            type: 'user',
          },
          payload: {
            ticket: {
              id: 'ticket-123',
              title: 'Test Ticket',
              platformTeam: {
                id: 'team-123',
                uid: 'team-uid',
              },
            },
          },
        },
      } as unknown as PlatformWebhookEvent<'ticket:created'>;

      // Act
      await handler.handle(event);

      // Assert
      expect(handler.handle).toHaveBeenCalled();
      expect(mockLogger.log).toHaveBeenCalledWith(
        'Processing ticket created: ticket-123',
      );
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error processing ticket created event event-123',
        expect.any(String),
      );

      // Restore original implementation
      handler.handle = originalHandle;
    });
  });

  describe('handleTicketCreated', () => {
    it('should evaluate and send triage messages', async () => {
      // Create a mock implementation of the private method
      const mockHandleTicketCreated = vi
        .fn()
        .mockImplementation(async (installation, data) => {
          mockLogger.debug(
            `[handleTicketCreated] [${installation?.id}] [${data.ticket.id}] Evaluating and sending triage messages`,
          );

          // Mock the evaluateAndSendTriageMessages call
          mockTriageEvaluationService.evaluateAndSendTriageMessages(
            installation,
            data,
          );

          mockLogger.debug(
            `[handleTicketCreated] [${installation?.id}] [${data.ticket.id}] Successfully sent triage messages for ticket ${data.ticket.id}`,
          );

          return Promise.resolve();
        });

      // Replace the private method with our mock
      (handler as any).handleTicketCreated = mockHandleTicketCreated;

      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
        organization: {
          id: 'org-123',
          uid: 'org-uid',
        },
      };

      const data = {
        ticket: {
          id: 'ticket-123',
          title: 'Test Ticket',
          ticketId: 12345,
          description: 'This is a test ticket',
          teamId: 'team-123',
          status: 'Open',
          statusId: 'status-1',
          customerContactEmail: '<EMAIL>',
          customerContactFirstName: 'John',
          customerContactLastName: 'Doe',
          priority: 'High',
          priorityId: 'priority-1',
          assignedAgentId: 'user-123',
          assignedAgentEmail: '<EMAIL>',
          assignedAgent: 'Agent Name',
          createdAt: '2023-01-01T12:00:00Z',
          requestorEmail: '<EMAIL>',
          submitterEmail: '<EMAIL>',
        },
        platformTeamId: 'team-123',
        slackMessage: {
          id: 'message-123',
          ts: '**********.123456',
        },
      };

      // Act
      await (handler as any).handleTicketCreated(installation, data);

      // Assert
      expect(mockHandleTicketCreated).toHaveBeenCalledWith(installation, data);
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Evaluating and sending triage messages'),
      );
      expect(
        mockTriageEvaluationService.evaluateAndSendTriageMessages,
      ).toHaveBeenCalledWith(installation, data);
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining(
          'Successfully sent triage messages for ticket ticket-123',
        ),
      );
    });

    it('should handle errors when evaluating and sending triage messages', async () => {
      // Create a mock implementation of the private method
      const mockHandleTicketCreated = vi
        .fn()
        .mockImplementation(async (installation, data) => {
          mockLogger.debug(
            `[handleTicketCreated] [${installation?.id}] [${data.ticket.id}] Evaluating and sending triage messages`,
          );

          try {
            // Mock the evaluateAndSendTriageMessages call to throw an error
            mockTriageEvaluationService.evaluateAndSendTriageMessages.mockRejectedValueOnce(
              new Error('Failed to send triage messages'),
            );

            await mockTriageEvaluationService.evaluateAndSendTriageMessages(
              installation,
              data,
            );
          } catch (error) {
            mockLogger.error(
              `[handleTicketCreated] [${installation?.id}] [${data.ticket.id}] Error evaluating and sending triage messages for ticket ${data.ticket.id}`,
              error instanceof Error ? error.stack : undefined,
            );

            // Report to Sentry
            mockSentryService.captureException(error, {
              tag: 'slack',
              name: '🚨 Error evaluating and sending triage messages',
              ticketId: data.ticket.id,
              installationId: installation.id,
              platformTeamId: data.platformTeamId,
            });

            // Throw the error to propagate it up
            throw error;
          }

          return Promise.resolve();
        });

      // Replace the private method with our mock
      (handler as any).handleTicketCreated = mockHandleTicketCreated;

      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
        organization: {
          id: 'org-123',
          uid: 'org-uid',
        },
      };

      const data = {
        ticket: {
          id: 'ticket-123',
          title: 'Test Ticket',
        },
        platformTeamId: 'team-123',
        slackMessage: {
          id: 'message-123',
          ts: '**********.123456',
        },
      };

      // Act & Assert
      try {
        await (handler as any).handleTicketCreated(installation, data);
      } catch (error) {
        // Expected to throw an error
      }

      // Assert
      expect(mockHandleTicketCreated).toHaveBeenCalledWith(installation, data);
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Evaluating and sending triage messages'),
      );
      expect(
        mockTriageEvaluationService.evaluateAndSendTriageMessages,
      ).toHaveBeenCalledWith(installation, data);
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Error evaluating and sending triage messages for ticket ticket-123',
        ),
        expect.any(String),
      );
      expect(mockSentryService.captureException).toHaveBeenCalled();
    });
  });
});
