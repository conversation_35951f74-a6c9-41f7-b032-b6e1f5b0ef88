import { beforeEach, describe, expect, it, vi } from 'vitest';
import { TicketCreated<PERSON>andler } from '../../../../../src/platform/event-handlers/tickets/ticket-created.handler';
import { PlatformWebhookEvent } from '../../../../../src/platform/type-system';
import { SLACK_SENTRY_TAG } from '../../../../../src/utils';

describe('TicketCreatedHandler - Improved Tests', () => {
  let handler: TicketCreatedHandler;
  let mockLogger: any;
  let mockTriageEvaluationService: any;
  let mockSentryService: any;
  let mockInstallationsRepository: any;
  let mockSlackMessageCore: any;
  let mockPlatformService: any;
  // No need for mockTeamChannelMapsRepository as it's not used in this handler

  const mockTicket = {
    id: 'ticket-123',
    title: 'Test Ticket',
    ticketId: 123,
    description: 'Test description',
    priorityId: 'priority-123',
    priorityName: 'High',
    customerContactEmail: '<EMAIL>',
    customerContactFirstName: 'John',
    customerContactLastName: 'Doe',
    statusId: 'status-123',
    statusName: 'Open',
    source: 'web',
    teamId: 'team-123',
    assignedTo: 'user-123',
    requestorEmail: '<EMAIL>',
    submitterEmail: '<EMAIL>',
    teamIdentifier: 'TEAM',
    sentimentName: 'Positive',
    assignedAgent: {
      id: 'user-123',
      email: '<EMAIL>',
      name: 'Agent Name',
    },
    customer: {
      id: 'customer-123',
      name: 'John Doe',
      email: '<EMAIL>',
    },
    tags: ['test'],
    customFields: [{ field1: 'value1' }],
    metadata: {},
    createdAt: new Date().toISOString(),
    isEscalated: false,
    isArchived: false,
    aiGeneratedTitle: 'AI Test Ticket',
    aiGeneratedSummary: 'AI generated summary',
  };

  const mockInstallation = {
    id: 'installation-123',
    botToken: 'xoxb-token',
    organization: { id: 'org-123', uid: 'org-123' },
  };

  const mockInstallation2 = {
    id: 'installation-456',
    botToken: 'xoxb-token-2',
    organization: { id: 'org-123', uid: 'org-123' },
  };

  const mockSlackMessage = {
    id: 'slack-message-123',
    ts: '1234567890.123456',
    channel: 'C12345',
    text: 'Test message',
    metadata: {
      ticket_details: {
        statusId: 'status-123',
        priorityId: 'priority-123',
      },
    },
  };

  const createMockEvent = (
    eventType = 'ticket:created',
  ): PlatformWebhookEvent<'ticket:created'> => ({
    xWebhookEvent: true as const,
    message: {
      actor: {
        email: '<EMAIL>',
        id: 'user-123',
        type: 'user',
      },
      eventId: 'event-123',
      eventType: eventType as any,
      orgId: 'org-123',
      payload: {
        ticket: mockTicket,
      },
      timestamp: '2023-01-01T12:00:00Z',
    },
  });

  beforeEach(async () => {
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    };

    // No need to initialize mockTeamChannelMapsRepository as it's not used in this handler

    mockTriageEvaluationService = {
      evaluateAndSendTriageMessages: vi.fn().mockResolvedValue(undefined),
    };

    mockSentryService = {
      captureException: vi.fn(),
    };

    mockInstallationsRepository = {
      findAll: vi.fn().mockResolvedValue([mockInstallation]),
    };

    mockSlackMessageCore = {
      getSlackMessageByPlatformTicketId: vi
        .fn()
        .mockResolvedValue(mockSlackMessage),
    };

    mockPlatformService = {
      getTicket: vi.fn().mockResolvedValue(mockTicket),
      createNewTicket: vi.fn().mockResolvedValue(mockTicket),
      updateTicket: vi.fn().mockResolvedValue(mockTicket),
    };

    // Create handler directly
    handler = new TicketCreatedHandler(
      mockLogger,
      mockSentryService,
      mockSlackMessageCore,
      mockTriageEvaluationService,
      mockPlatformService,
      mockInstallationsRepository,
    );
  });

  describe('handle', () => {
    it('should handle ticket created event successfully', async () => {
      // Setup
      const event = createMockEvent();

      // Execute
      await handler.handle(event);

      // Verify
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining('Processing ticket created: ticket-123'),
      );
      expect(mockInstallationsRepository.findAll).toHaveBeenCalledWith({
        where: { organization: { uid: 'org-123' } },
        relations: { organization: true },
      });
      expect(
        mockSlackMessageCore.getSlackMessageByPlatformTicketId,
      ).toHaveBeenCalledWith(mockInstallation, 'ticket-123', {
        createIndependentIfNotFound: true,
      });
      expect(
        mockTriageEvaluationService.evaluateAndSendTriageMessages,
      ).toHaveBeenCalledWith(
        mockInstallation,
        expect.objectContaining({
          ticket: expect.objectContaining({
            id: 'ticket-123',
            title: 'Test Ticket',
          }),
          slackMessage: mockSlackMessage,
        }),
      );
    });

    it('should skip processing if event type is not ticket:created', async () => {
      // Setup
      const event = createMockEvent('ticket:updated');

      // Execute
      await handler.handle(event);

      // Verify
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Skipping ticket created event processor'),
      );
      expect(mockInstallationsRepository.findAll).not.toHaveBeenCalled();
      expect(
        mockSlackMessageCore.getSlackMessageByPlatformTicketId,
      ).not.toHaveBeenCalled();
      expect(
        mockTriageEvaluationService.evaluateAndSendTriageMessages,
      ).not.toHaveBeenCalled();
    });

    it('should handle multiple installations', async () => {
      // Setup
      const event = createMockEvent();
      mockInstallationsRepository.findAll.mockResolvedValue([
        mockInstallation,
        mockInstallation2,
      ]);

      // Execute
      await handler.handle(event);

      // Verify
      expect(
        mockSlackMessageCore.getSlackMessageByPlatformTicketId,
      ).toHaveBeenCalledTimes(2);
      expect(
        mockTriageEvaluationService.evaluateAndSendTriageMessages,
      ).toHaveBeenCalledTimes(2);

      // Verify first installation call
      expect(
        mockSlackMessageCore.getSlackMessageByPlatformTicketId,
      ).toHaveBeenNthCalledWith(1, mockInstallation, 'ticket-123', {
        createIndependentIfNotFound: true,
      });

      // Verify second installation call
      expect(
        mockSlackMessageCore.getSlackMessageByPlatformTicketId,
      ).toHaveBeenNthCalledWith(2, mockInstallation2, 'ticket-123', {
        createIndependentIfNotFound: true,
      });
    });

    it('should handle error when getSlackMessageByPlatformTicketId fails', async () => {
      // Setup
      const event = createMockEvent();
      const error = new Error('Failed to get slack message');
      mockSlackMessageCore.getSlackMessageByPlatformTicketId.mockRejectedValue(
        error,
      );

      // Execute and expect error
      await expect(handler.handle(event)).rejects.toThrow();

      // Verify
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error processing ticket created event'),
        expect.any(String),
      );
      expect(mockSentryService.captureException).toHaveBeenCalledWith(
        error,
        expect.objectContaining({
          tag: SLACK_SENTRY_TAG,
          name: '🚨 Error processing ticket created event',
          eventId: 'event-123',
          ticketId: 'ticket-123',
          orgId: 'org-123',
        }),
      );
    });

    it('should handle error when evaluateAndSendTriageMessages fails', async () => {
      // Setup
      const event = createMockEvent();
      const error = new Error('Failed to evaluate and send triage messages');
      mockTriageEvaluationService.evaluateAndSendTriageMessages.mockRejectedValue(
        error,
      );

      // Execute and expect error
      await expect(handler.handle(event)).rejects.toThrow();

      // Verify
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error processing ticket created event'),
        expect.any(String),
      );
      expect(mockSentryService.captureException).toHaveBeenCalledWith(
        error,
        expect.objectContaining({
          tag: SLACK_SENTRY_TAG,
          name: '🚨 Error processing ticket created event',
        }),
      );
    });
  });

  describe('handleTicketCreated', () => {
    it('should call evaluateAndSendTriageMessages with correct parameters', async () => {
      // Setup
      const data = {
        ticket: {
          id: 'ticket-123',
          title: 'Test Ticket',
          ticketId: 123,
          description: 'Test description',
          teamId: 'team-123',
          status: 'Open',
          statusId: 'status-123',
          customerContactEmail: '<EMAIL>',
          customerContactFirstName: 'John',
          customerContactLastName: 'Doe',
          priority: 'High',
          priorityId: 'priority-123',
          assignedAgentId: 'user-123',
          assignedAgentEmail: '<EMAIL>',
          assignedAgent: 'Agent Name',
          createdAt: '2023-01-01T12:00:00Z',
          requestorEmail: '<EMAIL>',
          submitterEmail: '<EMAIL>',
        },
        platformTeamId: 'team-123',
        slackMessage: mockSlackMessage,
      };

      // Access the private method using any type
      await (handler as any).handleTicketCreated(mockInstallation, data);

      // Verify
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('Evaluating and sending triage messages'),
      );
      expect(
        mockTriageEvaluationService.evaluateAndSendTriageMessages,
      ).toHaveBeenCalledWith(mockInstallation, data);
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining(
          'Successfully sent triage messages for ticket ticket-123',
        ),
      );
    });

    it('should handle error in handleTicketCreated method', async () => {
      // Setup
      const data = {
        ticket: {
          id: 'ticket-123',
          title: 'Test Ticket',
          ticketId: 123,
          description: 'Test description',
          teamId: 'team-123',
          status: 'Open',
          statusId: 'status-123',
          customerContactEmail: '<EMAIL>',
          customerContactFirstName: 'John',
          customerContactLastName: 'Doe',
          priority: 'High',
          priorityId: 'priority-123',
          assignedAgentId: 'user-123',
          assignedAgentEmail: '<EMAIL>',
          assignedAgent: 'Agent Name',
          createdAt: '2023-01-01T12:00:00Z',
          requestorEmail: '<EMAIL>',
          submitterEmail: '<EMAIL>',
        },
        platformTeamId: 'team-123',
        slackMessage: mockSlackMessage,
      };

      const error = new Error('Failed to evaluate and send triage messages');
      mockTriageEvaluationService.evaluateAndSendTriageMessages.mockRejectedValue(
        error,
      );

      // Access the private method using any type and expect error
      await expect(
        (handler as any).handleTicketCreated(mockInstallation, data),
      ).rejects.toThrow();

      // Verify
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Error evaluating and sending triage messages for ticket ticket-123',
        ),
        expect.any(String),
      );
      expect(mockSentryService.captureException).toHaveBeenCalledWith(
        error,
        expect.objectContaining({
          tag: SLACK_SENTRY_TAG,
          name: '🚨 Error evaluating and sending triage messages',
          ticketId: 'ticket-123',
          installationId: 'installation-123',
          platformTeamId: 'team-123',
        }),
      );
    });
  });
});
