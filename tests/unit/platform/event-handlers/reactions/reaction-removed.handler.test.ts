import { beforeEach, describe, expect, it, vi } from 'vitest';
import { CommentReactionRemovedHandler } from '../../../../../src/platform/event-handlers/reactions/reaction-removed.handler';
import { PlatformWebhookEvent } from '../../../../../src/platform/type-system';

describe('CommentReactionRemovedHandler', () => {
  let handler: CommentReactionRemovedHandler;
  let mockLogger: any;
  let mockSentryService: any;
  let mockSlackApiProvider: any;
  let mockThenaPlatformApiProvider: any;
  let mockSlackMessagesRepository: any;
  let mockCommentConversationMapsRepository: any;
  let mockUsersRepository: any;

  beforeEach(() => {
    // Create mocks
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    mockSentryService = {
      captureException: vi.fn(),
    };

    mockSlackApiProvider = {
      addReactionToMessage: vi.fn().mockResolvedValue(undefined),
      removeReactionFromMessage: vi.fn().mockResolvedValue(undefined),
    };

    mockThenaPlatformApiProvider = {
      proxy: vi.fn(),
    };

    mockSlackMessagesRepository = {
      findByCondition: vi.fn(),
    };

    mockCommentConversationMapsRepository = {
      findByCondition: vi.fn(),
    };

    mockUsersRepository = {
      findOneBy: vi.fn(),
    };

    // Create handler instance directly with mocks
    handler = new CommentReactionRemovedHandler(
      mockLogger,
      mockSentryService,
      mockSlackApiProvider,
      mockThenaPlatformApiProvider,
      mockSlackMessagesRepository,
      mockCommentConversationMapsRepository,
      mockUsersRepository,
    );
  });

  describe('handle', () => {
    it('should process reaction removed event when slack message is found', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:reaction:removed',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            comment: {
              id: 'comment-123',
            },
            reaction: {
              name: 'thumbsup',
              author: {
                email: '<EMAIL>',
              },
            },
          },
        },
      } as unknown as PlatformWebhookEvent;

      const mockSlackMessage = {
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          channelId: 'C12345',
        },
        slackMessageTs: '**********.123456',
        organization: {
          id: 'org-123',
        },
      };

      mockSlackMessagesRepository.findByCondition.mockResolvedValue(
        mockSlackMessage,
      );

      // Create a spy on the removeReactionFromMessage method
      const removeReactionFromMessageSpy = vi.spyOn(
        handler as any,
        'removeReactionFromMessage',
      );

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Processing comment reaction removed on: comment-123',
        ),
      );
      expect(mockSlackMessagesRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          platformCommentId: 'comment-123',
          organization: { uid: 'org-123' },
        },
        relations: { installation: true, channel: true, organization: true },
      });
      expect(removeReactionFromMessageSpy).toHaveBeenCalledWith(
        mockSlackMessage.installation,
        'org-123',
        mockSlackMessage.channel.channelId,
        mockSlackMessage.slackMessageTs,
        'thumbsup',
        '<EMAIL>',
      );
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Successfully processed reaction thumbsup removed from comment comment-123',
        ),
      );
    });

    it('should process reaction removed event when slack message is not found but comment conversation map is found', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:reaction:removed',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            comment: {
              id: 'comment-123',
            },
            reaction: {
              name: 'thumbsup',
              author: {
                email: '<EMAIL>',
              },
            },
          },
        },
      } as unknown as PlatformWebhookEvent;

      mockSlackMessagesRepository.findByCondition.mockResolvedValue(null);

      const mockCommentConversationMap = {
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          channelId: 'C12345',
        },
        slackTs: '**********.123456',
        organization: {
          id: 'org-123',
        },
      };

      mockCommentConversationMapsRepository.findByCondition.mockResolvedValue(
        mockCommentConversationMap,
      );

      // Create a spy on the removeReactionFromMessage method
      const removeReactionFromMessageSpy = vi.spyOn(
        handler as any,
        'removeReactionFromMessage',
      );

      // Act
      await handler.handle(event);

      // Assert
      expect(
        mockCommentConversationMapsRepository.findByCondition,
      ).toHaveBeenCalledWith({
        where: {
          platformCommentId: 'comment-123',
          organization: { uid: 'org-123' },
        },
        relations: { installation: true, channel: true, organization: true },
      });
      expect(removeReactionFromMessageSpy).toHaveBeenCalledWith(
        mockCommentConversationMap.installation,
        'org-123',
        mockCommentConversationMap.channel.channelId,
        mockCommentConversationMap.slackTs,
        'thumbsup',
        '<EMAIL>',
      );
    });

    it('should log error when neither slack message nor comment conversation map is found', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:reaction:removed',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            comment: {
              id: 'comment-123',
            },
            reaction: {
              name: 'thumbsup',
              author: {
                email: '<EMAIL>',
              },
            },
          },
        },
      } as unknown as PlatformWebhookEvent;

      mockSlackMessagesRepository.findByCondition.mockResolvedValue(null);
      mockCommentConversationMapsRepository.findByCondition.mockResolvedValue(
        null,
      );

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'No slack message found for comment comment-123',
        ),
      );
    });

    it('should handle error during processing', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:reaction:removed',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            comment: {
              id: 'comment-123',
            },
            reaction: {
              name: 'thumbsup',
              author: {
                email: '<EMAIL>',
              },
            },
          },
        },
      } as unknown as PlatformWebhookEvent;

      const testError = new Error('Test error');
      mockSlackMessagesRepository.findByCondition.mockRejectedValue(testError);

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Error processing comment reaction removed: Test error',
        ),
        expect.any(String),
      );
      expect(mockSentryService.captureException).toHaveBeenCalledWith(
        testError,
        {
          name: '🚨 Error processing comment reaction removed!',
          tag: 'SLACK-APP',
        },
      );
    });
  });

  describe('removeReactionFromMessage', () => {
    it('should remove reaction using user token when user is authorized', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };
      const orgId = 'org-123';
      const channel = 'C12345';
      const messageTs = '**********.123456';
      const reactionName = 'thumbsup';
      const userEmail = '<EMAIL>';

      const mockUser = {
        id: 'user-123',
        slackAccessToken: 'xoxp-user-token',
      };

      mockUsersRepository.findOneBy.mockResolvedValue(mockUser);

      // Act
      await (handler as any).removeReactionFromMessage(
        installation,
        orgId,
        channel,
        messageTs,
        reactionName,
        userEmail,
      );

      // Assert
      expect(mockUsersRepository.findOneBy).toHaveBeenCalledWith({
        slackProfileEmail: userEmail,
        installation: { id: installation.id },
        organization: { uid: orgId },
      });
      expect(
        mockSlackApiProvider.removeReactionFromMessage,
      ).toHaveBeenCalledWith(mockUser.slackAccessToken, {
        channel,
        timestamp: messageTs,
        name: reactionName,
      });
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          `Using user token for reaction ${reactionName} from user ${mockUser.id}`,
        ),
      );
    });

    it('should fall back to bot token when user is not found', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };
      const orgId = 'org-123';
      const channel = 'C12345';
      const messageTs = '**********.123456';
      const reactionName = 'thumbsup';
      const userEmail = '<EMAIL>';

      mockUsersRepository.findOneBy.mockResolvedValue(null);

      // Act
      await (handler as any).removeReactionFromMessage(
        installation,
        orgId,
        channel,
        messageTs,
        reactionName,
        userEmail,
      );

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          `Using bot token for reaction removal ${reactionName} - user not authorized or token failed`,
        ),
      );
      expect(
        mockSlackApiProvider.removeReactionFromMessage,
      ).toHaveBeenCalledWith(installation.botToken, {
        channel,
        timestamp: messageTs,
        name: reactionName,
      });
    });

    it('should fall back to bot token when user has no access token', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };
      const orgId = 'org-123';
      const channel = 'C12345';
      const messageTs = '**********.123456';
      const reactionName = 'thumbsup';
      const userEmail = '<EMAIL>';

      const mockUser = {
        id: 'user-123',
        slackAccessToken: null, // No access token
      };

      mockUsersRepository.findOneBy.mockResolvedValue(mockUser);

      // Act
      await (handler as any).removeReactionFromMessage(
        installation,
        orgId,
        channel,
        messageTs,
        reactionName,
        userEmail,
      );

      // Assert
      expect(
        mockSlackApiProvider.removeReactionFromMessage,
      ).toHaveBeenCalledWith(installation.botToken, {
        channel,
        timestamp: messageTs,
        name: reactionName,
      });
    });

    it('should handle no_reaction error from Slack API', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };
      const orgId = 'org-123';
      const channel = 'C12345';
      const messageTs = '**********.123456';
      const reactionName = 'thumbsup';
      const userEmail = '<EMAIL>';

      const mockUser = {
        id: 'user-123',
        slackAccessToken: 'xoxp-user-token',
      };

      mockUsersRepository.findOneBy.mockResolvedValue(mockUser);

      // Mock the removeReactionFromMessage to throw a no_reaction error
      const slackError = new Error('no_reaction');
      (slackError as any).data = { error: 'no_reaction' };
      mockSlackApiProvider.removeReactionFromMessage.mockRejectedValueOnce(
        slackError,
      );

      // Act
      await (handler as any).removeReactionFromMessage(
        installation,
        orgId,
        channel,
        messageTs,
        reactionName,
        userEmail,
      );

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          `Reaction ${reactionName} not found on message ${messageTs} in channel ${channel} by user ${userEmail}`,
        ),
      );
      // Should not fall back to bot token
      expect(
        mockSlackApiProvider.removeReactionFromMessage,
      ).toHaveBeenCalledTimes(1);
    });

    it('should fall back to bot token when user token fails with other error', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };
      const orgId = 'org-123';
      const channel = 'C12345';
      const messageTs = '**********.123456';
      const reactionName = 'thumbsup';
      const userEmail = '<EMAIL>';

      const mockUser = {
        id: 'user-123',
        slackAccessToken: 'xoxp-user-token',
      };

      mockUsersRepository.findOneBy.mockResolvedValue(mockUser);

      // Mock the removeReactionFromMessage to throw a different error
      const slackError = new Error('token_expired');
      mockSlackApiProvider.removeReactionFromMessage.mockRejectedValueOnce(
        slackError,
      );

      // Act
      await (handler as any).removeReactionFromMessage(
        installation,
        orgId,
        channel,
        messageTs,
        reactionName,
        userEmail,
      );

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Error using user token, falling back to bot token: token_expired',
        ),
      );
      expect(
        mockSlackApiProvider.removeReactionFromMessage,
      ).toHaveBeenCalledWith(installation.botToken, {
        channel,
        timestamp: messageTs,
        name: reactionName,
      });
    });

    it('should handle error when removing reaction with bot token', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };
      const orgId = 'org-123';
      const channel = 'C12345';
      const messageTs = '**********.123456';
      const reactionName = 'thumbsup';
      const userEmail = '<EMAIL>';

      mockUsersRepository.findOneBy.mockResolvedValue(null);

      // Mock the removeReactionFromMessage to throw an error
      const slackError = new Error('Failed to remove reaction');
      mockSlackApiProvider.removeReactionFromMessage.mockRejectedValueOnce(
        slackError,
      );

      // Act
      await (handler as any).removeReactionFromMessage(
        installation,
        orgId,
        channel,
        messageTs,
        reactionName,
        userEmail,
      );

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Error removing reaction from slack message: Failed to remove reaction',
        ),
      );
      expect(mockSentryService.captureException).toHaveBeenCalledWith(
        slackError,
        {
          name: '🚨 Error removing reaction from slack message!',
          tag: 'SLACK-APP',
        },
      );
    });
  });
});
