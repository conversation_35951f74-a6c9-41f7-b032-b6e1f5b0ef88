import { beforeEach, describe, expect, it, vi } from 'vitest';
import { CommentReactionAddedHandler } from '../../../../../src/platform/event-handlers/reactions/reaction-added.handler';
import { PlatformWebhookEvent } from '../../../../../src/platform/type-system';

describe('CommentReactionAddedHandler', () => {
  let handler: CommentReactionAddedHandler;
  let mockLogger: any;
  let mockSentryService: any;
  let mockSlackApiProvider: any;
  let mockThenaPlatformApiProvider: any;
  let mockSlackMessagesRepository: any;
  let mockCommentConversationMapsRepository: any;
  let mockUsersRepository: any;

  beforeEach(() => {
    // Create mocks
    mockLogger = {
      log: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
    };

    mockSentryService = {
      captureException: vi.fn(),
    };

    mockSlackApiProvider = {
      addReactionToMessage: vi.fn().mockResolvedValue(undefined),
      removeReactionFromMessage: vi.fn().mockResolvedValue(undefined),
    };

    mockThenaPlatformApiProvider = {
      proxy: vi.fn(),
    };

    mockSlackMessagesRepository = {
      findByCondition: vi.fn(),
    };

    mockCommentConversationMapsRepository = {
      findByCondition: vi.fn(),
    };

    mockUsersRepository = {
      findOneBy: vi.fn(),
    };

    // Create handler instance directly with mocks
    handler = new CommentReactionAddedHandler(
      mockLogger,
      mockSentryService,
      mockSlackApiProvider,
      mockThenaPlatformApiProvider,
      mockSlackMessagesRepository,
      mockCommentConversationMapsRepository,
      mockUsersRepository,
    );
  });

  describe('handle', () => {
    it('should process reaction added event when slack message is found', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:reaction:added',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            comment: {
              id: 'comment-123',
            },
            reaction: {
              name: 'thumbsup',
              author: {
                email: '<EMAIL>',
              },
            },
          },
        },
      } as unknown as PlatformWebhookEvent;

      const mockSlackMessage = {
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          channelId: 'C12345',
        },
        slackMessageTs: '**********.123456',
        organization: {
          id: 'org-123',
        },
      };

      mockSlackMessagesRepository.findByCondition.mockResolvedValue(
        mockSlackMessage,
      );

      // Create a spy on the addReactionWithUserToken method
      const addReactionWithUserTokenSpy = vi.spyOn(
        handler as any,
        'addReactionWithUserToken',
      );

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Processing comment reaction added on: comment-123',
        ),
      );
      expect(mockSlackMessagesRepository.findByCondition).toHaveBeenCalledWith({
        where: {
          platformCommentId: 'comment-123',
          organization: { uid: 'org-123' },
        },
        relations: { installation: true, channel: true, organization: true },
      });
      expect(addReactionWithUserTokenSpy).toHaveBeenCalledWith(
        mockSlackMessage.installation,
        mockSlackMessage.channel.channelId,
        mockSlackMessage.slackMessageTs,
        'thumbsup',
        '<EMAIL>',
        'org-123',
      );
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Successfully processed reaction thumbsup added to comment comment-123',
        ),
      );
    });

    it('should process reaction added event when slack message is not found but comment conversation map is found', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:reaction:added',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            comment: {
              id: 'comment-123',
            },
            reaction: {
              name: 'thumbsup',
              author: {
                email: '<EMAIL>',
              },
            },
          },
        },
      } as unknown as PlatformWebhookEvent;

      mockSlackMessagesRepository.findByCondition.mockResolvedValue(null);

      const mockCommentConversationMap = {
        installation: {
          id: 'installation-123',
          botToken: 'xoxb-token',
        },
        channel: {
          channelId: 'C12345',
        },
        slackTs: '**********.123456',
        organization: {
          id: 'org-123',
        },
      };

      mockCommentConversationMapsRepository.findByCondition.mockResolvedValue(
        mockCommentConversationMap,
      );

      // Create a spy on the addReactionWithUserToken method
      const addReactionWithUserTokenSpy = vi.spyOn(
        handler as any,
        'addReactionWithUserToken',
      );

      // Act
      await handler.handle(event);

      // Assert
      expect(
        mockCommentConversationMapsRepository.findByCondition,
      ).toHaveBeenCalledWith({
        where: {
          platformCommentId: 'comment-123',
          organization: { uid: 'org-123' },
        },
        relations: { installation: true, channel: true, organization: true },
      });
      expect(addReactionWithUserTokenSpy).toHaveBeenCalledWith(
        mockCommentConversationMap.installation,
        mockCommentConversationMap.channel.channelId,
        mockCommentConversationMap.slackTs,
        'thumbsup',
        '<EMAIL>',
        'org-123',
      );
    });

    it('should log error when neither slack message nor comment conversation map is found', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:reaction:added',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            comment: {
              id: 'comment-123',
            },
            reaction: {
              name: 'thumbsup',
              author: {
                email: '<EMAIL>',
              },
            },
          },
        },
      } as unknown as PlatformWebhookEvent;

      mockSlackMessagesRepository.findByCondition.mockResolvedValue(null);
      mockCommentConversationMapsRepository.findByCondition.mockResolvedValue(
        null,
      );

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'No slack message found for comment comment-123',
        ),
      );
    });

    it('should handle error during processing', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:reaction:added',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            comment: {
              id: 'comment-123',
            },
            reaction: {
              name: 'thumbsup',
              author: {
                email: '<EMAIL>',
              },
            },
          },
        },
      } as unknown as PlatformWebhookEvent;

      const testError = new Error('Test error');
      mockSlackMessagesRepository.findByCondition.mockRejectedValue(testError);

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Error processing comment reaction added: Test error',
        ),
        expect.any(String),
      );
      expect(mockSentryService.captureException).toHaveBeenCalledWith(
        testError,
        {
          name: '🚨 Error processing comment reaction added!',
          tag: 'SLACK-APP',
        },
      );
    });

    it('should skip processing if reaction is from slack itself', async () => {
      // Arrange
      const event = {
        xWebhookEvent: true,
        message: {
          eventId: 'event-123',
          eventType: 'ticket:comment:reaction:added',
          orgId: 'org-123',
          timestamp: '2023-01-01T12:00:00Z',
          payload: {
            comment: {
              id: 'comment-123',
            },
            reaction: {
              name: 'thumbsup',
              author: {
                email: '<EMAIL>',
              },
              metadata: {
                ignoreSelf: true,
              },
            },
          },
        },
      } as unknown as PlatformWebhookEvent;

      // Act
      await handler.handle(event);

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          'Reaction thumbsup ignored because it was added from slack itself',
        ),
      );
      expect(
        mockSlackMessagesRepository.findByCondition,
      ).not.toHaveBeenCalled();
    });
  });

  describe('addReactionWithUserToken', () => {
    it('should add reaction using user token when user is authorized', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };
      const channel = 'C12345';
      const messageTs = '**********.123456';
      const reactionName = 'thumbsup';
      const userEmail = '<EMAIL>';
      const orgId = 'org-123';

      const mockUser = {
        id: 'user-123',
        slackAccessToken: 'xoxp-user-token',
      };

      mockUsersRepository.findOneBy.mockResolvedValue(mockUser);

      // Act
      await (handler as any).addReactionWithUserToken(
        installation,
        channel,
        messageTs,
        reactionName,
        userEmail,
        orgId,
      );

      // Assert
      expect(mockUsersRepository.findOneBy).toHaveBeenCalledWith({
        slackProfileEmail: userEmail,
        installation: { id: installation.id },
        organization: { uid: orgId },
      });
      expect(mockSlackApiProvider.addReactionToMessage).toHaveBeenCalledWith(
        mockUser.slackAccessToken,
        {
          channel,
          timestamp: messageTs,
          name: reactionName,
        },
      );
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          `Using user token for reaction ${reactionName} from user ${mockUser.id}`,
        ),
      );
    });

    it('should fall back to bot token when user is not found', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };
      const channel = 'C12345';
      const messageTs = '**********.123456';
      const reactionName = 'thumbsup';
      const userEmail = '<EMAIL>';
      const orgId = 'org-123';

      mockUsersRepository.findOneBy.mockResolvedValue(null);

      // Create a spy on the addReactionToMessage method
      const addReactionToMessageSpy = vi.spyOn(
        handler as any,
        'addReactionToMessage',
      );

      // Act
      await (handler as any).addReactionWithUserToken(
        installation,
        channel,
        messageTs,
        reactionName,
        userEmail,
        orgId,
      );

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          `Using bot token for reaction ${reactionName} - user not authorized or token failed`,
        ),
      );
      expect(addReactionToMessageSpy).toHaveBeenCalledWith(
        installation.botToken,
        channel,
        messageTs,
        reactionName,
      );
    });

    it('should fall back to bot token when user has no access token', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };
      const channel = 'C12345';
      const messageTs = '**********.123456';
      const reactionName = 'thumbsup';
      const userEmail = '<EMAIL>';
      const orgId = 'org-123';

      const mockUser = {
        id: 'user-123',
        slackAccessToken: null, // No access token
      };

      mockUsersRepository.findOneBy.mockResolvedValue(mockUser);

      // Create a spy on the addReactionToMessage method
      const addReactionToMessageSpy = vi.spyOn(
        handler as any,
        'addReactionToMessage',
      );

      // Act
      await (handler as any).addReactionWithUserToken(
        installation,
        channel,
        messageTs,
        reactionName,
        userEmail,
        orgId,
      );

      // Assert
      expect(addReactionToMessageSpy).toHaveBeenCalledWith(
        installation.botToken,
        channel,
        messageTs,
        reactionName,
      );
    });

    it('should handle already_reacted error from Slack API', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };
      const channel = 'C12345';
      const messageTs = '**********.123456';
      const reactionName = 'thumbsup';
      const userEmail = '<EMAIL>';
      const orgId = 'org-123';

      const mockUser = {
        id: 'user-123',
        slackAccessToken: 'xoxp-user-token',
      };

      mockUsersRepository.findOneBy.mockResolvedValue(mockUser);

      // Mock the addReactionToMessage to throw an already_reacted error
      const slackError = new Error('already_reacted');
      (slackError as any).data = { error: 'already_reacted' };
      mockSlackApiProvider.addReactionToMessage.mockRejectedValueOnce(
        slackError,
      );

      // Act
      await (handler as any).addReactionWithUserToken(
        installation,
        channel,
        messageTs,
        reactionName,
        userEmail,
        orgId,
      );

      // Assert
      expect(mockLogger.log).toHaveBeenCalledWith(
        expect.stringContaining(
          `Reaction ${reactionName} already added to message ${messageTs} in channel ${channel} by user ${userEmail}`,
        ),
      );
      // Should not fall back to bot token
      expect(mockSlackApiProvider.addReactionToMessage).toHaveBeenCalledTimes(
        1,
      );
    });

    it('should fall back to bot token when user token fails with other error', async () => {
      // Arrange
      const installation = {
        id: 'installation-123',
        botToken: 'xoxb-token',
      };
      const channel = 'C12345';
      const messageTs = '**********.123456';
      const reactionName = 'thumbsup';
      const userEmail = '<EMAIL>';
      const orgId = 'org-123';

      const mockUser = {
        id: 'user-123',
        slackAccessToken: 'xoxp-user-token',
      };

      mockUsersRepository.findOneBy.mockResolvedValue(mockUser);

      // Mock the addReactionToMessage to throw a different error
      const slackError = new Error('token_expired');
      mockSlackApiProvider.addReactionToMessage.mockRejectedValueOnce(
        slackError,
      );

      // Create a spy on the addReactionToMessage method
      const addReactionToMessageSpy = vi.spyOn(
        handler as any,
        'addReactionToMessage',
      );

      // Act
      await (handler as any).addReactionWithUserToken(
        installation,
        channel,
        messageTs,
        reactionName,
        userEmail,
        orgId,
      );

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Error using user token, falling back to bot token: token_expired',
        ),
      );
      expect(addReactionToMessageSpy).toHaveBeenCalledWith(
        installation.botToken,
        channel,
        messageTs,
        reactionName,
      );
    });
  });

  describe('addReactionToMessage', () => {
    it('should add reaction to message using bot token', async () => {
      // Arrange
      const token = 'xoxb-token';
      const channel = 'C12345';
      const messageTs = '**********.123456';
      const reactionName = 'thumbsup';

      // Act
      await (handler as any).addReactionToMessage(
        token,
        channel,
        messageTs,
        reactionName,
      );

      // Assert
      expect(mockSlackApiProvider.addReactionToMessage).toHaveBeenCalledWith(
        token,
        {
          channel,
          timestamp: messageTs,
          name: reactionName,
        },
      );
    });

    it('should handle error when adding reaction', async () => {
      // Arrange
      const token = 'xoxb-token';
      const channel = 'C12345';
      const messageTs = '**********.123456';
      const reactionName = 'thumbsup';

      const slackError = new Error('Failed to add reaction');
      mockSlackApiProvider.addReactionToMessage.mockRejectedValueOnce(
        slackError,
      );

      // Act
      await (handler as any).addReactionToMessage(
        token,
        channel,
        messageTs,
        reactionName,
      );

      // Assert
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'Error adding reaction to slack message: Failed to add reaction',
        ),
      );
      expect(mockSentryService.captureException).toHaveBeenCalledWith(
        slackError,
        {
          name: '🚨 Error adding reaction to slack message!',
          tag: 'SLACK-APP',
        },
      );
    });
  });
});
