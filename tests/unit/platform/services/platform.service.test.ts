import { BadRequestException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { EventDeduplicationService } from '../../../../src/common/redis/event-deduplication.service';
import { Organizations } from '../../../../src/database/entities';
import { AppInstallation } from '../../../../src/platform/interfaces';
import { PlatformService } from '../../../../src/platform/services/platform.service';
import { PlatformWebhookEvent } from '../../../../src/platform/type-system';
import { ILogger } from '../../../../src/utils/logger';

describe('PlatformService', () => {
  let service: PlatformService;
  let mockLogger: ILogger;
  let mockOrganizationsRepository: Repository<Organizations>;
  let _mockEventHandlers: Map<string, any>;
  let mockEventDeduplicationService: EventDeduplicationService;

  beforeEach(async () => {
    // Reset mocks
    vi.resetAllMocks();

    // Create mock logger
    mockLogger = {
      log: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      verbose: vi.fn(),
    } as unknown as ILogger;

    // Create mock organizations repository
    mockOrganizationsRepository = {
      findOne: vi.fn(),
      save: vi.fn(),
      update: vi.fn(),
    } as unknown as Repository<Organizations>;

    // Create mock event deduplication service
    mockEventDeduplicationService = {
      processIdempotently: vi.fn(),
    } as unknown as EventDeduplicationService;

    // Create mock event handlers
    _mockEventHandlers = new Map<string, any>();

    // Create service instance directly
    service = new PlatformService(
      mockLogger,
      mockOrganizationsRepository,
      mockEventDeduplicationService,
    );
  });

  describe('installApp', () => {
    it('should update an existing organization with new metadata', async () => {
      // Arrange
      const installation: AppInstallation = {
        organization_id: 'org123',
        bot_token: 'xoxb-token',
        application_id: 'app123',
        installation_id: 'install123',
        created_by: 'user123',
        created_at: '2023-01-01T00:00:00Z',
        team_ids: ['team123'],
        configuration: {
          optional_settings: {},
          required_settings: {},
        },
        metadata: {
          slack_team_id: 'T12345',
        },
      };

      const existingOrg = {
        uid: 'org123',
        installingUserId: 'oldUser',
        metadata: {
          oldKey: 'oldValue',
        },
      };

      (mockOrganizationsRepository.findOne as any).mockResolvedValue(
        existingOrg,
      );

      // Act
      const result = await service.installApp(installation);

      // Assert
      expect(mockOrganizationsRepository.findOne).toHaveBeenCalledWith({
        where: { uid: installation.organization_id },
      });

      expect(mockOrganizationsRepository.update).toHaveBeenCalledWith(
        { uid: installation.organization_id },
        {
          apiKey: installation.bot_token,
          metadata: {
            oldKey: 'oldValue',
            applicationId: installation.application_id,
            createdBy: installation.created_by,
            installationId: installation.installation_id,
          },
        },
      );

      expect(result).toEqual({ ok: true });
    });

    it('should create a new organization if it does not exist', async () => {
      // Arrange
      const installation: AppInstallation = {
        organization_id: 'newOrg123',
        bot_token: 'xoxb-token',
        application_id: 'app123',
        installation_id: 'install123',
        created_by: 'user123',
        created_at: '2023-01-01T00:00:00Z',
        team_ids: ['team123'],
        configuration: {
          optional_settings: {},
          required_settings: {},
        },
        metadata: {
          slack_team_id: 'T12345',
        },
      };

      const newOrg = {
        uid: 'newOrg123',
        installingUserId: 'user123',
        metadata: {},
      };

      (mockOrganizationsRepository.findOne as any).mockResolvedValue(null);
      (mockOrganizationsRepository.save as any).mockResolvedValue(newOrg);

      // Act
      const result = await service.installApp(installation);

      // Assert
      expect(mockOrganizationsRepository.findOne).toHaveBeenCalledWith({
        where: { uid: installation.organization_id },
      });

      expect(mockOrganizationsRepository.save).toHaveBeenCalledWith({
        uid: installation.organization_id,
        installingUserId: installation.created_by,
      });

      expect(mockOrganizationsRepository.update).toHaveBeenCalledWith(
        { uid: installation.organization_id },
        {
          apiKey: installation.bot_token,
          metadata: {
            applicationId: installation.application_id,
            createdBy: installation.created_by,
            installationId: installation.installation_id,
          },
        },
      );

      expect(result).toEqual({ ok: true });
    });

    it('should handle errors during installation', async () => {
      // Arrange
      const installation: AppInstallation = {
        organization_id: 'org123',
        bot_token: 'xoxb-token',
        application_id: 'app123',
        installation_id: 'install123',
        created_by: 'user123',
        created_at: '2023-01-01T00:00:00Z',
        team_ids: ['team123'],
        configuration: {
          optional_settings: {},
          required_settings: {},
        },
        metadata: {
          slack_team_id: 'T12345',
        },
      };

      const error = new Error('Database connection error');
      (mockOrganizationsRepository.findOne as any).mockRejectedValue(error);

      // Mock console.error to prevent actual console logs during test
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      // Act & Assert
      await expect(service.installApp(installation)).rejects.toThrow(error);
      expect(consoleErrorSpy).toHaveBeenCalledWith(error);
    });
  });

  describe('setEventHandlers', () => {
    it('should set event handlers', () => {
      // Arrange
      const handlers = new Map<string, any>();
      handlers.set('event1', { instance: { handle: vi.fn() } });

      // Act
      service.setEventHandlers(handlers);

      // Assert - we'll verify this works in the handlePlatformEvent test
    });
  });

  describe('handlePlatformEvent', () => {
    it('should handle valid platform events', async () => {
      // Arrange
      const event: PlatformWebhookEvent = {
        xWebhookEvent: true,
        message: {
          actor: {
            email: '<EMAIL>',
            id: 'user123',
            type: 'user',
          },
          eventId: 'evt123',
          eventType: 'testEvent',
          orgId: 'org123',
          payload: { data: 'test' },
          timestamp: '2023-01-01T00:00:00Z',
        },
      };

      const expectedResult = { processed: true };
      const mockHandler = {
        instance: {
          handle: vi.fn().mockResolvedValue(expectedResult),
        },
      };

      const handlers = new Map<string, any>();
      handlers.set('testEvent', mockHandler);
      service.setEventHandlers(handlers);

      // Mock the processIdempotently method to call the callback immediately
      (
        mockEventDeduplicationService.processIdempotently as any
      ).mockImplementation(
        async (
          _eventId: string,
          _eventType: string,
          callback: () => Promise<any>,
        ) => {
          return await callback();
        },
      );

      // Act
      const result = await service.handlePlatformEvent(event);

      // Assert
      expect(mockHandler.instance.handle).toHaveBeenCalledWith(event);
      expect(result).toEqual(expectedResult);
      expect(
        mockEventDeduplicationService.processIdempotently,
      ).toHaveBeenCalledWith(
        'evt123',
        'testEvent',
        expect.any(Function),
        'platform:org123',
        'slack:event:status:',
      );
    });

    it('should return null for unknown event types', async () => {
      // Arrange
      const event: PlatformWebhookEvent = {
        xWebhookEvent: true,
        message: {
          actor: {
            email: '<EMAIL>',
            id: 'user123',
            type: 'user',
          },
          eventId: 'evt123',
          eventType: 'unknownEvent',
          orgId: 'org123',
          payload: { data: 'test' },
          timestamp: '2023-01-01T00:00:00Z',
        },
      };

      const handlers = new Map<string, any>();
      service.setEventHandlers(handlers);

      // Mock the processIdempotently method to call the callback immediately
      (
        mockEventDeduplicationService.processIdempotently as any
      ).mockImplementation(
        async (
          _eventId: string,
          _eventType: string,
          callback: () => Promise<any>,
        ) => {
          return await callback();
        },
      );

      // Act
      const result = await service.handlePlatformEvent(event);

      // Assert
      expect(result).toBeNull();
      expect(
        mockEventDeduplicationService.processIdempotently,
      ).toHaveBeenCalledWith(
        'evt123',
        'unknownEvent',
        expect.any(Function),
        'platform:org123',
        'slack:event:status:',
      );
    });

    it('should throw BadRequestException for invalid webhook events', async () => {
      // Arrange
      const invalidEvent = {
        xWebhookEvent: false,
        message: {
          actor: {
            email: '<EMAIL>',
            id: 'user123',
            type: 'user',
          },
          eventId: 'evt123',
          eventType: 'testEvent',
          orgId: 'org123',
          payload: { data: 'test' },
          timestamp: '2023-01-01T00:00:00Z',
        },
      };

      // Act & Assert
      await expect(
        service.handlePlatformEvent(invalidEvent as any),
      ).rejects.toThrow(BadRequestException);
    });
  });
});
