import { Test } from '@nestjs/testing';
import { describe, expect, it } from 'vitest';
import { ConfigService } from '../../../src/config/config.service';
import { ThenaPlatformApiProvider } from '../../../src/external/provider/thena-platform-api.provider';
import { FormBuilderService } from '../../../src/slack/services/form-builder.service';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../src/utils';

/**
 * Basic dependency injection test for FormBuilderService
 */
describe('FormBuilderService DI Test', () => {
  it('should create the FormBuilderService with its dependencies', async () => {
    // Create logger mock
    const logger = {
      log: () => {},
      error: () => {},
      warn: () => {},
      debug: () => {},
      verbose: () => {},
    } as ILogger;

    // Create mock API provider
    const apiProvider = {
      fetchFormsForTeam: () => Promise.resolve({ results: [] }),
    };

    // Create the test module
    const moduleRef = await Test.createTestingModule({
      providers: [
        FormBuilderService,
        {
          provide: ThenaPlatformApiProvider,
          useValue: apiProvider,
        },
        {
          provide: ConfigService,
          useValue: {
            get: () => {},
          },
        },
        {
          provide: CUSTOM_LOGGER_TOKEN,
          useValue: logger,
        },
      ],
    }).compile();

    // Get service instance
    const service = moduleRef.get<FormBuilderService>(FormBuilderService);

    // Just verify that the service was created successfully
    expect(service).toBeDefined();
    expect(service).toBeInstanceOf(FormBuilderService);
  });
});
