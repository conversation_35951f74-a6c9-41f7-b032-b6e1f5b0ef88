{"name": "thena-curated-apps-slack", "version": "0.0.1", "description": "", "author": "", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "clean:modules": "rimraf node_modules", "build": "rimraf dist && nest build && pnpm sentry:sourcemaps && pnpm sentry:sourcemaps", "build:clean": "rimraf dist && nest build", "format": "biome format ./src", "format:write": "biome format --write ./src", "start": "nest start", "start:dev": "nest start --watch | pino-pretty", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "biome check ./src", "lint:fix": "biome check --apply ./src", "test": "NODE_OPTIONS='--max-old-space-size=8192' vitest run", "test:watch": "NODE_OPTIONS='--max-old-space-size=8192' vitest", "test:unit": "NODE_OPTIONS='--max-old-space-size=8192' vitest run tests/unit", "test:integration": "NODE_OPTIONS='--max-old-space-size=8192' vitest run tests/integration", "test:e2e": "NODE_OPTIONS='--max-old-space-size=8192' vitest run tests/e2e", "test:coverage": "NODE_OPTIONS='--max-old-space-size=8192' vitest run --coverage", "test:clear": "vitest --clearCache", "test:typecheck": "tsc --noEmit && NODE_OPTIONS='--max-old-space-size=8192' vitest run --passWithNoTests", "test:fixed": "NODE_OPTIONS='--max-old-space-size=4096' vitest run tests/unit/slack/core/slack-channel/bot-channel-joined.handler.test.ts", "test:threads": "NODE_OPTIONS='--max-old-space-size=4096' vitest run --pool=vmThreads", "test:optimized": "NODE_OPTIONS='--max-old-space-size=6144' vitest run --pool=forks", "test:stable": "NODE_OPTIONS='--max-old-space-size=6144' vitest run --pool=forks --exclude=tests/unit/slack/processors/jobs/slack-users-sync.job.test.ts", "typeorm": "TS_NODE_PROJECT=./tsconfig.migration.json typeorm-ts-node-commonjs", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org thena --project thena-curated-apps-slack ./dist && sentry-cli sourcemaps upload --org thena --project thena-curated-apps-slack ./dist", "check-services": "node scripts/check-services.js", "check-api-keys": "node scripts/check-api-keys.js"}, "dependencies": {"@anthropic-ai/sdk": "^0.37.0", "@aws-sdk/client-sqs": "^3.738.0", "@nestjs/bullmq": "^11.0.1", "@nestjs/cache-manager": "^3.0.0", "@nestjs/common": "^10.4.4", "@nestjs/config": "^4.0.0", "@nestjs/core": "^10.4.4", "@nestjs/devtools-integration": "^0.2.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@sentry/cli": "^2.42.2", "@sentry/nestjs": "^9.4.0", "@sentry/node": "^8.53.0", "@sentry/profiling-node": "^9.4.0", "@slack/bolt": "^4.2.0", "@slack/types": "^2.14.0", "@slack/web-api": "^7.8.0", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-mention": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/html": "^2.11.5", "@tiptap/starter-kit": "^2.10.1", "ajv": "^8.17.1", "bullmq": "^5.37.0", "cache-manager": "^6.4.1", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cls-rtracer": "^2.6.3", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "fastify": "^5.3.3", "glob": "^11.0.1", "html-entities": "^2.5.2", "ioredis": "^5.6.1", "joi": "^17.13.3", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "nest-commander": "^3.16.0", "node-emoji": "^2.2.0", "node-vault": "^0.10.2", "openai": "^4.85.4", "pg": "^8.13.0", "pino": "^9.6.0", "qs": "^6.14.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "slack-block-builder": "^2.8.0", "uuid": "^11.0.5"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.1.3", "@nestjs/testing": "^10.4.4", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/lodash": "^4.17.15", "@types/node": "^22.9.0", "@types/qs": "^6.9.18", "@types/supertest": "^6.0.0", "@vitest/coverage-v8": "3.0.5", "pino-pretty": "^13.0.0", "rimraf": "^6.0.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typeorm": "^0.3.20", "typescript": "^5.6.3", "vitest": "^3.0.4"}, "packageManager": "pnpm@10.1.0+sha512.c89847b0667ddab50396bbbd008a2a43cf3b581efd59cf5d9aa8923ea1fb4b8106c041d540d08acb095037594d73ebc51e1ec89ee40c88b30b8a66c0fae0ac1b"}