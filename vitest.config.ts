/// <reference types="vitest" />
import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    include: ['tests/**/*.test.ts'],
    // Keep original exclude for non-coverage related exclusions
    exclude: ['**/node_modules/**', '**/dist/**'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        // Standard ignores
        'node_modules/**',
        'tests/**', // Exclude test files themselves from coverage report
        '**/dist/**', // Exclude build output

        // Type Definitions / Interfaces / DTOs / Constants
        'src/**/*.interface.ts',
        'src/**/*.dto.ts',
        'src/**/*.d.ts',
        'src/**/*interfaces/**', // Covers directories named 'interfaces'
        'src/**/*constants/**', // Covers directories named 'constants'
        'src/**/constants/**', // Alternative pattern for constants
        'src/**/interfaces/**', // Alternative pattern for interfaces
        'src/**/types/**', // Covers directories named 'types'
        'src/**/dtos/**', // Covers directories named 'dtos'
        'src/ai/constants/**', // Specific constants dir
        'src/ai/interfaces/**', // Specific interfaces dir
        'src/type-system/**', // Type definitions dir
        'src/platform/type-system/**', // Type definitions dir (may overlap with src/**/types/**)
        'src/utils/primitives/**', // Primitive types/helpers

        // Database specific (Entities are often just decorators + types)
        'src/database/migrations/**',
        'src/database/entities/**/*.entity.ts',
        'src/database/seeds/**', // NEW: Exclude seeding scripts

        // Entry points / Config / Setup
        'src/main.ts',
        'src/instrument.js',
        'src/config/env.validation.ts',
        '**/*.module.ts', // NEW: Exclude NestJS module files

        // Barrel Files (Re-exporting modules)
        'src/**/index.ts',

        // Specific low-logic components
        'src/**/errors/**', // NEW: Exclude custom error definitions
        'src/utils/logger/**', // NEW: Exclude logger setup
        'src/slack/query-params/**', // NEW: Exclude query param definitions (if simple like DTOs)
        'src/utils/filters/**', // Exclude utility filters (mostly simple decorators/pipes)
        'src/slack/abstract/emoji-action.abstract.ts',
        'src/slack/slack-common.providers.ts',
        'src/slack/core/factories/for-message.factory.ts',
        'src/external/external.service.ts',
        'src/slack/registry/action-registry.ts',
        'src/utils/vault/get-certificate.vault.ts',
        'src/database/datasource.ts',
        'src/auth/auth.service.ts',
        'src/app.controller.ts',
        'src/app.service.ts',
        'src/utils/web-api.client.ts',
        'vitest.config.ts',

        // Swagger/OpenAPI Documentation
        'src/utils/swagger.ts', // Swagger setup and configuration
        'swagger-output/**', // Generated Swagger files
        '**/swagger-output/**', // Generated Swagger files (any location)
        '**/*.swagger.ts', // Swagger-related files
        '**/*openapi*', // OpenAPI specification files
        '**/example-controller.ts', // Example controllers for documentation
        '**/swagger-enhancer.plugin.ts', // Swagger enhancement plugins
        '**/api-documentation.decorator.ts', // API documentation decorators
        '**/generate-swagger.js', // Swagger generation scripts

        // Test-related files (additional patterns)
        '**/*.test.ts',
        '**/*.spec.ts',
        '**/__tests__/**',
        '**/__mocks__/**',
        '**/tests/**',
        '**/mocks/**',
        '**/*.mock.ts',
        '**/*.fixture.ts',

        // Configuration files
        '**/*.config.ts',
        '**/*.config.js',
        '**/config/**/*.ts', // Config directories
        'src/config/throttler.config.ts', // Specific config files

        // NestJS Framework files (decorators, guards, filters, etc.)
        '**/*.decorator.ts',
        '**/*.guard.ts',
        '**/*.filter.ts',
        '**/*.interceptor.ts',
        '**/*.pipe.ts',
        '**/*.middleware.ts',
        'src/**/decorators/**',
        'src/**/guards/**',
        'src/**/filters/**',
        'src/**/interceptors/**',
        'src/**/pipes/**',
        'src/**/middleware/**',

        // Build and tooling files
        '**/*.d.ts', // TypeScript declaration files
        'tsconfig*.json',
        'jest.config.*',
        'vitest.config.*',
        '.eslintrc.*',
        'prettier.config.*',
        'turbo.json',
        'Dockerfile*',
        'docker-compose*',
        '.dockerignore',
        '.gitignore',

        // Documentation and examples
        '**/README.md',
        '**/docs/**',
        'src/utils/examples/**',

        // Generated or auto-generated files
        '**/generated/**',
        '**/auto-generated/**',

        // You might want to keep metadata in if it has logic, despite the name
        // 'src/slack/metadata/**',
      ],
    },
    environment: 'node',
    globals: true,
    testTimeout: 10000,
    hookTimeout: 10000,
    poolOptions: {
      threads: {
        singleThread: false,
        maxThreads: 4,
        minThreads: 1,
      },
      vmThreads: {
        singleThread: false,
        maxThreads: 4,
        minThreads: 1,
        memoryLimit: '2GB',
      },
      forks: {
        maxForks: 2,
        minForks: 2,
      },
    },
    sequence: {
      shuffle: true,
    },
    // Run tests in isolation to prevent memory leaks
    isolate: true,
    // Cleanup between tests to free memory
    restoreMocks: true,
    mockReset: true,
    clearMocks: true,
  },
});
